/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/2/2024, 2:55 PM
 */

import { Component, Input } from '@angular/core';
import { GisService } from '../service/gis.service';
import { environment } from '../../../../environments/environment';
import {DataService} from "../../../data/data.service";

export interface ButtonInfo {
  svgIcon: string;
  label: string;
  action: string;
  selected?: boolean;
  hidden?: boolean;
}

export interface ExpandedToolbars {
  entities: boolean;
  drawing: boolean;
  styling: boolean;
  movement: boolean;
  all: boolean;
}

@Component({
  selector: 'vcci-toolbar-container',
  styleUrls: ['./toolbar-container.component.scss'],
  templateUrl: './toolbar-container.component.html',
  standalone: false
})
export class ToolbarContainerComponent {
  env = environment;
  expandedToolbars: ExpandedToolbars = {
    entities: false,
    drawing: false,
    styling: false,
    movement: false,
    all: false
  };

  @Input() selectedShapeInfoInput: {
    shapeId: string;
    normal: {
      stroke: {
        color: string;
        type: string;
      };
      fill: {
        color: string;
        type: string;
      };
    };
    selected: {
      stroke: {
        color: string;
        type: string;
      };
      fill: {
        color: string;
        type: string;
      };
    };
  };
  protected readonly environment = environment;

  constructor(private gisService: GisService, protected data: DataService) {}

  toggleToolbars(expandedToolbars: ExpandedToolbars): void {
    this.expandedToolbars = expandedToolbars;
  }
}
