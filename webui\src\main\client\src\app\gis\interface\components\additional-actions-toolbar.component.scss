@import 'toolbar-mixins';

.additional-actions-toolbar {
  z-index: 450;
  display: flex;
  position: absolute;
  bottom: 5px;
  left: 10px;
}

.action-buttons-wrapper {
  display: none;

  &.expanded {
    display: flex;
  }
}

.button-wrapper {
  @include button-wrapper-styling;
}

.button-wrapper {
  width: 40px;
  height: 40px;
  padding: 0;
  margin-right: 2px;
}

button {
  @include toolbar-button-styling;
  border: unset;
}

button:hover {
  @include button-hover-styling;
}

button:active {
  @include button-active-styling;
}

::ng-deep .button-icon {
  @include toolbar-button-icon-styling;
}

::ng-deep .collapse {
  transform: rotate(-180deg);
}


