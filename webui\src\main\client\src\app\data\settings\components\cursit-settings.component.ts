import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSelectChange } from '@angular/material/select';

import { CursitSettings, DEFAULT_SETTINGS } from '../settings.model';

@Component({
  selector: 'vcci-cursit-settings',
  template: `
    <div
      style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;"
    >
      <h2>CURSIT Settings</h2>
      <button
        mat-stroked-button
        matTooltip="Reset default CURSIT Settings"
        (click)="resetSettings()"
        style="margin: 2px;"
      >
        Reset
      </button>
    </div>

    <div class="field">
      <h3>GIS Engine</h3>
      <mat-form-field>
        <mat-select
          [value]="settings.engine"
          (selectionChange)="onSelectChange('engine', $event)"
        >
          <mat-option value="Leaflet">Leaflet</mat-option>
          <mat-option value="Luciad 2D" disabled>Luciad 2D</mat-option>
          <mat-option value="Luciad 3D" disabled>Luciad 3D</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="field">
      <h3>Coordinate System</h3>
      <mat-form-field>
        <mat-select
          [value]="settings.locationFormat"
          (selectionChange)="onSelectChange('locationFormat', $event)"
        >
          <mat-option
            [vcciHoverOverlay]="previewTemplate"
            [context]="{ src: previewSrc['Lat Lon'] }"
            value="Lat Lon"
            >Lat/Lon</mat-option
          >
          <mat-option
            [vcciHoverOverlay]="previewTemplate"
            [context]="{ src: previewSrc['Mgrs'] }"
            value="Mgrs"
            >MGRS</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>
    <div class="field">
      <h3>Grid Colour</h3>
      <mat-form-field>
        <mat-select
          [value]="settings.gridColour"
          (selectionChange)="onSelectChange('gridColour', $event)"
        >
          <mat-option value="#7e1e9c">Purple</mat-option>
          <mat-option value="#033500">Dark Green</mat-option>
          <mat-option value="#00035b">Dark Blue</mat-option>
          <mat-option value="#650021">Maroon</mat-option>
          <mat-option value="#e6daa6">Beige</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <ng-template #previewTemplate let-src="src">
      <mat-card>
        <img
          mat-card-image
          class="preview-img"
          [ngSrc]="src"
          alt="Coordinates System Preview"
          width="258"
          height="54"
        />
      </mat-card>
    </ng-template>
  `,
  styleUrls: ['settings.component.css'],
  standalone: false
})
export class CursitSettingsComponent {
  @Input() settings: CursitSettings;
  @Output() onSettingsChanged = new EventEmitter<{
    cursit: Partial<CursitSettings>;
  }>();

  previewSrc = {
    Mgrs: 'assets/images/mgrs-preview.png',
    'Lat Lon': 'assets/images/latlng-preview.png'
  };

  constructor() {}

  onSelectChange(prop: string, event: MatSelectChange) {
    this.saveChange(prop, event.value);
  }

  onCheckboxChange(prop: string, event: MatCheckboxChange) {
    this.saveChange(prop, event.checked);
  }

  saveChange(prop: string, value: any) {
    this.onSettingsChanged.emit({ cursit: { [prop]: value } });
  }

  resetSettings() {
    this.onSettingsChanged.emit({ cursit: DEFAULT_SETTINGS.cursit });
  }
}
