import { Directive, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2 } from '@angular/core';
import { DraggableService } from '../services/draggable.service';

@Directive({
  selector: '[vcciDraggable]',
  standalone: true
})
export class DraggableDirective<T> implements OnInit, OnDestroy {
  private _onDragStartFunc: Function;
  private _onDragEnterFunc: Function;
  private _onDragEndFunc: Function;
  private _onDragOverFunc: Function;
  private _onDroppedFun: Function;

  @Input('vcciDraggable') data: T;
  @Input() isAccepted: (a: T, b: T) =>  boolean;

  @Output() onDragStart = new EventEmitter<{ event: DragEvent, data: T }>();
  @Output() onDragEnter = new EventEmitter<{ event: DragEvent, data: T }>();
  @Output() onDragOver = new EventEmitter<{ event: DragEvent, data: T }>();
  @Output() onDropped = new EventEmitter<{ data: T, dropped: T }>();

  constructor(private elementRef: ElementRef, private renderer: Renderer2, private dragApi: DraggableService) {
    this.renderer.setProperty(this.elementRef.nativeElement, 'draggable', true);
  }

  ngOnInit() {
    this.addDragEvents();
  }

  ngOnDestroy() {
    this._onDragStartFunc();
    this._onDragEnterFunc();
    this._onDragEndFunc();
    this._onDragOverFunc();
    this._onDroppedFun();
  }

  private addDragEvents(): void {
    this._onDragStartFunc = this.renderer.listen(
      this.elementRef.nativeElement,
      'dragstart',
      (event: DragEvent) => {
        this.dragApi.setData(this.data);
        this.onDragStart.emit({ event, data: this.data });
      }
    );
    this._onDragEndFunc = this.renderer.listen(
      this.elementRef.nativeElement,
      'dragend',
      () => this.dragApi.setData(undefined)
    );
    this._onDragEnterFunc = this.renderer.listen(
      this.elementRef.nativeElement,
      'dragenter',
      (event: DragEvent) => this.onDragEnter.emit({ event, data: this.dragApi.getData() })
    );
    this._onDragOverFunc = this.renderer.listen(
      this.elementRef.nativeElement,
      'dragover',
      (event: DragEvent) => {
        if (this.isAccepted && this.isAccepted(this.data, this.dragApi.getData())) {
          event.preventDefault();
        }
        this.onDragOver.emit({ event, data: this.dragApi.getData() });
      }
    );
    this._onDroppedFun = this.renderer.listen(
      this.elementRef.nativeElement,
      'drop',
      () => this.onDropped.emit({ data: this.dragApi.getData(), dropped: this.data })
    );
  }
}
