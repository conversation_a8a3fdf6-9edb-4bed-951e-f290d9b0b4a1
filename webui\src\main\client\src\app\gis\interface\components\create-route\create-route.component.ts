import {
  Component,
  inject,
  Renderer2,
  signal,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CdkDrag } from '@angular/cdk/drag-drop';
import {
  CoreModule,
  TableAction,
  TableColumn,
  TableSelectionMode
} from '@simfront/common-libs';
import { GisService, RouteClickData } from '../../service/gis.service';
import { ShapeType } from '../../models/map-element.model';
import { MilitaryElement } from '../../ElementFactory';
import { Coordinate } from 'ol/coordinate';
import { DataService } from 'src/app/data/data.service';
import { LatLng, Shapes } from 'src/app/data/entity/entity.model';
import { point } from 'leaflet';
import { Shape } from '@luciad/ria/shape/Shape';

@Component({
  selector: 'nexus-create-route',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    CdkDrag,
    CoreModule
  ],
  templateUrl: './create-route.component.html',
  styleUrl: './create-route.component.css'
})
export class CreateRouteComponent {
  gisService = inject(GisService);
  renderer = inject(Renderer2);
  dialogRef = inject(MatDialogRef<CreateRouteComponent>);
  data = inject(DataService);
  creatingRoute = signal<boolean>(true);
  indexToDelete = signal<number | undefined>(undefined);
  routePoints = this.gisService.routePoints;
  routePointsCount = this.gisService.routePointsCount;
  selectionMode = TableSelectionMode.Single;

  columns: TableColumn<RouteClickData>[] = [
    {
      header: 'Latitude',
      prop: (t) => t.latitude
    },
    {
      header: 'Longitude',
      prop: (t) => t.longitude
    }
  ];

  tableActions: TableAction<RouteClickData>[] = [
    {
      icon: 'delete',
      action: (e) => this.removePoint(this.routePoints().indexOf(e[0])),
      label: 'Delete'
    }
  ];

  onSelectionChange(e: RouteClickData): void {
    this.indexToDelete.set(this.routePoints().indexOf(e));
  }

  trackBy = (t: RouteClickData) =>
    (t.latitude + t.longitude) as unknown as string;

  startRouteCreation(): void {
    this.creatingRoute.set(true);
    this.gisService.startRouteCreation();
    this.renderer.setStyle(
      this.gisService.mapDisplay.getContainerDOM(),
      'cursor',
      'crosshair'
    );
  }

  endRouteCreation(): void {
    this.creatingRoute.set(false);
    this.gisService.cancelRouteCreation();
    this.renderer.setStyle(
      this.gisService.mapDisplay.getContainerDOM(),
      'cursor',
      'default'
    );
  }

  cancelRouteCreation(): void {
    this.endRouteCreation();
    this.clearRoutePoints();
  }

  clearRoutePoints(): void {
    this.gisService.clearRoutePoints();
  }

  removePoint(index: number): void {
    this.gisService.removeRoutePoint(index);
  }

  /**
   * Creates a route from the route points. Currently using the Polyline shape type.
   * TODO: Add support for routes (create a new class) to add labels, points for deletion, etc.
   */
  createRoute(): void {
    const linePoints = this.routePoints().map((point) => ({
      '@class': Shapes.Point,
      latitude : point.latitude,
      longitude : point.longitude
    }));
    // const coordinates: Coordinate[] = this.routePoints().map((point) => [
    //   point.latitude,
    //   point.longitude
    // ]);
    // const route: MilitaryElement = {
    //   type: ShapeType.POLYLINE,
    //   coordinates: coordinates as any
    // };
    // const layer = this.gisService.layerHandler.getLayer('-222');

    // layer.addElement(
    //   this.gisService.elementFactory.createMilitaryElement(route)
    // );
    // layer.redraw();
    // this.cancelRouteCreation();
    this.data.route.saveRoute({
      jRouteNameText: 'Test Route',
      jRouteLine: {
        '@class': Shapes.Line,
        linePoints
      }
    });
  }
}
