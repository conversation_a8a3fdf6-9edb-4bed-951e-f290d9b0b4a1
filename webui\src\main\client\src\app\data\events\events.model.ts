export const DataClassName = {
  'com.simfront.appobjmodel.obj.BattleSpaceRoutingNode': 'node',
  'com.simfront.applicationlayer.webui.obj.WebNode': 'node',
  'com.simfront.applicationlayer.webui.obj.WebConnection': 'connection',
  'com.simfront.applicationlayer.webui.obj.WebEndpoint': 'endpoint',
  'com.simfront.applicationlayer.webui.obj.WebDataPath': 'dataPath',
  'com.simfront.applicationlayer.webui.obj.WebLayerNode': 'layer',
  'com.simfront.applicationlayer.webui.obj.WebObject': 'route',
  'com.simfront.applicationlayer.webui.obj.WebMoveCommand': 'movement'
} as const;
export type DataNotificationCrud = 'Create' | 'Update' | 'Delete';
export interface DataNotification {
  crud: DataNotificationCrud;
  className: keyof typeof DataClassName;
  payload: any;
}
export const validCrud = (crud: any): boolean =>
  !!crud && (crud === 'Create' || crud === 'Update' || crud === 'Delete');
export const validClassName = (className: any): boolean =>
  !!className && Object.keys(DataClassName).includes(className);
export const isDataNotification = (val: any): val is DataNotification =>
  !!val['payload'] &&
  validCrud(val['crud']) &&
  validClassName(val['className']);
