import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { Component, inject, Renderer2, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { CursitControllerType } from 'src/app/cursit/models/cursit.model';
import { ShapeType } from 'src/app/cursit/service/gis.model';
import { DataService } from 'src/app/data/data.service';
import { GisService } from '../../service/gis.service';
import { cursitActions } from 'src/app/cursit/actions/cursit.actions';
import { EntitySearchComponent } from '../entity-search.component/entity-search.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { environment } from 'src/environments/environment';
import {
  EntityDetails,
  createEntityData
} from 'src/app/data/entity/entity.model';

@Component({
  selector: 'nexus-entity-menu',
  imports: [
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatMenuModule,
    CommonModule,
    FormsModule
  ],
  templateUrl: './entity-menu.component.html',
  styleUrl: './entity-menu.component.css'
})
export class EntityMenuComponent {
  data = inject(DataService);
  gisService = inject(GisService);
  private renderer = inject(Renderer2);
  bs = inject(MatBottomSheet);
  isProduction = environment.production;
  entitySelected = signal<EntityDetails[] | null>(null);

  entityDetails$ = this.data.entity.getSelectedEntityDetails();

  constructor() {
    this.entityDetails$.subscribe((details) => {
      this.entitySelected.set(details);
    });
  }

  activateEntityCreation(): void {
    this.data.entity.openCreateEntityDialog();
    this.gisService.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.Create,
        controllerPayload: {
          shapeType: ShapeType.POLYLINE,
          code: 'GFG*OLKGM-****X'
        }
      })
    );
  }

  openRouteCreation(): void {
    this.data.route.openRouteCreationDialog();
    this.gisService.startRouteCreation();
    this.renderer.setStyle(
      this.gisService.mapDisplay.getContainerDOM(),
      'cursor',
      'crosshair'
    );
  }

  startRouteCreation(): void {
    this.gisService.startRouteCreation();
  }

  hideAllUnits(): void {
    this.gisService.hideShowElementType('OR', true);
  }

  hideAllMateriels(): void {
    this.gisService.hideShowElementType('MA', true);
  }

  findEntity(): void {
    this.bs.open(EntitySearchComponent);
  }

  createFromSelection(): void {
    // set to the last element of the array
    const milStd =
      this.entitySelected()?.[this.entitySelected().length - 1]?.val;

    if (typeof milStd === 'string') {
      const entityDetails = this.entitySelected() || [];
      const detailsMap = new Map(
        entityDetails.map((detail) => [detail.key, detail.val])
      );

      const entityData: Partial<createEntityData> = {
        name: detailsMap.get('Name') as string,
        latitude: String(detailsMap.get('Latitude') || ''),
        longitude: String(detailsMap.get('Longitude') || ''),
        bearing: Number(detailsMap.get('Bearing') || 0),
        altitude: Number(detailsMap.get('Altitude') || 0),
        speed: Number(detailsMap.get('Speed') || 0)
      };

      this.data.entity.openCreateEntityDialog(entityData, milStd);
    }
  }
}
