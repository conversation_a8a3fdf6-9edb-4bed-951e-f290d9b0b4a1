/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/3/2024, 8:28 AM
 */
import * as L from 'leaflet';

export const GARSGraticuleLayer = L.Layer.extend({

  initialize: function (options) {

    this.garsLetters = [
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'J',
      'K',
      'L',
      'M',
      'N',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z',
    ];
    this._HALF_DEGREE = 0.5; // 30 minutes
    this._QUARTER_DEGREE = 0.25; // 15 minutes
    this._ONE_TWELFTH_DEGREE = this._QUARTER_DEGREE / 3; // 5 Minutes

    this._THIRTY_MINUTES_MIN_ZOOM = 8;
    this._FIFTEEN_MINUTES_MIN_ZOOM = 11;
    this._FIVE_MINUTES_MIN_ZOOM = 12;

    this._THIRTY_MINUTES_LINE_WIDTH = 3;
    this._FIFTEEN_MINUTES_LINE_WIDTH = 2;
    this._FIVE_MINUTES_LINE_WIDTH = 1;

    this._THIRTY_MINUTES_LINE_COLOR = '#F79800';
    this._FIFTEEN_MINUTES_LINE_COLOR = '#2C6FB6';
    this._FIVE_MINUTES_LINE_COLOR = '#FFF';

    this._FONT = '16px Courier New';
    this._FONT_COLOUR = '#000';

    this._setOptions(options);
  },

  _setOptions: function (options) {
    L.setOptions(this, options);
    L.stamp(this);
  },

  onAdd: function (map) {
    this.map = map;
    this.name = 'gars'
    this._initContainer();
    this.showGraticule();
  },

  onRemove: function (map) {
    this.clearRect();
  },

  _initContainer: function () {
    this.canvas = document.createElement('canvas');
    this.canvas.classList.add('leaflet-zoom-animated');
    this.canvas.classList.add(this.name);

    this.ctx = this.canvas.getContext('2d');

    // Strip any spaces as they can't be used in class names
    this.name = this.name.replace(/\s/g, '');

    this.options.showGrid = true;
    this.reset();

    // Add the canvas only if it hasn't already been added
    if (!this.map.getPanes().overlayPane.classList.contains(this.name)) {
      this.map.getPanes().overlayPane.appendChild(this.canvas);
      this.canvas.style.zIndex = 250;
    }
  },

  getEvents: function () {
    var events = {
      zoom: this._onZoom,
      move: this._onMove,
      moveend: this._update
    };
    if (this._zoomAnimated) {
      events.zoomanim = this._onAnimZoom;
    }
    return events;
  },

  _onZoom: function () {
    this.reset();
  },

  _update: function () {
    this.reset();
  },

  _onAnimZoom: function (e) {
    this.reset();
  },

  _onMove: function (e) {
    this.reset();
  },

  clearRect: function () {
    const ctx = this.canvas.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.options.showGrid = false;
    }
  },

  showGraticule: function () {
    this.options.showGrid = true;
    this.reset();
  },

  reset: function () {
    if (this.options.showGrid) {
      const mapSize = this.map.getSize();
      const mapLeftTop = this.map.containerPointToLayerPoint([0, 0]);
      this.canvas.style['transform'] = `translate3d(${mapLeftTop.x}px,${mapLeftTop.y}px,0)`;

      this.canvas.width = mapSize.x;
      this.canvas.height = mapSize.y;

      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      this.drawFiveMinuteGraticules(this.ctx);
      this.drawFifteenMinuteGraticules(this.ctx);
      this.drawThirtyMinuteGraticules(this.ctx);
      this.drawLabels(this.ctx);
    }
  },

  /**
   * This procedure will draw all 30 minute GARS labels on the visible map
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   */
  drawLabels: function (ctx) {
    if (!this.canvas || !this.map) {
      return;
    }

    if (this.map.getZoom() < this._THIRTY_MINUTES_MIN_ZOOM) {
      return;
    }

    ctx.strokeStyle = this._THIRTY_MINUTES_LINE_COLOR;
    ctx.fillStyle = this._THIRTY_MINUTES_LINE_COLOR;
    ctx.font = this._FONT;

    // Get the visible area boundaries
    const leftTopLl = this.map.containerPointToLatLng({
      x: 0,
      y: 0
    });
    const rightBottomLl = this.map.containerPointToLatLng({
      x: this.canvas.width,
      y: this.canvas.height
    });

    // Round the boundary LL up/down to make the bounding box larger than the visible area

    const effectiveLeftTopLl = {
      lat: Math.ceil(leftTopLl.lat / this._HALF_DEGREE) * this._HALF_DEGREE,
      lng: Math.floor(leftTopLl.lng / this._HALF_DEGREE) * this._HALF_DEGREE
    };

    const effectiveRightBottomLl = {
      lat: Math.floor(rightBottomLl.lat / this._HALF_DEGREE) * this._HALF_DEGREE,
      lng: Math.ceil(rightBottomLl.lng / this._HALF_DEGREE) * this._HALF_DEGREE
    };

    for (let currLng = effectiveLeftTopLl.lng; currLng <= effectiveRightBottomLl.lng; currLng += this._HALF_DEGREE) {
      for (let currLat = effectiveRightBottomLl.lat; currLat <= effectiveLeftTopLl.lat; currLat += this._HALF_DEGREE) {
        const labelText = this.getGARSNumbers(currLng) + this.getGARSLetters(currLat);
        const labelPosnXy = this.map.latLngToContainerPoint({
          lat: currLat + this._QUARTER_DEGREE,
          lng: currLng + this._QUARTER_DEGREE
        });

        this.drawLabel(ctx, labelText, labelPosnXy);
      }
    }
  },

  /**
   * This procedure will draw an individual GARS 30 minute label
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   * @param {string} labelText - The 30 minute GARS label
   * @param {Point} labelPosnXy - The XY position of the GARS label
   */
  drawLabel: function (ctx, labelText, labelPosnXy) {
    const textWidth = ctx.measureText(labelText).width;
    const textHeight = ctx.measureText(labelText).fontBoundingBoxAscent;

    ctx.fillStyle = this._THIRTY_MINUTES_LINE_COLOR;
    // Magic numbers centre the rectangle and text in the proper area
    ctx.fillRect(labelPosnXy.x - textWidth / 2 - 1, labelPosnXy.y - textHeight + 4, textWidth + 3, textHeight + 2);

    ctx.fillStyle = this._FONT_COLOUR;
    ctx.fillText(labelText, labelPosnXy.x - textWidth / 2, labelPosnXy.y + 3);
  },

  /**
   * This is a generic procedure which takes the properties of the graticule and then draws them
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   * @param {GraticuleProperties} graticuleProperties - The properties of the graticule being drawn
   */
  drawGraticules: function (ctx, graticuleProperties) {
    if (!this.canvas || !this.map) {
      return;
    }

    if (this.map.getZoom() < graticuleProperties.minZoom) {
      return;
    }

    ctx.lineWidth = graticuleProperties.lineWidth;
    ctx.strokeStyle = graticuleProperties.lineColor;
    ctx.fillStyle = graticuleProperties.lineColor;

    const leftTopLl = this.map.containerPointToLatLng({
      x: 0,
      y: 0
    });
    const rightBottomLl = this.map.containerPointToLatLng({
      x: this.canvas.width,
      y: this.canvas.height
    });

    const baseStartingLatitude = Math.floor(rightBottomLl.lat / this._HALF_DEGREE) * this._HALF_DEGREE;

    const baseStartingLongitude = Math.floor(leftTopLl.lng / this._HALF_DEGREE) * this._HALF_DEGREE;

    const startingLatitude = graticuleProperties.interval === 1
      ? baseStartingLatitude + this._QUARTER_DEGREE
      : baseStartingLatitude;

    // Horizontal Lines
    for (let i = startingLatitude; i <= leftTopLl.lat; i += graticuleProperties.increment) {
      if (!(graticuleProperties.interval === 2 && Math.abs(i % this._HALF_DEGREE) < 1e-6)) {
        this.drawLatitudeLine(ctx, i, leftTopLl.lng, rightBottomLl.lng);
      }
    }

    const startingLongitude = graticuleProperties.interval === 1
      ? baseStartingLongitude + this._QUARTER_DEGREE
      : baseStartingLongitude;

    // Vertical lines
    for (let i = startingLongitude; i <= rightBottomLl.lng; i += graticuleProperties.increment) {
      if (!(graticuleProperties.interval === 2 && Math.abs(i % this._HALF_DEGREE) < 1e-6)) {
        this.drawLongitudeLine(ctx, i, leftTopLl.lat, rightBottomLl.lat);
      }
    }
  },

  /**
   * This procedure draws 30 minute graticules
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   */
  drawThirtyMinuteGraticules: function (ctx) {
    const lineProperties = {
      interval: 0,
      increment: this._HALF_DEGREE,
      minZoom: this._THIRTY_MINUTES_MIN_ZOOM,
      lineWidth: this._THIRTY_MINUTES_LINE_WIDTH,
      lineColor: this._THIRTY_MINUTES_LINE_COLOR
    };

    this.drawGraticules(ctx, lineProperties);
  },

  /**
   * This procedure draws 15 minute graticules
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   */
  drawFifteenMinuteGraticules: function (ctx) {
    const lineProperties = {
      interval: 1,
      increment: this._HALF_DEGREE,
      minZoom: this._FIFTEEN_MINUTES_MIN_ZOOM,
      lineWidth: this._FIFTEEN_MINUTES_LINE_WIDTH,
      lineColor: this._FIFTEEN_MINUTES_LINE_COLOR
    };

    this.drawGraticules(ctx, lineProperties);
  },

  /**
   * This procedure draws 5 minute graticules
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   */
  drawFiveMinuteGraticules: function (ctx) {
    const lineProperties = {
      interval: 2,
      increment: this._ONE_TWELFTH_DEGREE,
      minZoom: this._FIVE_MINUTES_MIN_ZOOM,
      lineWidth: this._FIVE_MINUTES_LINE_WIDTH,
      lineColor: this._FIVE_MINUTES_LINE_COLOR
    };

    this.drawGraticules(ctx, lineProperties);
  },

  /**
   * This procedure will draw a single Latitude line on the map
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   * @param {number} latitude - The latitude of the latitude line to be drawn
   * @param {number} westLng - The West longitude boundary of the visible area
   * @param {number} eastLng - The East longitude boundary of the visible area
   */
  drawLatitudeLine: function (ctx, latitude, westLng, eastLng) {
    const westEnd = this.map.latLngToContainerPoint({
      lat: latitude,
      lng: westLng
    });

    const eastEnd = this.map.latLngToContainerPoint({
      lat: latitude,
      lng: eastLng
    });

    ctx.beginPath();
    ctx.moveTo(westEnd.x, westEnd.y);
    ctx.lineTo(eastEnd.x, eastEnd.y);
    ctx.stroke();
  },

  /**
   * This procedure will draw a single Longitude line on the map
   * @param {CanvasRenderingContext2D} ctx - The context of the canvas it is being drawn on
   * @param {number} longitude - The longitude of the longitude line to be drawn
   * @param {number} northLat - The North latitude boundary of the visible area
   * @param {number} southLat - The South latitude boundary of the visible area
   */
  drawLongitudeLine: function (ctx, longitude, northLat, southLat) {
    const northEnd = this.map.latLngToContainerPoint({
      lat: northLat,
      lng: longitude
    });

    const southEnd = this.map.latLngToContainerPoint({
      lat: southLat,
      lng: longitude
    });

    ctx.beginPath();
    ctx.moveTo(northEnd.x, northEnd.y);
    ctx.lineTo(southEnd.x, southEnd.y);
    ctx.stroke();
  },

  getGARSLetters: function (lat) {
    const difference = lat - -90;
    // Difference in degrees divided by 24 total GARS letters (at 0.5 degrees each)
    const firstChar = this.garsLetters[Math.floor(difference / 12)];
    // The remainder of the modulus indicates the number of degrees North we must traverse.
    // Dividing by 0.5 degrees indicates the final index in the garsLetters array
    const secondChar = this.garsLetters[Math.floor((difference % 12) / 0.5)];

    return firstChar + secondChar;
  },

  /**
   * This function returns the GARS longitude string for a given longitude
   * @param {number} lng - Longitude
   * @returns {string} - A GARS longitude string
   */
  getGARSNumbers: function (lng) {
    let count = 1;
    while (lng > -180) {
      lng -= 0.5;
      count++;
    }

    let result = count.toString();

    while (result.length < 3) {
      result = '0' + result;
    }
    return result;
  }


});
