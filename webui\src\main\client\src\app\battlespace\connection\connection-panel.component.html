@if (!isPopout) {
<ng-container>
  <div class="control-buttons" [ngClass]="{'expanded': expanded, 'left': isLeft, 'right': !isLeft}">
    <button mat-icon-button class="conn-expand-button" (click)="toggleExpand()"
      [matTooltip]="expanded ? 'Collapse Sidebar' : 'Expand Sidebar'">
      <mat-icon [@rotate]="expanded">
        {{isLeft ? 'chevron_right' : 'chevron_left'}}
      </mat-icon>
    </button>
    @if (position !== undefined) {
    <button mat-icon-button class="conn-expand-button" (click)="dockSidebar()"
      [matTooltip]="'Dock Sidebar to ' + ((position === 'left') ? 'Right' : 'Left')">
      <mat-icon>{{position === 'left' ? 'dock_to_left' : 'dock_to_right'}}</mat-icon>
    </button>
    }
    <button mat-icon-button class="conn-expand-button popout-button" (click)="popout.emit()"
      matTooltip="Popout Sidebar">
      <mat-icon>open_in_new</mat-icon>
    </button>
  </div>
</ng-container>
}

<mat-card-header>
  <mat-card-title [ngClass]="{'align-right': !isLeft}">{{ title }}</mat-card-title>
  <mat-card-subtitle>
    <mat-slide-toggle color="primary" class="conn-toggle" [checked]="displayType !== 'ALL'"
      (change)="toggleConnectionDisplayType($event.checked)">
      ENDPOINTS ONLY
    </mat-slide-toggle>
  </mat-card-subtitle>

  @if (displayType === 'ALL' && connections.length > 0) {
  <mat-card-subtitle class="header-actions">
    <button mat-button (click)="toggleExpandConnections()">
      <span>{{allConnectionsExpanded ? 'Collapse' : 'Expand'}} All Connections</span>
      <mat-icon [@rotate]="allConnectionsExpanded">expand_more</mat-icon>
    </button>
  </mat-card-subtitle>
  }
</mat-card-header>

<hr style="margin: 0 10px; opacity: 0.2;" />
<mat-card-content class="conn-container" [ngClass]="isLeft ? 'left' : 'right'">
  @for (con of connections; track con.name + ':' + con.node) {
  <vcci-connection-item [connection]="con" [support]="data.node.support$(con.node) | async" [displayType]="displayType"
    [selectedEndpoints]="selectedEndpointsByConn[con.name]" [expanded]="connectionExpandedMap[con.name]"
    (onExpanded)="onConnectionExpanded(con.name, $event)" />
  }

  @if ((this.data.connection.creating$ | async) > 0) {
  <div class="skeleton conn-progress-indicator"></div>
  }
</mat-card-content>
<hr style="margin: 0 10px; opacity: 0.2;" />

<mat-card-actions class="connection-actions" [class.right]="!isLeft">
  <button mat-stroked-button (click)="this.data.connection.openCreateConnectionDialog()"
    [matTooltip]="expanded || isPopout ? '' : 'Add New Connection'">
    <mat-icon>add_circle_outline</mat-icon>
    <span>ADD NEW CONNECTION</span>
  </button>
</mat-card-actions>
