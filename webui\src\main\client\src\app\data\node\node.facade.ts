import { Injectable } from '@angular/core';

import { createEffect, ofType } from '@ngrx/effects';

import { bufferTime, combineLatestWith, concatMap, exhaustMap, filter, first, map, of, switchMap, withLatestFrom} from 'rxjs';
import { ConnectionFacade } from '../connection/connection.facade';
import { EndpointFilterDialog } from '../endpoint/endpoint-filter/endpoint-filter.dialog';
import { Endpoint } from '../endpoint/endpoint.model';
import { environment } from '../../../environments/environment';
import {
  Node,
  NodeActions,
  NodeFacadeBase,
  NodeLaunchInstructions
} from './node.model';
import { HttpClient } from '@angular/common/http';
import { GenericDialog } from '../../shared/generic-dialog/generic-dialog.dialog';

@Injectable({ providedIn: 'root' })
export class NodeFacade extends NodeFacadeBase {
  // <editor-fold desc="Selectors">
  on$ = this.filter$((n) => n.launchable.status === 'ON');
  simMovementNode$ = this.get$('SIM_MOVE');
  filterInfos$ = (nodeId: string) =>
    this.entities$.pipe(map((entities) => entities[nodeId]?.supportedFilters));

  support$ = (nodeId: string) =>
    this.entities$.pipe(map((entities) => entities[nodeId]?.support));

  constructor(
    private connection: ConnectionFacade,
    private httpClient: HttpClient
  ) {
    super();
  }

  activateNode(node: NodeLaunchInstructions): void {
    this.store.dispatch(NodeActions.activateNode(node));
  }

  deactivateNode(name: string): void {
    this.store.dispatch(NodeActions.deactivateNode(name));
  }

  receivedNotification(node: Node): void {
    this.store.dispatch(NodeActions.notification(node));
  }

  openFiltersDialog(endpoint: Endpoint): void {
    this.store.dispatch(NodeActions.openFilterDialog(endpoint));
  }

  activateNode$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(NodeActions.activateNode),
        switchMap(({ payload }) => {
          return this.httpClient.post(
            `${environment.origin}/nodes/activate`,
            payload
          );
        })
      ),
    { dispatch: false }
  );

  deactivateNode$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(NodeActions.deactivateNode),
        switchMap(({ payload }) => {
          return this.httpClient.post(
            `${environment.origin}/nodes/deactivate/${payload}`,
            {}
          );
        })
      ),
    { dispatch: false }
  );

  NodeDeactivatedDialog$ = createEffect(() => this.actions$.pipe(
     ofType(NodeActions.deactivateNode),
     bufferTime(1000),
     filter(nodes => nodes.length > 0),
      map(nodes => {
        this.dialog.open(GenericDialog, {
          data: {
            nodes: nodes.map(node => node.payload),
            title: `Microservice Disconnected (${nodes.length})`,
              message:
                `Disconnected microservices <${nodes.map(node => node.payload).join(', ')}>. Restart to re-establish connections.`
           }
        });
      }),
   ), { dispatch: false });

  // TODO: Look into having the backend sending a Delete Connection Notification?
  receivedNotification$ = createEffect(() =>
    this.actions$.pipe(
      ofType(NodeActions.notification),
      map((action) => action.payload),
      withLatestFrom(this.entities$),
      concatMap(([node, nodes]) => {
        const currNode = nodes[node.name];
        const deleted =
          currNode?.connections?.filter((n) => !node.connections.includes(n)) ||
          [];
        // Check if some connections were removed.
        if (deleted.length > 0) {
          return [
            this.actions.updateOneReceived(node),
            ...deleted.map((c) =>
              this.connection.actions.deleteOneReceived(`${node.name}:${c}`)
            )
          ];
        }
        return [this.actions.updateOneReceived(node)];
      })
    )
  );

  openFilterDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(NodeActions.openFilterDialog),
      map((action) => action.payload),
      switchMap((endpoint) =>
        of(endpoint).pipe(
          combineLatestWith(this.filterInfos$(endpoint.node).pipe(first())),
          map(([endpoint, filterInfos]) => ({
            endpoint,
            filterInfos,
            filters: endpoint?.filters ?? [],
            busy: false
          }))
        )
      ),
      exhaustMap((data) =>
        this.dialog
          .open(EndpointFilterDialog, {
            data,
            maxWidth: '80vw',
            panelClass: 'custom-mat-dialog-panel',
            autoFocus: false,
            disableClose: true,
            hasBackdrop: false
          })
          .afterClosed()
      ),
      map(() => NodeActions.dismissFilterDialog())
    )
  );
  // </editor-fold>
}
