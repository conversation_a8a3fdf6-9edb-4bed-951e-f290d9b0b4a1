import { Component } from '@angular/core';
import { SFMap } from '@simfront/common-libs';
import {
  asyncScheduler,
  BehaviorSubject,
  combineLatestWith,
  debounceTime,
  map
} from 'rxjs';
import { DataService } from '../../data/data.service';
import {
  LOG_SEVERITIES,
  LogEntry,
  LogSeverity
} from '../../data/log/log.model';

export interface LogFilters {
  nodes: string[];
  severities: LogSeverity[];
  filter: string;
  grouped: boolean;
}

@Component({
  selector: 'log-container',
  templateUrl: 'log-container.component.html',
  styleUrls: ['log-container.component.css'],
  host: {
    style: `
      width: 100%;
      box-shadow: none;
    `
  },
  standalone: false
})
export class LogContainerComponent {
  filters$ = new BehaviorSubject<LogFilters>({
    nodes: ['VCCI'],
    severities: LOG_SEVERITIES,
    filter: '',
    grouped: false
  });

  nodes$ = this.dataService.node.on$.pipe(
    map((nodes) => ['VCCI', ...nodes.map((n) => n.name)])
  );
  logEntries$ = this.dataService.log.all$.pipe(
    combineLatestWith(this.filters$, this.nodes$),
    debounceTime(100, asyncScheduler),
    map((value) => this.filterLogEntries(value))
  );

  constructor(public dataService: DataService) {
    this.refreshLogs();
  }

  refreshLogs(): void {
    this.dataService.log.readAll();
  }

  onFiltersChange(filters: LogFilters): void {
    this.filters$.next(filters);
  }

  filterLogEntries([
    logEntries,
    { nodes, severities, filter, grouped },
    availableNodes
  ]: [logEntry: LogEntry[], filters: LogFilters, nodes: string[]]): SFMap<
    LogEntry[]
  > {
    const startGroup = grouped
      ? { Group: [] }
      : availableNodes
          .filter((n) => nodes.includes(n))
          .reduce((acc, n) => ({ [n]: [], ...acc }), {});
    return logEntries
      .filter(
        (e) =>
          severities.includes(e.severity) && this.filterValueMatch(e, filter)
      )
      .reduce<SFMap<LogEntry[]>>((acc, entry) => {
        if (nodes.includes(entry.originator)) {
          if (grouped) {
            acc['Group'] = [...(acc['Group'] ?? []), entry];
          } else {
            acc[entry.originator] = [...(acc[entry.originator] ?? []), entry];
          }
        }
        return acc;
      }, startGroup);
  }

  filterValueMatch(e: LogEntry, filter: string): boolean {
    const entry: string =
      `${new Date(e.time).toLocaleString()} ${e.severity} ${e.message}`.toLowerCase();
    const splits = filter.split('/');

    // Regex case
    if (splits.length > 1) {
      const entries: string[] = [
        new Date(e.time).toLocaleString(),
        e.severity,
        e.message
      ];
      const regexFlags: string = splits[splits.length - 1];
      const regexSearch: string = splits
        .filter((_, i) => i !== 0 && i !== splits.length - 1)
        .join('/');
      try {
        const regex = new RegExp(regexSearch, regexFlags);
        return entries.some((r) => r.search(regex) !== -1);
      } catch (_) {}
    }

    // Default to includes.
    return filter === '' || entry.includes(filter.toLowerCase());
  }
}
