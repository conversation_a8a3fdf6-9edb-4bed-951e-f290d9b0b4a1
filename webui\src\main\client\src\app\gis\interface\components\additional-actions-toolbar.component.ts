/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/6/2024, 3:01 PM
 */

import { Component } from '@angular/core';
import { GisService } from '../service/gis.service';

@Component({
    selector: 'vcci-additional-actions-toolbar',
    styleUrls: ['./additional-actions-toolbar.component.scss'],
    templateUrl: './additional-actions-toolbar.component.html',
    standalone: false
})

export class AdditionalActionsToolbarComponent {
  expanded: boolean = false;

  constructor(private gisService: GisService) {
  }

  toggleExpansion(): void {
    this.expanded = !this.expanded;
  }

  printMap(): void {
    this.gisService.printCursitMap();
  }

  addToFavorites(): void {

  }

  upload(): void {

  }

  radar(): void {

  }
}
