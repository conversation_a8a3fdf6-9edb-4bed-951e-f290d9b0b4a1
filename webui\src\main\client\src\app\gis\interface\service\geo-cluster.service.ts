import { Injectable } from '@angular/core';

const EARTH_RADIUS_KM = 6371; // Earth's ≈ radius in kilometers

export interface GeoPoint {
  lat: number;
  lng: number;
  alt?: number;
  id?: string | number;
}

export interface ClusterParams {
  maxDistance: number; // Maximum distance (in kilometers)
  minPoints: number; // Minimum points to form a cluster
}

@Injectable({
  providedIn: 'root'
})
export class GeoClusteringService {
  constructor() {}

  /**
   * Generate clusters from an array of points (stateless)
   * @param points Array of geographic points
   * @param params Clustering parameters
   * @returns Array of clusters, where each cluster is an array of points
   */
  public generateClusters(
    points: GeoPoint[],
    params: ClusterParams
  ): GeoPoint[][] {
    // Convert maxDistance from km to radians (for haversine)
    const maxDistanceRadians = params.maxDistance / EARTH_RADIUS_KM;

    return this.clusterGeoPoints(points, params.minPoints, maxDistanceRadians);
  }

  /**
   * Add a point to the appropriate cluster or create a new cluster
   * @param point The point to add
   * @param existingClusters Current clusters
   * @param params Clustering parameters
   * @returns Object containing updated clusters and the index of the affected cluster (-1 if none)
   */
  public addPointToClusters(
    point: GeoPoint,
    existingClusters: GeoPoint[][],
    params: ClusterParams
  ): {
    clusters: GeoPoint[][];
    affectedClusterIndex: number;
  } {
    // Convert maxDistance from km to radians
    const maxDistanceRadians = params.maxDistance / 6371;

    // First, check if the point belongs to an existing cluster
    const clusterInfo = this.findClusterAndNeighbors(
      point,
      existingClusters,
      maxDistanceRadians,
      params.minPoints
    );

    // Create a copy of existing clusters to avoid mutation
    const updatedClusters = existingClusters.map((cluster) => [...cluster]);

    if (clusterInfo.clusterIndex >= 0) {
      // Point belongs to an existing cluster - add it
      updatedClusters[clusterInfo.clusterIndex].push(point);
      return {
        clusters: updatedClusters,
        affectedClusterIndex: clusterInfo.clusterIndex
      };
    }

    // If we have enough neighbors to form a cluster
    if (clusterInfo.neighborCount >= params.minPoints - 1) {
      // Create a flat array of all points from all clusters + the new point
      const flattenedPoints: GeoPoint[] = [point];
      for (let i = 0; i < existingClusters.length; i++) {
        flattenedPoints.push(...existingClusters[i]);
      }

      // Recalculate clusters with the new point
      const recalculatedClusters = this.clusterGeoPoints(
        flattenedPoints,
        params.minPoints,
        maxDistanceRadians
      );

      // Find the index of the cluster containing our new point
      let newClusterIndex = -1;
      for (let i = 0; i < recalculatedClusters.length; i++) {
        if (
          recalculatedClusters[i].some(
            (p) =>
              p === point ||
              (p.id !== undefined &&
                point.id !== undefined &&
                p.id === point.id)
          )
        ) {
          newClusterIndex = i;
          break;
        }
      }

      return {
        clusters: recalculatedClusters,
        affectedClusterIndex: newClusterIndex
      };
    }

    // Not enough points to form a new cluster
    return {
      clusters: updatedClusters,
      affectedClusterIndex: -1
    };
  }

  private precalculateConstants(
    lat: number,
    lng: number
  ): { radLat: number; radLng: number; cosLat: number } {
    const radLat = (lat * Math.PI) / 180;
    const radLng = (lng * Math.PI) / 180;
    const cosLat = Math.cos(radLat);

    return { radLat, radLng, cosLat };
  }

  /**
   * Find which cluster a point belongs to and count neighbors across all clusters
   * @returns Object with cluster index and neighbor count
   */
  private findClusterAndNeighbors(
    point: GeoPoint,
    clusters: GeoPoint[][],
    maxDistanceRadians: number,
    minPoints: number
  ): {
    clusterIndex: number;
    neighborCount: number;
  } {
    let totalNeighborCount = 0;

    // Pre-calculate constants for the current point
    const { radLat, radLng, cosLat } = this.precalculateConstants(
      point.lat,
      point.lng
    );

    for (let i = 0; i < clusters.length; i++) {
      const cluster = clusters[i];

      // Count how many points in the cluster are within maxDistance
      let closePoints = 0;

      for (let j = 0; j < cluster.length; j++) {
        const distance = this.haversineDistanceOptimized(
          radLat,
          radLng,
          cosLat,
          cluster[j].lat,
          cluster[j].lng
        );

        if (distance <= maxDistanceRadians) {
          closePoints++;
          totalNeighborCount++;

          // If we find enough close points, this point belongs to this cluster
          if (closePoints >= minPoints) {
            return {
              clusterIndex: i,
              neighborCount: totalNeighborCount
            };
          }
        }
      }
    }

    return {
      clusterIndex: -1,
      neighborCount: totalNeighborCount
    };
  }

  /**
   * DBSCAN-based algorithm for clustering geographic coordinates
   */
  private clusterGeoPoints(
    points: GeoPoint[],
    minPoints: number,
    maxDistanceRadians: number
  ): GeoPoint[][] {
    // Track visited points and their cluster assignments
    const visited = new Set<number>();
    const clusters: GeoPoint[][] = [];

    // For each unvisited point, explore potential cluster
    for (let i = 0; i < points.length; i++) {
      if (visited.has(i)) continue;

      visited.add(i);

      // Find neighbors - use a more efficient neighbor finding for large datasets
      const neighbors = this.getNeighbors(points, i, maxDistanceRadians);

      // If not enough neighbors, mark as noise (don't track noise explicitly)
      if (neighbors.length < minPoints) {
        continue;
      }

      // Start a new cluster
      const cluster: GeoPoint[] = [points[i]];

      // Expand the cluster
      this.expandCluster(
        points,
        neighbors,
        cluster,
        visited,
        minPoints,
        maxDistanceRadians
      );

      clusters.push(cluster);
    }

    return clusters;
  }

  /**
   * Expand a cluster by exploring neighboring points
   */
  private expandCluster(
    points: GeoPoint[],
    neighbors: number[],
    cluster: GeoPoint[],
    visited: Set<number>,
    minPoints: number,
    maxDistanceRadians: number
  ): void {
    // Use queue-based approach for better memory efficiency
    const queue = [...neighbors];
    const processed = new Set<number>();

    let i = 0;
    while (i < queue.length) {
      const pointIdx = queue[i++];

      if (processed.has(pointIdx)) continue;
      processed.add(pointIdx);

      // Add point to cluster if not already present
      if (!cluster.includes(points[pointIdx])) {
        cluster.push(points[pointIdx]);
      }

      // If unvisited, mark as visited and find its neighbors
      if (!visited.has(pointIdx)) {
        visited.add(pointIdx);

        const pointNeighbors = this.getNeighbors(
          points,
          pointIdx,
          maxDistanceRadians
        );

        // If enough neighbors, add unprocessed ones to the queue
        if (pointNeighbors.length >= minPoints) {
          for (const newNeighbor of pointNeighbors) {
            if (!processed.has(newNeighbor) && !queue.includes(newNeighbor)) {
              queue.push(newNeighbor);
            }
          }
        }
      }
    }
  }

  /**
   * Find all points within the maxDistance of the point at pointIdx
   */
  private getNeighbors(
    points: GeoPoint[],
    pointIdx: number,
    maxDistanceRadians: number
  ): number[] {
    const neighbors: number[] = [];
    const point = points[pointIdx];

    // Pre-calculate constants for the current point
    const { radLat, radLng, cosLat } = this.precalculateConstants(
      point.lat,
      point.lng
    );

    for (let i = 0; i < points.length; i++) {
      if (i === pointIdx) continue;

      // Quick bounding box check before expensive haversine calculation
      // Approximately 111km per degree at the equator
      const latDiff = Math.abs(points[i].lat - point.lat);
      const lngDiff = Math.abs(points[i].lng - point.lng);

      // Convert max distance from radians back to degrees for quick check
      const maxDistanceDegrees = (maxDistanceRadians * 180) / Math.PI;

      if (latDiff > maxDistanceDegrees || lngDiff > maxDistanceDegrees) {
        continue; // Skip points that are definitely too far
      }

      const distance = this.haversineDistanceOptimized(
        radLat,
        radLng,
        cosLat,
        points[i].lat,
        points[i].lng
      );

      if (distance <= maxDistanceRadians) {
        neighbors.push(i);
      }
    }

    return neighbors;
  }

  /**
   * Optimized haversine distance calculation that accepts pre-calculated values
   * @returns Distance in radians
   */
  private haversineDistanceOptimized(
    radLat1: number,
    radLng1: number,
    cosLat1: number,
    lat2: number,
    lng2: number
  ): number {
    // Convert point 2 from degrees to radians
    const radLat2 = (lat2 * Math.PI) / 180;
    const radLng2 = (lng2 * Math.PI) / 180;

    const deltaLat = radLat2 - radLat1;
    const deltaLng = radLng2 - radLng1;

    const sinDeltaLatHalf = Math.sin(deltaLat / 2);
    const sinDeltaLngHalf = Math.sin(deltaLng / 2);

    const a =
      sinDeltaLatHalf * sinDeltaLatHalf +
      cosLat1 * Math.cos(radLat2) * sinDeltaLngHalf * sinDeltaLngHalf;

    return 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  }

  /**
   * Calculate the great-circle distance between two points using the haversine formula
   * @returns Distance in radians
   */
  private haversineDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const { radLat, radLng, cosLat } = this.precalculateConstants(lat1, lng1);

    return this.haversineDistanceOptimized(radLat, radLng, cosLat, lat2, lng2);
  }
}
