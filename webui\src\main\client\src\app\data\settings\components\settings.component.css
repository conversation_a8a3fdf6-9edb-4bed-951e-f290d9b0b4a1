:host {
  display: flex;
  flex-direction: column;
  padding: 10px;
}


.field {
  margin-left: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: fit-content;
}

.field:has(mat-checkbox) {
  margin-bottom: 10px;
}

.field > h3 {
  min-width: 200px;
  margin-bottom: 0;
}

.preview-img {
  border-radius: 10px;
  box-shadow: 0 0 10px 5px rgba(0,0,0,0.3);
  opacity: 0.6;
}
