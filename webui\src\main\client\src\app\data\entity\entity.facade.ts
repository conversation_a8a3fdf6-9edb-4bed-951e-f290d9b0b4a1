import { inject, Injectable } from '@angular/core';

import { createEffect, ofType } from '@ngrx/effects';

import {
  catchError,
  combineLatest,
  filter,
  map,
  Observable,
  of,
  switchMap,
  tap,
  withLatestFrom
} from 'rxjs';

import { GisService } from '../../gis/interface/service/gis.service';

import {
  BoundingBox,
  CreateEntity,
  createEntityData,
  Entity,
  EntityActions,
  EntityDetails,
  EntityFacadeBase,
  isCorridorArea,
  isEllipse,
  isFanArea,
  isPointLocation,
  Point,
  PointLocation
} from './entity.model';
import { EntityService } from './entity.service';
import { selectMapElementInformation } from '../../gis/interface/reducers/element-selection.reducer';
import { selectSearchQuery } from '../../cursit/reducers/cursit.reducer';
import { CreateEntityDialogComponent } from '../../gis/interface/components/create-entity/create-entity-dialog.component';
import { mapActions } from 'src/app/gis/interface/actions/map.actions';
import { EntityDetailsComponent } from 'src/app/gis/interface/components/entity-details.component';
import { PopoutWindowService } from '../../shared/popout-window/popout-window.service';
import { UniqueId } from './id.model';
import { PopoutWindowConfig } from '../../shared/popout-window/popout-window.model';
import { NodeFacade } from '../node/node.facade';
import { GenericDialog } from '../../shared/generic-dialog/generic-dialog.dialog';
import { CreateRouteComponent } from '../../gis/interface/components/create-route/create-route.component';

@Injectable({ providedIn: 'root' })
export class EntityFacade extends EntityFacadeBase {
  popoutService = inject(PopoutWindowService);
  nodeFacade = inject(NodeFacade);

  constructor(
    private entityService: EntityService,
    private gisService: GisService
  ) {
    super();
  }

  open(url?: string): void {
    this.entityService.open(url);
    this.entityService.entities$.subscribe((entities) =>
      this.readManyReceived(entities)
    );
  }

  openEntityDetailsDialog(id?: UniqueId, popout?: PopoutWindowConfig): void {
    this.store.dispatch(EntityActions.openEntityDetailsDialog(id, popout));
  }

  // noinspection JSUnusedLocalSymbols
  private openEntityDetailsDialog$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(EntityActions.openEntityDetailsDialog),
        map(({ id, popout }) => ({ id, popout })),
        withLatestFrom(this.store.select(selectMapElementInformation)),
        map(([config, { elementId }]) => ({
          config: config.popout,
          id: config?.id ?? elementId
        })),
        tap(({ config, id }) =>
          config
            ? this.popoutService.open(EntityDetailsComponent, {
                ...config,
                data: id
              })
            : this.dialog.open(EntityDetailsComponent, { data: id })
        )
      ),
    { dispatch: false }
  );

  // noinspection JSUnusedGlobalSymbols
  /**
   * Get the current selected entity details to display if user selects
   * entity details popout.Currently, we are only displaying details that we know about for the
   * entity in the store. In the future a request might be made to the app layer for additional
   * details. This can easily be done because the return here is any values in form of key-val
   * so any properties should be easy to add. Catch error is here because an event is fired when
   * an element is deselected the selection moves to undefined.
   */

  getSelectedEntityDetails(): Observable<EntityDetails[]> {
    return this.store.select(selectMapElementInformation).pipe(
      // Get the ID from selectedInfo here
      map((selectedInfo) => selectedInfo.elementId),
      // Now, use the ID to call get$ and retrieve the entity details
      switchMap((id) =>
        this.get$(id as string).pipe(
          map((entity) => this.createEntityDetails(entity)),
          catchError(() => of([]))
        )
      )
    );
  }

  private createEntityDetails(entity: Entity): EntityDetails[] {
    // Start with the name detail
    const details: EntityDetails[] = [{ key: 'Name', val: entity.name }];

    const location = entity.defaultLayerIcon.location;

    // Add location details based on location type
    if (isPointLocation(location)) {
      const pointDetails: Array<{ key: string; val: any }> = [
        { key: 'Latitude', val: location.latitude },
        { key: 'Longitude', val: location.longitude },
        { key: 'Bearing', val: location.bearing },
        { key: 'Altitude', val: location.altitude }
      ];
      details.push(...pointDetails);
    } else if (isFanArea(location)) {
      const fanAreaDetails: Array<{ key: string; val: any }> = [
        { key: 'Vertex Latitude', val: location.vertexPoint.latitude },
        { key: 'Vertex Longitude', val: location.vertexPoint.longitude },
        { key: 'Minimum Range', val: location.minimumRange },
        { key: 'Maximum Range', val: location.maximumRange },
        { key: 'Orientation Angle', val: location.orientationAngle },
        { key: 'Sector Angle', val: location.sectorAngle }
      ];
      details.push(...fanAreaDetails);
    } else if (isEllipse(location)) {
      const ellipseDetails: Array<{ key: string; val: any }> = [
        { key: 'Center Latitude', val: location.centerPoint.latitude },
        { key: 'Center Longitude', val: location.centerPoint.longitude },
        {
          key: 'First Diameter Point Latitude',
          val: location.firstDiameterPoint.latitude
        },
        {
          key: 'First Diameter Point Longitude',
          val: location.firstDiameterPoint.longitude
        },
        {
          key: 'Second Diameter Point Latitude',
          val: location.secondDiameterPoint.latitude
        },
        {
          key: 'Second Diameter Point Longitude',
          val: location.secondDiameterPoint.longitude
        }
      ];
      details.push(...ellipseDetails);
    } else if (isCorridorArea(location)) {
      const corridorDetails: Array<{ key: string; val: any }> = [
        { key: 'Center Latitude', val: location.centerLinePoints[0].latitude },
        {
          key: 'Center Longitude',
          val: location.centerLinePoints[0].longitude
        },
        { key: 'Width', val: location.width }
      ];
      details.push(...corridorDetails);
    }

    // Add MILSTD_2525B detail
    details.push({
      key: 'MILSTD_2525B',
      val: entity.defaultLayerIcon.iconCodeTypeMap.MILSTD_2525B
    });

    return details;
  }

  // noinspection JSUnusedGlobalSymbols
  gisHandleManyReceived$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(this.actions.readManyReceived, this.actions.readAllReceived),
        map((action) => action.payload),
        filter((entities) => entities.length > 0),
        map((entities) => this.gisService.updateEntities(entities))
      ),
    { dispatch: false }
  );

  /**
   * Used to get the user query,
   * @return list of filtered entities
   */
  getFilteredEntities$(): Observable<Entity[]> {
    //Get all the entities and the search query (passed from the search component)
    return combineLatest([
      this.all$,
      this.store.select(selectSearchQuery)
    ]).pipe(
      //select and return the entities that match the search query
      map(([entities, searchQuery]) => {
        if (searchQuery === '') {
          return Object.values(entities);
        }
        return entities.filter((ent) =>
          ent.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      })
    );
  }

  getEntitiesBoundingBox$(): Observable<BoundingBox> {
    return this.all$.pipe(
      map((entities) => {
        if (!entities || entities.length === 0) {
          return {
            north: 0,
            south: 0,
            east: 0,
            west: 0
          };
        }

        return entities.reduce(
          (acc, entity) => {
            const lat = (
              entity.defaultLayerIcon.location as Point | PointLocation
            )?.latitude;
            const lng = (
              entity.defaultLayerIcon.location as Point | PointLocation
            )?.longitude;

            if (isNaN(lat) || isNaN(lng)) {
              return acc;
            }

            return {
              north: Math.max(acc.north, lat),
              south: Math.min(acc.south, lat),
              east: Math.max(acc.east, lng),
              west: Math.min(acc.west, lng)
            };
          },
          {
            north: Number.NEGATIVE_INFINITY,
            south: Number.POSITIVE_INFINITY,
            east: Number.NEGATIVE_INFINITY,
            west: Number.POSITIVE_INFINITY
          }
        );
      })
    );
  }

  openCreateEntityDialog(
    payload?: Partial<createEntityData>,
    milStd?: string
  ): void {
    this.store.dispatch(EntityActions.openCreateEntityDialog(payload, milStd));
  }

  // noinspection JSUnusedGlobalSymbols
  openSelectEntityDetailsMenu(): void {
    this.store.dispatch(mapActions.openEntitySelectionDialog());
  }

  saveEntity(entity: CreateEntity): void {
    this.store.dispatch(EntityActions.saveEntity(entity));
  }

  // noinspection JSUnusedGlobalSymbols
  openCreateEntityDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EntityActions.openCreateEntityDialog),
      withLatestFrom(this.nodeFacade.simMovementNode$),
      switchMap(([action, simMove]) =>
        simMove?.launchable.status === 'ON'
          ? this.dialog
              .open(CreateEntityDialogComponent, {
                width: '1000px',
                hasBackdrop: false,
                panelClass: 'unscrollable',
                data: {
                  ...action.payload,
                  milStd: action.milStd
                }
              })
              .afterClosed()
          : this.dialog
              .open(GenericDialog, {
                data: {
                  title: 'Cannot Create Entities without SIM_MOVE',
                  message:
                    'SIM_MOVE is currently turned OFF. Turn on the node to create entities.'
                }
              })
              .afterClosed()
      ),
      map(() => EntityActions.createEntityDialogDismissed())
    )
  );

  openSelectEntityDetailsDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(mapActions.openEntitySelectionDialog),
      switchMap(() =>
        this.dialog
          .open(EntityDetailsComponent, {
            width: '500px'
          })
          .afterClosed()
      ),
      map(() => mapActions.closeEntitySelectionDialog())
    )
  );

  saveEntity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EntityActions.saveEntity),
      map((action) => action.payload),
      tap((entity) => this.createOne(entity as unknown as Entity)),
      map(() => EntityActions.saveEntitySuccess())
    )
  );
}
