import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { first, fromEvent, merge, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

type AcceptDefaultType = 'video' | 'image' | 'application' | 'audio';
export type AcceptType = `.${string}` | `${AcceptDefaultType}/*`;
export interface UploadOptions {
  id?: string;
  accept?: AcceptType | AcceptType[];
  multiple?: boolean | 'webkitdirectory';
  value?: string;
  name?: string;
}
interface InputEvent {
  target: HTMLInputElement;
}

@Injectable({ providedIn: 'root' })
export class FileUploadService {

  private renderer: Renderer2;

  constructor(rendererFactory: RendererFactory2) {
    this.renderer = rendererFactory.createRenderer(null, null);
  }

  /**
   * Open the file explorer to select files.
   * @param options input type='file' options, {@link https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file}
   */
  public selectFiles(options?: UploadOptions): Observable<File[] | null> {
    return new Observable(observer => {
      // Create the input element and set opacity to 0. it is not recommended to set display or visibility.
      const inputElement: HTMLInputElement = this.renderer.createElement('input');
      inputElement.type = 'file';
      inputElement.style.opacity = '0';
      // Set the options of the input type=file.
      if (options) {
        if (options.id) {
          inputElement.id = options.id;
        }
        if (options.value) {
          inputElement.value = options.value;
        }
        if (options.accept) {
          inputElement.accept = Array.isArray(options.accept) ? options.accept.join(',') : options.accept;
        }
        if (options.name) {
          inputElement.name = options.name;
        }
        if (options.multiple) {
          inputElement.multiple = true;
          if (options.multiple === 'webkitdirectory') {
            inputElement.webkitdirectory = true;
          }
        }
      }
      document.body.appendChild(inputElement);
      // Listen for the first change event of the input.
      const changeEvent$ = fromEvent<InputEvent>(inputElement, 'change').pipe(
        first(),
        map((event: InputEvent) => {
          const files = event.target.files;
          return files ? Array.from(files) : null;
        }),
        catchError(() => of(null))
      );
      // Listen for the first cancel event of the input.
      const cancelEvent$ = fromEvent(inputElement, 'cancel').pipe(
        first(),
        map(() => null)
      );
      // Merge the events together to perform cleanup.
      const fileSelect$ = merge(changeEvent$, cancelEvent$).pipe(
        first(),
        tap(() => this.renderer.removeChild(document.body, inputElement))
      );
      // Virtual click to make the file explorer appear.
      inputElement.click();
      fileSelect$.subscribe({
        next: files => {
          observer.next(files);
          observer.complete();
        },
        error: err => {
          observer.error(err);
        }
      });
    });
  }

}
