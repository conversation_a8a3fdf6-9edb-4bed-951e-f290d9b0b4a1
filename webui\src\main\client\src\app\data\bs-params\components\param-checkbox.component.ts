import { Component } from '@angular/core';
import { MatCheckbox } from '@angular/material/checkbox';
import { IconButtonSizeDirective } from '../../../shared/directives/icon-button-size.directive';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { MatSuffix } from '@angular/material/form-field';
import { SvgIconSizeDirective } from '../../../shared/directives/svg-icon-size.directive';
import { BaseParamComponent } from './base-param.component';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'vcci-param-checkbox',
  template: `
    <div class="checkbox">
      <mat-checkbox
        [formControl]="formControl"
        (keydown.tab)="onUseDefault()"
        (change)="onValueChange($event.checked)"
        >{{ label }}</mat-checkbox
      >
      @if (hasNext) {
        <button [iconSize]="20" matSuffix mat-icon-button (click)="onGetNext()">
          <mat-icon>east</mat-icon>
        </button>
      }
    </div>
  `,
  styles: `
    .checkbox {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  `,
  imports: [
    IconButtonSizeDirective,
    MatCheckbox,
    MatIcon,
    MatIconButton,
    MatSuffix,
    SvgIconSizeDirective,
    ReactiveFormsModule
  ]
})
export class ParamCheckboxComponent extends BaseParamComponent {}
