import { Injectable } from '@angular/core';

import { createEffect } from '@ngrx/effects';

import { fromEvent, merge, switchMap, timer } from 'rxjs';
import { map } from 'rxjs/operators';

import { DEFAULT_TIMEOUT, UserActions } from '../actions/user.actions';
import { UserService } from '../services/user.service';

@Injectable()
export class UserEffects {
  click$ = fromEvent(document, 'click');
  keydown$ = fromEvent(document, 'keydown');
  mousemove$ = fromEvent(document, 'mousemove');

  idle$ = createEffect(() => merge(this.click$, this.keydown$, this.mousemove$).pipe(
    switchMap(() => timer(DEFAULT_TIMEOUT)),
    map(() => UserActions.idleTimeout())
  ));

  // trackActivity$ = createEffect(() => this.click$.pipe(
  //   tap(event => this.api.trackActivity(event))
  // ), { dispatch: false });
  //
  // trackActivityKey$ = createEffect(() => this.keydown$.pipe(
  //   tap(event => this.api.trackActivity(event))
  // ), { dispatch: false });

  // trackActivityMove$ = createEffect(() => this.mousemove$.pipe(
  //   tap(event => this.api.trackActivity(event))
  // ), { dispatch: false });

  constructor(private api: UserService) {}
}
