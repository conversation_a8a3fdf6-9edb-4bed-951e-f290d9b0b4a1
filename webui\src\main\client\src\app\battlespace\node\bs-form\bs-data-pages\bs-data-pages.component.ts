import { Component, Input } from '@angular/core';
import { DataPage } from '../../../../data/bs-params/bs-params.model';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { BsDataPageComponent } from '../bs-data-page/bs-data-page.component';
import { BsDataParamBase } from '../bs-data-param/bs-data-param.base';

@Component({
  selector: 'vcci-bs-data-pages',
  templateUrl: 'bs-data-pages.component.html',
  imports: [MatTabGroup, MatTab, BsDataPageComponent],
  styleUrl: 'bs-data-pages.component.css'
})
export class BsDataPagesComponent extends BsDataParamBase {
  @Input({ required: true }) dataPages: DataPage[] = [];
}
