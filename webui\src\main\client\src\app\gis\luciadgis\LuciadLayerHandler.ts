import { Layer } from '@luciad/ria/view/Layer';
import { LayerGroup } from '@luciad/ria/view/LayerGroup';
import { RasterTileSetLayer } from '@luciad/ria/view/tileset/RasterTileSetLayer';
import { WebGLMap } from '@luciad/ria/view/WebGLMap';
import { mapActions } from '../interface/actions/map.actions';
import { findLayerAndGroupLevel, getLayerInfo, ParentLayerInfo } from '../interface/gis-util';
import { MapLayerHandler } from '../interface/LayerHandler';
import { MapLayer } from '../interface/MapLayer';
import { LuciadLayerGroup } from './LuciadLayerGroup';
import { LuciadMapDisplay } from './LuciadMapDisplay';

export class Luciad<PERSON>ayerHandler extends MapLayerHandler<Layer, LayerGroup> {
  private map: WebGLMap | undefined;

  constructor(lcdMapDisplay: LuciadMapDisplay) {
    super(lcdMapDisplay);
    this.map = lcdMapDisplay.map;
  }

  gisAddLayer(layer: MapLayer<Layer>): void {
    const layerInfo = getLayerInfo(layer);
    // If parent name of the layer to be added is defined
    if (layer.parentName !== undefined) {
      // First, try to find the parent in the layer tree, so that the layer can be added to that
      // parent
      const { mapLayer, level } = findLayerAndGroupLevel(layer.parentName, this.rootLayer);
      // If we find the parent layer
      if (mapLayer !== undefined && (mapLayer as any).childLayers !== undefined) {
        // Cast the found mapLayer to LayerGroup
        const luciadMapLayerGroup = mapLayer as LuciadLayerGroup;
        // Add the desired layer to layer group
        luciadMapLayerGroup.addLayer(layer);
        // Then we need to add the layer's information to the layer tree to send to the layer
        // manager
        this.mapDisplay.store.dispatch(mapActions.addLayer(
          { layerInfo, parentName: luciadMapLayerGroup.layerName }
        ));
      } else {
        // If we cannot find the parent layer in the layer tree, then create a new parent layer
        const {
          parentLayer,
          layerInfo: parentLayerInfo
        } = this.createParentLayer(layer.parentName);
        // Currently, we add parent layer directly to the root layer. But If there are multiple
        // parents we will need to do another procedure.
        this.rootLayer.addLayer(parentLayer);
        parentLayer.addLayer(layer);

        // Then we need to add the layerInfo to parentLayer information
        parentLayerInfo.children.push(layerInfo);
        // Again we add the parent layer info to the root.
        this.mapDisplay.store.dispatch(mapActions.addLayer({ layerInfo: parentLayerInfo }));
      }
    } else if (layer.layerId !== 'root') {
      // If there is no parent layer name in the layer, we directly add the layer to the root
      this.rootLayer.addLayer(layer);
      this.mapDisplay.store.dispatch(mapActions.addLayer({ layerInfo }));
    } else {
      // If parent name is undefined, this should be the root layer group,
      // NOTE: In luciad the root is already added to the map.
      this.mapDisplay.store.dispatch(mapActions.addLayer({ layerInfo }));
    }
  }

  createParentLayer(groupName: string, layerId?: string, parentId?: string): ParentLayerInfo<Layer, LayerGroup> {
    const parentLuciadLayer = new LayerGroup();
    parentLuciadLayer.label = groupName;
    const parentLayer = new LuciadLayerGroup(parentLuciadLayer, this.mapDisplay as LuciadMapDisplay);
    parentLayer.layerId = parentLuciadLayer.id;
    parentLayer.layerName = parentLuciadLayer.label;
    parentLayer.layerSource = 'System';
    parentLayer.layerType = 'Parent';
    parentLayer.parentId = parentId ? parentId : '-11'
    const layerInfo = getLayerInfo(parentLayer);
    return { parentLayer, layerInfo };
  }

  gisRemoveLayer(layer: MapLayer<Layer>): void {
    const gisLayer: Layer = layer?.getGISLayer();
    if (gisLayer) {
      this.map?.layerTree.removeChild(gisLayer);
      console.log(this.map.layerTree.children.length);
    }
  }

  mapOpacity(opacity: number): void {
    Object.values(this.layerCache).forEach(
      layer => {
        if (!(layer instanceof LuciadLayerGroup)) {
          const luciadLayer = layer.getGISLayer();
          if (luciadLayer instanceof RasterTileSetLayer) {
            luciadLayer.rasterStyle.alpha = opacity;
          }
        }
      }
    );
  }

  // override getLayer(name: string): Layer | undefined {
  //   return this.layerCache[name];
  // }
}
