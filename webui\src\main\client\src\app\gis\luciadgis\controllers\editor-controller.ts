import { Feature } from '@luciad/ria/model/feature/Feature';
import { Point } from '@luciad/ria/shape/Point';
import { Polygon } from '@luciad/ria/shape/Polygon';
import { Polyline } from '@luciad/ria/shape/Polyline';
import { EditController, EditControllerConstructorOptions } from '@luciad/ria/view/controller/EditController';
import { HandleEventResult } from '@luciad/ria/view/controller/HandleEventResult';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { GestureEventType } from '@luciad/ria/view/input/GestureEventType';
import { ModifierType } from '@luciad/ria/view/input/ModifierType';
import { Coordinate } from '../../../cursit/models/cursit.model';
import { mapActions } from '../../interface/actions/map.actions';
import { LuciadMapDisplay } from '../LuciadMapDisplay';
import { MS2525Symbol } from '../MS2525Symbol';

export class ShapeEditController extends EditController {
  luciadMapDisplay: LuciadMapDisplay;

  constructor(
    luciadMapDisplay: LuciadMapDisplay,
    layer: FeatureLayer,
    feature: Feature,
    options?: EditControllerConstructorOptions
  ) {
    super(layer, feature, options);
    this.luciadMapDisplay = luciadMapDisplay;
  }

  override onGestureEvent(ge: GestureEvent): HandleEventResult {
    if (ge.type === GestureEventType.DRAG && ge.modifier !== ModifierType.CTRL) {
      const { shape } = this.feature;
      const { feature } = this;
      let coordinates: Coordinate[];
      if (feature instanceof MS2525Symbol) {
        if (shape instanceof Polyline || shape instanceof Polygon) {
          coordinates = feature.getLocations();
        } else if (shape instanceof Point) {
          coordinates = [feature.getLocation()];
        }
      }
      this.luciadMapDisplay.store.dispatch(mapActions.mapElementSelection({
        elementId: feature.id,
        coordinates,
        screenCoordinates: { x: ge.viewPosition[0], y: ge.viewPosition[1] }
      }));
    }
    if (ge.modifier === ModifierType.CTRL) {
      return HandleEventResult.EVENT_IGNORED;
    }

    return super.onGestureEvent(ge);
  }
}
