import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { GIS_ENGINE_TOKEN, GIS_SERVICE_PROVIDER } from '../service/gis.model';
import { GisService } from '../service/gis.service';
import { LeafletModule } from '@bluehalo/ngx-leaflet';
import { LeafletMarkerClusterModule } from '@bluehalo/ngx-leaflet-markercluster';

@Component({
    selector: 'vcci-leaflet-cursit',
    templateUrl: 'leaflet-cursit.component.html',
    styleUrls: ['leaflet-cursit.component.css'],
    providers: [{ provide: GIS_ENGINE_TOKEN, useValue: 'leaflet' }, GIS_SERVICE_PROVIDER],
    imports: [LeafletModule, LeafletMarkerClusterModule]
})
export class LeafletCursitComponent implements OnInit, OnDestroy {

  constructor(private gisService: GisService) {
  }

  ngOnInit() {
    this.gisService.initMap();
  }

  ngOnDestroy() {
    this.gisService.destroy();
  }

}
