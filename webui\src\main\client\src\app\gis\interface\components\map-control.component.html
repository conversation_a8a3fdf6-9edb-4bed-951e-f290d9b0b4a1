<div class="map-control-toolbar">
  <div class="map-controller-button-group">
    <!--    <div class="map-controller-button-wrapper">-->
    <!--      <button-->
    <!--        matTooltip="Undo"-->
    <!--        matTooltipPosition="right"-->
    <!--        (mousedown)="undo()">-->
    <!--        <mat-icon class="button-icon" svgIcon="undo-mp-tb"></mat-icon>-->
    <!--      </button>-->
    <!--    </div>-->
    <!--    <div class="map-controller-button-wrapper">-->
    <!--      <button-->
    <!--        matTooltip="Redo"-->
    <!--        matTooltipPosition="right"-->
    <!--        (mousedown)="redo()">-->
    <!--        <mat-icon class="button-icon" svgIcon="redo-mp-tb"></mat-icon>-->
    <!--      </button>-->
    <!--    </div>-->
  </div>

  <div class="map-controller-button-group">
    <div class="map-controller-button-wrapper">
      <button
        matTooltip="Zoom to Battle Field"
        matTooltipPosition="right"
        (click)="battleField()">
        <mat-icon class="button-icon" svgIcon="battle-field-mp-tb"></mat-icon>
      </button>
    </div>
  </div>

  <div class="map-controller-button-group">
    <div class="map-controller-button-wrapper">
      <button [ngClass]="{'active-button': panActive}"
              matTooltip="Pan Mode"
              matTooltipPosition="right"
              (mousedown)="activatePanMode()">
        <mat-icon class="button-icon" svgIcon="navigate_hand"></mat-icon>
      </button>
    </div>
    <div class="map-controller-button-wrapper">
      <button [ngClass]="{'active-button': selectActive}"
              matTooltip="Select Mode"
              matTooltipPosition="right"
              (mousedown)="activateBulkSelectMode()">
        <mat-icon class="button-icon" svgIcon="navigate_pointer"></mat-icon>
      </button>
    </div>
    <div class="map-controller-button-wrapper">
      <button [ngClass]="{'active-button': measuring}"
              matTooltip="Measuring Mode"
              matTooltipPosition="right"
              (mousedown)="activateMeasuringMode()">
        <mat-icon class="button-icon" svgIcon="measure-etb"></mat-icon>
      </button>
    </div>
  </div>

  <div class="map-controller-button-group">
    <div class="map-controller-button-wrapper">
      <button
        matTooltip="Zoom In"
        matTooltipPosition="right"
        (mousedown)="zoomIn()">
        <mat-icon class="button-icon" svgIcon="zoom-in"></mat-icon>
      </button>
    </div>
    <div class="map-controller-button-wrapper">
      <button
        matTooltip="Zoom Out"
        matTooltipPosition="right"
        (mousedown)="zoomOut()">
        <mat-icon class="button-icon" svgIcon="zoom-out"></mat-icon>
      </button>
    </div>
  </div>

<!--  <div class="button-wrapper">-->
<!--    <button matTooltip="Print CURSIT" (click)="printMap()">-->
<!--      <mat-icon class="button-icon" svgIcon="print" />-->
<!--    </button>-->
<!--  </div>-->

  <div class="map-controller-button-group">
<!--    <div class="map-controller-button-wrapper">-->
<!--      <button-->
<!--        matTooltip="{{showDetails ? 'Show Details' : 'Hide Details'}}"-->
<!--        matTooltipPosition="right"-->
<!--        (mousedown)="showHideDetails()">-->
<!--        <mat-icon class="button-icon"-->
<!--                  svgIcon="{{showDetails ? 'show-eye-mp-tb' : 'hidden-eye-mp-tb'}}"></mat-icon>-->
<!--      </button>-->
<!--    </div>-->
<!--    <div class="map-controller-button-wrapper">-->
<!--      <button-->
<!--        matTooltip="{{locked ? 'Unlock All' : 'Lock All'}}"-->
<!--        matTooltipPosition="right"-->
<!--        (mousedown)="toggleLock()">-->
<!--        <mat-icon class="button-icon"-->
<!--                  svgIcon="{{locked? 'unlock-mp-tb' : 'locked-mp-tb'}}"></mat-icon>-->
<!--      </button>-->
<!--    </div>-->
  </div>

  <!--    <div class="map-controller-button-wrapper">-->
  <!--      <button class="label-button"-->
  <!--        (mousedown)="toggleLabels()">-->
  <!--        <mat-icon class="button-icon"-->
  <!--                  svgIcon="{{ showLabels? 'labels-on-r-tb' : 'labels-off-r-tb' }}"></mat-icon>-->
  <!--      </button>-->
  <!--    </div>-->
</div>
