import { Ng<PERSON><PERSON> } from '@angular/core';
import { Feature, FeatureProperties } from '@luciad/ria/model/feature/Feature';
import { CoordinateReference } from '@luciad/ria/reference/CoordinateReference';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { ReferenceType } from '@luciad/ria/reference/ReferenceType';
import { GeoBuffer } from '@luciad/ria/shape/GeoBuffer';
import { Point } from '@luciad/ria/shape/Point';
import { Polygon } from '@luciad/ria/shape/Polygon';
import { Polyline } from '@luciad/ria/shape/Polyline';

import { createBounds, createPoint } from '@luciad/ria/shape/ShapeFactory';
import { ShapeType } from '@luciad/ria/shape/ShapeType';
import { BasicCreateController } from '@luciad/ria/view/controller/BasicCreateController';
import { Controller } from '@luciad/ria/view/controller/Controller';
import { PanController } from '@luciad/ria/view/controller/PanController';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { Layer, Layer as LcdLayer } from '@luciad/ria/view/Layer';
import { LayerGroup } from '@luciad/ria/view/LayerGroup';
import { LayerType } from '@luciad/ria/view/LayerType';
import { PickInfo } from '@luciad/ria/view/PickInfo';
import { SelectionType } from '@luciad/ria/view/SelectionType';
import { IconStyle } from '@luciad/ria/view/style/IconStyle';
import { ShapeStyle } from '@luciad/ria/view/style/ShapeStyle';
import { WebGLMap } from '@luciad/ria/view/WebGLMap';

import { Store } from '@ngrx/store';
import { cursitActions } from '../../cursit/actions/cursit.actions';
import { Coordinate, CursitControllerType } from '../../cursit/models/cursit.model';
import { mapActions } from '../interface/actions/map.actions';

import { MapInputHandler } from '../interface/InputHandler';
import { AbstractMapDisplay } from '../interface/MapDisplay';
import { MapLayer } from '../interface/MapLayer';
import { Reference } from '../interface/models/gis.model';
import {
  convertFromStandard,
  convertToStandard,
  ScaleIndicator,
  Unit
} from '../interface/models/scale-indicator.model';
import { MouseCoordinateType } from '../interface/MouseCoordinateLocator';
import { BulkSelectController } from './controllers/bulk-select-controller';
import { ChainedEditController } from './controllers/composite.controller';
import { MilitarySymbolCreateController } from './controllers/military-symbol-createcontroller';
import { MoveController } from './controllers/multi-drag-controller';
import { SelectionController } from './controllers/select-controller';
import { getPointFromViewPoint, getScreenXYFromMapPoint, luciadStyleToGeneralStyle } from './luciad.utils';
import { LuciadElementFactory } from './LuciadElementFactory';
import { LuciadElementHandler } from './LuciadElementHandler';
import { LuciadLayerFactory } from './LuciadLayerFactory';
import { LuciadLayerGroup } from './LuciadLayerGroup';
import { LuciadLayerHandler } from './LuciadLayerHandler';
import { LuciadMouseCoordinateLocator } from './LuciadMouseCoordinateLocator';
import { ScaleUtil } from './LuciadScaleUtil';
import { LuciadShape } from './LuciadShape';
import { MS2525Symbol } from './MS2525Symbol';
import { BoundingBox } from 'src/app/data/entity/entity.model';

export const Dimension = {
  _2D: '2D',
  _3D: '3D'
} as const;

export type MapDimension = typeof Dimension[keyof typeof Dimension];

export class LuciadMapDisplay extends AbstractMapDisplay<Layer, LayerGroup> {
  private luciadMap: WebGLMap | undefined;
  mapReference: CoordinateReference;
  idCounter = 0;
  scaleUtil: ScaleUtil = new ScaleUtil();
  selectedElement: MS2525Symbol | LuciadShape;

  constructor(store: Store, ngZone: NgZone, dimension?: MapDimension) {
    super(store, ngZone);
    this.iconSize = 64;
    switch (dimension) {
      case Dimension._3D: {
        this.mapReference = getReference(Reference.GeoCentric);
        break;
      }
      default:
        this.mapReference = getReference(Reference.Equidistant);
    }

    this.luciadMap = new WebGLMap('map', { reference: this.mapReference, globeColor: '#415568' });

    this.luciadMap.mapNavigator.constraints = {
      limitBounds: {
        bounds: createBounds(this.mapReference, [90, 180, -90, -180]),
        padding: {
          right: 5,
          top: 5
        }
      }
    };

    // const limitBounds = createBounds(this.mapReference, []);
    // this.luciadMap.mapNavigator.constraints.limitBounds = { bounds: limitBounds };

    const is3DMap = this.map.reference.referenceType === ReferenceType.GEOCENTRIC;

    if (is3DMap) {
      console.log('3D');
    }

    this.elementFactory = new LuciadElementFactory(this);
    this.layerFactory = new LuciadLayerFactory(this);
    this.layerHandler = new LuciadLayerHandler(this);
    this.inputHandler = new MapInputHandler(this);
    this.elementHandler = new LuciadElementHandler(this);

    const rootLayer = new LuciadLayerGroup(this.luciadMap.layerTree, this);
    rootLayer.layerName = 'Layers';
    rootLayer.layerId = 'root';
    rootLayer.layerType = 'Parent';
    rootLayer.setVisible(true);
    this.layerHandler.rootLayer = rootLayer;
    // We need to add the root layer to laterList in ngrx state
    this.layerHandler.addLayer(rootLayer);

    this.store.dispatch(cursitActions.updateCursitController(
      { controllerType: CursitControllerType.BulkSelect }
    ));

    this.map.on('ControllerChanged', controller => {
      console.log('In ControllerChanged.', controller);
      if (this.map.controller === null) {
        // We set BulkSelectController as default. This is useful when create controller done
        // creating the feature and the map controller is set here.
        this.map.controller = new BulkSelectController(this);
        this.store.dispatch(cursitActions.updateCursitController(
          { controllerType: CursitControllerType.BulkSelect }
        ));
        console.log('null controller');
      }
    });

    this.map.on('MapChange', () => {
      this.ngZone.run(() => {
        if (this.selectedElement) {
          const { shape } = this.selectedElement;
          const coordinates = shape instanceof Polyline
        || shape instanceof Polygon || shape instanceof GeoBuffer
            ? this.selectedElement.getLocations()
            : [this.selectedElement.getLocation()];

          const screenXYCoordinate = getScreenXYFromMapPoint(
            this.luciadMap,
            createPoint(
              getReference('CRS:84'),
              coordinates[0]
            )
          );

          this.store.dispatch(mapActions.mapElementSelection({
            elementId: this.selectedElement.id,
            screenCoordinates: {
              x: screenXYCoordinate[0],
              y: screenXYCoordinate[1]
            },
            coordinates
          }));

          if (this.selectedElement instanceof LuciadShape) {
            if (shape instanceof Point) {
              const iconStyle =  this.selectedElement.normalStyle as IconStyle;
              console.log(iconStyle.modulationColor);
            } else {
              const shapeStyle =  this.selectedElement.normalStyle as ShapeStyle;
            }

            this.store.dispatch(mapActions.setSelectedShapeInfo(
              {
                shapeId: this.selectedElement.id
                // shapeStyleInfo: {
                //   normal: {
                //     strokeInfo: {
                //       strokeColor: this.selectedElement.normalStyle as ShapeStyle
                //     }
                //     fillInfo: {
                //       fillColor:
                //       fillType:
                //     }
                //   }
                // }
              }

            ));
          }
        }

        this.updateScaleIndicator();
      });
    });

    this.map.on('SelectionChanged', event => {
      if (!(this.map.controller instanceof PanController)
        && !(this.map.controller instanceof BasicCreateController)) {
        const { selectionChanges } =  event;
        selectionChanges.forEach(selectionsChange => {
          selectionsChange.deselected.forEach(deselected => {
            if (deselected instanceof MS2525Symbol || deselected instanceof LuciadShape) {
              deselected.selected = false;
            }
          });
        });

        let selectedCount = 0;
        let selectedFeature;
        this.map.selectedObjects.forEach(selectedList => {
          selectedList.selected.forEach(selected => {
            selectedFeature = selected;
            selectedCount += 1;
          });
        });

        if (event.selectionChanges.length > 0 && selectedCount > 1) {
          this.map.controller = new MoveController(this.mapReference, this.map.selectedObjects);
        } else if (event.selectionChanges.length > 0
          && selectedCount === 1) {
          if (selectedFeature instanceof Feature) {
            if (selectedFeature instanceof MS2525Symbol || selectedFeature instanceof LuciadShape) {
              selectedFeature.selected = true;
              this.selectedElement = selectedFeature;
              const { shape } = this.selectedElement;
              const coordinates = shape instanceof Polyline
                || shape instanceof Polygon || shape instanceof GeoBuffer
                ? this.selectedElement.getLocations()
                : [this.selectedElement.getLocation()];

              const screenXYCoordinate = getScreenXYFromMapPoint(
                this.luciadMap,
                createPoint(
                  getReference('CRS:84'),
                  coordinates[0]
                )
              );
              this.store.dispatch(mapActions.mapElementSelection({
                elementId: selectedFeature.id,
                screenCoordinates: {
                  x: screenXYCoordinate[0],
                  y: screenXYCoordinate[1]
                },
                coordinates: selectedFeature.shape instanceof Polyline
                  || selectedFeature.shape instanceof Polygon
                  ? selectedFeature.getLocations()
                  : [selectedFeature.getLocation()]
              }));

              if (selectedFeature instanceof LuciadShape) {
                let generalNormalStyle;
                if (selectedFeature.normalStyle) {
                  generalNormalStyle = luciadStyleToGeneralStyle(selectedFeature.normalStyle);
                } else {
                  generalNormalStyle = {
                    strokeInfo: {
                      strokeColor: 'rgb(0, 0, 255)',
                      strokeType: 'solid',
                      strokeWidth: 1
                    },
                    fillInfo: {
                      fillColor: 'rgba(0,0,0, 0.2)',
                      fillType: 'plain'
                    }
                  };
                }

                let generalSelectedStyle;
                if (selectedFeature.selectedStyle) {
                  generalSelectedStyle = luciadStyleToGeneralStyle(selectedFeature.selectedStyle);
                } else {
                  generalSelectedStyle = {
                    strokeInfo: {
                      strokeColor: 'rgb(255,213,0)',
                      strokeType: 'solid',
                      strokeWidth: 1
                    },
                    fillInfo: {
                      fillColor: 'rgba(0,0,0, 0.2)',
                      fillType: 'plain'
                    }
                  };
                }

                this.store.dispatch(mapActions.setSelectedShapeInfo({
                  shapeId: selectedFeature.id,
                  shapeStyleInfo: {
                    normal: shape instanceof Point
                      ? selectedFeature.genericNormalIconStyle
                      : generalNormalStyle,
                    selected: shape instanceof Point
                      ? selectedFeature.genericSelectedIconStyle
                      : generalSelectedStyle
                  }
                }));
              }
            }
            this.map.controller = new ChainedEditController(
              this,
              <FeatureLayer>event.selectionChanges[0].layer,
              selectedFeature
            );
          }
        } else {
          this.selectedElement = null;
          this.store.dispatch(mapActions.mapElementSelection({
            elementId: undefined,
            screenCoordinates: undefined,
            coordinates: undefined
          }));

          this.store.dispatch(mapActions.setSelectedShapeInfo({
            shapeId: undefined,
            shapeStyleInfo: this.elementHandler.shapeStyleInfo
          }));
          console.log('selection change else - bulk selected');
          this.map.controller = new BulkSelectController(this);
        }
      }
    });

    this.mouseCoordinateLocator = new LuciadMouseCoordinateLocator(this);
    this.mouseCoordinateLocator.setCoordinateType('MGRS');
    this.mouseCoordinateLocator.startMouseCoordinateLocator();
  }

  get map(): WebGLMap {
    return this.luciadMap;
  }

  set map(webGLMap: WebGLMap) {
    this.luciadMap = webGLMap;
  }

  setMilitaryElementIconSize(iconSize: number): void {
    let iconSizeNew = iconSize;
    if (iconSizeNew === 0) {
      iconSizeNew = 0.1;
    }
    this.iconSize = 128 * (iconSizeNew / 100);
    this.layerHandler.redrawAllLayers();
  }

  centerMapOnSelectedElement(): void {
    const { shape } = this.selectedElement;
    let centerLocation: Coordinate;
    if (shape instanceof Polyline || shape instanceof Polygon) {
      [centerLocation] = this.selectedElement.getLocations();
    } else {
      centerLocation = this.selectedElement.getLocation();
    }
    this.map.mapNavigator.pan(
      { targetLocation: createPoint(this.mapReference, centerLocation) }
    );
  }

  setMouseCoordinateType(mouseCoordinateType: MouseCoordinateType): void {
    if (this.mouseCoordinateLocator) {
      this.mouseCoordinateLocator.setCoordinateType(mouseCoordinateType);
    }
  }

  override addSymbolOnMapClick(): void {
    const layer: MapLayer<Layer> | undefined = this.layerFactory.getLayersHash()['Military Symbol Layer'];
    if (layer) {
      const getRandomCode = (): string => {
        const infantry = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCI---*****`;
        const engineer = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCE---*****`;
        const medic = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSM---*****`;
        const maintenance = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSX---*****`;
        return [infantry, engineer, medic, maintenance][
          Math.floor(Math.random() * 4)
        ];
      };

      const mouseClick = (event: GestureEvent): void => {
        this.idCounter += 1;
        const point = getPointFromViewPoint(this.map, event.viewPoint);
        const element = this.elementFactory.createMilitaryElement(
          getRandomCode(),
          this.idCounter.toString(),
          this.idCounter.toString(),
          ShapeType.POINT.valueOf(),
          'TEST'
          [point?.x, point?.y, point?.z],
          point?.reference.name
        );
        if (element) {
          layer?.addElement(element);
        }
      };
    }
  }

  getReference(): CoordinateReference {
    return this.mapReference;
  }

  private getSelectController(): SelectionController {
    return new SelectionController(this);
  }

  private static getPanController(): PanController {
    return new PanController();
  }

  // private getDefaultController(): DefaultController {
  //   return new DefaultController(this);
  // }

  private getBulkSelectController(): Controller {
    return new BulkSelectController(this);
  }

  // private getUnitSelectedController(): UnitSelectedController {
  //   return new UnitSelectedController(this.store, this);
  // }

  override syncController(): void {
    if (this.map) {
      const { controllerType, controllerPayload } = this.getCurrentController();
      if (controllerType === CursitControllerType.SelectController) {
        this.map.controller = this.getSelectController();
      } else if (controllerType === CursitControllerType.Pan) {
        this.map.controller = LuciadMapDisplay.getPanController();
      } else if (controllerType === CursitControllerType.BulkSelect) {
        this.map.controller = this.getBulkSelectController();
      } else if (controllerType === CursitControllerType.UnitSelected) {
        //   this.map.controller = this.getUnitSelectedController();
      } else if (controllerType === CursitControllerType.Create) {
        const { shapeType, code } = controllerPayload;
        this.map.controller = new MilitarySymbolCreateController(
          this,
          shapeType,
          { code },
          { finishOnSingleClick: true }
        );
      } else if (
        this.getCurrentController().controllerType === CursitControllerType.GraphicSelected
      ) {
        //    this.map.controller = new EditController();
      }
    }
  }

  public selectPickAt(pickAt: PickInfo[], bulkSelect?: boolean): void {
    const selection: { layer: FeatureLayer, objects: Feature[] }[] = [];
    const pickAtDynamic = pickAt?.filter(e => e.layer.type === LayerType.DYNAMIC);
    if (pickAtDynamic) {
      pickAtDynamic.forEach(pickInfo => {
        const { layer } = pickInfo;
        // Nebi: In bulk selection, it is enough for layer to be selectable for selection of
        // elements.
        if (layer instanceof FeatureLayer && layer.selectable) {
          selection.push({
            layer,
            objects: bulkSelect ? pickInfo.objects : [pickInfo.objects[0]]
          });
        }
      });

      if (selection) {
        if (bulkSelect || selection.length > 1) {
          // this.elementSelected = false;
          this.map.selectObjects(selection, { editSelection: SelectionType.NEW });
        } else if (!bulkSelect || selection.length === 1) {
          // TODO: Implement later
        }
      } else {
        this.map.clearSelection();
      }

      // if (this.layout === Routes.Laydown ||
      // this.layout === Routes.Graphic ||
      // this.layout === Routes.StartStateCursit
      // ) {
      //   let pickAtLayers: PickInfo[] = [];
      //   if (this.layout === Routes.Laydown || this.layout === Routes.Graphic) {
      //     pickAtLayers = this.activeWorkspace
      //       ? pickAtDynamic.filter(e =>
      //         e.layer.id === 'sandbox' ||
      //         e.layer.id === this.activeWorkspace?.name)
      //       : [];
      //   } else if (this.layout === Routes.StartStateCursit) {
      //     pickAtLayers = pickAtDynamic;
      //   }
      //   if (pickAtLayers && pickAtLayers.length > 0) {
      //     if (this.layout === Routes.Laydown) {
      //       // Find units in the active workspace
      //       const pickAtLayer = pickAtLayers[0];
      //       const objects = pickAtLayer.objects;
      //       const units = objects.filter(object =>
      //         (object.properties as FeatureProperties).type === 'OR' ||
      //         (object.properties as FeatureProperties).type === 'MA');
      //       if (units && units.length > 0) {
      //         selection = [{
      //           layer: pickAtLayer.layer,
      //           selected: bulkSelect ? units : [units[0]]
      //         }]
      //       }
      //     } else if (this.layout === Routes.Graphic) {
      //       // Find control features
      //       const pickAtLayer = pickAtLayers[0];
      //       const objects = pickAtLayer.objects;
      //       const controlFeatures = objects.filter(object =>
      //         (object.properties as FeatureProperties).type === 'FE');
      //       if (controlFeatures && controlFeatures.length > 0) {
      //         selection = [{
      //           layer: pickAtLayer.layer,
      //           selected: bulkSelect ? controlFeatures : [controlFeatures[0]]
      //         }]
      //       }
      //     } else if (this.layout === Routes.StartStateCursit) {
      //       // Find units and control features
      //       pickAtLayers.forEach(pi => {
      //         const objects = pi.objects;
      //         const unitsAndControlFeatures = objects.filter(object =>
      //           (object.properties as FeatureProperties).type === 'OR' ||
      //           (object.properties as FeatureProperties).type === 'MA' ||
      //           (object.properties as FeatureProperties).type === 'FE');
      //         if (unitsAndControlFeatures && unitsAndControlFeatures.length > 0) {
      //           const newSelection = {
      //             layer: pi.layer,
      //             selected: bulkSelect ? unitsAndControlFeatures : [unitsAndControlFeatures[0]]
      //           };
      //           if (selection == null) {
      //             selection = [newSelection];
      //           } else {
      //             selection.push(newSelection);
      //           }
      //         }
      //       })
      //     }
      //   }
      // }
    }

    // if (this.layout === Routes.Laydown && selection != null) {
    //   controller = CursitController.UnitSelected;
    // } else if (this.layout === Routes.Graphic && selection != null) {
    //   controller = CursitController.GraphicSelected;
    // }
    // this.setSelectedEntities(selection);
    this.updateCursitController(this.currentController);
  }

  selectPoint(point: Point): void {
    const pickAt: PickInfo[] | undefined = this.map?.pickAt(point.x, point.y, 0);
    this.selectPickAt(pickAt || []);
  }

  static setSelectedEntities(selection: { layer: LcdLayer, selected: Feature[] }[] | null): void {
    // const updateSelectedEntities: WorkspaceEntity[] = [];
    if (selection && selection.length > 0) {
      // Add the selected entities from each layer to [] as WorkspaceEntity
      selection.forEach(layer => {
        layer.selected.forEach(selected => {
          const props = selected.properties as FeatureProperties;

          // const workspaceEntity: WorkspaceEntity = {
          //   entityId: parseInt(props.id.toString()),
          //   contextId: props.contextId,
          //   uniqueDesignation: props.modifiers.uniqueDesignation
          // };
          //     updateSelectedEntities.push(workspaceEntity);
        });
      });
    }
    // this.store.dispatch(new UpdateSelectedEntities(updateSelectedEntities));
  }

  setMouseLocation(): void {
    throw new Error('Method not implemented.', { cause: this });
  }

  getScaleIndicator(
    map: WebGLMap,
    maxWidthPixels: number,
    distanceUnit = this.distanceUnits.METRE
  ): ScaleIndicator | null {
    try {
      const scalePixelsPerMeter = this.scaleUtil.getScaleAtMapCenter(map, 0);
      const barWidthInMeter = maxWidthPixels / scalePixelsPerMeter;
      let barWidthInDistanceUnit = convertFromStandard(
        barWidthInMeter,
        distanceUnit.toMetreFactor
      );
      const localDistanceUnit = this._getBestDistanceUnit(
        distanceUnit,
        barWidthInDistanceUnit,
        barWidthInMeter
      );
      barWidthInDistanceUnit = convertFromStandard(
        barWidthInMeter,
        localDistanceUnit.toMetreFactor
      );

      // convert to round number
      barWidthInDistanceUnit = LuciadMapDisplay._findLower_1_2_5(barWidthInDistanceUnit);
      const barWidthInPixels = scalePixelsPerMeter * convertToStandard(
        barWidthInDistanceUnit,
        localDistanceUnit.toMetreFactor
      );
      return {
        text: `${barWidthInDistanceUnit} ${localDistanceUnit.uomSymbol}`,
        style: {
          width: `${barWidthInPixels}px`,
          left: `${(maxWidthPixels - barWidthInPixels) / 2}px`
        }
      };
    } catch (ex) {
      console.log(ex);
      return null;
    }
  }

  updateScaleIndicator(): void {
    const scaleIndicator = this.getScaleIndicator(
      this.map,
      this.scaleIndicatorMaxWidth
    );
    // Compare saved and updated scale indicators, as MapChange can occur frequently/repeatedly
    if (scaleIndicator && JSON.stringify(this.scaleIndicator) !== JSON.stringify(scaleIndicator)) {
      // Dispatch update to scale indicator if there's actually a difference in parameters
      try {
        this.store.dispatch(mapActions.scaleIndicator(scaleIndicator));
      } catch (error) {
        console.log(error);
      }
    }
  }

  redraw(): void {
    this.map.invalidate();
  }

  zoomIn(): void {
    this.luciadMap.mapNavigator.zoom({
      factor: 2,
      animate: { duration: 250 }
    });
  }

  zoomOut(): void {
    this.luciadMap.mapNavigator.zoom({
      factor: 0.5,
      animate: { duration: 250 }
    });
  }

  fitBounds(bounds: BoundingBox): void {
    this.luciadMap.mapNavigator.fit({
      bounds: createBounds(
        this.mapReference,
        [bounds.north, bounds.east, bounds.south, bounds.west]
      ),
      animate: { duration: 250 }
    });
  }

  getContainerDOM(): HTMLElement {
    return this.luciadMap.domNode;
  }

  // latlonFormatter = new LonLatPointFormat({ pattern: 'lat(DMSa),lon(DMSa)' });
  //
  // mgrsFormatter = LuciadMouseCoordinateLocator.getMGRSPointFormat();
  //
  // coordinateType = 'MGRS';
  //
  // override getMapCoordinateFromMouseEvent(me: MouseEvent): string {
  //   const mapNodePosition = this.map.domNode.getBoundingClientRect();
  //   const mouseEventPoint = createPoint(
  //     this.map.reference,
  //     [me.clientX - mapNodePosition.left, me.clientY - mapNodePosition.top]
  //   );
  //   const point = getPointFromViewPoint(this.map, mouseEventPoint);
  //   if (this.coordinateType === 'LatLon') {
  //     return this.latlonFormatter.format(point);
  //   }
  //   return this.mgrsFormatter.format(point);
  // }

  _getBestDistanceUnit(
    aCurrentDistanceUnit: Unit,
    aLengthInDistanceUnit: number,
    aLengthInMeter: number
  ): Unit {
    if (this.distanceUnits.METRE === aCurrentDistanceUnit
      && aLengthInDistanceUnit > 1000) {
      return this.distanceUnits.KM;
    }
    if (this.distanceUnits.KM === aCurrentDistanceUnit
      && aLengthInDistanceUnit < 1) {
      return this.distanceUnits.METRE;
    }
    if (this.distanceUnits.FT === aCurrentDistanceUnit
      && convertFromStandard(
        aLengthInMeter,
        this.distanceUnits.MILE_US.toMetreFactor
      ) > 1) {
      return this.distanceUnits.MILE_US;
    }
    if (this.distanceUnits.MILE_US === aCurrentDistanceUnit
      && aLengthInDistanceUnit < 1) {
      return this.distanceUnits.FT;
    }
    return aCurrentDistanceUnit;
  }

  static _findLower_1_2_5(value: number): number {
    // 10^exponent = aInput  =>  exponent = log(aInput) / log(10)
    const lowestValue = 10 ** Math.floor(Math.log(value) / Math.log(10));
    if (value > 5 * lowestValue) {
      return 5 * lowestValue;
    }
    if (value > 2 * lowestValue) {
      return 2 * lowestValue;
    }
    return lowestValue;
  }
}

