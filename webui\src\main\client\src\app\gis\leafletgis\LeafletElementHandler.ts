import { MapElementHandler } from '../interface/ElementHandler';
import { LeafletMapDisplay } from './LeafletMapDisplay';

/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/26/2024, 11:56 AM
 */
export class LeafletElementHandler extends MapElementHandler {
  leafletMapDisplay: LeafletMapDisplay;

  constructor(leafletMapDisplay: LeafletMapDisplay) {
    super();
    this.leafletMapDisplay = leafletMapDisplay;
  }

  updateSelectedElementStyle(): void {
  }
}
