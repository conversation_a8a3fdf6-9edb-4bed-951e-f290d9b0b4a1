<div class="create-movement-container" [formGroup]="createMovementForm()">
  <!-- Movement Settings Section -->
  <div class="movement-settings">
    <div class="settings-row">
      <h3>Movement Settings</h3>
      <div class="settings-group">
        <mat-radio-group
          formControlName="startMovements"
          class="radio-group-inline"
        >
          <mat-radio-button value="IMMEDIATE"
            >Immediately After Creation</mat-radio-button
          >
          <mat-radio-button value="FUTURE">After Creation</mat-radio-button>
        </mat-radio-group>

        @if (isStartMovementsFuture) {
          <div class="time-inputs">
            <mat-form-field>
              <mat-label>Hours</mat-label>
              <mat-select formControlName="startHours">
                @for (hour of hours; track hour.value) {
                  <mat-option [value]="hour.value">{{ hour.label }}</mat-option>
                }
              </mat-select>
            </mat-form-field>

            <mat-form-field>
              <mat-label>Minutes</mat-label>
              <mat-select formControlName="startMinutes">
                @for (minute of minutes; track minute.value) {
                  <mat-option [value]="minute.value">{{
                    minute.label
                  }}</mat-option>
                }
              </mat-select>
            </mat-form-field>

            <mat-form-field>
              <mat-label>Seconds</mat-label>
              <mat-select formControlName="startSeconds">
                @for (second of seconds; track second.value) {
                  <mat-option [value]="second.value">{{
                    second.label
                  }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>
        }
      </div>
    </div>
  </div>

  <!-- Entities Involved Section -->
  <div class="entities-involved">
    <mat-stepper
      linear
      #stepper
      [selectedIndex]="selectedStepIndex"
      (selectionChange)="onStepChange($event)"
    >
      <mat-step [stepControl]="entitySelectionFormGroup" editable="true">
        <!-- Entity Selection Lists -->
        <ng-template matStepLabel>Select Entities</ng-template>
        <div class="entities-controls">
          <h3>Entities Involved</h3>
          <div class="entity-type-toggles">
            <mat-radio-group
              formControlName="entityType"
              class="radio-group-inline"
            >
              <mat-radio-button value="land">Land Entities</mat-radio-button>
              <mat-radio-button value="air">Air Entities</mat-radio-button>
            </mat-radio-group>
          </div>
        </div>
        <div class="entity-lists">
          <!-- Source Entities -->
          <div class="entity-source">
            <h4>Source</h4>
            <div class="entity-items">
              @for (entity of availableEntities(); track entity.uniqueId) {
                <div class="entity-item" (click)="onEntityToggle(entity)">
                  <mat-icon class="entity-icon">airplanemode_active</mat-icon>
                  <span class="entity-name">{{ entity.name }}</span>
                </div>
              }
            </div>

            <div class="entity-count">
              Listed Entities: {{ availableEntities().length }}
            </div>
          </div>

          <!-- Selected Entities -->
          <div class="entity-selection">
            <h4>Selection</h4>
            <div class="selected-entities">
              @for (entity of selectedEntities(); track entity.uniqueId) {
                <div class="entity-item selected">
                  <mat-icon class="entity-icon">airplanemode_active</mat-icon>
                  <span class="entity-name">{{ entity.name }}</span>

                  <mat-icon class="remove-icon" (click)="onEntityToggle(entity)"
                    >close</mat-icon
                  >
                </div>
              }
              @if (selectedEntities().length === 0) {
                <div class="no-selection">No entities selected</div>
              }
            </div>

            <div class="entity-count">
              Added Entities: {{ selectedEntities().length }}
            </div>
          </div>
        </div>
        <div class="entity-actions">
          <button
            mat-raised-button
            color="primary"
            matStepperNext
            [disabled]="disableNextButton(0)"
          >
            Next
          </button>
        </div>
      </mat-step>

      <mat-step [stepControl]="routeSelectionFormGroup" editable="true">
        <ng-template matStepLabel>Select Route</ng-template>
        <div class="route-controls" [formGroup]="routeSelectionFormGroup">
          <div class="route-type-selection-container">
            <!-- Route Type Selection -->
            <div class="route-type-selection">
              <h4>Route</h4>
              <mat-radio-group
                formControlName="routeType"
                class="radio-group-vertical"
              >
                <mat-radio-button value="WAY_POINT"
                  >Move to Waypoint</mat-radio-button
                >
                <mat-radio-button value="EXISTING_ROUTE"
                  >Use Existing Route</mat-radio-button
                >
                <mat-radio-button value="NEW_ROUTE"
                  >Create New Route</mat-radio-button
                >
              </mat-radio-group>
            </div>

            <!-- Route Points Preview (placeholder for now) -->
            @if (selectedRouteType === 'EXISTING_ROUTE') {
              <div class="existing-route-section">
                <h5>Select Existing Route</h5>
                <mat-form-field>
                  <mat-label>Route</mat-label>
                  <mat-select>
                    <mat-option value="route1">MY ROUTE</mat-option>
                    <mat-option value="route2">Route 2</mat-option>
                    <mat-option value="route3">Route 3</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            }

            @if (selectedRouteType === 'NEW_ROUTE') {
              <div class="create-route-section">
                <h5>Create New Route</h5>
                <div class="route-creation-controls">
                  <mat-form-field>
                    <mat-label>Route Name</mat-label>
                    <input matInput formControlName="routeName" />
                  </mat-form-field>

                  <button
                    mat-raised-button
                    color="primary"
                    (click)="createRouteRequested.emit()"
                    [disabled]="disableAddPointsButton()"
                  >
                    <mat-icon>add_location</mat-icon>
                    Add Points
                  </button>

                  @if (routePointsCount() > 0) {
                    <button mat-button (click)="clearRoutePoints()">
                      <mat-icon>clear</mat-icon>
                      Clear Points
                    </button>
                  }
                </div>

                @if (gisService.routePointsCount() > 0) {
                  <div class="route-points-container">
                    <div class="route-points-header">
                      <span
                        >Route Points ({{
                          gisService.routePointsCount()
                        }})</span
                      >
                    </div>
                    <div
                      class="route-points-list"
                      [formArrayName]="'routePointsInformation'"
                      cdkDropList
                      (cdkDropListDropped)="drop($event)"
                    >
                      @for (point of gisService.routePoints(); track $index) {
                        <div
                          class="route-point-item"
                          cdkDrag
                          [formGroupName]="$index"
                        >
                          <mat-expansion-panel>
                            <mat-expansion-panel-header>
                              <mat-panel-title>
                                @if (locationFormat() === 'Lat Lon') {
                                  Latitude: {{ point.latitude.toFixed(6) }},
                                  Longitude: {{ point.longitude.toFixed(6) }}
                                } @else {
                                  {{
                                    gisService.convertToMgrs(
                                      point.latitude,
                                      point.longitude
                                    )
                                  }}
                                }
                              </mat-panel-title>
                            </mat-expansion-panel-header>
                            <div class="route-point-item-content">
                              <div class="route-point-fields">
                                <div class="route-point-row">
                                  @for (
                                    field of routePointFormFields(entityType());
                                    track field.name
                                  ) {
                                    @if (field.type === 'select') {
                                      <mat-form-field>
                                        <mat-label>{{ field.label }}</mat-label>
                                        <mat-select
                                          [formControlName]="field.name"
                                        >
                                          @for (
                                            option of field.options;
                                            track option.value
                                          ) {
                                            <mat-option
                                              [value]="option.value"
                                              >{{ option.label }}</mat-option
                                            >
                                          }
                                        </mat-select>
                                      </mat-form-field>
                                    } @else {
                                      <mat-form-field>
                                        <mat-label>{{
                                          getRoutePointFieldLabel(field)
                                        }}</mat-label>
                                        <input
                                          matInput
                                          type="number"
                                          [formControlName]="field.name"
                                        />
                                      </mat-form-field>
                                    }
                                  }
                                </div>

                                <div class="bottom-row">
                                  <div class="checkbox-row">
                                    @for (
                                      field of routePointCheckboxFields;
                                      track field.name
                                    ) {
                                      <mat-checkbox
                                        [formControlName]="field.name"
                                      >
                                        {{ field.label }}
                                      </mat-checkbox>
                                    }
                                  </div>
                                  <button
                                    mat-icon-button
                                    (click)="removeRoutePoint($index)"
                                    class="remove-point-btn"
                                    matTooltip="Remove Point"
                                  >
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </mat-expansion-panel>
                        </div>
                      }
                    </div>
                  </div>
                }
              </div>
            }

            @if (selectedRouteType === 'WAY_POINT') {
              <div class="move-to-point-section">
                <h5>Select Route Point</h5>
                <p class="route-placeholder">
                  Route point selection interface will be implemented here...
                </p>
              </div>
            }
          </div>
        </div>

        <div class="route-actions">
          <button mat-button matStepperPrevious>Back</button>
          <button
            mat-raised-button
            color="primary"
            matStepperNext
            [disabled]="disableNextButton(1)"
          >
            Next
          </button>
        </div>
      </mat-step>

      <mat-step [stepControl]="routeInformationFormGroup" editable="true">
        <ng-template matStepLabel>Route Information</ng-template>
        <div
          class="route-info-controls"
          [formGroup]="routeInformationFormGroup"
        >
          <!-- Basic Route Information -->
          <div class="route-info-section">
            <h5>Route Information</h5>
            <div class="route-info-row">
              @for (field of routeInformationFormFields; track field.name) {
                @if (field.type === 'checkbox') {
                  <mat-checkbox [formControlName]="field.name">
                    {{ field.label }}
                  </mat-checkbox>
                } @else if (field.name === 'callsign') {
                  <mat-form-field>
                    <mat-label>{{ getFieldLabel(field) }}</mat-label>
                    <input
                      matInput
                      [formControlName]="field.name"
                      type="text"
                    />
                  </mat-form-field>
                } @else {
                  <mat-form-field>
                    <mat-label>{{ getFieldLabel(field) }}</mat-label>
                    <input
                      matInput
                      [formControlName]="field.name"
                      type="number"
                    />
                  </mat-form-field>
                }
              }
            </div>
          </div>
        </div>

        <div class="route-info-actions">
          <button mat-button matStepperPrevious>Back</button>
        </div>
      </mat-step>
    </mat-stepper>
  </div>
</div>
