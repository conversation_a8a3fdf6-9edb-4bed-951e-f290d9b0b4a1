import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { DataService } from '../../data.service';
import { Endpoint } from '../endpoint.model';
import { EndpointFilterStore } from './endpoint-filter.store';

@Component({
  templateUrl: 'endpoint-filter.dialog.html',
  styleUrls: ['endpoint-filter.dialog.css'],
  providers: [EndpointFilterStore],
  standalone: false
})
export class EndpointFilterDialog implements OnDestroy {
  endpoint$ = this.store.endpoint$;
  filterInfos$ = this.store.filterInfos$;
  filters$ = this.store.filters$;
  selectedInfo$ = this.store.selectedInfo$;
  selectedFilter$ = this.store.selectedFilterValue$;
  busy$ = this.store.busy$;

  constructor(
    private readonly store: EndpointFilterStore,
    public data: DataService
  ) {}

  setEndpoint(endpoint: Endpoint): void {
    this.store.updater((state) => ({ ...state, endpoint }));
  }

  compareEndpoint = (a: Endpoint, b: Endpoint): boolean =>
    `${a.node}:${a.name}` === `${b.node}:${b.name}`;

  submit(): void {
    this.store.submit();
  }

  ngOnDestroy() {
    this.store.ngOnDestroy();
  }
}
