import { ComponentType } from '@angular/cdk/overlay';
import { ComponentRef, ElementRef, Inject, Injectable, Injector, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import {
  IPopoutWindow,
  IPopoutWindowModal,
  IS_POPOUT_WINDOW,
  POPOUT_DIALOG_TYPE,
  PopoutDialogType,
  PopoutWindowConfig,
  PopoutWindowRect
} from './popout-window.model';
import { PopoutWindowService } from './popout-window.service';

const popoutDialogServiceFactory = <T, C extends IPopoutWindowModal>(
  injector: Injector,
  elementRef: ElementRef,
  popoutWindowService: PopoutWindowService
) => {
  const isPopoutWindow = injector.get<boolean>(IS_POPOUT_WINDOW, false, { optional: true });
  const component = injector.get<PopoutDialogType<C>>(POPOUT_DIALOG_TYPE);
  const data = injector.get<T>(MAT_DIALOG_DATA, null, { optional: true });
  const matDialog = injector.get<MatDialogRef<any>>(MatDialogRef, null, { optional: true });
  return isPopoutWindow ? undefined : new PopoutDialogService(component, elementRef, popoutWindowService, matDialog, data);
};
export const popoutDialogProvider = <C extends IPopoutWindowModal>(component: PopoutDialogType<C>) => [
  { provide: POPOUT_DIALOG_TYPE, useValue: component },
  {
    provide: PopoutDialogService,
    useFactory: popoutDialogServiceFactory,
    deps: [
      Injector,
      ElementRef,
      PopoutWindowService
    ]
  }
];

@Injectable()
export class PopoutDialogService<T, C extends IPopoutWindowModal> implements IPopoutWindow {
  private _window: Window | null;
  private readonly _config: PopoutWindowConfig<T>;
  private _observer: MutationObserver | undefined;
  private _componentRef: ComponentRef<C> | undefined;
  private _subscription: Subscription | undefined;

  // TODO: Maybe make the component type not injected to support changing after providing.
  constructor(
    @Inject(POPOUT_DIALOG_TYPE) private _component: ComponentType<C>,
    private _elementRef: ElementRef,
    private _popoutWindowService: PopoutWindowService,
    @Optional() private _matDialog?: MatDialogRef<any>,
    @Optional() @Inject(MAT_DIALOG_DATA) private data?: T
  ) {
    if (this.data) {
      this._config = {
        injectorToken: MAT_DIALOG_DATA,
        data: this.data
      };
    }
  }

  public get window(): Window | null {
    return this._window;
  }

  public set observer(o: MutationObserver | undefined) {
    this._observer = o;
  }

  public get observer(): MutationObserver | undefined {
    return this._observer;
  }

  public get document(): Document | undefined {
    return this._window?.document;
  }

  public observeStyleChanges(): void {
    this._closeObserver();
    this._observer = PopoutWindowService.createStyleObserver(this.window);
  }

  /**
   * This will open a new window without keeping a reference to the original.
   * @param config If undefined default will be provided.
   */
  public popout(config?: PopoutWindowConfig<T>): void {
    if (this._window) {
      this._window.focus();
    } else {
      const defaultConfig = this._getDefaultConfig(config);
      this._setWindow(PopoutWindowService.createWindow(defaultConfig));
      if (this._window) {
        this._popoutWindowService.assignStyleToWindow(this._window, defaultConfig);
        this.observeStyleChanges();
        this._window.addEventListener('unload', () => this.close());
        window.addEventListener('beforeunload', () => this.close());
        const outlet = this._popoutWindowService.createCDKPortal(this._window);
        const injector = this._createInjector();
        this.componentRef = PopoutWindowService.attachTemplateContainer(outlet, this._component, injector);
        this.subscription = this._componentRef.instance.onClose.subscribe(() => this.close());
      }
    }
  }

  public set subscription(subscription: Subscription) {
    this._closeSubscription();
    this._subscription = subscription;
  }

  public set componentRef(componentRef: ComponentRef<C>) {
    this._destroyComponent();
    this._componentRef = componentRef;
  }

  public close(): void {
    this._closeObserver();
    this._closeWindow();
    this._closeSubscription();
    this._destroyComponent();
  }

  public closeDialog(): void {
    if (this._matDialog) {
      this._matDialog.close();
    }
  }

  public toggleDarkMode(_: boolean): void {
    // Not implemented...
  }

  private _closeObserver(): void {
    if (this._observer) {
      this._observer.disconnect();
      this._observer = undefined;
    }
  }

  private _closeWindow(): void {
    if (this._window) {
      window.removeEventListener('beforeunload', this.close);
      this._window.removeEventListener('unload', this.close);
      this._window.close();
      this._window = null;
    }
  }

  private _closeSubscription(): void {
    if (this._subscription) {
      this._subscription.unsubscribe();
      this._subscription = undefined;
    }
  }

  private _destroyComponent(): void {
    if (this._componentRef) {
      this._componentRef.destroy();
      this._componentRef = null;
    }
  }

  private _createInjector(): Injector {
    return Injector.create({
      providers: [
        { provide: MAT_DIALOG_DATA, useValue: this.data },
        { provide: IS_POPOUT_WINDOW, useValue: true },
        { provide: POPOUT_DIALOG_TYPE, useValue: this._component }
      ]
    });
  }

  private _setWindow(w: Window | null): void {
    if (w) {
      this._window = w;
    } else if (this._window) {
      close();
    }
  }

  private _getDefaultConfig(config?: PopoutWindowConfig<T>): PopoutWindowConfig<T> {
    const rect = this._getRect();
    return {
      ...this._config,
      rect,
      ...(config ?? {})
    };
  }

  private _getRect(): PopoutWindowRect {
    const parentRect: PopoutWindowRect = this._elementRef.nativeElement.parentNode.getBoundingClientRect();
    return { ...parentRect, height: parentRect.height + 1 };
  }
}
