import {
  Component,
  computed,
  inject,
  Input,
  model,
  OnChanges,
  signal,
  SimpleChanges,
  viewChild
} from '@angular/core';
import { OutboundObject } from '../../../data/endpoint/association/association.model';
import { EndpointFilterStore } from '../../../data/endpoint/endpoint-filter/endpoint-filter.store';
import { EntityFilter } from '../../../data/endpoint/filter.model';
import { Entity } from '../../../data/entity/entity.model';
import {
  SFFlatNode,
  SFTreeMetaData,
  StickyService,
  VirtualTableTreeComponent
} from '@simfront/common-libs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, distinctUntilChanged } from 'rxjs';
import { EntityFacade } from '../../../data/entity/entity.facade';
import { UniqueId } from '../../../data/entity/id.model';

type TreeEntity = Entity & {
  type: 'Entity';
  assoc: OutboundObject | undefined;
  symbol: string;
  children: [];
  fullRoute: boolean;
};

interface Endpoint {
  type: 'Endpoint';
  name: string;
  fullRoute: boolean;
  children: TreeEntity[];
}

interface Service {
  type: 'Service';
  name: string;
  children: Endpoint[];
}

type ServiceNode = Service | Endpoint | TreeEntity;

@Component({
  selector: 'vcci-entity-filter',
  templateUrl: './entity-filter.component.html',
  styleUrls: ['./entity-filter.component.css'],
  standalone: false,
  providers: [StickyService]
})
export class EntityFilterComponent implements OnChanges {
  store = inject(EndpointFilterStore);
  entityFacade = inject(EntityFacade);

  protected readonly isEntity = (
    n: Omit<ServiceNode, 'children'>
  ): n is Omit<TreeEntity, 'children'> => n.type === 'Entity';
  protected readonly isEndpoint = (
    n: Omit<ServiceNode, 'children'>
  ): n is Omit<Endpoint, 'children'> => n.type === 'Endpoint';

  @Input({ required: true }) entities: Service[];
  @Input({ required: true }) entityFilter!: EntityFilter;

  columns = ['Name', 'Type', ' '];
  metaData: SFTreeMetaData<ServiceNode, string> = {
    controlTrackBy: (n) =>
      `${n.type}-${n.name}-${n.type === 'Entity' ? n.uniqueId : ''}`,
    children: (n) => n.children,
    childrenProp: 'children',
    label: (n) => n.name
  };

  table = viewChild.required<VirtualTableTreeComponent<ServiceNode, string>>(
    VirtualTableTreeComponent
  );
  expanded = computed(() => this.table().treeControl.expandedHash);
  options = computed<IntersectionObserverInit>(() => ({
    threshold: [0],
    root: this.table().scrollContainer().getElementRef().nativeElement,
    rootMargin: '-56px 0px 0px 0px'
  }));

  filteredData = signal<Service[]>([]);
  search = model('');
  included = model(true);
  excluded = model(true);
  partial = model(true);
  full = model(true);

  constructor() {
    combineLatest([
      toObservable(this.search),
      toObservable(this.included),
      toObservable(this.excluded),
      toObservable(this.partial),
      toObservable(this.full)
    ])
      .pipe(takeUntilDestroyed(), debounceTime(250), distinctUntilChanged())
      .subscribe(() => this.filterData());
  }

  ngOnChanges(changes: SimpleChanges) {
    this.filterData();
  }

  updateFilters(
    added: boolean,
    ...items: Omit<TreeEntity, 'children'>[]
  ): void {
    if (items.length === 0) {
      return;
    }
    const updatedFilter: EntityFilter = items.reduce<EntityFilter>((acc, i) => {
      // It's a full route we need to check excluded.
      if (i.fullRoute) {
        const index = acc.fullRouteExcludedEntities.indexOf(i.uniqueId);
        if (index !== -1 && added) {
          // This means that it was removed from the excluded list.
          acc.fullRouteExcludedEntities.splice(index, 1);
        } else if (index === -1 && !added) {
          // This means it was excluded we need to add it.
          acc.fullRouteExcludedEntities.push(i.uniqueId);
        }
      } else {
        const index = acc.partialRouteIncludedEntities.indexOf(i.uniqueId);
        if (index === -1 && added) {
          acc.partialRouteIncludedEntities.push(i.uniqueId);
        } else if (index !== -1 && !added) {
          acc.partialRouteIncludedEntities.splice(index, 1);
        }
      }
      return acc;
    }, structuredClone(this.entityFilter));
    this.store.updateFilter(updatedFilter);
  }

  isSelected(entityFilter: EntityFilter, item: TreeEntity): boolean {
    return item.fullRoute
      ? !entityFilter.fullRouteExcludedEntities.includes(item.uniqueId)
      : entityFilter.partialRouteIncludedEntities.includes(item.uniqueId);
  }

  filterData(): void {
    // TODO: Might need to move this to web worker if it gets too slow.
    const lowerSearch = this.search().toLowerCase();
    const compare = (node: TreeEntity, search: string): boolean => {
      const searchIncluded =
        node.name.toLowerCase().includes(search) ||
        node.uniqueId.toLowerCase().includes(search);
      if (!searchIncluded) {
        return false;
      }
      const isRouteIncluded = node.fullRoute ? this.full() : this.partial();
      if (!isRouteIncluded) {
        return false;
      }
      return this.isSelected(this.entityFilter, node)
        ? this.included()
        : this.excluded();
    };
    const filterNode = (node: ServiceNode): ServiceNode | null => {
      if (this.isEntity(node)) {
        return compare(node, lowerSearch) ? node : null;
      } else {
        const filteredChildren = node.children
          .map(filterNode)
          .filter((c) => c !== null);
        if (filteredChildren.length > 0) {
          return node.type === 'Service'
            ? { ...node, children: filteredChildren as Endpoint[] }
            : { ...node, children: filteredChildren as TreeEntity[] };
        } else {
          return null;
        }
      }
    };
    this.filteredData.set(
      this.entities.map(filterNode).filter((n) => !!n) as Service[]
    );
  }

  toggleAll(added: boolean, item: SFFlatNode<Endpoint, string>): void {
    const children = this.table()
      .treeControl.getDescendants(item)
      .map((c) => c.obj as Omit<TreeEntity, 'children'>);
    this.updateFilters(added, ...children);
  }

  openDetails(id: UniqueId): void {
    this.entityFacade.openEntityDetailsDialog(id);
  }
}
