<div class="geo-filter-container" [formGroup]="geoForm">
  <button
    (click)="onGeoClick()"
    mat-raised-button
    [ngClass]="{ 'geo-drawn': isGeoDrawn }"
  >
    Draw Geo Filter on Cursor
  </button>

  <div class="geo-filter-table">
    <mat-form-field appearance="outline" formGroupName="p1">
      <mat-label>First Point Latitude</mat-label>
      <input matInput type="number" formControlName="latitude" />
    </mat-form-field>

    <mat-form-field appearance="outline" formGroupName="p1">
      <mat-label>First Point Longitude</mat-label>
      <input matInput type="number" formControlName="longitude" />
    </mat-form-field>

    <mat-form-field appearance="outline" formGroupName="p2">
      <mat-label>Second Point Latitude</mat-label>
      <input matInput type="number" formControlName="latitude" />
    </mat-form-field>

    <mat-form-field appearance="outline" formGroupName="p2">
      <mat-label>Second Point Longitude</mat-label>
      <input matInput type="number" formControlName="longitude" />
    </mat-form-field>
  </div>

  @if (
    geoForm.value.p1.latitude ||
    geoForm.value.p1.longitude ||
    geoForm.value.p2.latitude ||
    geoForm.value.p2.longitude
  ) {
    <button (click)="resetGeoArea()" mat-raised-button>
      Reset Coordinates
    </button>
  }
</div>
