import {
  Component,
  inject,
  signal,
  OnInit,
  Renderer2,
  WritableSignal,
  computed,
  output,
  effect,
  input
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatStepperModule } from '@angular/material/stepper';
import { MatExpansionModule } from '@angular/material/expansion';
import {
  CdkDragDrop,
  CdkDropList,
  CdkDrag,
  moveItemInArray
} from '@angular/cdk/drag-drop';
import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { DataService } from 'src/app/data/data.service';
import { Entity } from 'src/app/data/entity/entity.model';
import { Node } from 'src/app/data/node/node.model';
import {
  routeInformationFormFields,
  routePointFormFields,
  routePointCheckboxFields,
  RoutePoint,
  CreateMovementForm
} from 'src/app/data/movement/movement.model';
import { GisService, RouteClickData } from '../../../service/gis.service';
import { LocationFormat } from 'src/app/data/settings/settings.model';
import { map, of, startWith, switchMap } from 'rxjs';

@Component({
  selector: 'nexus-create-movement',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRadioModule,
    MatStepperModule,
    MatExpansionModule,
    CdkDropList,
    CdkDrag
  ],
  templateUrl: './create-movement.component.html',
  styleUrl: './create-movement.component.css'
})
export class CreateMovementComponent implements OnInit {
  data = inject(DataService);
  gisService = inject(GisService);
  renderer = inject(Renderer2);
  createMovementForm = input.required<FormGroup<CreateMovementForm>>();
  movementEngine = input.required<Node | null>();
  creatingRoute = input.required<WritableSignal<boolean>>();
  savedMovement = input<CreateMovementForm>(null);
  createRouteRequested = output<void>();
  createMovementRestoreRequested = output<void>();

  selectedEntities = signal<Entity[]>([]);

  // Convert observables to signals
  private userSettings = toSignal(this.data.settings.get$('user'));
  private allEntities = toSignal(this.data.entity.all$, { initialValue: [] });

  createMovementForm$ = toObservable(this.createMovementForm).pipe(
    switchMap(() =>
      this.createMovementForm()
        ? this.createMovementForm().valueChanges
        : of(null)
    )
  );

  entityType = toSignal(
    this.createMovementForm$.pipe(
      map(() => this.createMovementForm()?.get('entityType')?.value)
    )
  );

  private formSelectedEntities = toSignal(
    this.createMovementForm$.pipe(
      map(
        () =>
          this.createMovementForm()?.get('entitySelection.selectedEntities')
            ?.value || []
      )
    ),
    { initialValue: [] }
  );

  locationFormat = computed(
    () =>
      this.userSettings()?.cursit?.locationFormat ??
      ('Lat Lon' as LocationFormat)
  );

  entities = computed(() => {
    const entities = this.allEntities();
    const movementEngine = this.movementEngine();
    const movementEngineName = movementEngine?.name;
    return entities.filter((e) =>
      movementEngineName && e.uniqueId
        ? e.uniqueId.includes(movementEngineName)
        : false
    );
  });

  availableEntities = computed(() =>
    this.entities().filter(
      (entity) => !this.selectedEntities().includes(entity)
    )
  );
  routePointsCount = computed(() => this.gisService.routePointsCount());
  routePoints = computed(() => this.gisService.routePoints());

  readonly hours = Array.from({ length: 24 }, (_, i) => ({
    value: i,
    label: i.toString().padStart(2, '0')
  }));

  readonly minutes = [0, 15, 30, 45].map((m) => ({
    value: m,
    label: m.toString().padStart(2, '0')
  }));

  readonly seconds = [0, 15, 30, 45].map((s) => ({
    value: s,
    label: s.toString().padStart(2, '0')
  }));

  routeInformationFormFields = routeInformationFormFields;
  routePointFormFields = routePointFormFields;
  routePointCheckboxFields = routePointCheckboxFields;

  get entitySelectionFormGroup() {
    return this.createMovementForm()?.get('entitySelection') as FormGroup;
  }

  get routeSelectionFormGroup() {
    return this.createMovementForm()?.get('routeSelection') as FormGroup;
  }

  get routeInformationFormGroup() {
    return this.createMovementForm()?.get('routeInformation') as FormGroup;
  }

  get isStartMovementsFuture(): boolean {
    return this.createMovementForm()?.get('startMovements')?.value === 'FUTURE';
  }

  get selectedStepIndex(): number {
    return this.createMovementForm()?.get('stepIndex')?.value ?? 0;
  }

  get selectedRouteType(): string {
    return this.routeSelectionFormGroup.get('routeType')?.value;
  }

  get routePointsInformation() {
    return (
      this.routeSelectionFormGroup.get('routePointsInformation')?.value || []
    );
  }

  private updateFieldsEffect = effect(() => {
    this.updateFieldsBasedOnEntityType(this.entityType());
  });

  private syncSelectedEntitiesEffect = effect(() => {
    const formEntities = this.formSelectedEntities();

    // Only update if the values are different to avoid unnecessary updates
    if (
      JSON.stringify(this.selectedEntities()) !== JSON.stringify(formEntities)
    ) {
      this.selectedEntities.set(formEntities);
    }
  });

  ngOnInit() {
    const form = this.createMovementForm();
    if (!form) return;

    const initialEntityType = form.get('entityType')?.value;
    this.updateFieldsBasedOnEntityType(initialEntityType);

    // Initialize selectedEntities from form control (for persistence)
    const savedSelectedEntities =
      form.get('entitySelection.selectedEntities')?.value || [];
    this.selectedEntities.set(savedSelectedEntities);
  }

  updateRoutePointsInformation(points: RouteClickData[]): void {
    const fa = this.routeSelectionFormGroup.get(
      'routePointsInformation'
    ) as FormArray;

    fa.clear();

    const entityType = this.createMovementForm()?.get('entityType')?.value;
    const savedRoutePoints =
      this.savedMovement()?.routeSelection?.routePointsInformation || [];

    points.forEach((point, index) => {
      // Get saved values for this point index, or use defaults
      const savedPoint = savedRoutePoints[index] || {};

      const formControls: Record<string, FormControl> = {
        latitude: new FormControl(point.latitude),
        longitude: new FormControl(point.longitude),
        speed: new FormControl(savedPoint.speed ?? 60, Validators.required),
        altitude: new FormControl(savedPoint.altitude ?? 0),
        pointType: new FormControl(savedPoint.pointType ?? 'normal'),
        pause: new FormControl(savedPoint.pause ?? 0),
        majorAxis: new FormControl(savedPoint.majorAxis ?? 0.1),
        minorAxis: new FormControl(savedPoint.minorAxis ?? 0.2),
        startOffset: new FormControl(savedPoint.startOffset ?? 0),
        isLoopStart: new FormControl(savedPoint.isLoopStart ?? false),
        isLoopEnd: new FormControl(savedPoint.isLoopEnd ?? false),
        numLoops: new FormControl(savedPoint.numLoops ?? 1),
        duration: new FormControl(savedPoint.duration ?? -1),
        continuous: new FormControl(savedPoint.continuous ?? false)
      };

      if (entityType === 'land') {
        formControls['patrol'] = new FormControl(savedPoint.patrol ?? 0);
      }

      if (entityType === 'air') {
        formControls['hover'] = new FormControl(savedPoint.hover ?? 0);
        formControls['orbit'] = new FormControl(savedPoint.orbit ?? 0);
      }

      const fg = new FormGroup(formControls);

      // Apply initial disabled state based on entity type
      if (entityType === 'land') {
        fg.get('altitude')!.disable({ emitEvent: false });
        fg.get('majorAxis')!.disable({ emitEvent: false });
        fg.get('minorAxis')!.disable({ emitEvent: false });
      }

      fg.get('pointType')!
        .valueChanges.pipe(startWith(fg.get('pointType')!.value))
        .subscribe((type: string) => {
          const pause = fg.get('pause')!;
          const patrol = fg.get('patrol');
          const hover = fg.get('hover');
          const orbit = fg.get('orbit');
          const majorAxis = fg.get('majorAxis')!;
          const minorAxis = fg.get('minorAxis')!;

          // Pause & Patrol logic
          if (type === 'normal') {
            pause.disable({ emitEvent: false });
            patrol?.disable({ emitEvent: false });
            hover?.disable({ emitEvent: false });
            orbit?.disable({ emitEvent: false });
          } else {
            pause[type === 'pause' ? 'enable' : 'disable']({
              emitEvent: false
            });
            if (patrol) {
              patrol[type === 'patrol' ? 'enable' : 'disable']({
                emitEvent: false
              });
            }
            if (hover) {
              hover[type === 'hover' ? 'enable' : 'disable']({
                emitEvent: false
              });
            }
            if (orbit) {
              orbit[type === 'orbit' ? 'enable' : 'disable']({
                emitEvent: false
              });
            }
          }

          // Axis logic: only enabled for 'patrol'
          if (type === 'patrol' || type === 'orbit') {
            majorAxis.enable({ emitEvent: false });
            minorAxis.enable({ emitEvent: false });
          } else {
            majorAxis.disable({ emitEvent: false });
            minorAxis.disable({ emitEvent: false });
          }
        });

      fg.get('isLoopStart')!
        .valueChanges.pipe(startWith(fg.get('isLoopStart')!.value))
        .subscribe((isStart: boolean) => {
          const continuous = fg.get('continuous')!;
          const numLoops = fg.get('numLoops')!;
          const duration = fg.get('duration')!;

          if (isStart) {
            continuous.enable({ emitEvent: false });
            numLoops.enable({ emitEvent: false });
            duration.enable({ emitEvent: false });
          } else {
            continuous.disable({ emitEvent: false });
            numLoops.disable({ emitEvent: false });
            duration.disable({ emitEvent: false });
          }
        });

      fa.push(fg);
    });
  }

  updateRoutePointInformation(
    index: number,
    field: string,
    value: string | number | boolean
  ) {
    const fa = this.routeSelectionFormGroup.get(
      'routePointsInformation'
    ) as FormArray;
    const fg = fa.at(index) as FormGroup;
    fg.get(field)!.setValue(value);
  }

  getRoutePointInfo(index: number): RoutePoint {
    return this.routePointsInformation[index] || {};
  }

  getRoutePointFieldLabel(field: RoutePoint): string {
    if (field.name === 'speed') {
      const entityType = this.createMovementForm()?.get('entityType')?.value;
      const unit = entityType === 'air' ? 'knots' : 'km/h';
      return `Speed (${unit})`;
    }
    return field.label;
  }

  disableNextButton(index: number): boolean {
    if (index === 0) {
      return this.selectedEntities().length === 0;
    } else if (index === 1) {
      return (
        this.routeSelectionFormGroup.invalid ||
        this.gisService.routePointsCount() < 2
      );
    }

    return false;
  }

  updateFieldsBasedOnEntityType(entityType: string): void {
    const altitudeFields = ['altitude', 'minHeight', 'maxHeight'];
    const routeInformation = this.routeInformationFormGroup;

    // Default values for altitude fields when enabled
    const defaultValues: Record<string, number> = {
      altitude: 1,
      minHeight: 1,
      maxHeight: 1
    };

    altitudeFields.forEach((fieldName) => {
      const control = routeInformation.get(fieldName);
      if (control) {
        if (entityType === 'land') {
          control.disable();
          control.setValue('');
        } else {
          control.enable();
          if (!control.value || control.value === '') {
            control.setValue(defaultValues[fieldName]);
          }
        }
      }
    });

    // Handle route points altitude and axis fields
    const fa = this.routeSelectionFormGroup.get(
      'routePointsInformation'
    ) as FormArray;
    const routePointAltitudeFields = ['altitude'];

    fa.controls.forEach((fg) => {
      routePointAltitudeFields.forEach((fieldName) => {
        const control = (fg as FormGroup).get(fieldName);
        if (control) {
          if (entityType === 'land') {
            control.disable({ emitEvent: false });
          } else {
            const pointType = (fg as FormGroup).get('pointType')?.value;
            if (fieldName === 'altitude' || pointType === 'patrol')
              control.enable({ emitEvent: false });
          }
        }
      });
    });

    this.updateRoutePointsInformation(this.gisService.routePoints());
  }

  getFieldLabel(field: RoutePoint): string {
    if (field.name === 'speed') {
      const entityType = this.createMovementForm()?.get('entityType')?.value;
      return entityType === 'air' ? 'Speed (knots)' : 'Speed (km/hr)';
    }
    return field.label;
  }

  onEntityToggle(entity: Entity) {
    const index = this.selectedEntities().indexOf(entity);
    if (index > -1) {
      this.selectedEntities.set(
        this.selectedEntities().filter((e) => e !== entity)
      );
    } else {
      this.selectedEntities.set([...this.selectedEntities(), entity]);
    }

    this.entitySelectionFormGroup
      .get('selectedEntities')
      ?.setValue(this.selectedEntities());
  }

  getAvailableEntities() {
    return this.entities().filter(
      (entity) => !this.selectedEntities().includes(entity)
    );
  }

  clearRoutePoints(): void {
    this.gisService.clearRoutePoints();
  }

  removeRoutePoint(index: number): void {
    this.gisService.removeRoutePoint(index);
  }

  onStepChange(event: StepperSelectionEvent): void {
    this.createMovementForm()?.get('stepIndex')?.setValue(event.selectedIndex);
  }

  drop(event: CdkDragDrop<string[]>) {
    // Reorder the route points in the GIS service
    moveItemInArray(
      this.gisService.routePoints(),
      event.previousIndex,
      event.currentIndex
    );

    const currentRoutePointsInfo = [...this.routePointsInformation];
    moveItemInArray(
      currentRoutePointsInfo,
      event.previousIndex,
      event.currentIndex
    );

    this.routeSelectionFormGroup
      .get('routePointsInformation')
      ?.patchValue(currentRoutePointsInfo);
  }

  disableAddPointsButton(): boolean {
    return this.creatingRoute()?.() ?? false;
  }
}
