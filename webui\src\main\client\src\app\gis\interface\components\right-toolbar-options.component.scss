@import 'toolbar-mixins';

.right-toolbar-options {
  @include toolbar-standards
}

.button-wrapper {
  @include button-wrapper-styling;
}

.bottom-margin {
  margin-bottom: 31.25px;
}

button {
  @include toolbar-button-styling;

  &.label-button {
    @include label-button-styling;
  }
}

button:hover {
  @include button-hover-styling;
}

button:active, .active {
  @include button-active-styling;
}

label {
  @include label-styling;

  &.hide-label {
    @include hide-label-styling;
  }
}

::ng-deep .button-icon {
  @include toolbar-button-icon-styling;
}
