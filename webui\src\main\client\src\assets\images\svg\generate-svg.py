#!/usr/bin/python
import os
import re

def generate_svg_set():
    # Prompt user for input directory and output file path
    # input_directory = input("Enter the path to your SVG files directory: ")
    input_directory = os.getcwd()
    # output_file_path = input("Enter the desired path for the generated SVG set file: ")
    output_file_path = 'bundled.svg'

    # Read all files in the directory
    files = os.listdir(input_directory)

    # Filter out non-SVG files
    svg_files = [file for file in files if file.endswith('.svg')]

    # Generate the SVG set content
    svg_set_content = ""
    for svg_file in svg_files:
        svg_path = os.path.join(input_directory, svg_file)
        with open(svg_path, 'r', encoding='utf-8') as file:
            file_content = file.read()
            svg_view_box =  extract_viewbox(file_content)
            svg_content = re.sub(r'<[\/]?svg[^>]*?>', '', file_content)
            symbol_id = os.path.splitext(svg_file)[0]
            svg_set_content += f'<symbol id="{symbol_id}" viewBox="{svg_view_box}">\n{svg_content}\n</symbol>\n'

    # Create the SVG set file content
    svg_set = f'<svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n<defs>\n{svg_set_content}</defs>\n</svg>'

    # Write the SVG set to the output file
    with open(output_file_path, 'w', encoding='utf-8') as output_file:
        output_file.write(svg_set)

    print(f"SVG set file generated at {output_file_path}")

def extract_viewbox(svg_content):
    match = re.search(r'viewBox\s*=\s*["\']([^"\']+)["\']', svg_content)
    if match:
        return match.group(1)
    return "0 0 24 24"

# Run the script
generate_svg_set()
