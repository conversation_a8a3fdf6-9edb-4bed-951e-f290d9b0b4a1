/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/14/2023, 11:47 AM
 */
import { createAction } from '@ngrx/store';

import { payload } from '@simfront/common-libs';

export const setLayerRedrawInterval = createAction('[Redraw Interval]', payload<number>());

export const pauseLayerRedrawAction = createAction('[Pause Layer Redraw]');

export const startLayerRedrawAction = createAction('[Start Layer Redraw]');

export const layerCommand = createAction('[Layer-Redraw] Command', payload<'Start' | 'Pause'>());
