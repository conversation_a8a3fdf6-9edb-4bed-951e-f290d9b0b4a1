* {
  user-select: none;
}

.check-box {
  transform: translateY(-8px);
}

.layerManager {
  width: 350px;
  height: 410px;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 5px;
  right: 15px;
  background: rgba(var(--secondary-background-light-rgb), var(--opacity-high));
  z-index: 451;
  border: 2px solid var(--secondary-background-dark);
  transition: height 0.3s ease;
}

.wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
}

.layerName {
  font-size: 1.05rem;
}

.layer-manager-header {
  display: flex;
  align-content: center;
  justify-content: center;
  position: relative;
  height: 40px;
  background-color: var(--background);
}

.layer-manager-header h1 {
  transform: translateY(4px);
}

.collapse-button {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: fit-content;
}

.main-tree-div {
  flex-grow: 1;
  flex-shrink: 1;
  overflow: auto;
}

.small-size {
  height: 40px;
}

.resizable {
  resize: vertical;
  overflow: auto;
}

.hide {
  display: none;
}

.divider {
  width: 90%;
  margin-left: 5%;
  border-color: var(--divider-dark);
  border-width: 3px;
}

.card {
  margin-left: 15%;
  margin-top: 2%;
}

.expandable {
  margin-left: -50px;
}

::ng-deep ul {
  padding: 0 0 0 5px;
}

::ng-deep #treeNoderoot {
  margin-left: -20px;
}

.layer-row-wrapper {
  min-width: 200px;
}

.layer-row-wrapper:hover {
  background: var(--secondary-background-light);
}

.grid-parent {
  margin-left: 5px;
}

.grid-toggle {
  margin-left: 5px;
}

::ng-deep .grid-tree li {
  margin-left: 35px;
}

::ng-deep .world-borders li {
  margin-left: 5px;
}

.layer-lock-button {
  display: flex;
  width: 26px;
  height: 26px;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
}

.layer-settings-icon {
  font-size: 24px;
  cursor: pointer;
  height: 26px;
  width: 26px;
}

.layer-settings-icon:hover {
  border: solid 1px var(--divider-dark);
  background-color: rgba(var(--background-rgb), var(--opacity));
}

.layer-settings-icon:active {
  border: solid 1px var(--divider-dark);
  background-color: rgba(var(--secondary-background-light-rgb), var(--opacity));
}

.layer-settings-button {
  padding: 0;
  background: none;
  height: 30px;
  width: 30px;
}

.last-divider {
  border-color: var(--divider-dark);
  border-width: 3px;
}

button .svg-img,
.layer-lock-button svg {
  display: flex;
  width: 32px;
  color: var(--text) !important;
  height: auto;
  align-items: center;
  justify-content: center;
  align-self: center;
}

.layer-lock-button svg {
  width: 24px;
}

::ng-deep .lock-unlock-icon .foregroundColor {
  fill: #ffff00;
}

.layer-row {
  display: flex;
  align-items: flex-start;
}

.layer-row-wrapper .layer-lock-button, .layer-row-wrapper .layer-settings-button{
  visibility: hidden;
}

.layer-row-wrapper:hover .layer-lock-button, .layer-row-wrapper:hover .layer-settings-button{
  visibility: visible;
}

.radio-button:hover,
.radio-button span:hover{
  cursor: pointer;
}

.slider-wrapper {
  position: relative;
  width: 140px;
  left: 50px;
}

.slider-background-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.slider-background-scale {
  margin-top: -30px;
  width: 140px;
  height: auto;
}

.map-opacity-slider {
  margin-top: -15px;
  margin-left: 8px;
  width: 128px;
  height: 20px;
}

.map-opacity-label {
  margin-top: 3px;
  max-width: unset;
}

.hidden {
  display: none;
}
