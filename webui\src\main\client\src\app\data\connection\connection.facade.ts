import { Injectable } from '@angular/core';

import { createEffect, ofType } from '@ngrx/effects';

import { catchError, concatMap, map, of, switchMap } from 'rxjs';

import { BSDialogComponent } from '../../battlespace/node/bs-dialog.component';

import { CreateBSC } from '../bs-params/bs-params.model';
import { BSParamDialogPayload } from '../bs-params/bs-params.store';

import { ConnectionForm } from './connection-form.component';
import { ConnectionActions, ConnectionFacadeBase } from './connection.model';

@Injectable({ providedIn: 'root' })
export class ConnectionFacade extends ConnectionFacadeBase {
  override facadeDialogContent = ConnectionForm;

  // <editor-fold desc="Selectors">
  nonExchange$ = this.filter$((c) => !c.exchangeConnection);
  // </editor-fold>

  // <editor-fold desc="Actions">
  public openDeleteDialog(id: string): void {
    this.openDialog({ crud: 'Delete', quantity: 'One', id });
  }

  public createSpecial(bsc: CreateBSC): void {
    this.store.dispatch(ConnectionActions.createSpecial(bsc));
  }

  public openCreateConnectionDialog(payload?: BSParamDialogPayload): void {
    this.store.dispatch(ConnectionActions.openCreateDialog(payload));
  }
  // </editor-fold>

  // <editor-fold desc="Effects">

  openCreateDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ConnectionActions.openCreateDialog),
      switchMap((action) =>
        this.dialog
          .open(BSDialogComponent, {
            width: '500px',
            data: action.payload,
            disableClose: true
          })
          .afterClosed()
      ),
      map(() => ConnectionActions.createDialogDismissed())
    )
  );

  createSpecial$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ConnectionActions.createSpecial),
      switchMap((action) =>
        this.api.createOne(action.payload.battleSpaceConnection).pipe(
          concatMap((r) => [
            this.actions.createOneReceived(r),
            ConnectionActions.createSpecialSuccess(action.payload)
          ]),
          catchError((err) => {
            console.error(err);
            return of(this.actions.createOneFailed());
          })
        )
      )
    )
  );
  // </editor-fold>
}
