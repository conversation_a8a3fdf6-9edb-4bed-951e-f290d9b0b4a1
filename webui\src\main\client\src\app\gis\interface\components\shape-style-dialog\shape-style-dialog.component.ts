import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { CdkDrag } from '@angular/cdk/drag-drop';
import { MatButtonModule } from '@angular/material/button';
import { ShapeOptionsToolbarComponent } from '../shape-options-toolbar.component';

@Component({
  selector: 'nexus-shape-style-dialog',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    CdkDrag,
    ShapeOptionsToolbarComponent
  ],
  templateUrl: './shape-style-dialog.component.html',
  styleUrl: './shape-style-dialog.component.css'
})
export class ShapeStyleDialogComponent {
  private dialogRef = inject(MatDialogRef<ShapeStyleDialogComponent>);

  closeDialog() {
    this.dialogRef.close();
  }
}
