/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/21/2023, 1:13 PM
 */

export class QuadtreeNode {

  constructor(x, y, width, height, maxObjects, maxLevels, level) {
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
    this.maxObjects = maxObjects;
    this.maxLevels = maxLevels;
    this.level = level;
    this.nodes = [];
    this.entities = [];
  }

  split() {
    const subWidth = this.width / 2;
    const subHeight = this.height / 2;
    const nextLevel = this.level + 1;

    this.nodes[0] = new QuadtreeNode(this.x + subWidth, this.y, subWidth, subHeight, this.maxObjects, this.maxLevels, nextLevel);
    this.nodes[1] = new QuadtreeNode(this.x, this.y, subWidth, subHeight, this.maxObjects, this.maxLevels, nextLevel);
    this.nodes[2] = new QuadtreeNode(this.x, this.y + subHeight, subWidth, subHeight, this.maxObjects, this.maxLevels, nextLevel);
    this.nodes[3] = new QuadtreeNode(this.x + subWidth, this.y + subHeight, subWidth, subHeight, this.maxObjects, this.maxLevels, nextLevel);
  }

  // Adjusted getIndex method to consider resolution
  getIndex(entity, resolution) {
    const verticalMidpoint = this.x + this.width / 2 / resolution;
    const horizontalMidpoint = this.y + this.height / 2 / resolution;

    const isTopQuadrant = entity.lat < horizontalMidpoint;
    const isBottomQuadrant = entity.lat >= horizontalMidpoint;

    if (entity.lon < verticalMidpoint) {
      if (isTopQuadrant) return 1; // Top-left quadrant
      if (isBottomQuadrant) return 2; // Bottom-left quadrant
    } else if (entity.lon >= verticalMidpoint) {
      if (isTopQuadrant) return 0; // Top-right quadrant
      if (isBottomQuadrant) return 3; // Bottom-right quadrant
    }

    return -1; // Entity does not fit in any quadrant
  }

  insert(entity) {
    if (this.nodes.length !== 0) {
      const index = this.getIndex(entity);

      if (index !== -1) {
        this.nodes[index].insert(entity);
        return;
      }
    }

    this.entities.push(entity);

    if (this.entities.length > this.maxObjects && this.level < this.maxLevels) {
      if (this.nodes.length === 0) {
        this.split();
      }

      let i = 0;
      while (i < this.entities.length) {
        const index = this.getIndex(this.entities[i]);
        if (index !== -1) {
          this.nodes[index].insert(this.entities.splice(i, 1)[0]);
        } else {
          i++;
        }
      }
    }
  }

  // Return all objects in the same quadrant as the specified object
  retrieve(entity) {
    const index = this.getIndex(entity);
    const returnEntities = this.entities.slice();

    if (this.nodes.length !== 0) {
      if (index !== -1) {
        returnEntities.push(...this.nodes[index].retrieve(entity));
      } else {
        for (let i = 0; i < 4; i++) {
          returnEntities.push(...this.nodes[i].retrieve(entity));
        }
      }
    }

    return returnEntities;
  }
}
