import { Component, computed, signal } from '@angular/core';
import {
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogTitle
} from '@angular/material/dialog';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { Mat<PERSON>utton } from '@angular/material/button';
import {
  Mat<PERSON><PERSON>r,
  MatFormField,
  MatHint,
  MatLabel,
  MatSuffix
} from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import {
  MatDatepicker,
  MatDatepickerInput,
  MatDatepickerToggle
} from '@angular/material/datepicker';
import { toSignal } from '@angular/core/rxjs-interop';
import { filter, map } from 'rxjs';

type RapTimeFormGroup = FormGroup<{
  date: FormControl<null | Date>;
  hour: FormControl<number>;
  minute: FormControl<number>;
  second: FormControl<number>;
}>;

@Component({
  selector: 'set-rt-dialogue',
  templateUrl: './set-rt-dialog.component.html',
  imports: [
    FormsModule,
    MatButton,
    MatDialogTitle,
    MatFormField,
    MatLabel,
    MatInput,
    MatDialogActions,
    ReactiveFormsModule,
    MatDatepickerInput,
    MatDatepickerToggle,
    MatSuffix,
    MatDatepicker,
    MatDialogClose,
    MatDialogContent,
    MatError,
    MatHint
  ],
  styleUrl: './set-rt-dialog.component.css'
})
export class SetRtDialogComponent {
  rapTime: RapTimeFormGroup = new FormGroup({
    date: new FormControl(null, [Validators.required]),
    hour: new FormControl(0, [
      Validators.required,
      Validators.min(0),
      Validators.max(23)
    ]),
    minute: new FormControl(0, [
      Validators.required,
      Validators.min(0),
      Validators.max(59)
    ]),
    second: new FormControl(0, [
      Validators.required,
      Validators.min(0),
      Validators.max(59)
    ])
  });
  fullDateTime = toSignal(
    this.rapTime.valueChanges.pipe(
      filter(() => this.rapTime.valid),
      map(({ date, hour, minute, second }) => {
        if (!date) return null;
        const newDate = new Date(date);
        newDate.setHours(hour);
        newDate.setMinutes(minute);
        newDate.setSeconds(second);
        console.log(newDate);
        return newDate.getTime();
      })
    )
  );
}
