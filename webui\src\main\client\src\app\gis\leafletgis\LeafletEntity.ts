import { SFMap } from '@simfront/common-libs';

import { LatLngTuple, Point } from 'leaflet';
import ms from 'milsymbol';
import {
  Bounds,
  Container,
  FederatedPointerEvent,
  Graphics,
  Sprite,
  Text,
  Texture
} from 'pixi.js';

import { Coordinate } from '../../cursit/models/cursit.model';

import { calculateArrowhead, calculateXAndY } from '../interface/gis-util';
import { LocationSettable } from '../interface/LocationSettable';

import { LeafletMapDisplay } from './LeafletMapDisplay';

//only Containers are allowed to add children not with Sprite since PIXI v8.
export class LeafletEntity extends Container implements LocationSettable {
  _selected = false;
  selectionBox: Graphics = new Graphics();
  directionArrow: Graphics = new Graphics();
  _label: Text;
  _iconLocalBounds: Bounds;
  _sidc: string;
  location: LatLngTuple | null | undefined;
  private _originalIndexInParent: number;
  private _broughtToFront = false;
  _bearing: number;
  _speed: number;
  directionArrowAdded = false;
  _labelAdded = false;
  _id: string;
  _catCode: string;
  _iconTextureHash: SFMap<Texture> = {};
  private newSprite: Sprite;
  constructor(
    id: string,
    sidc: string,
    symbolTexture: Texture,
    private mapDisplay: LeafletMapDisplay,
    iconTextureHash: SFMap<Texture>,
    catCode: string,
    label?: string
  ) {
    super();
    this._iconTextureHash = iconTextureHash;
    this._id = id;
    this._catCode = catCode;
    this._sidc = sidc;
    this.cullable = true;
    this.newSprite = new Sprite(symbolTexture);
    // this.cacheAsBitmap = true;
    // this.cacheAsBitmapResolution = 0.5;
    this._iconLocalBounds = new Bounds();
    this.newSprite.anchor.set(0.5, 1.0);
    this._iconLocalBounds = this.getLocalBounds().clone();
    this.boundsArea = this.toLocal(this.newSprite.getBounds());

    this.addChild(this.newSprite);
    const newLabel = `${label}`;
    this.addLabel(newLabel);
    this.addSelectionBox();
    this.addSelectionMenu();

    const { cursor } = this.mapDisplay.map.getContainer().style;

    // this.on('mousedowncapture', (event: FederatedPointerEvent) => {
    //   this.selected = true;
    //   this.bringToFront();
    // });

    this.on('pointerdown', () => {
      this.select();
      this.bringToFront();
    });
    //
    // this.on('pointerover', () => {
    //   this.mapDisplay.map.getContainer().style.cursor = 'move';
    // });
    //
    // this.on('pointerout', () => {
    //   this.mapDisplay.map.getContainer().style.cursor = cursor;
    // });

    // this.on('mousedown', this.onDragStart);
    // this.on('mousemove', this.onDragMove);
    // this.on('mouseupoutside', this.onDragEnd);
    // this.on('mouseup', this.onDragEnd);

    this.eventMode = 'dynamic';
    this.interactiveChildren = true;
    this.setLabelVisibility(false);
  }

  set id(id: string) {
    this._id = id;
  }

  get id(): string {
    return this._id;
  }

  setLocation(latLon: Coordinate): void {
    this.location = [latLon[0], latLon[1]];
    const point: Point = this.mapDisplay.map.projectFromLatLonToPoint(
      this.location
    );
    this.x = point.x;
    this.y = point.y;
  }

  setSpeedAndBearing(speed: number, bearing: number): void {
    this.speed = speed;
    this.bearing = bearing;
    if (speed > 0) {
      if (!this.directionArrowAdded) {
        this.directionArrowAdded = true;
        this.addChild(this.directionArrow);
      }
      this.drawDirectionArrow();
    } else {
      this.directionArrowAdded = false;
      this.directionArrow.clear();
      this.removeChild(this.directionArrow);
    }
  }

  setSymbolCode(symbolCode: string): void {
    if (symbolCode !== this._sidc) {
      if (this._iconTextureHash[symbolCode] === undefined) {
        const symbol = new ms.Symbol(symbolCode, { size: 25 }).asCanvas();
        const symbolTexture = Texture.from(symbol);
        this._iconTextureHash[symbolCode] = symbolTexture;
        this.newSprite.texture = symbolTexture;
      } else {
        this.newSprite.texture = this._iconTextureHash[symbolCode];
      }
    }
  }

  drawDirectionArrow(): void {
    this.directionArrow.clear();
    let directionArrowColor = 0x000000;
    if (this.speed > 0) {
      switch (this._sidc.charAt(1)) {
        case 'F':
          directionArrowColor = 0x80e0ff;
          break;
        case 'U':
          directionArrowColor = 0xffff00;
          break;
        case 'H':
          directionArrowColor = 0xff8080;
          break;
        default:
          directionArrowColor = 0x000000;
      }
      const iconBounds: Bounds = this.getIconLocalBounds();
      const middle = iconBounds.left + iconBounds.width / 2;
      this.directionArrow.moveTo(middle, iconBounds.bottom);
      const bottomExtension = iconBounds.bottom + 20;
      this.directionArrow.lineTo(middle, bottomExtension);
      const { x, y } = calculateXAndY(this.bearing, 30);
      const lastPointX = middle + x;
      const lastPointY = bottomExtension - y;
      this.directionArrow.lineTo(lastPointX, lastPointY);
      const [{ x: arrowX1, y: arrowY1 }, { x: arrowX2, y: arrowY2 }] =
        calculateArrowhead(
          middle,
          bottomExtension,
          lastPointX,
          lastPointY,
          10,
          15
        );

      this.directionArrow.lineTo(arrowX1, arrowY1);
      this.directionArrow.lineTo(arrowX2, arrowY2);
      this.directionArrow.lineTo(lastPointX, lastPointY);
      this.directionArrow.stroke({
        width: 2,
        color: directionArrowColor,
        alpha: 1
      });
    }
  }
  onDragStart(event: FederatedPointerEvent): void {
    // this.data = event.target as Entity;
    this.mapDisplay.map.dragging.disable();
    this.alpha = 0.5;
    this.mapDisplay.dragged = true;
    this.mapDisplay.selectedElement = this;
  }

  // onDragEnd(event: FederatedPointerEvent) {
  //   this.alpha = 1.0;
  //   this._mapComponent.map.dragging.enable();
  //   this._mapComponent.dragged = false;
  //   //this.data = null;
  //   this._mapComponent.selectedGraphic = null;
  //   this._mapComponent.militarySymbolLayer.redraw();
  // }

  // onDragMove(event: FederatedPointerEvent) {
  //   if (this._mapComponent.dragged && this.data) {
  //     const newPosition = event.getLocalPosition(this.parent);
  //     this.x = newPosition.x;
  //     this.y = newPosition.y;
  //     //let latlon = this._mapComponent.militarySymbolLayer.utils.layerPointToLatLng(
  //     [this.x, this.y]);
  //     // this.popup.setLatLng(latlon);
  //     // this.popup.setContent('latlon is: ' + latlon.lat + ' ' + latlon.lng);
  //     this._mapComponent.militarySymbolLayer.redraw();
  //   }
  // }

  set selected(selected: boolean) {
    this._selected = selected;
    this.mapDisplay.selected = selected;
    // console.log('entity den' + this.leafletElementFactory.mapDisplay.selected);
  }

  get selected(): boolean {
    return this._selected;
  }

  set labelText(label: string) {
    this._label.text = label;
  }

  get labelText(): string {
    return this._label.text;
  }

  select(): void {
    this.selected = true;
    this.alpha = 1.0;
    this.selectionBox.visible = true;
    this.bringToFront();
  }

  addSelectionBox(): void {
    const { x, y } = this.boundsArea;
    const { height, width } = this.newSprite.getSize();
    this.selectionBox.rect(x - 10, y - 10, width + 20, height + 20);
    this.selectionBox.stroke({ width: 2, color: 0xff0000 });
    this.selectionBox.visible = false;
    this.addChild(this.selectionBox);
  }

  updateSelectionMenuScale(): void {
    if (this.mapDisplay.selectionMenuElement) {
      this.mapDisplay.selectionMenuElement.scale.set(
        1 / this.mapDisplay.map.scaleForProjection
      );
    }
  }

  addSelectionMenu(): void {
    this.updateSelectionMenuScale();
    this.mapDisplay.selectionMenuElement.zIndex = 1000;

    // Add zoom event listener
    this.mapDisplay.map.on('zoom', () => {
      this.updateSelectionMenuScale();
    });
  }

  addLabel(text?: string): void {
    if (text) {
      this._labelAdded = true;
      this._label = new Text();
      this._label.style.fontSize = 14;
      this._label.style.fontWeight = 'bold';
      this._label.text = text;
      this._label.x = this._iconLocalBounds.right + 10;
      this._label.y = this._iconLocalBounds.top - 10;
      this.addChild(this._label);
    }
  }

  removeSelectionBox(): void {
    this.selectionBox.visible = false;
  }

  // removeSelectionMenu(): void {
  //   this.mapDisplay.selectionMenuElement.visible = false;
  // }

  removeLabel(): void {
    if (this._labelAdded) {
      this._labelAdded = false;
      this.removeChild(this._label);
      this._label.destroy();
    }
  }

  setLabelVisibility(visible: boolean): void {
    this._label.visible = visible;
  }

  setElementVisibility(isVisible: boolean) {
    this.visible = isVisible;
  }

  testHideIcon(vis: boolean) {
    this.visible = vis;
  }

  getIconLocalBounds(): Bounds {
    return this._iconLocalBounds;
  }

  bringToFront(): void {
    if (this.parent) {
      const { parent } = this;
      this._originalIndexInParent = parent.getChildIndex(this);
      parent.removeChild(this);
      parent.addChild(this);
      this._broughtToFront = true;
    }
  }

  set broughtToFront(value: boolean) {
    this._broughtToFront = value;
  }

  get broughtToFront(): boolean {
    return this._broughtToFront;
  }

  get originalParentIndex(): number {
    return this._originalIndexInParent;
  }

  set bearing(bearing: number) {
    this._bearing = bearing;
  }

  get bearing(): number {
    return this._bearing;
  }

  set speed(speed: number) {
    this._speed = speed;
  }

  get speed(): number {
    return this._speed;
  }
}
