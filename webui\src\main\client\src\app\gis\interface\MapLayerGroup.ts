/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/3/2024, 12:15 PM
 */
import { SFMap } from '@simfront/common-libs';
import { getLayerGroupVisibility } from './gis-util';
import { MapDisplay } from './MapDisplay';
import { MapLayer } from './MapLayer';
import { LayerSource, LayerType } from '../../data/layer-manager/layer-manager.model';

export const LAYER_GROUP_VISIBILITY = {
  fullVisible: 'fullVisible',
  partialVisible: 'partialVisible',
  fullHidden: 'fullHidden'
} as const;

export type LayerGroupVisibility = typeof LAYER_GROUP_VISIBILITY[
  keyof typeof LAYER_GROUP_VISIBILITY
];

export interface MapLayerGroup<L, G> extends MapLayer<L> {

  addLayer(mapLayer?: MapLayer<L> | MapLayerGroup<L, G>, parentLayer?: MapLayerGroup<L, G>): void;

  removeLayer(
    mapLayer?: MapLayer<L> | MapLayerGroup<L, G>,
    mapLayerId?: string,
    mapLayerName?: string
  ): void;

  setGISGroupLayer(layer: G): void;

  getGISGroupLayer(): G;

  getVisibility(): boolean;

  get childLayers(): SFMap<MapLayer<L> | MapLayerGroup<L, G>>;

  getLayer(layerName?: string, layerId?: string): MapLayer<L> | MapLayerGroup<L, G>;

}

export abstract class AbstractMapLayerGroup<L, G> implements MapLayerGroup<L, G> {
  layerGroup: G;
  _layerId: string;
  _parentId: string;
  _layerSource: LayerSource;
  _layerType: LayerType;
  private _childLayers: SFMap<MapLayer<L> | MapLayerGroup<L, G>> = {};
  private _layerGroupName: string;
  protected _visible: boolean;
  mapDisplay: MapDisplay<L, G>;

  protected constructor(mapDisplay: MapDisplay<L, G>) {
    this.mapDisplay = mapDisplay;
  }

  abstract addLayer(
    mapLayer: MapLayer<L> | MapLayerGroup<L, G>,
    parentLayer?: MapLayerGroup<L, G>
  ): void;

  abstract removeLayer(
    mapLayer?: MapLayer<L> | MapLayerGroup<L, G>,
    mapLayerId?: string,
    mapLayerName?: string): void;

  abstract setVisible(visible: boolean): void;

  isVisible(): boolean {
    const visibility = getLayerGroupVisibility(this);
    return this._visible;
  }

  abstract getVisibility(): boolean;

  set parentId(parentId: string) {
    this._parentId = parentId;
  }

  get parentId(): string {
    return this._parentId;
  }

  set layerName(layerName: string) {
    this._layerGroupName = layerName;
  }

  get layerName(): string {
    return this._layerGroupName;
  }

  get childLayers(): SFMap<MapLayer<L> | MapLayerGroup<L, G>> {
    return this._childLayers;
  }

  abstract getLayer(layerName?: string, layerId?: string): MapLayer<L> | MapLayerGroup<L, G>;

  abstract setGISGroupLayer(layer: G): void;

  abstract getGISGroupLayer(): G;

  set layerId(id: string) {
    this._layerId = id;
  }

  get layerId(): string {
    return this._layerId;
  }

  set layerSource(source: LayerSource) {
    this._layerSource = source;
  }

  get layerSource(): LayerSource {
    return this._layerSource;
  }

  set layerType(type: LayerType) {
    this._layerType = type;
  }

  get layerType(): LayerType {
    return this._layerType;
  }
}
