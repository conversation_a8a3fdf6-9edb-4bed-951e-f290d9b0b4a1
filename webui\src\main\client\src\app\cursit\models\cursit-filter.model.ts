import { FormControl } from '@angular/forms';
import {
  Affiliation,
  Affiliations,
  BattleDimension,
  BattleDimensions,
  Echelon,
  Echelons,
  Mobilities,
  Mobility,
  OperationalCondition,
  OperationalConditions,
  SymbolSet,
  SymbolSets
} from '../../milstd/models/milstd-2525.model';
import {
  FunctionDescription,
  FunctionIdMappings,
  getSymbolInfo
} from '../../milstd/models/milstd-2525b.model';

export interface CursitFilter {
  symbolSet: SymbolSet[];
  affiliation: Affiliation[];
  dimension: BattleDimension[];
  echelon: Echelon[];
  mobility: Mobility[];
  operationalCondition: OperationalCondition[];
  description: FunctionDescription[];
}

export interface CursitFilterForm {
  symbolSet: FormControl<CursitFilter['symbolSet']>;
  affiliation: FormControl<CursitFilter['affiliation']>;
  dimension: FormControl<CursitFilter['dimension']>;
  echelon: FormControl<CursitFilter['echelon']>;
  mobility: FormControl<CursitFilter['mobility']>;
  operationalCondition: FormControl<CursitFilter['operationalCondition']>;
  description: FormControl<CursitFilter['description']>;
}

export const INITIAL_FILTER: CursitFilter = {
  symbolSet: [...SymbolSets],
  affiliation: [...Affiliations],
  dimension: [...BattleDimensions],
  echelon: [...Echelons],
  mobility: [...Mobilities],
  operationalCondition: [...OperationalConditions],
  description: [...Object.values(FunctionIdMappings)]
};

interface FormField<T extends keyof CursitFilter> {
  label: string;
  name: T;
  options: CursitFilter[T];
  checked: boolean;
}

export const FORM_FIELDS: FormField<keyof CursitFilter>[] = [
  {
    label: 'Symbols',
    options: [...SymbolSets],
    name: 'symbolSet',
    checked: true
  },
  {
    label: 'Affiliations',
    options: [...Affiliations],
    name: 'affiliation',
    checked: true
  },
  {
    label: 'Battle Dimensions',
    options: [...BattleDimensions],
    name: 'dimension',
    checked: true
  },
  {
    label: 'Function ID',
    options: Object.values(FunctionIdMappings),
    name: 'description',
    checked: true
  },
  { label: 'Echelons', options: [...Echelons], name: 'echelon', checked: true },
  {
    label: 'Mobility Indicators',
    options: [...Mobilities],
    name: 'mobility',
    checked: true
  },
  {
    label: 'Operational Conditions',
    options: [...OperationalConditions],
    name: 'operationalCondition',
    checked: true
  }
];

const FILTER_KEYS: string[] = FORM_FIELDS.map((f) => f.name);

export const isVisible = (code: string, filters?: CursitFilter): boolean => {
  if (!filters) {
    return true;
  }

  try {
    const info = getSymbolInfo(code);
    return FILTER_KEYS.every((key) => filters[key].includes(info[key]));
  } catch (_) {
    return true;
  }
};
