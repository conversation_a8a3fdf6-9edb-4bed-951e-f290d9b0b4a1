.svg-container {
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
    overflow: visible;
}

.filter-color-normal {
    filter: brightness(0) saturate(100%) invert(21%) sepia(0%) saturate(4449%) hue-rotate(62deg) brightness(95%) contrast(81%);
}

::ng-deep svg-img.increase-stroke {
    stroke: rgb(66, 66, 66);
    stroke-width: 0.25;
}

.brightness {
    animation: brightness 2s infinite;
}

.clickable-svg {
    pointer-events: auto;
    cursor: pointer;
}

.dash-animation {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    stroke-opacity: 0.2;
    animation: dash 3s infinite;
}

@keyframes dash {
    0% {
        stroke-dashoffset: 1000;
        stroke-opacity: 0;
    }
    10% {
        stroke-opacity: 0.2;
    }
    100% {
        stroke-dashoffset: 0;
        stroke-opacity: 0;
    }
}

::ng-deep.svg-status-icon.PAUSED, ::ng-deep.svg-status-icon.ERROR {
    animation: flashing 2s infinite;
}

::ng-deep.svg-status-icon.ERROR {
    filter: brightness(0) saturate(100%) invert(31%) sepia(73%) saturate(2692%) hue-rotate(345deg) brightness(89%) contrast(86%);
}

::ng-deep.svg-status-icon.PAUSED {
    filter: brightness(0) saturate(100%) invert(86%) sepia(93%) saturate(574%) hue-rotate(324deg) brightness(85%) contrast(99%);
}

@keyframes flashing {
    0%, 100% {
        opacity: 100%;
    }
    50% {
        opacity: 50%;
    }
}
