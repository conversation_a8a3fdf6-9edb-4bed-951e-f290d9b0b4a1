<div class="toolbarWrapper">
  <button mat-mini-fab color="accent"
          matTooltip="Pan Mode"
          (click)="activatePanMode()">
    <mat-icon>open_with</mat-icon>
  </button>
  <button mat-mini-fab color="accent"
          matTooltip="Select Mode"
          (click)="activateBulkSelect()">
    <mat-icon>arrow_selector_tool</mat-icon>
  </button>
  <button mat-mini-fab color="accent"
          matTooltip="Create Entity"
          (click)="activateEntityCreation()">
    <mat-icon>create</mat-icon>
  </button>
  <button mat-mini-fab color="accent"
          matTooltip="Layer Manager"
          (click)="toggleLayerManager()">
    <mat-icon>layers</mat-icon>
  </button>
  <button mat-mini-fab color="accent"
          matTooltip="Add Entites For Test"
          (click)="addEntitiesForTest()">
    <mat-icon>add</mat-icon>
  </button>
</div>
