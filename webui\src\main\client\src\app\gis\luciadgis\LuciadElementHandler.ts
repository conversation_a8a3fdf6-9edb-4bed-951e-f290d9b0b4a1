import { Point } from '@luciad/ria/shape/Point';
import { MapElementHandler } from '../interface/ElementHandler';
import { LuciadMapDisplay } from './LuciadMapDisplay';
import { LuciadShape } from './LuciadShape';

/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/26/2024, 11:54 AM
 */
export class LuciadElementHandler extends MapElementHandler {
  luciadMapDisplay: LuciadMapDisplay;
  constructor(luciadMapDisplay: LuciadMapDisplay) {
    super();
    this.luciadMapDisplay = luciadMapDisplay;
  }

  updateSelectedElementStyle(): void {
    const { selectedElement } = this.luciadMapDisplay;
    if (selectedElement instanceof LuciadShape) {
      const { shape } = selectedElement;
      if (shape instanceof Point) {
        // selectedElement.genericSelectedIconStyle =
        // selectedElement.normalStyle = convertGenericStyleToLuciadStyle(this.shapeStyleInfo.normal);
        // selectedElement.selectedStyle = convertGenericStyleToLuciadStyle(this.shapeStyleInfo.selected);
      } else {
        // selectedElement.normalStyle = convertGenericStyleToLuciadStyle(this.shapeStyleInfo.normal);
        // selectedElement.selectedStyle = convertGenericStyleToLuciadStyle(this.shapeStyleInfo.selected);
      }
    }
  }
}
