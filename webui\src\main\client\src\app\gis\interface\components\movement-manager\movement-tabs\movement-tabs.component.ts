import { Component, Input, WritableSignal, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { FormGroup } from '@angular/forms';
import { CreateMovementComponent } from '../create-movement/create-movement.component';
import { Node } from 'src/app/data/node/node.model';
import { CreateMovementForm } from 'src/app/data/movement/movement.model';

@Component({
  selector: 'nexus-movement-tabs',
  imports: [CommonModule, MatTabsModule, CreateMovementComponent],
  templateUrl: './movement-tabs.component.html',
  styleUrl: './movement-tabs.component.css'
})
export class MovementTabsComponent {
  createMovementForm = input.required<FormGroup<CreateMovementForm>>();
  movementEngine = input.required<Node | null>();
  creatingRoute = input.required<WritableSignal<boolean>>();
  savedMovement = input<any>(null); // Movement data from sessionStorage
  createRouteRequested = output<void>();
  createMovementRestoreRequested = output<void>();
}
