import { createActionGroup, emptyProps } from '@ngrx/store';
import { createEverything, payload, SFMap } from '@simfront/common-libs';

import { environment } from '../../../environments/environment';
import { DataPath } from '../data-path/data-path.model';
import { Endpoint } from '../endpoint/endpoint.model';
import { FilterInfo } from '../endpoint/filter.model';
import { RootState } from '../index';

export interface NodeSupport {
  externalInputSupported: boolean;
  externalOutputSupported: boolean;
  multiInputBSEEnabled: boolean;
  multiOutputBSEEnabled: boolean;
  createInputFeedWithConnection: boolean;
  createOutputFeedWithConnection: boolean;
  internalInputSupported: boolean;
  tcpserverInputSupported: boolean;
}

export interface NodeLaunchInstructions{
  host:string;
  port:number;
  args:string[];
  identity:string;
  environment:string[];
}

export type NodeStatus = 'OFF' | 'ON' | 'ERROR';

export type NodeTransientStatus = 'NONE' | 'STARTING' | 'TIMED_OUT' | 'UNLICENSED' | 'TERMINATING';

export interface NodeLaunchable {
  hosts: string[];
  selectedHost: string;
  identifier: string;
  displayName: string;
  transientStatus: NodeTransientStatus;
  status: NodeStatus;
  transientStatusTime: number;
  statusTime: number;
}

// TODO: Remove statusTime and status when launchable is done.
export interface Node {
  name: string;
  creation: boolean;
  servers: string[];
  dataPaths: DataPath[];
  endpoints: string[];
  connections: string[];
  support?: NodeSupport;
  supportedFilters?: FilterInfo[];
  launchable: NodeLaunchable;
  serviceType: 'NONE' | 'ROUTING' | 'DATA_SRC';
  host: string;
}

export const NODE_ICONS: SFMap<string> = {
  ABACUS: 'abacus-icon',
  DIS: 'dis-icon',
  CLOCS: 'clocs-icon',
  TAK: 'tak-icon',
  SITAWARE: 'sita-icon'
};

export const {
  reducer: nodeReducer,
  facade: NodeFacadeBase,
  initialState
} = createEverything<RootState, Node, 'node', 'name', string>('node', n => n.name, `${environment.origin}/nodes`, state => state.node ?? initialState);

export const NodeActions = createActionGroup({
  source: 'node',
  events: {
    'Open Filter Dialog': payload<Endpoint>(),
    'Dismiss Filter Dialog': emptyProps(),
    'Notification': payload<Node>(),
    'Activate Node': payload<NodeLaunchInstructions>(),
    'Deactivate Node': payload<string>()
  }
});

export const isNode = (val: any): val is Node => val.name !== undefined
  && val.status !== undefined
  && val.endpoints !== undefined
  && val.connections !== undefined;
