export type ThreePartId
  <One extends string, Two extends string, Three extends string, Delimiter extends string>
  = `${One}${Delimiter}${Two}${Delimiter}${Three}`;

export type PartOne<U, D extends string> = U extends ThreePartId<infer One, string, string, D> ? One : never;
export type PartTwo<U, D extends string> = U extends ThreePartId<string, infer Two, string, D> ? Two : never;
export type PartThree<U, D extends string> = U extends ThreePartId<string, string, infer Three, D> ? Three : never;

export type UniqueId = ThreePartId<string, string, string, '__'>;

export const getServiceId = <U extends UniqueId>(uid: U) => uid.split('__')[0] as PartOne<U, '__'>;

export const getEntryPointName = <U extends UniqueId>(uid: U) => uid.split('__')[1] as PartTwo<U, '__'>;

export const getExternalId = <U extends UniqueId>(uid: U) => uid.split('__')[2] as PartThree<U, '__'>;
