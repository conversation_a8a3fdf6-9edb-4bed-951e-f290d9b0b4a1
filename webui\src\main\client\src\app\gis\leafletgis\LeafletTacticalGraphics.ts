import { LatLngExpression, LatLngTuple } from 'leaflet';
import { toRadians } from 'ol/math';
import { Graphics, Text, Container } from 'pixi.js';
import { SecWebRenderer } from '../../app.component';
import { Coordinate } from '../../cursit/models/cursit.model';
import {
  LocationSettable,
  MilSymbolSettable
} from '../interface/LocationSettable';
import { ShapeType } from '../interface/models/map-element.model';
import { LeafletMap } from './LeafletMap';

export interface MilSymDTOItemList {
  milSymDTOItemList: SentTacticalGraphics[];
}

export interface SentTacticalGraphics {
  id: string;
  name: string;
  description: string;
  symbolCode: string;
  controlPoints: string;
  pixelWidth: number;
  pixelHeight: number;
  bbox: string;
  modifiers: string;
}

export interface Line {
  line: LatLngTuple[];
  LineColor: string;
  lineWidth: string;
}

export interface Label {
  label: [number, number];
  text: string;
  angle: string;
}

export interface ReceivedTacticalGraphics {
  type: string;
  polygons: [];
  lines: Line[];
  labels: Label[];
}

export interface ProducedTacticalGraphics {
  type: string;
  features: ProducedFeature[];
  properties: ProducedProperties;
}

export interface ProducedFeature {
  type: string;
  geometry: ProducedGeometry;
  properties: ProducedFeatureProperties;
}

export interface ProducedProperties {
  id: string;
  name: string;
  description: string;
  symbolID: string;
  wasClipped: boolean;
}

export interface ProducedFeatureProperties {
  label: string;
  lineOpacity: number;
  strokeColor: string;
  strokeWeight: number;
  strokeWidth: number;
}

export type MultiLineCoordinates = [number, number][][];
export type LineCoordinates = [number, number][];
export type FeatureCoordinate =
  | Coordinate
  | LineCoordinates
  | MultiLineCoordinates;

export interface ProducedGeometry {
  type: string;
  coordinates: FeatureCoordinate;
  properties: ProducedGeometryProperties;
}

// export interface ProducedPoint {
//   coordinates: [number, number];
//   properties: ProducedGeometryProperties;
// }

// export interface ProducedMultilineProperties {
//   label
// }

export interface ProducedGeometryProperties {
  angle?: number;
  fontColor?: string;
  fontFamily?: string;
  fontSize?: number;
  fontWeight?:
    | 'normal'
    | 'bold'
    | 'bolder'
    | 'lighter'
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '700'
    | '800'
    | '900';
  label: string;
  labelAlign?: 'left' | 'center' | 'right' | 'justify';
  labelBaseline?: string;
  labelOutlineColor?: string;
  labelOutlineWidth?: number;
  labelXOffset?: number;
  labelYOffset?: number;
  pointRadius?: number;
  rotation?: number;
  lineOpacity?: number;
  strokeColor?: string;
  strokeWeight?: number;
  strokeWidth?: number;
}

export class LeafletTacticalGraphics
  extends Container
  implements LocationSettable, MilSymbolSettable
{
  _projectedLocations: LatLngTuple[];
  _locations: LatLngTuple[];
  _locationsString: string;
  _sentTacticalGraphics: SentTacticalGraphics;
  _receivedTacticalGraphics: ReceivedTacticalGraphics;
  type: ShapeType;
  _id: string;
  private graphicsElement: Graphics;

  constructor(
    id: string,
    private sidc: string,
    private map: LeafletMap,
    type: ShapeType,
    coordinates?: Coordinate[],
    private modifiers?: string | object
  ) {
    super();
    this._id = id;
    this.map = map;
    this.type = type;
    this.graphicsElement = new Graphics();
    this.addChild(this.graphicsElement);

    if (coordinates) {
      this.locations = coordinates;
      this.projectLocations(coordinates as LatLngTuple[]);
      this.redraw();
    }

    this.cullable = true;

    this.eventMode = 'dynamic';
    this.interactiveChildren = true;
    this.on('mouseenter', () => {
      this.map.getContainer().style.cursor = 'move';
    });

    this.on('mouseleave', () => {
      this.map.getContainer().style.cursor = 'pointer';
    });
  }

  setSymbolCode(symbolCode: string): void {
    this.sidc = symbolCode;
    this.redraw();
  }

  set id(id: string) {
    this._id = id;
  }

  get id(): string {
    return this._id;
  }

  processGeoJSON(geoJSON: string): {
    affiliationColor?: string;
    multiLines: LatLngTuple[][];
    polygons: LatLngTuple[][];
    points: {
      coordinates: LatLngTuple[];
      properties: ProducedGeometryProperties[];
    };
  } {
    const prjMultiLineList: LatLngTuple[][] = [];
    const prjPolygonList: LatLngTuple[][] = [];
    const prjPointsList: LatLngTuple[] = [];
    const pointProperties: ProducedGeometryProperties[] = [];
    const parsedTacticalGraphics = JSON.parse(
      geoJSON
    ) as ProducedTacticalGraphics;
    // console.log(parsedTacticalGraphics);
    parsedTacticalGraphics.features.forEach((feature) => {
      const geomType = feature.geometry.type;
      if (geomType === 'Point') {
        const coords = feature.geometry.coordinates as [number, number];
        prjPointsList.push(
          this.map.projectFromLatLonToLatLngExpression([coords[1], coords[0]])
        );
        pointProperties.push(feature.properties);
      } else if (geomType === 'MultiLineString') {
        const coords = feature.geometry.coordinates;
        coords.forEach((coordList) => {
          prjMultiLineList.push(this.projectLocations(coordList));
        });
      } else if (geomType === 'Polygon') {
        const coords = feature.geometry.coordinates;
        coords.forEach((coordList) => {
          prjPolygonList.push(this.projectLocations(coordList));
        });
      }
    });

    const affiliationColor =
      parsedTacticalGraphics.features[0]?.properties.strokeColor;

    return {
      affiliationColor: affiliationColor || '0x000000',
      multiLines: prjMultiLineList,
      polygons: prjPolygonList,
      points: { coordinates: prjPointsList, properties: pointProperties }
    };
  }

  redraw(): void {
    this.removeChildren();
    // Create a new Graphics element for drawing
    this.graphicsElement = new Graphics();
    this.addChild(this.graphicsElement);

    const rendererMP = SecWebRenderer;
    const pixelWidth = this.map.getSize().x;
    const pixelHeight = this.map.getSize().y;

    const controlPoints3 = this.convertLocationsToControlPointsString(
      this.locations
    );

    const bounds = this.map.getBounds();
    const bbox3 = `${bounds.getSouthWest().lng},${bounds.getSouthWest().lat},${bounds.getNorthEast().lng},${bounds.getNorthEast().lat}`;

    const json3 = rendererMP.RenderSymbol2D(
      'ID',
      'Name',
      'Description',
      this.sidc,
      controlPoints3,
      pixelWidth,
      pixelHeight,
      bbox3,
      this.modifiers ?? {},
      2
    );

    const locsList = this.processGeoJSON(json3);
    const multiLineList = locsList.multiLines;
    const polygonsList = locsList.polygons;
    const pointsProperties = locsList.points.properties;
    const pointsListCoordinates = locsList.points.coordinates;
    const { affiliationColor } = locsList;
    pointsListCoordinates.forEach((coord, index) => {
      const pointProperty = pointsProperties[index];
      const text: Text = new Text({
        text: pointProperty.label,
        style: {
          fontFamily: pointProperty.fontFamily,
          fontSize: pointProperty.fontSize,
          fill: pointProperty.fontColor,
          fontWeight: pointProperty.fontWeight,
          align: 'center'
        }
      });
      const [x, y] = coord;
      text.x = x;
      text.y = y;
      switch (pointProperty.labelAlign) {
        case 'center':
          // console.log(`${pointProperty.label} centre`);
          text.anchor.set(0.5, 0.5);
          break;
        case 'left':
          text.anchor.set(0, 0.5);
          // console.log(`${pointProperty.label} left`);
          break;
        case 'right':
          text.anchor.set(1, 0.5);
          //  console.log(`${pointProperty.label} right`);
          break;
        default:
          text.anchor.set(1, 1);
      }

      text.rotation = toRadians(pointProperty.angle);
      text.scale.set(1 / this.map.scaleForProjection);
      this.addChild(text);
      // console.log(`${coord} ${pointsProperties[index]}`);
    });

    this.graphicsElement.clear();
    multiLineList.forEach((locs) => {
      // Create a regular Graphics object instead of SubGraph
      const graphics = new Graphics();

      // Set the line style with separate parameters for compatibility
      const width = 3 / this.map.scaleForProjection;
      const color = affiliationColor;
      const alpha = 1;

      // Use the newer method for PixiJS v8
      graphics.setStrokeStyle({ width, color, alpha });

      graphics.eventMode = 'dynamic';
      this.addChild(graphics);

      locs.forEach((loc, innerIndex) => {
        if (innerIndex === 0) {
          graphics.moveTo(loc[0], loc[1]);
        } else {
          graphics.lineTo(loc[0], loc[1]);
        }
      });
    });

    polygonsList.forEach((locs) => {
      this.graphicsElement.fill(affiliationColor);
      locs.forEach((loc, index) => {
        if (index === 0) {
          this.graphicsElement.moveTo(loc[0], loc[1]);
        } else {
          this.graphicsElement.lineTo(loc[0], loc[1]);
        }
      });
    });
  }

  get locations(): LatLngTuple[] {
    return this._locations;
  }

  set locations(locations: LatLngTuple[]) {
    this._locations = locations;
  }

  get locationsAsCoordinate(): Coordinate[] {
    return this._locations.map((tuple) => [tuple[0], tuple[1]]);
  }

  getLocation(index: number): LatLngTuple {
    return this._locations[index];
  }

  setLocation(locations: LatLngTuple[]): void {
    this.projectLocations(locations);
    this._locations = locations;
    this.redraw();
  }

  addLocation(location: LatLngTuple, index?: number): void {
    if (index) {
      this._locations.splice(index, 0, location);
    } else {
      this._locations.push(location);
    }
  }

  removeLocation(index: number): void {
    this._locations.splice(index, 1);
  }

  set projectedLocations(locations: LatLngTuple[]) {
    this._projectedLocations = locations;
  }

  get projectedLocations(): LatLngTuple[] {
    return this._projectedLocations;
  }

  projectLocations(locations: LatLngTuple[]): LatLngTuple[] {
    this._projectedLocations = [];
    locations.forEach((latLonTuple) => {
      this._projectedLocations.push(
        this.map.projectFromLatLonToLatLngExpression([
          latLonTuple[1],
          latLonTuple[0]
        ])
      );
    });
    return this._projectedLocations;
  }

  projectAllReceivedLocations(
    receivedTacticalGraphics: ReceivedTacticalGraphics
  ): LatLngExpression[] {
    const { lines } = receivedTacticalGraphics;
    this._projectedLocations = [];
    lines.forEach((line) => {
      this.projectLocations(line.line);
    });

    return this._projectedLocations;
  }

  addProjectedLocation(location: LatLngTuple, index?: number): void {
    if (index) {
      this._projectedLocations.splice(index, 0, location);
    } else {
      this._projectedLocations.push(location);
    }
  }

  addProjectedLocationFromLatLon(location: LatLngTuple, index?: number): void {
    const generatedLocation =
      this.map.projectFromLatLonToLatLngExpression(location);
    if (index) {
      this._projectedLocations.splice(index, 0, generatedLocation);
    } else {
      this._projectedLocations.push(generatedLocation);
    }
  }

  removeProjectedLocation(index: number): void {
    this._projectedLocations.splice(index, 1);
  }

  convertLocationsToControlPointsString(locations: LatLngTuple[]): string {
    this._locationsString = locations
      .map((tuple) => {
        const reverseTuple = [tuple[1], tuple[0]];
        return reverseTuple.join(',');
      })
      .join(' ');
    return this._locationsString;
  }

  convertLocationsStringToLocations(): string {
    throw new Error('Method not implemented.', { cause: this });
    return '';
  }

  // TODO: Implement this method
  convertControlPointsStringFromTacticalGraphicsToLocations(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    tacticalGraphics: SentTacticalGraphics
  ): void {
    throw new Error('Method not implemented.', { cause: this });
  }

  convertBoundingBoxToBBOXString(): string {
    const bounds = this.map.getBounds();
    return `${bounds.getSouthWest().lat},${bounds.getSouthWest().lng},${bounds.getNorthEast().lat}${bounds.getNorthEast().lng}`;
  }

  set sentTacticalGraphics(tacticalGraphics: SentTacticalGraphics) {
    this._sentTacticalGraphics = tacticalGraphics;
  }

  get sentTacticalGraphics(): SentTacticalGraphics {
    return this._sentTacticalGraphics;
  }

  convertToTacticalGraphics(): SentTacticalGraphics {
    const symbolCode = this.sidc;
    const controlPoints = this.convertLocationsToControlPointsString(
      this._locations
    );
    const pixelWidth = this.map.getSize().x;
    const pixelHeight = this.map.getSize().y;
    const bbox = this.convertBoundingBoxToBBOXString();
    this.sentTacticalGraphics = {
      id: '',
      name: '',
      description: '',
      symbolCode,
      controlPoints,
      pixelWidth,
      pixelHeight,
      bbox,
      modifiers: ''
    };
    return this.sentTacticalGraphics;
  }

  // Graphics methods
  clear(): this {
    this.graphicsElement.clear();
    return this;
  }

  moveTo(x: number, y: number): this {
    this.graphicsElement.moveTo(x, y);
    return this;
  }

  lineTo(x: number, y: number): this {
    this.graphicsElement.lineTo(x, y);
    return this;
  }

  fill(color?: number | string): this {
    this.graphicsElement.fill(color);
    return this;
  }
}
