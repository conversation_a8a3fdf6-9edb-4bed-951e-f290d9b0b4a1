<h1 mat-dialog-title>Create Route</h1>
<mat-dialog-content
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
>
  <div class="route-creation-section">
    @if (!creatingRoute()) {
      <button
        mat-mini-fab
        color="primary"
        (click)="startRouteCreation()"
        matTooltip="Continue Creation"
      >
        <mat-icon>play_arrow</mat-icon>
      </button>
    } @else {
      <button
        mat-mini-fab
        color="primary"
        (click)="endRouteCreation()"
        matTooltip="Pause Creation"
      >
        <mat-icon>pause</mat-icon>
      </button>
    }

    <button
      mat-mini-fab
      color="warn"
      (click)="clearRoutePoints()"
      matTooltip="Clear All Points"
      [disabled]="routePoints().length === 0"
    >
      <mat-icon>delete</mat-icon>
    </button>
  </div>

  <sf-core-table
    [columns]="columns"
    [trackBy]="trackBy"
    [data]="routePoints()"
    identifier="route-points"
    [tableRowActions]="tableActions"
  >
  </sf-core-table>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button
    mat-button
    type="submit"
    [disabled]="routePoints().length === 0"
    (click)="createRoute()"
  >
    Create
  </button>
  <button mat-button mat-dialog-close (click)="cancelRouteCreation()">
    Cancel
  </button>
</mat-dialog-actions>
