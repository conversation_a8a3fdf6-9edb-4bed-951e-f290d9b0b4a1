import { Injectable } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';

const REL_SVG_DIR = 'assets/images/svg';

@Injectable({ providedIn: 'root' })
export class IconsService {
  constructor(private registry: MatIconRegistry, private domSanitizer: <PERSON><PERSON>anitizer) {}

  init(): void {
    this.registry.addSvgIconSet(this.domSanitizer.bypassSecurityTrustResourceUrl(`${REL_SVG_DIR}/bundled.svg`));
    this.registry.addSvgIcon("node-protocol", this.domSanitizer.bypassSecurityTrustResourceUrl(`${REL_SVG_DIR}/node-protocol-icon.svg`));
    this.registry.addSvgIcon("nexus-brand", this.domSanitizer.bypassSecurityTrustResourceUrl(`${REL_SVG_DIR}/nexus-brand.svg`));
  }
}
