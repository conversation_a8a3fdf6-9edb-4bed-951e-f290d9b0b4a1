import { NgIf } from '@angular/common';
import { Component, EventEmitter, Optional, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { PopoutDialogService } from './popout-dialog.service';
import { IPopoutWindowModal } from './popout-window.model';

@Component({
    selector: 'vcci-popout-dialog',
    template: `
      <div #innerWrapper class="container" [class.mat-mdc-dialog-container]="isPopoutWindow">
        <div class="title">
          <ng-content select="[mat-dialog-title]"/>
          <button *ngIf="!isPopoutWindow" mat-icon-button (click)="open()">
            <mat-icon>open_in_new</mat-icon>
          </button>
          <button mat-icon-button (click)="close()">
            <mat-icon>close</mat-icon>
          </button>
        </div>
        <ng-content/>
        <ng-content select="[mat-dialog-content]"/>
        <ng-content select="[mat-dialog-actions]"/>
      </div>
    `,
    styles: [`
        .container {
            background-color: var(--mdc-dialog-container-color, white);
            color: whitesmoke !important;
            height: 100%;
        }

        .title {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 5px;
        }

        .title > button:last-child {
            margin-right: 10px;
        }

        .title > ::ng-deep *:first-child,
        .title > *:first-child {
            flex: 1;
        }

        .container:not(.mat-mdc-dialog-container) > .title > ::ng-deep *:first-child {
            padding-bottom: 0;
        }
    `],
    imports: [
        MatButtonModule,
        MatDialogModule,
        MatIconModule,
        NgIf
    ]
})
export class PopoutDialogComponent<T, C extends IPopoutWindowModal> {
  isPopoutWindow = false;

  @Output() onClose = new EventEmitter<any>();

  constructor(@Optional() private popoutDialogService?: PopoutDialogService<T, C>) {
    this.isPopoutWindow = !this.popoutDialogService;
  }

  open(): void {
    if (this.popoutDialogService) {
      this.popoutDialogService.popout();
    }
  }

  close(): void {
    if (this.popoutDialogService) {
      this.popoutDialogService.closeDialog();
    } else {
      this.onClose.emit();
    }
  }
}
