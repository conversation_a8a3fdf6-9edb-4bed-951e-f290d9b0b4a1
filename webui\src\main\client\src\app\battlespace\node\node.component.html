<ng-template #hostDropdown let-item>
  <div class="host-selection">
    <mat-select
      [disabled]="!distributedMode || getStatus(item) === 'ON' || getStatus(item) === 'STARTING' || item.launchable.hosts.length === 0"
      [value]="getHost(item)" (selectionChange)="onHostChange($event, item)">
      <mat-option *ngFor="let host of item.launchable.hosts" [value]="host">{{ host }}</mat-option>
    </mat-select>
  </div>
</ng-template>

<ng-template #statusIndicator let-item>
  <div class="status-indicator system-text" [class]="getStatus(item).toLocaleLowerCase()">
    @if (getStatus(item) === 'ON') {
    <mat-icon>check</mat-icon>
    } @else if (getStatus(item) === 'OFF') {
    <mat-icon>close</mat-icon>
    } @else if (getStatus(item) === 'ERROR') {
    <mat-icon>error_outline</mat-icon>
    } @else if (getStatus(item) === 'TIMED_OUT') {
    <mat-icon>delete_history</mat-icon>
    } @else if (getStatus(item) === 'UNLICENSED') {
    <mat-icon>unlicense</mat-icon>
    } @else if (getStatus(item) === 'STARTING' || getStatus(item) === 'TERMINATING') {
    <mat-spinner diameter="20" />
    }
    {{getStatus(item)}}
  </div>
</ng-template>

<ng-template #startStopButton let-item>
  @if (isNodeRunning(item)) {
  <button [disabled]="!item.launchable.identifier" class="stop-button" mat-icon-button (click)="stopNodes([item])"
    [matTooltip]="'Stop ' + (item.launchable.identifier || item.name)">
    <mat-icon>stop</mat-icon>
  </button>
  } @else {
  <button [disabled]="!item.launchable.identifier" class="start-button" mat-icon-button (click)="startNodes([item])"
    [matTooltip]="'Start ' + (item.launchable.identifier || item.name)">
    <mat-icon>play_arrow</mat-icon>
  </button>
  }
</ng-template>

<ng-template #nodeName let-item>
  <div class="component-name">
    <mat-icon [svgIcon]="NODE_ICONS[item.launchable.identifier]" />
    <span>{{ item | table : getNodeName }}</span>
  </div>
</ng-template>

<nexus-page pageTitle="SERVICE CONFIGURATION" svgIcon="node-protocol">
  <ng-template #headerSlot>
    <button mat-stroked-button class="distributed-mode-button" [class.active]="distributedMode"
      [matTooltip]="(distributedMode ? 'Disable' : 'Enable') + ' Ability to Run Services in Distributed Mode'"
      (click)="toggleDistributedMode()">
      <mat-icon svgIcon="node" />
      <span>Distributed Mode: {{ distributedMode ? 'ON' : 'OFF' }}</span>
    </button>
  </ng-template>


  <div class="node-table-container">
    <sf-core-table (tableSelectionChange)="onNodeSelect($event)" class="node-table" identifier="components"
      [columns]="columns" [data]="this.nodes()" [selectionMode]="selectionMode" [trackBy]="trackBy"
      [tableRowActions]="tableActions" [stickyHeader]="true" includeSort="true" />
    <div class="table-footer">
      <div class="table-actions">
        <button mat-button [disabled]="!selectedNodes.length" class="start-button" (click)="startNodes(selectedNodes)"
          matTooltip="Start Selected Services">
          <mat-icon>play_arrow</mat-icon>
          Start
        </button>
        <button mat-button [disabled]="!selectedNodes.length" class="stop-button" (click)="stopNodes(selectedNodes)"
          matTooltip="Stop Selected Services">
          <mat-icon>stop</mat-icon>
          Stop
        </button>
      </div>
      <div class="system-stats">
        <div class="stat-item">
          <span class="stat-label">Total:</span>
          <span class="stat-value">{{total()}}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Ready:</span>
          <span class="stat-value" [ngClass]="{'pending': busy() > 0}">{{ready()}}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Running:</span>
          <span class="stat-value" [ngClass]="{'success': running() > 0}">{{running()}}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Failed:</span>
          <span class="stat-value" [ngClass]="{'error': failed() > 0}">{{failed()}}</span>
        </div>
      </div>
    </div>
  </div>

</nexus-page>
