import { createFeature, createReducer, on } from '@ngrx/store';
import { updateTacticalGraphicsAction } from '../actions/leaflet.actions';
import { MilSymDTOItemList } from '../LeafletTacticalGraphics';

export interface TacticalGraphicsState {
  tacticalGraphicsList: MilSymDTOItemList;
}

export const initialState:
TacticalGraphicsState = { tacticalGraphicsList: { milSymDTOItemList: [] } };

export const updateTacticalGraphicsFeature = createFeature({
  name: 'tacticalGraphics',
  reducer: createReducer(
    initialState,
    on(
      updateTacticalGraphicsAction,
      (state, action) => ({ ...state, tacticalGraphics: action.payload })
    )
  )
});

export const {
  name,
  reducer,
  selectTacticalGraphicsState,
  selectTacticalGraphicsList
} = updateTacticalGraphicsFeature;
