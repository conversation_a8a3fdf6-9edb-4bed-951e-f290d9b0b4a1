import { MGRSFormat } from '@luciad/ria-milsym/text/mgrs/MGRSFormat';
import { MGRSFormatPrecision } from '@luciad/ria-milsym/text/mgrs/MGRSFormatPrecision';
import { MGRSFormatType } from '@luciad/ria-milsym/text/mgrs/MGRSFormatType';
import { LonLatPointFormat } from '@luciad/ria/shape/format/LonLatPointFormat';
import { createPoint } from '@luciad/ria/shape/ShapeFactory';
import { Map } from '@luciad/ria/view/Map';
import { mapActions } from '../interface/actions/map.actions';
import { MouseCoordinateLocator, MouseCoordinateType, MousePos } from '../interface/MouseCoordinateLocator';
import { getPointFromViewPoint } from './luciad.utils';

export class LuciadMouseCoordinateLocator extends MouseCoordinateLocator {
  latlonFormatter = new LonLatPointFormat({ pattern: 'lat(DMSa),lon(DMSa)' });

  mgrsFormatter = LuciadMouseCoordinateLocator.getMGRSPointFormat();

  protected override gisLocateMouseCoordinate(
    coordinateType: MouseCoordinateType,
    mousePos: MousePos
  ): void {
    const mouseEventPoint = createPoint(
      null,
      [mousePos[0], mousePos[1]]
    );
    const point = getPointFromViewPoint(this.mapDisplay.map as Map, mouseEventPoint);
    let mouseCoordinate = '';
    if (coordinateType === 'LatLon') {
      mouseCoordinate = this.latlonFormatter.format(point);
    } else if (coordinateType === 'MGRS') {
      mouseCoordinate = this.mgrsFormatter.format(point);
    }
    this.mapDisplay.store.dispatch(mapActions.mouseMove(mouseCoordinate));
  }

  static getMGRSPointFormat(p?: MGRSFormatPrecision): MGRSFormat {
    return new MGRSFormat({
      formatType: MGRSFormatType.MGRS,
      precision: p || MGRSFormatPrecision.PRECISION_1M
    });
  }
}
