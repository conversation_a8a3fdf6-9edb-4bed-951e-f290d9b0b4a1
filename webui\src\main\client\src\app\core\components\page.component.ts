import { Component, ContentChild, Input, TemplateRef } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { FlexBlockDirective } from 'src/app/shared/directives/flex-block.directive';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'nexus-page',
  imports: [MatIconModule, FlexBlockDirective, NgTemplateOutlet],
  template: `
    <div class="title custom-font">
      @if (icon) {
        <mat-icon inline>{{ icon }}</mat-icon>
      } @else if (svgIcon) {
        <mat-icon inline [svgIcon]="svgIcon" />
      }
      <label>{{ pageTitle }}</label>
      <flex-block />
      <ng-container *ngTemplateOutlet="headerSlot" />
    </div>
    <div class="page-content">
      <ng-content />
    </div>
  `,
  styleUrl: 'page.component.scss'
})
export class PageComponent {
  @Input({ required: true }) pageTitle: string;
  @ContentChild('headerSlot') headerSlot?: TemplateRef<any>;

  @Input() icon: string | undefined;
  @Input() svgIcon: string | undefined;
}
