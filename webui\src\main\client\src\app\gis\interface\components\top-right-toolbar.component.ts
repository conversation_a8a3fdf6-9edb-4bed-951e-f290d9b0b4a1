/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/1/2024, 9:25 AM
 */
import { Component, inject, Input } from '@angular/core';
import { GisService } from '../service/gis.service';
import { cursitActions } from '../../../cursit/actions/cursit.actions';
import { environment } from '../../../../environments/environment';
import {selectLayerManagerVisibility} from "../../../cursit/reducers/cursit.reducer";

@Component({
  selector: 'vcci-top-right-toolbar',
  templateUrl: 'top-right-toolbar.component.html',
  styleUrls: ['top-right-toolbar.component.scss'],
  standalone: false
})
export class TopRightToolbarComponent {
  filterActive: boolean = false;
  mapOpacitySliderValue = 1.0;
  elementSizeValue = 50;
  layerManagerVisibility$ = this.gisService.store.select(
    selectLayerManagerVisibility
  );
  readonly env = environment;

  constructor(private gisService: GisService) {}

  filterAction(): void {
    this.gisService.store.dispatch(cursitActions.openFilterDialog());
  }

  editMapAction(): void {}

  mapServersAction(): void {}

  resetMapOpacity(): void {
    this.mapOpacitySliderValue = 1.0;
    this.gisService.setBackgroundMapOpacity(1.0);
  }

  setBackgroundMapOpacity(opacity: number): void {
    this.gisService.setBackgroundMapOpacity(opacity);
  }

  showHideLayerList(): void {
    this.gisService.mapDisplay.toggleLayerManagerVisibility();
  }

  setElementSize(iconSize: number): void {
    this.gisService.mapDisplay.setMilitaryElementIconSize(iconSize);
  }

  centerSelected(): void {
    this.gisService.mapDisplay.centerMapOnSelectedElement();
  }

  elementActions(): void {}

  showDetails(): void {}
}
