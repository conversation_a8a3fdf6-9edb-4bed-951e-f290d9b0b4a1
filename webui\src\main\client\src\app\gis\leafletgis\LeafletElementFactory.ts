import { SFMap } from '@simfront/common-libs';

import { Layer } from 'leaflet';

import ms from 'milsymbol';
import { Texture } from 'pixi.js';
import { Coordinate } from '../../cursit/models/cursit.model';

import {
  MapElementFactory,
  MilitaryElement,
  MilitaryElementCorridor,
  MilitaryElementFanArea
} from '../interface/ElementFactory';
import { ShapeType } from '../interface/models/map-element.model';

import { LeafletEntity } from './LeafletEntity';
import { LeafletMapDisplay } from './LeafletMapDisplay';
import { LeafletMilitaryMapElement } from './LeafletMilitaryMapElement';
import { LeafletPolygonArea } from './LeafletPolygonArea';
import { LeafletTacticalGraphicsService } from './service/LeafletTacticalGraphicsService';
import { LeafletFanArea } from './LeafletFanArea';
import { LeafletEllipse } from './LeafletEllipse';
import { LeafletCorridor } from './LeafletCorridor';

export class LeafletElementFactory extends MapElementFactory<Layer> {
  public ms2525IconTextureHash: SFMap<Texture> = {};
  service: LeafletTacticalGraphicsService;

  constructor(public mapDisplay: LeafletMapDisplay) {
    super();
    this.service = this.mapDisplay.service;
  }

  //todo nate, check on adding cat code to other leaflet entities here too
  createMilitaryElement(
    element: MilitaryElement
  ): LeafletMilitaryMapElement | null {
    const { sidc, id, label, type, catCode, coordinates, reference } = element;

    const leafletMilitaryMapElement = new LeafletMilitaryMapElement(id);
    leafletMilitaryMapElement.sidc = sidc;
    leafletMilitaryMapElement.mapLayer = null;

    switch (type) {
      case ShapeType.POINT: {
        let symbolTexture: Texture;
        if (this.ms2525IconTextureHash[sidc]) {
          symbolTexture = this.ms2525IconTextureHash[sidc];
        } else {
          const symbol = new ms.Symbol(sidc, { size: 25 }).asCanvas();
          symbolTexture = Texture.from(symbol);
          this.ms2525IconTextureHash[sidc] = symbolTexture;
        }
        const entity: LeafletEntity = new LeafletEntity(
          id,
          sidc,
          symbolTexture,
          this.mapDisplay,
          this.ms2525IconTextureHash,
          catCode,
          label
        );
        const coordinate = coordinates as Coordinate;
        entity.setLocation(coordinate);
        leafletMilitaryMapElement.gisObject = entity;
        return leafletMilitaryMapElement;
      }
      case ShapeType.POLYLINE:
      case ShapeType.POLYGON: {
        const coords = coordinates as Coordinate[];

        const polygon: LeafletPolygonArea = new LeafletPolygonArea(
          id,
          this.mapDisplay,
          type,
          catCode,
          coords,
          label
        );
        leafletMilitaryMapElement.gisObject = polygon;
        return leafletMilitaryMapElement;
      }
      case ShapeType.ARC_BAND: {
        const coords = coordinates as Coordinate[];
        const { fanAreaOptions } = element as MilitaryElementFanArea;

        const arc: LeafletFanArea = new LeafletFanArea(
          id,
          this.mapDisplay,
          type,
          catCode,
          coords,
          label,
          fanAreaOptions
        );
        leafletMilitaryMapElement.gisObject = arc;
        return leafletMilitaryMapElement;
      }
      case ShapeType.ELLIPSE: {
        const coords = coordinates as Coordinate[];
        const ellipse: LeafletEllipse = new LeafletEllipse(
          id,
          this.mapDisplay,
          type,
          catCode,
          coords,
          label
        );
        leafletMilitaryMapElement.gisObject = ellipse;
        return leafletMilitaryMapElement;
      }
      case ShapeType.GEO_BUFFER: {
        const coords = coordinates as Coordinate[];
        const { width } = element as MilitaryElementCorridor;

        const corridor: LeafletCorridor = new LeafletCorridor(
          id,
          this.mapDisplay,
          type,
          catCode,
          coords,
          label,
          { width }
        );
        leafletMilitaryMapElement.gisObject = corridor;
        return leafletMilitaryMapElement;
      }
      default:
        return null;
    }
  }
}
