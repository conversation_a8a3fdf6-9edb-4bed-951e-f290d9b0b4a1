import { EventSourceService } from './event-source.service';
import { concatMap, exhaustMap, merge, Observable, of, Subject, Subscriber, takeUntil, tap } from 'rxjs';
import { Action, createAction, Store } from '@ngrx/store';
import { payload } from '@simfront/common-libs';
import { inject } from '@angular/core';
import { Actions, createEffect, EffectSources, ofType } from '@ngrx/effects';

export abstract class EventSourceStoreService<T> extends EventSourceService<T> {

  readonly actions = {
    open: createAction(`[EventSource/${this.model}] Open`),
    onOpen: createAction(`[EventSource/${this.model}] On Open`),
    onMessage: createAction(`[EventSource/${this.model}] On Message`, payload<T>()),
    onError: createAction( `[EventSource/${this.model}] On Error`, payload<any>()),
    close: createAction(`[EventSource/${this.model}] Close`),
  };
  readonly store = inject(Store);
  readonly actions$ = inject(Actions);
  readonly effectSources = inject(EffectSources);

  override onOpen = () => this.subscriber.next(this.actions.onOpen());
  override onError = (error: Event) =>
    this.subscriber.next(this.actions.onError(error.eventPhase === 0 ? 'Failed to open event stream.' : 'An error occurred.'));
  override onMessage = (me: MessageEvent<T>) => this.subscriber.next(this.actions.onMessage(me.data));

  private subscriber: Subscriber<Action> | undefined = undefined;
  private stop$ = new Subject<void>();

  protected constructor(private _url: string, private model: string) {
    super(_url);
    this.effectSources.addEffects(this);
  }

  override open(): void {
    this.store.dispatch(this.actions.open());
  }

  close(): void {
    this.store.dispatch(this.actions.close());
  }

  private connect(): Observable<Action> {
    return new Observable<Action>(subscriber => {
      this.subscriber = subscriber;
      this._connect();

      return () => {
        this.subscriber = null;
      }
    });
  }

  open$ = createEffect(() =>
    this.actions$.pipe(
      ofType(this.actions.open),
      exhaustMap(() => this.connect().pipe(
        takeUntil(this.stop$)
      )),
      concatMap(action => of(action))
    )
  );

  close$ = createEffect(() =>
    merge(
      this.actions$.pipe(ofType(this.actions.close))
    ).pipe(
      tap(() => this.stop())
    ), { dispatch: false }
  );

  override stop() {
    super.stop();
    this.stop$.next();
  }

}
