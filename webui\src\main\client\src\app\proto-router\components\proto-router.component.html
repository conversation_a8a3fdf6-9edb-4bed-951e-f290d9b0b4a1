<vcci-proto-router-header style="margin-left: 20px" [nodes]="inputNodes" type="inputs" (onFilterNodes)="onFilterChange('inputs', $event)"/>
<vcci-proto-router-header type="router"/>
<vcci-proto-router-header [nodes]="outputNodes" type="outputs" (onFilterNodes)="onFilterChange('outputs', $event)"/>

<mat-divider style="grid-column: 1/-1"/>

<div class="endpoint-container border-right">
  <vcci-proto-router-endpoint
      #inputs
      *ngFor="let endpoint of filteredInputs"
      [endpoint]="endpoint"
      [selected]="selectedEndpointIds|array:'includes': getEndpointId(endpoint)"
      (onEndpointClicked)="handleEndpointClicked($event)"
  />
</div>

<div class="proto-router-svg-container">
  <vcci-proto-router-svg
    [routeItems]="routeItems"
    [selected]="selectedRoutes" />
</div>


<div class="endpoint-container border-left">
  <vcci-proto-router-endpoint
      #outputs
      *ngFor="let endpoint of filteredOutputs"
      [endpoint]="endpoint"
      [selected]="selectedEndpointIds|array:'includes': getEndpointId(endpoint)"
      (onEndpointClicked)="handleEndpointClicked($event)"
  />
</div>






