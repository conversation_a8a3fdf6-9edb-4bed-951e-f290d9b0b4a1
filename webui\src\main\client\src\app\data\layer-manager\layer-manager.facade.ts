import { Injectable } from '@angular/core';
import {
  LayerFacadeBase, LayerInfo,
  LayerManagerActions,
  LayerNode,
  LayerVisibility
} from './layer-manager.model';
import { concatMap, EMPTY, map, of, switchMap, tap, withLatestFrom } from 'rxjs';
import { createEffect, ofType } from '@ngrx/effects';
import { GisService } from '../../gis/interface/service/gis.service';

// function getParentVisibility(
//   node: LayerInfo,
//   updateSubParent: boolean
// ): LayerVisibility {
//   if (!node.children || node.children.length === 0) {
//     return node.visibility;
//   }
//
//   let allVisible = true;
//   let allHidden = true;
//
//   for (const child of node.children) {
//     const childVisibility = getParentVisibility(child, updateSubParent);
//     if (updateSubParent) {
//       child.visibility = childVisibility;
//     }
//     if (childVisibility === LayerVisibility.PARTIAL) {
//       return LayerVisibility.PARTIAL;
//     }
//     if (childVisibility === LayerVisibility.HIDDEN) {
//       allVisible = false;
//     } else {
//       allHidden = false;
//     }
//   }
//
//   if (allVisible) {
//     return LayerVisibility.VISIBLE;
//   }
//   if (allHidden) {
//     return LayerVisibility.HIDDEN;
//   }
//   return LayerVisibility.PARTIAL;
// }
//
// function updateTreeParentVisibility(
//   layerTree: LayerInfo[],
//   updateSubParent: boolean
// ): void {
//   layerTree.forEach((node) => {
//     node.visibility = getParentVisibility(node, updateSubParent);
//   });
// }
//
// function flattenTree(nodes: LayerInfo[]): LayerInfo[] {
//   const result: LayerInfo[] = [];
//
//   function traverse(nodeList: LayerInfo[]) {
//     for (const node of nodeList) {
//       result.push(node);
//       if (node.children) {
//         traverse(node.children);
//       }
//     }
//   }
//
//   traverse(nodes);
//   return result;
// }
//
// function filterObject(obj: object, excludeKeys: string[]): object {
//   const result = {};
//
//   for (const [key, value] of Object.entries(obj)) {
//     if (!excludeKeys.includes(key)) {
//       result[key] = value;
//     }
//   }
//
//   return result;
// }
//
// function addLayerToTree(
//   layerTree: LayerInfo[],
//   layerToInsert: LayerInfo,
//   parentId: string
// ): LayerInfo[] {
//   const clone = structuredClone(layerTree);
//   return TreeFunctions.insert(
//     clone,
//     (lyrInfo) => lyrInfo.layerId === parentId,
//     layerToInsert,
//     TreeFunctions.childrenDefault,
//     TreeFunctions.addChildDefault
//   );
// }

@Injectable({providedIn: 'root'})
export class LayerManagerFacade extends LayerFacadeBase {

  constructor(private gisService: GisService) {
    super();
  }

  readAllReceived$ = createEffect(() =>
    this.actions$.pipe(
      ofType(this.actions.readAllReceived),
      tap((action) => {
        this.gisService.addToLayerQueue([...action.payload]);
      })
    ), {dispatch: false}
  );

  tree$ = this.entities$.pipe(
    map((entities) => {
      const buildTree = (id: string): LayerNode => {
        const entity = entities[id];
        if (!entity) {
          return null;
        }

        const children = Object.keys(entities)
          .filter((key) => entities[key].parentId === id)
          .map((childId) => buildTree(childId))
          .filter(Boolean);

        return {
          ...entity,
          layers: children
        };
      };

      const roots = Object.keys(entities)
        .filter((id) => entities[id].parentId === '-11')
        .map((rootId) => buildTree(rootId))
        .filter(Boolean);

      return roots;
    })
  );

  // private updateLayer = createEffect(() =>
  //     this.actions$.pipe(
  //       ofType(LayerManagerActions.updateLayer),
  //       withLatestFrom(this.entities$),
  //       switchMap((actionAndEntities) => {
  //         const action = actionAndEntities[0];
  //         const layerInfo = action.payload;
  //         const layerInfoNew: LayerInfo = {
  //           ...layerInfo,
  //           layerId: layerInfo.layerId,
  //           parentId: layerInfo.parentId === -1 ? -999 : layerInfo.parentId
  //         };
  //
  //         this.updateOneReceived(layerInfoNew);
  //         return of(this.actions.updateOneReceived(layerInfoNew));
  //
  //       })
  //     ),
  //   {dispatch: false}
  // );


  private addLayer$ = createEffect(() =>
      this.actions$.pipe(
        ofType(LayerManagerActions.addLayer),
        withLatestFrom(this.entities$),
        switchMap((actionAndEntities) => {
          const action = actionAndEntities[0];
          const entities = actionAndEntities[1];
          const layerInfo = action.payload;

          const layerInfoNew: LayerInfo = {
            ...layerInfo,
            layerId: layerInfo.layerId.toString(),
            parentId: (layerInfo.parentId as any) === -1 ? '-999' : layerInfo.parentId.toString()
          };

          // Check if the layer already exists in the entities
          if ((layerInfo.parentId as any) === -1 && layerInfoNew.parentId === '-999') {
            const dynamicLayersRoot = entities[layerInfoNew.parentId];
            if (dynamicLayersRoot) {
              const updatedLayerInfo: LayerInfo = {
                ...dynamicLayersRoot,
                children: [...dynamicLayersRoot.children, layerInfoNew.layerId]
              };
              this.updateOneReceived(updatedLayerInfo);
            }
            this.updateOneReceived(layerInfoNew);
            return of(this.actions.updateOneReceived(layerInfoNew));
          } else if (entities[layerInfo.layerId]) {
            if (entities[layerInfoNew.parentId]) {
              const parentLayer = entities[layerInfoNew.parentId];

              const parentLayerInfoNew: LayerInfo = {
                ...parentLayer,
                layerId: parentLayer.layerId.toString(),
                parentId: parentLayer.parentId.toString() === '-1' ? '-999' : parentLayer.parentId.toString()
              };

              const parentLayerCreated =
                this.gisService.mapDisplay.layerHandler.createParentLayer(
                  parentLayerInfoNew.layerName,
                  parentLayerInfoNew.layerId,
                  parentLayerInfoNew.parentId,
                  false
                );

              this.gisService.layerHandler.addToLayerCache(
                parentLayerCreated.parentLayer.layerId,
                parentLayerCreated.parentLayer
              );

              const updatedParentLayer: LayerInfo = {
                ...parentLayerInfoNew,
                children: [...parentLayerInfoNew.children, layerInfoNew.layerId]
              };
              this.updateOneReceived(updatedParentLayer);

              const dynamicLayersRoot = entities[parentLayerInfoNew.parentId];
              if (dynamicLayersRoot) {
                const updatedLayerInfo: LayerInfo = {
                  ...dynamicLayersRoot,
                  children: [...dynamicLayersRoot.children, parentLayerInfoNew.layerId]
                };
                this.updateOneReceived(updatedLayerInfo);
              }
            }
            this.updateOneReceived(layerInfoNew);
            return of(this.actions.updateOneReceived(layerInfoNew));
          } else {
            const parentOfTheLayer: LayerInfo = entities[layerInfoNew.parentId];
            if (parentOfTheLayer) {
              const updatedParentLayer: LayerInfo = {
                ...parentOfTheLayer,
                children: [...parentOfTheLayer.children, layerInfoNew.layerId.toString()]
              };

              this.updateOneReceived(updatedParentLayer);
            }
            this.createOneReceived(layerInfoNew);
            return of(this.actions.createOneReceived(layerInfoNew));
          }
        })
      ),
    {dispatch: false}
  );


  private setLayerVisibility$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LayerManagerActions.setLayerVisibility),
      map(({payload}) => payload),
      withLatestFrom(this.entities$),
      concatMap(([{layerId, visibility}, entities]) => {
        const layer = entities[layerId];
        const hasChildren = layer.children && layer.children.length > 0;

        // Conditionally build the array
        return hasChildren
          ? [
            this.actions.updateOneReceived({...layer, visibility}),
            LayerManagerActions.updateChildrenVisibility({layerId, visibility}),
            LayerManagerActions.updateParentVisibility({layerId, visibility})
          ]
          : [
            this.actions.updateOneReceived({...layer, visibility}),
            LayerManagerActions.updateParentVisibility({layerId, visibility})
          ];
      })
    )
  );


  private updateLayerChildren$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LayerManagerActions.updateChildrenVisibility),
      map(({payload}) => payload),
      withLatestFrom(this.entities$),
      map(([{layerId, visibility}, entities]) => {
        const collectDescendants = (id: string, acc: any[] = []): any[] => {
          const layer = entities[id];
          if (!layer?.children?.length) return acc;

          for (const childId of layer.children) {
            const child = entities[childId];
            if (child) {
              acc.push({...child, visibility});
              collectDescendants(childId, acc);
            }
          }

          return acc;
        };

        const updates = collectDescendants(layerId);
        return this.actions.updateManyReceived(updates);
      })
    )
  );

  private updateLayerParent$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LayerManagerActions.updateParentVisibility),
      map(({payload}) => payload),
      withLatestFrom(this.entities$),
      concatMap(([{layerId, visibility}, entities]) => {
        const updates: any[] = [];
        let currentId = layerId;
        let currentVisibility = visibility;

        while (true) {
          const currentLayer = entities[currentId];
          const parentId = currentLayer?.parentId;
          if (!parentId) break;

          const parent = entities[parentId];
          if (!parent) break;

          let allVisible = true;
          let allHidden = true;

          for (const childId of parent.children) {
            const v = childId === currentId
              ? currentVisibility
              : entities[childId]?.visibility;

            if (v === 'PARTIAL') {
              allVisible = false;
              allHidden = false;
              break;
            } else if (v === 'HIDDEN') {
              allVisible = false;
            } else {
              allHidden = false;
            }

            if (!allVisible && !allHidden) break;
          }

          const newVisibility =
            allHidden ? 'HIDDEN' : allVisible ? 'VISIBLE' : 'PARTIAL';

          updates.push(this.actions.updateOneReceived({
            ...parent,
            visibility: newVisibility
          }));

          currentId = parentId;
          currentVisibility = newVisibility;
        }

        return updates;
      })
    )
  );

  private layerOpacity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LayerManagerActions.setLayerOpacity),
      map(({payload}) => payload),
      withLatestFrom(this.entities$),
      map(([{layerId, opacity}, entities]) => {
          this.updateOneReceived({...entities[layerId], opacity});
          return this.actions.updateOneReceived({...entities[layerId], opacity});
        }
      )
    )
  );

  private layerLockState = createEffect(() =>
    this.actions$.pipe(
      ofType(LayerManagerActions.setLayerLockStatus),
      map(({payload}) => payload),
      withLatestFrom(this.entities$),
      map(([{layerId, layerLockStatus}, entities]) => {
          this.updateOneReceived({...entities[layerId], locked: layerLockStatus});
          return this.actions.updateOneReceived({...entities[layerId], locked: layerLockStatus});
        }
      )
    )
  );


  public setLayerVisibility(layerId: string, visibility: LayerVisibility) {
    this.store.dispatch(
      LayerManagerActions.setLayerVisibility({layerId, visibility})
    );
  }

  public setLayerOpacity(layerId: string, opacity: number) {
    this.store.dispatch(
      LayerManagerActions.setLayerOpacity({layerId, opacity})
    );
  }

  getLayerByName(
    layerName: string,
    allLayers?: LayerNode
  ): LayerNode | undefined {
    return allLayers.layers.find((layer) => layer.layerName === layerName);
  }

  getLayerById(
    layerId: string,
    allLayers?: LayerNode[]
  ): LayerNode | undefined {
    return allLayers.find((layer) => layer.layerId === layerId);
  }
}
