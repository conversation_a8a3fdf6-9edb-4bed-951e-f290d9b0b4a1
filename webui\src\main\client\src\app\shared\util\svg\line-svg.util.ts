import { ElementRef } from '@angular/core';
import {
  isMarkerStyle,
  Line,
  LineConnection,
  LineMarker,
  MarkerColor,
  MarkerStyle,
  MarkerType,
  Point,
  PointPair,
  RectPos
} from './line-svg.model';

// <editor-fold desc="- Standard Line Command">
/**
 * Standard straight line command
 * will generate straight line.
 *
 * @param point A point a line(Point[])
 */
export const lineCommand = (point: Point): string => `L ${point.x} ${point.y}`;
// </editor-fold>

// <editor-fold desc="- Bezier Line Command">
/**
 * Standard bezier curve line command
 * will generate said line.
 *
 * @param p A point a line(Point[])
 * @param i the index of the point
 * @param a the whole point array.
 */
export const bezierCommand = (p: Point, i: number, a: Point[]): string => {
  const cps = controlPoint(a[i - 1], a[i - 2], p);
  const cpe = controlPoint(p, a[i - 1], a[i + 1], true);
  return `C ${cps.x},${cps.y} ${cpe.x},${cpe.y} ${p.x},${p.y}`;
};

/**
 * Get the opposed line to generate the control point for bezier line.
 *
 * @param pA First coordinates.
 * @param pB Second coordinates.
 */
export const getOpposedLine = (pA: Point, pB: Point): { length: number, angle: number } => {
  const lengthX = pB.x - pA.x;
  const lengthY = pB.y - pA.y;
  return {
    length: Math.sqrt(lengthX ** 2 + lengthY ** 2),
    angle: Math.atan2(lengthY, lengthX)
  };
};

/**
 * Calculate the control point for bezier curve of 2 points.
 *
 * @param current Current point in the line
 * @param previous Previous point in the line
 * @param next Next point in the line.
 * @param reverse Is the control point reversed.
 */
export const controlPoint = (current: Point, previous: Point, next: Point, reverse?: boolean): Point => {
  const o = getOpposedLine(previous || current, next || current);
  const angle = o.angle + (reverse ? Math.PI : 0);
  const length = o.length * 0.2;
  const x = current.x + Math.cos(angle) * length;
  const y = current.y + Math.sin(angle) * length;
  return { x, y };
};
// </editor-fold>

// <editor-fold desc="- Marker String Creation">
/**
 * Get the ViewBox string value.
 *
 * @param rect The rect position.
 */
export const getMarkerViewBox = (rect: RectPos | undefined): string => `viewBox="${rect ? `${rect.x} ${rect.y} ${rect.width} ${rect.height}` : '0 0 10 10'}"`;

/**
 * Get the Ref position string key and value.
 *
 * @param p The x and y.
 */
export const getMarkerRefPos = (p: Point | undefined): string => `refX="${p ? p.x : '5'}" refY="${p ? p.y : '5'}"`;

/**
 * Get the marker color string key and value.
 *
 * @param c The color values for a marker.
 */
export const getMarkerColor = (c: MarkerColor | undefined): string => (c ? `${c.fill ? `fill="${c.fill}" ` : ''} ${c.stroke ? `stroke="${c.stroke}"` : ''}` : '');

/**
 * Create a basic arrow head marker to use later.
 *
 * @param style custom marker style.
 */
export const createArrowMarker = (style: MarkerStyle): string => `<marker id="${style.id || 'arrow'}" ${getMarkerViewBox(style.viewBox)} ${getMarkerRefPos(style.refPos)} ${getMarkerColor(style.color)}
        markerWidth="${style.size || 4}" markerHeight="${style.size || 4}" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z"></path>
   </marker>`;

/**
 * Create a basic circle shape.
 *
 * @param style custom marker style.
 */
export const createCircleMarker = (style: MarkerStyle): string => `<marker id="${style.id || 'circle'}" ${getMarkerViewBox(style.viewBox)} ${getMarkerRefPos(style.refPos)}
          markerWidth="${style.size || 4}" markerHeight="${style.size || 4}">
            <circle cx="5" cy="5" r="${style.size || 4}" ${getMarkerColor(style.color)} />
   </marker>`;

/**
 * Create correct marker based on the type.
 *
 * @param style The style of the marker.
 */
export const createMarker = (style: MarkerStyle): string => {
  switch (style.type) {
    case MarkerType.Arrow:
      return createArrowMarker(style);
    case MarkerType.Circle:
      return createCircleMarker(style);
    default:
      return '';
  }
};

/**
 * Create passed markers based on the style.
 *
 * @param styles All the marker styles.
 */
export const createMarkers = (styles: MarkerStyle[]): string => `<defs>${styles.map(createMarker).join(' ')}</defs>`;
// </editor-fold>

// <editor-fold desc="- Line String Build Util">
/**
 * Get the start and end connectors for the line.
 *
 * @param c The connector start and end type.
 */
export const getConnectors = (c: LineMarker): string => `${isMarkerStyle(c.start) ? `marker-start="url(#${c.start.id ?? c.start})"` : ''} ${isMarkerStyle(c.end) ? `marker-end="url(#${c.end.id ?? c.end})"` : ''}`;

/**
 * Get all the extra styles to apply to the path.
 * see: https://developer.mozilla.org/en-US/docs/Web/SVG/Tutorial/Paths#:~:text=The%20element%20is%20the,created%20as%20s.
 *
 * @param style K:V of style to apply on path
 */
export const getExtraStyles = (style: { [k: string]: string } | undefined): string => (style ? Object.keys(style).map(k => `${k}="${style ? style[k] : ''}"`).join(' ') : '');

/**
 * Get The connectors and extra styles appended to the path string.
 *
 * @param l The current line object.
 */
export const getExtraInfo = (l: Line): string => `${getExtraStyles(l.extraStyle)} ${l.markers ? getConnectors(l.markers) : ''}`;

/**
 * Build the complete path string.
 *
 * @param l The current line object.
 * @param d The calculate direction for the path.
 */
export const buildLineString = (l: Line, d: string): string => `<path d="${d}" fill="${l.fill}" stroke="${l.stroke}" stroke-width="${l.strokeWidth}" ${getExtraInfo(l)}/>`;

/**
 * Create the svg path string with all the information and style.
 *
 * @param line The line object to render.
 */
export const svgPath = (line: Line): string => {
  const d = line.points.reduce(
    (ac, p, i, a) => (i === 0
      ? `M ${p.x},${p.y}`
      : `${ac} ${line.command ? line.command(p, i, a) : lineCommand(p)}`),
    ''
  );
  return buildLineString(line, d);
};

/**
 * Create all the passed string lines with their respective style.
 *
 * @param lines Array of lines.
 */
export const createLines = (lines: Line[]): string => lines.map(svgPath).join(' ');
// </editor-fold>

// <editor-fold desc="- Line Connection for Round Element">
/**
 * Get the Radius for the line connection without offset.
 *
 * @param c Line connection with HTML element.
 */
export const getRadius = (c: LineConnection): number => c.start.nativeElement.offsetWidth / 2;

/**
 * Add the offset to the a point.
 *
 * @param points The given points to offset.
 * @param x The X offset.
 * @param y The Y offset.
 */
export const getOffsetPoints = (points: PointPair, x: number, y: number): PointPair => ({
  a: { ...points.a, x: points.a.x + (points.a.x > points.b.x ? x : -x), y: points.a.y + y },
  b: { ...points.b, x: points.b.x + (points.b.x > points.a.x ? x : -x), y: points.b.y + y }
});

/**
 * Get the center point of an HTML element.
 *
 * @param el The HTML element.
 */
export const getElementPoint = (el: ElementRef): Point => ({
  x: el.nativeElement.offsetLeft + el.nativeElement.offsetWidth / 2,
  y: el.nativeElement.offsetTop + el.nativeElement.offsetHeight / 2
});

/**
 * Get the intersection points for start and end.
 * used for points on a circle element.
 *
 * @param l Line connection object.
 */
export const getRoundIntersectionPoints = (l: LineConnection): PointPair => {
  const r = getRadius(l);
  const a = getElementPoint(l.start);
  const b = getElementPoint(l.end);
  const radius = {
    a: l.offsetR && l.markers && l.markers.start !== MarkerType.None ? r + l.offsetR : r,
    b: l.offsetR && l.markers && l.markers.end !== MarkerType.None ? r + l.offsetR : r
  };
  const angle = { a: Math.atan2(b.y - a.y, b.x - a.x), b: Math.atan2(a.y - b.y, a.x - b.x) };
  return {
    a: { x: a.x + radius.a * Math.cos(angle.a), y: a.y + radius.a * Math.sin(angle.a) },
    b: { x: b.x + radius.b * Math.cos(angle.b), y: b.y + radius.b * Math.sin(angle.b) }
  };
};

/**
 * Build connection lines for two round elements.
 *
 * @param l The Line connection object.
 */
export const createRoundElementConnection = (l: LineConnection): Line => {
  const points = getOffsetPoints(getRoundIntersectionPoints(l), l.offsetX || 0, l.offsetY || 0);
  return { ...l, points: [points.a, points.b] };
};

/**
 * Create all the lines for round elements connection.
 *
 * @param lines All the Line Connections.
 */
export const createAllRoundConnection = (lines: LineConnection[]): Line[] => lines.map(createRoundElementConnection);
// </editor-fold>

const getLineIntersectionPoint = (distance: Point, center: Point, rect: Point): Point => ((Math.abs(distance.y / distance.x) < rect.y / rect.x)
  ? { x: center.x + (distance.x > 0 ? rect.x : -rect.x), y: center.y + distance.y * rect.x / Math.abs(distance.x) }
  : { x: center.x + distance.x * rect.x / Math.abs(distance.y), y: center.y + (distance.y > 0 ? rect.x : -rect.x) });

export const getLineIntersectionPoints = (l: LineConnection): PointPair => {
  const centerA = getElementPoint(l.start);
  const centerB = getElementPoint(l.end);
  const distanceA = { x: centerB.x - centerA.x, y: centerB.y - centerA.y };
  const distanceB = { x: -distanceA.x, y: -distanceA.y };
  const rectA = { x: l.start.nativeElement.offsetWidth / 2, y: l.start.nativeElement.offsetHeight / 2 };
  const rectB = { x: l.end.nativeElement.offsetWidth / 2, y: l.end.nativeElement.offsetHeight / 2 };
  const a = getLineIntersectionPoint(distanceA, centerA, rectA);
  const b = getLineIntersectionPoint(distanceB, centerB, rectB);
  return { a, b };
};

const createLineElementConnection = (l: LineConnection): Line => {
  const points = getOffsetPoints(getLineIntersectionPoints(l), l.offsetX || 0, l.offsetY || 0);
  return { ...l, points: [points.a, points.b] };
};

export const createAllLineConnection = (lines: LineConnection[]): Line[] => lines.map(createLineElementConnection);
