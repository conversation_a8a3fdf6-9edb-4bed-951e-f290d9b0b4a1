
$icon-color: #0f2648;
$active-icon-color: #e1eff9;
$button-background-color: $active-icon-color;
$active-button-background-color: $icon-color;
$toolbar-button-label-color: rgb(255, 213, 0);

@mixin toolbar-standards() {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  width: fit-content;
}

@mixin flex-center(){
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin toolbar-styling {
  display: flex;
}

@mixin toolbar-button-styling() {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: $button-background-color;
  width: 40px;
  height: 40px;
  border: 1px solid $icon-color;
  border-radius: 2px;
  line-height: 12px;
}

@mixin expanded-button-styling() {
  fill: white;
  background-color: $active-button-background-color;
}

@mixin button-wrapper-styling() {
  display: flex;
  padding: 0;
  width: 55px;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

@mixin button-hover-styling() {
  cursor: pointer;
  background-color: $active-button-background-color;

  ::ng-deep .foregroundColor {
    fill: $active-icon-color;
  }
}

@mixin button-active-styling() {
  cursor: pointer;
  background-color: $active-button-background-color;

  ::ng-deep .foregroundColor {
    fill: $active-icon-color;
  }
}

@mixin label-styling() {
  display: flex;
  font-size: 12px;
  font-weight: 400;
  color: $toolbar-button-label-color;
  text-shadow: 2px 2px 2px #000000;
  padding: 0;
  margin-top: 5px;
  max-width: 80px;
  white-space: normal;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin hide-label-styling() {
  display: none;
}

@mixin toolbar-button-icon-styling {
  width: 40px;
  height: 40px;

  ::ng-deep .foregroundColor {
    fill: $icon-color;
  }
}

@mixin label-button-styling() {
  margin-top: 10px;
  border: unset;
  width: 30px;
  height: 30px;
  padding: 0;
  border-radius: 50%;
}

@mixin stroke-option-styling($strokeWidth, $style, $color) {
  content: '\00a0';
  display: inline-block;
  width: 80px;
  height: 1px;
  border-bottom: $strokeWidth $style $color;
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
}
