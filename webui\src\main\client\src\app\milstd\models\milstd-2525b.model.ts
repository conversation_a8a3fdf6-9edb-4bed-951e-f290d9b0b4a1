import * as milstd from './milstd-2525.model';
import { Echelons, MILSTD2525 } from './milstd-2525.model';

export const FunctionIdMappingsByValue = (dimesion: string) => {
  if (dimesion === 'Space') {
    return FunctionIdSpace;
  } else if (dimesion === 'Air') {
    return FunctionIdAir;
  } else if (dimesion === 'Ground') {
    return FunctionIdGround;
  } else if (dimesion === 'Sea Surface') {
    return FunctionIdSeaSurface;
  } else if (dimesion === 'Subsurface') {
    return FunctionIdSeaSubsurface;
  } else if (dimesion === 'SOF (Special Operations Forces)') {
    return FunctionIdSpecialForce;
  } else {
    return FunctionIdUnknown;
  }
};

export const EchelonsMappingsByDimension = (dimension: string) => {
  if (dimension === 'Ground') {
    return Echelons;
  } else {
    return ['Unspecified'];
  }
};

export const FunctionIdMappings = {
  // Example Function IDs for 2525B
  'AP----': 'Airborne Unit',
  'AF----': 'Fixed-Wing Aircraft',
  'AR----': 'Rotary-Wing Aircraft',
  'AG----': 'Armor',
  'IN----': 'Infantry',
  'SS----': 'Submarine',
  'CG----': 'Cruiser',
  'DD----': 'Destroyer',
  // Add more as necessary
  '------': 'Unknown'
} as const;

export const FunctionIdUnknown = {
  '------': 'Unknown'
} as const;

export const FunctionIdSpace = {
  '------': 'Space Track',
  'S-----': 'Satellite',
  'V-----': 'Crewed Space Vehicle',
  'T-----': 'Space Station'
} as const;

export const FunctionIdAir = {
  '------': 'Air Track',
  'MF----': 'Fixed-Wing Aircraft',
  'MFB---': 'Bomber',
  'MFF---': 'Fighter',
  'MFFI--': 'Interceptor',
  'MFT---': 'Trainer',
  'MFA---': 'Attack/Strike',
  'MFL---': 'VSTOL',
  'MFK---': 'Tanker',
  'MFC---': 'Cargo Airlift (Transport)',
  'MFCL--': 'Cargo Airlift (Light)',
  'MFCM--': 'Cargo Airlift (Medium)',
  'MFCH--': 'Cargo Airlift (Heavy)',
  'MFJ---': 'Electronic Countermeasures (ECM/JAMMER)',
  'MFO---': 'Medevac',
  'MFR---': 'Reconnaissance',
  'MFRW--': 'Airborne Early Warning (AEW)',
  'MFRZ--': 'Electronic Surveillance Measures',
  'MFRX--': 'Photographic',
  'MFP---': 'Patrol',
  'MFPN--': 'Antisurface Warfare/ASUW',
  'MFPM--': 'Mine Countermeasures',
  'MFU---': 'Utility',
  'MFUL--': 'Utility (Light)',
  'MFUM--': 'Utility (Medium)',
  'MFUH--': 'Utility (Heavy)',
  'MFY---': 'Communications (C3I)',
  'MFH---': 'Combat Search and Rescue (CSAR)',
  'MFD---': 'Airborne Command Post (C2)',
  'MFQ---': 'Drone (RPV/UAV)',
  'MFS---': 'Antisubmarine Warfare (ASW) Carrier-Based',
  'MFM---': 'Special Operations Forces (SOF)',
  'MH----': 'Rotary-Wing Aircraft',
  'MHA---': 'Attack',
  'MHS---': 'Antisubmarine Warfare/MPA',
  'MHU---': 'Utility',
  'MHUL--': 'Utility (Light)',
  'MHUM--': 'Utility (Medium)',
  'MHUH--': 'Utility (Heavy)',
  'MHI---': 'Mine Countermeasures',
  'MHH---': 'Combat Search and Rescue (CSAR)',
  'MHR---': 'Reconnaissance',
  'MHQ---': 'Drone (RPV/UAV)',
  'MHC---': 'Cargo Airlift (Transport)',
  'MHCL--': 'Cargo Airlift (Light)',
  'MHCM--': 'Cargo Airlift (Medium)',
  'MHCH--': 'Cargo Airlift (Heavy)',
  'MHT---': 'Trainer',
  'MHO---': 'Medevac',
  'MHM---': 'Special Operations Forces (SOF)',
  'MHD---': 'Airborne Command Post (C2)',
  'MHK---': 'Tanker',
  'MHJ---': 'Electronic Countermeasures (ECM/JAMMER)',
  'ML----': 'Lighter-Than-Air',
  'W-----': 'Weapon',
  'WM----': 'Missile In Flight',
  'WMS---': 'Surface/Land Launched Missile',
  'WMSS--': 'Surface to Surface Missile',
  'WMSA--': 'Surface to Air Missile',
  'WMA---': 'Air Launched Missile',
  'WMAS--': 'Air to Surface Missile',
  'WMAA---': 'Air to Air Missile',
  'WMU---': ' Subsurface to Surface Missile',
  'WDL---': 'Land Attack Missile',
  'C-----': 'Civil Aircraft',
  'CF----': 'Fixed Wing',
  'CH----': 'Rotary Wing',
  'CL----': 'Lighter-Than-Air'
};

export const FunctionIdGround = {
  'U-----': 'Unit',
  'UC----': 'Combat',
  'UCD---': 'Air Defense',
  'UCDS--': 'Short Range Air Defense',
  'UCDSC-': 'Chaparral',
  'UCDSS-': 'Stinger',
  'UCDSV-': 'Vulcan',
  'UCDM--': 'Air Defense Missile',
  'UCDML-': 'Air Defense Missile Light',
  UCDMLA: 'Air Defense Missile Motorized (Avenger)',
  'UCDMM-': 'Air Defense Missile Medium',
  'UCDMH-': 'Air Defense Missile Heavy',
  'UCDH--': 'H/MAD',
  'UCDHH-': 'Hawk',
  'UCDHP-': 'Patriot',
  'UCDG--': 'Gun Unit',
  'UCDC--': 'Composite',
  'UCDT--': 'Targeting Unit',
  'UCDO--': 'Theater Missile Defense Unit',
  'UCA---': 'Armor',
  'UCAT--': 'Armor, Track',
  'UCATA-': 'Armor, Track Airborne',
  'UCATW-': 'Armor, Track Amphibious',
  UCATWR: 'Armor, Track Amphibious Recovery',
  'UCATL-': 'Armor, Track, Light',
  'UCATM-': 'Armor, Track, Medium',
  'UCATH-': 'Armor, Track, Heavy',
  'UCATR-': 'Armor, Track, Recovery',
  'UCAW--': 'Armor, Wheeled',
  'UCAWS-': 'Armor, Wheeled Air Assault',
  'UCAWA-': 'Armor, Wheeled Airborne',
  'UCAWW-': 'Armor, Wheeled Amphibious',
  UCAWWR: 'Armor, Wheeled Amphibious Recovery',
  'UCAWL-': 'Armor, Wheeled Light',
  'UCAWM-': 'Armor, Wheeled Medium',
  'UCAWH-': 'Armor, Wheeled Heavy',
  'UCAWR-': 'Armor, Wheeled Recovery',
  'UCAA--': 'Anti-Armor',
  'UCAAD-': 'Anti-Armor Dismounted',
  'UCAAL-': 'Anti-Armor Light',
  'UCAAM-': 'Anti-Armor Airborne',
  'UCAAS-': 'Anti-Armor Air Assault',
  'UCAAU-': 'Anti-Armor Mountain',
  'UCAAC-': 'Anti-Armor Arctic',
  'UCAAA-': 'Anti-Armor Armored',
  UCAAAT: 'Anti-Armor Armored Tracked',
  'UCAA AW': 'Anti-Armor Armored Wheeled',
  UCAAAS: 'Anti-Armor Armored Air Assault',
  UCAAOS: 'Anti-Armor Motorized',
  'UCV---': 'Aviation',
  'UCVF--': 'Aviation, Fixed Wing',
  'UCVFU-': 'Aviation, Utility Fixed Wing',
  'UCVFA-': 'Aviation, Attack Fixed Wing',
  'UCVFR-': 'Aviation, Recon Fixed Wing',
  'UCVR--': 'Aviation, Rotary Wing',
  'UCVRA-': 'Aviation, Attack Rotary Wing',
  'UCVRS-': 'Aviation, Scout Rotary Wing',
  'UCVRW-': 'Aviation, ASW Rotary Wing',
  'UCVRU-': 'Aviation, Utility Rotary Wing',
  UCVRUL: 'Aviation, Light Utility Rotary Wing',
  UCVRUM: 'Aviation, Medium Utility Rotary Wing',
  UCVRUH: 'Aviation, Heavy Utility Rotary Wing',
  UCVRUC: 'Aviation, C2 Rotary Wing',
  UCVRUE: 'Aviation, Medevac Rotary Wing',
  'UCVRM-': 'Aviation, Mine Countermeasure Rotary Wing',
  'UCVS--': 'Aviation, Search and Rescue',
  'UCVC--': 'Aviation, Composite',
  'UCVV--': 'Aviation, V/STOL',
  'UCVU--': 'Aviation, Unmanned Aerial Vehicle',
  'UCVUF-': 'UAV, Fixed Wing',
  'UCVUR-': 'UAV, Rotary Wing',
  'UCI---': 'Infantry',
  'UCIL--': 'Infantry, Light',
  'UCIM--': 'Infantry, Motorized',
  'UCIO--': 'Infantry, Mountain',
  'UCIA--': 'Infantry, Airborne',
  'UCIS--': 'Infantry, Air Assault',
  'UCIZ--': 'Infantry, Mechanized',
  'UCIN--': 'Infantry, Naval',
  'UCII--': 'Infantry Fighting Vehicle',
  'UCIC--': 'Infantry, Arctic',
  'UCE---': 'Engineer',
  'UCEC--': 'Engineer, Combat',
  'UCECS-': 'Engineer, Combat Air Assault',
  'UCECA-': 'Engineer, Combat Airborne',
  'UCECC-': 'Engineer, Combat Arctic',
  'UCECL-': 'Engineer, Combat Light (Sapper)',
  'UCECM-': 'Engineer, Combat Medium',
  'UCECH-': 'Engineer, Combat Heavy',
  'UCECT-': 'Engineer, Combat Mechanized (Track)',
  'UCECW-': 'Engineer, Combat Motorized',
  'UCECO-': 'Engineer, Combat Mountain',
  'UCECR-': 'Engineer, Combat Recon',
  'UCEN--': 'Engineer, Construction',
  'UCENN-': 'Engineer, Naval Construction',
  'UCF---': 'Field Artillery',
  'UCFH--': 'Field Artillery, Howitzer/Gun',
  'UCFHE-': 'Field Artillery, Howitzer/Gun Self-Propelled',
  'UCFHS-': 'Field Artillery, Howitzer/Gun Air Assault',
  'UCFHA-': 'Field Artillery, Howitzer/Gun Airborne',
  'UCFS L-': 'Field Artillery, Section Light',
  'UCFSO-': 'Field Artillery, Section Mountain',
  'UCFO--': 'Meteorological',
  'UCFOS-': 'Meteorological Air Assault',
  'UCFOA-': 'Meteorological Airborne',
  'UCFOL-': 'Meteorological Light',
  'UCFOO-': 'Meteorological Mountain',
  'UCR---': 'Reconnaissance',
  'UCRH--': 'Reconnaissance, Horse',
  'UCRV--': 'Reconnaissance, Cavalry',
  'UCRVA-': 'Reconnaissance, Cavalry Armored',
  'UCRVM-': 'Reconnaissance, Cavalry Motorized',
  'UCRVG-': 'Reconnaissance, Cavalry Ground',
  'UCRVO-': 'Reconnaissance, Cavalry Air',
  'UCRC--': 'Reconnaissance, Arctic',
  'UCRS--': 'Reconnaissance, Air Assault',
  'UCRA--': 'Reconnaissance, Airborne',
  'UCRO--': 'Reconnaissance, Mountain',
  'UCRLL-': 'Reconnaissance, Light',
  'UCRR--': 'Reconnaissance, Marine',
  'UCRRD-': 'Reconnaissance, Marine Division',
  'UCRRF-': 'Reconnaissance, Marine Force',
  'UCRRL-': 'Reconnaissance, Marine Light Armored (LAR)',
  'UCRX--': 'Reconnaissance, Long Range Surveillance (LRS)',
  'UCM---': 'Missile (Surface-to-Surface)',
  'UCMT--': 'Missile (Surface-to-Surface) Tactical',
  'UCMS--': 'Missile (Surface-to-Surface) Strategic',
  'UCS---': 'Internal Security Forces',
  'UCSW--': 'Internal Security Forces, Riverine',
  'UCSG--': 'Internal Security Forces, Ground',
  'UCSGD-': 'Internal Security Forces, Dismounted Ground',
  'UCSGM-': 'Internal Security Forces, Motorized Ground',
  'UCSGA-': 'Internal Security Forces, Mechanized Ground',
  'UCSM--': 'Internal Security Forces, Wheeled Mechanized',
  'UCSR--': 'Internal Security Forces, Railroad',
  'UCSA--': 'Internal Security Forces, Aviation',
  'UU----': 'Combat Support',
  'UUA---': 'Combat Support NBC',
  'UUAC--': 'Chemical',
  'UUACC-': 'Smoke/Decon',
  UUACCK: 'Mechanized Smoke/Decon',
  UUACCM: 'Motorized Smoke/Decon',
  'UUACS-': 'Smoke',
  UUACSM: 'Motorized Smoke',
  UUACSA: 'Armor Smoke',
  'UUACR-': 'Chemical Recon',
  UUACRW: 'Chemical Wheeled Armored Vehicle',
  UUACRS: 'Chemical Wheeled Armored Vehicle Reconnaissance Surveillance',
  'UUAN--': 'Nuclear',
  'UUAB--': 'Biological',
  'UUABR-': 'Recon Equipped',
  'UUAD--': 'Decontamination',
  'UUM---': 'Military Intelligence',
  'UUMA--': 'Aerial Exploitation',
  'UUMS--': 'Signal Intelligence (SIGINT)',
  'UUMSE-': 'Electronic Warfare',
  UUMSEA: 'Armored Wheeled Vehicle',
  UUMSED: 'Direction Finding',
  UUMSEI: 'Intercept',
  UUMSEJ: 'Jamming',
  UUMSET: 'Theater',
  UUMSEC: 'Corps',
  'UUMC--': 'Counter Intelligence',
  'UUMR--': 'Surveillance',
  'UUMRG-': 'Ground Surveillance Radar',
  'UUMRS-': 'Sensor',
  UUMRSS: 'Sensor SCM',
  'UUMRX-': 'Ground Station Module',
  'UUMMO-': 'Meteorological',
  'UUMO--': 'Operations',
  'UUMT--': 'Tactical Exploit',
  'UUMQ--': 'Interrogation',
  'UUMJ--': 'Joint Intelligence Center',
  'UUL---': 'Law Enforcement Unit',
  'UULS--': 'Shore Patrol',
  'UULM--': 'Military Police',
  'UULC--': 'Civilian Law Enforcement',
  'UULF--': 'Security Police (Air)',
  'UULD--': 'Central Intelligence Division (CID)',
  'UUS---': 'Signal Unit',
  'UUSA--': 'Area',
  'UUSC--': 'Communication Configured Package',
  'UUSCL-': 'Large Communication Configured Package (LCCP)',
  'UUSO--': 'Command Operations',
  'UUSF--': 'Forward Communications',
  'UUSM--': 'Multiple Subscriber Element',
  'UUSMS-': 'Small Extension Node',
  'UUSML-': 'Large Extension Node',
  'UUSMN-': 'Node Center',
  'UUSR--': 'Radio Unit',
  'UUSRS-': 'Tactical Satellite',
  'UUSRT-': 'Teletype Center',
  'UUSRW-': 'Relay',
  'UUSS--': 'Signal Support',
  'UUSW--': 'Telephone Switch',
  'UUSX--': 'Electronic Ranging',

  'US----': 'Combat Service Support',
  'USA---': 'Administrative (Admin)',
  'USAT--': 'Admin Theater',
  'USAC--': 'Admin Corps',
  'USAJ--': 'Judge Advocate General (JAG)',
  'USAJT-': 'JAG Theater',
  'USAJC-': 'JAG Corps',
  'USAO--': 'Postal',
  'USAOT-': 'Postal Theater',
  'USAOC-': 'Postal Corps',
  'USAF--': 'Finance',
  'USAFT-': 'Finance Theater',
  'USAFC-': 'Finance Corps',
  'USAS--': 'Personnel Services',
  'USAST-': 'Personnel Theater',
  'USASC-': 'Personnel Corps',
  'USAM--': 'Mortuary/Graves Registry',
  'USAMT-': 'Mortuary/Graves Registry Theater',
  'USAMC-': 'Mortuary/Graves Registry Corps',
  'USAR--': 'Religious/Chaplain',
  'USART-': 'Religious/Chaplain Theater',
  'USARC-': 'Religious/Chaplain Corps',
  'USAP--': 'Public Affairs',
  'USAPT-': 'Public Affairs Theater',
  'USAPC-': 'Public Affairs Corps',
  'USAPB-': 'Public Affairs Broadcast',
  USAPBT: 'Public Affairs Broadcast Theater',
  USAPBC: 'Public Affairs Broadcast Corps',
  'USAPM-': 'Public Affairs Joint Information Bureau',
  USAPMT: 'Public Affairs JIB Theater',
  USAPMC: 'Public Affairs JIB Corps',
  'USAX--': 'Replacement Holding Unit (RHU)',
  'USAXT-': 'RHU Theater',
  'USAXC-': 'RHU Corps',
  'USAL--': 'Labor',
  'USALT-': 'Labor Theater',
  'USALC-': 'Labor Corps',
  'USAW--': 'Morale, Welfare, Recreation (MWR)',
  'USAWT-': 'MWR Theater',
  'USAWC-': 'MWR Corps',
  'USAQ--': 'Quartermaster (Supply)',
  'USAQT-': 'Quartermaster Theater',
  'USAQC-': 'Quartermaster Corps',

  'USM---': 'Medical',
  'USMT--': 'Medical Theater',
  'USMC--': 'Medical Corps',
  'USMM--': 'Medical Treatment Facility',
  'USMMT-': 'MTF Theater',
  'USMMC-': 'MTF Corps',
  'USMV--': 'Medical Veterinary',
  'USMVT-': 'Medical Veterinary Theater',
  'USMVC-': 'Medical Veterinary Corps',
  'USMD--': 'Medical Dental',
  'USMDT-': 'Medical Dental Theater',
  'USMDC-': 'Medical Dental Corps',
  'USMP--': 'Medical Psychological',
  'USMPT-': 'Medical Psychological Theater',
  'USMPC-': 'Medical Psychological Corps',

  'USS---': 'Supply',
  'USST--': 'Supply Theater',
  'USSC--': 'Supply Corps',
  'USS1--': 'Supply Class I',
  'USS1T-': 'Class I Theater',
  'USS1C-': 'Class I Corps',
  'USS2--': 'Supply Class II',
  'USS2T-': 'Class II Theater',
  'USS2C-': 'Class II Corps',
  'USS3--': 'Supply Class III',
  'USS3T-': 'Class III Theater',
  'USS3C-': 'Class III Corps',
  'USS3A-': 'Supply Class III Aviation',
  USS3AT: 'Class III Aviation Theater',
  USS3AC: 'Class III Aviation Corps',
  'USS4--': 'Supply Class IV',
  'USS4T-': 'Class IV Theater',
  'USS4C-': 'Class IV Corps',
  'USS5--': 'Supply Class V',
  'USS5T-': 'Class V Theater',
  'USS5C-': 'Class V Corps',
  'USS6--': 'Supply Class VI',
  'USS6T-': 'Class VI Theater',
  'USS6C-': 'Class VI Corps',
  'USS7--': 'Supply Class VII',
  'USS7T-': 'Class VII Theater',
  'USS7C-': 'Class VII Corps',
  'USS8--': 'Supply Class VIII',
  'USS8T-': 'Class VIII Theater',
  'USS8C-': 'Class VIII Corps',
  'USS9--': 'Supply Class IX',
  'USS9T-': 'Class IX Theater',
  'USS9C-': 'Class IX Corps',
  'USSX--': 'Supply Class X',
  'USSXT-': 'Class X Theater',
  'USSXC-': 'Class X Corps',
  'USSL--': 'Supply Laundry/Bath',
  'USSLT-': 'Laundry/Bath Theater',
  'USSLC-': 'Laundry/Bath Corps',
  'USSW--': 'Supply Water',
  'USSWT-': 'Water Theater',
  'USSWC-': 'Water Corps',
  'USSWP-': 'Supply Water Purification',
  'USS WPT': 'Purification Theater',
  'USS WPC': 'Purification Corps',

  'UST---': 'Transportation',
  'USTT--': 'Transportation Theater',
  'USTC--': 'Transportation Corps',
  'USTM--': 'Movement Control Center (MCC)',
  'USTMT-': 'MCC Theater',
  'USTMC-': 'MCC Corps',
  'USTR--': 'Railhead',
  'USTRT-': 'Railhead Theater',
  'USTRC-': 'Railhead Corps',
  'USTS--': 'SPOD/SPOE',
  'USTST-': 'SPOD/SPOE Theater',
  'USTSC-': 'SPOD/SPOE Corps',
  'USTA--': 'APOD/APOE',
  'USTAT-': 'APOD/APOE Theater',
  'USTAC-': 'APOD/APOE Corps',
  'USTI--': 'Missile',
  'USTIT-': 'Missile Theater',
  'USTIC-': 'Missile Corps',

  'USX---': 'Maintenance',
  'USXT--': 'Maintenance Theater',
  'USXC--': 'Maintenance Corps',
  'USXH--': 'Maintenance Heavy',
  'USXHT-': 'Maintenance Heavy Theater',
  'USXHC-': 'Maintenance Heavy Corps',
  'USXR--': 'Maintenance Recovery',
  'USXRT-': 'Recovery Theater',
  'USXRC-': 'Recovery Corps',
  'USXO--': 'Ordnance',
  'USXOT-': 'Ordnance Theater',
  'USXOC-': 'Ordnance Corps',
  'USXOM-': 'Ordnance Missile',
  USXOMT: 'Missile Theater',
  USXOMC: 'Missile Corps',
  'USXE--': 'Electro-Optical',
  'USXET-': 'Electro-Optical Theater',
  'USXEC-': 'Electro-Optical Corps',

  'UH----': 'Special C2 Headquarters Component',

  'E-----': 'Ground Track Equipment',
  'EW----': 'Weapon',
  'EWM---': 'Missile Launcher',
  'EWMA--': 'Air Defense (AD) Missile Launcher',
  'EWMAS-': 'Short Range AD Missile Launcher',
  'EWMAI-': 'Intermediate Range AD Missile Launcher',
  'EWMA L': 'Long Range AD Missile Launcher',
  'EWMAT-': 'AD Missile Launcher Theater',
  'EWMS--': 'Surf-Surf (SS) Missile Launcher',
  'EWMSS-': 'Short Range SS Missile Launcher',
  'EWMSI-': 'Intermediate Range SS Missile Launcher',
  'EWMSL-': 'Long Range SS Missile Launcher',
  'EWMT--': 'Missile Launcher Antitank (AT)',
  'EWMTL-': 'AT Launcher Light',
  'EWMTM-': 'AT Launcher Medium',
  'EWMT H': 'AT Launcher Heavy',
  'EWS---': 'Single Rocket Launcher',
  'EWSL--': 'Single Rocket Launcher Light',
  'EWSM--': 'Single Rocket Launcher Medium',
  'EWSH--': 'Single Rocket Launcher Heavy',
  'EWX---': 'Multiple Rocket Launcher',
  'EWXL--': 'Multiple Rocket Launcher Light',
  'EWXM--': 'Multiple Rocket Launcher Medium',
  'EWXH--': 'Multiple Rocket Launcher Heavy',
  'EWT---': 'Anti-Tank Rocket Launcher',
  'EWTL--': 'AT Rocket Launcher Light',
  'EWTM--': 'AT Rocket Launcher Medium',
  'EWTH--': 'AT Rocket Launcher Heavy',
  'EWR---': 'Rifle/Automatic Weapon',
  'EWRR--': 'Rifle',
  'EWRL--': 'Light Machine Gun',
  'EWRH--': 'Heavy Machine Gun',
  'EWZ---': 'Grenade Launcher',
  'EWZL--': 'Grenade Launcher Light',
  'EWZM--': 'Grenade Launcher Medium',
  'EWZH--': 'Grenade Launcher Heavy',
  'EWO---': 'Mortar',
  'EWOL--': 'Mortar Light',
  'EWOM--': 'Mortar Medium',
  'EWOH--': 'Mortar Heavy',
  'EWH---': 'Howitzer',
  'EWHL--': 'Howitzer Light',
  'EWHLS-': 'Howitzer Light Self-Propelled',
  'EWHM--': 'Howitzer Medium',
  'EWHMS-': 'Howitzer Medium Self-Propelled',
  'EWHH--': 'Howitzer Heavy',
  'EWHHS-': 'Howitzer Heavy Self-Propelled',
  'EWG---': 'Anti-Tank Gun',
  'EWGL--': 'Anti-Tank Gun Light',
  'EWGM--': 'Anti-Tank Gun Medium',
  'EWGH--': 'Anti-Tank Gun Heavy',
  'EWGR--': 'Anti-Tank Gun Recoilless',
  'EWD---': 'Direct Fire Gun',
  'E WDDL-': 'Direct Fire Gun Light',
  'EW DLS-': 'Light Self-Propelled',
  'EWDM--': 'Direct Fire Gun Medium',
  'EW DMS-': 'Medium Self-Propelled',
  'EWDH--': 'Direct Fire Gun Heavy',
  'EWDHS-': 'Heavy Self-Propelled',
  'EWA---': 'Air Defense Gun',
  'EWAL--': 'AD Gun Light',
  'EWAM--': 'AD Gun Medium',
  'EWAH--': 'AD Gun Heavy',

  'EV----': 'Ground Vehicle',
  'EVA---': 'Armored Vehicle',
  'EVAT--': 'Tank',
  'EVATL-': 'Tank Light',
  EVATLR: 'Tank Light Recovery',
  'EVATM-': 'Tank Medium',
  EVATMR: 'Tank Medium Recovery',
  'EVATH-': 'Tank Heavy',
  EVATHR: 'Tank Heavy Recovery',
  'EVAA--': 'Armored Personnel Carrier',
  'EVAAR-': 'APC Recovery',
  'EVAI--': 'Armored Infantry',
  'EVAC--': 'C2V/ACV',
  'EVAS--': 'Combat Service Support Vehicle',
  'EVAL--': 'Light Armored Vehicle',
  'EVU---': 'Utility Vehicle',
  'EVUB--': 'Bus',
  'EVUS--': 'Semi',
  'EVUL--': 'Limited Cross-Country Truck',
  'EVUX--': 'Cross-Country Truck',
  'EVUR--': 'Water Craft',
  'EVE---': 'Engineer Vehicle',
  'EVEB--': 'Bridge',
  'EVEE--': 'Earthmover',
  'EVEC--': 'Construction Vehicle',
  'EVEM--': 'Mine Laying Vehicle',
  'EVEMV-': 'Armored Carrier with Volcano',
  'EVEML-': 'Truck Mounted with Volcano',
  'EVEA--': 'Mine Clearing Vehicle',
  'EVEAA-': 'Armored Mounted Mine Clearing Vehicle',
  'EVEAT-': 'Trailer Mounted Mine Clearing Vehicle',
  'EVED--': 'Dozer',
  'EVT---': 'Train Locomotive',
  'EVC---': 'Civilian Vehicle',

  'ES----': 'Sensor',
  'ESR---': 'Radar',
  'ESE---': 'Emplaced Sensor',

  'EX----': 'Special Equipment',
  'EXL---': 'Laser',
  'EXN---': 'NBC Equipment',
  'EXF---': 'Flame Thrower',
  'EXM---': 'Land Mines',
  'EXMC--': 'Claymore',
  'EXML--': 'Less Than Lethal',

  'I-----': 'Installation',
  'IRM---': 'Raw Material Production/Storage',
  'IRM M-': 'Mine',
  'IRP---': 'Petroleum/Gas/Oil',
  'IRN---': 'NBC',
  'IRNB--': 'Biological',
  'IRNC--': 'Chemical',
  'IRNN--': 'Nuclear',
  'IP----': 'Processing Facility',
  'IPD---': 'Decon',
  'IE----': 'Equipment Manufacture',
  'IU----': 'Service, Research, Utility Facility',
  'IUR---': 'Technological Research Facility',
  'IUT---': 'Telecommunications Facility',
  'IUE---': 'Electric Power Facility',
  'IUEN--': 'Nuclear Plant',
  'IUED--': 'Dam',
  'IUEF--': 'Fossil Fuel',
  'IUP---': 'Public Water Services',
  'IM----': 'Military Materiel Facility',
  'IMF---': 'Nuclear Energy',
  'IMFA--': 'Atomic Energy Reactor',
  'IMFP--': 'Nuclear Material Production',
  'IMFPW-': 'Weapons Grade',
  'IMFS--': 'Nuclear Material Storage',
  'IMA---': 'Aircraft Production & Assembly',
  'IME---': 'Ammunition and Explosives Production',
  'IMG---': 'Armament Production',
  'IMV---': 'Military Vehicle Production',
  'IMN---': 'Engineering Equipment Production',
  'IMNB--': 'Bridge',
  'IMC---': 'Chemical & Biological Warfare Production',
  'IMS---': 'Ship Construction',
  'IMM---': 'Missile & Space System Production',
  'IG----': 'Government Leadership',
  'IB----': 'Military Base/Facility',
  'IBA---': 'Airport/Airbase',
  'IBN---': 'Seaport/Naval Base',
  'IT----': 'Transport Facility',
  'IX----': 'Medical Facility',
  'IXH---': 'Hospital'
} as const;

export const FunctionIdSeaSurface = {
  '------': 'Sea Surface Track',
  'C-----': 'Combatant',
  'CL----': 'Line',
  'CLCV--': 'Carrier',
  'CLBB--': 'Battleship',
  'CLCC--': 'Cruiser',
  'CLDD--': 'Destroyer',
  'CLFF--': 'Frigate/Corvette',
  'SSCA--': 'Amphibious Warfare Ship',
  'CALA--': 'Assault Vessel',
  'CALS--': 'Landing Ship',
  'CALC--': 'Landing Craft',
  'CM----': 'Mine Warfare Vessel',
  'CMML--': 'Minelayer',
  'CMMS--': 'Minesweeper',
  'CMMH--': 'Minehunter',
  'CMMA--': 'MCM Support',
  'CMMD--': 'MCM Drone',
  'CP----': 'Patrol',
  'CPSB--': 'Antisubmarine Warfare',
  'CPSU--': 'Antisurface Warfare',
  'CH----': 'Hovercraft',
  'S-----': 'Station',
  'SP----': 'Picket',
  'SA----': 'ASW Ship',
  'G-----': 'Navy Group',
  'GT----': 'Navy Task Force',
  'GG----': 'Navy Task Group',
  'GU----': 'Navy Task Unit',
  'GC----': 'Convoy',
  'N-----': 'Noncombatant',
  'NR----': 'Underway Replenishment',
  'NF----': 'Fleet Support',
  'NI----': 'Intelligence',
  'NS----': 'Service & Support Harbor',
  'NM----': 'Hospital Ship',
  'NH----': 'Hovercraft',
  'NN----': 'Sub Station',
  'NNR---': 'Rescue',
  'X-----': 'Non-Military',
  'XM----': 'Merchant',
  'XMC---': 'Cargo',
  'XMR---': 'Roll On/Roll Off',
  'XMO---': 'Oiler/Tanker',
  'XMTU--': 'Tug',
  'XMF---': 'Ferry',
  'XMP---': 'Passenger',
  'XMH---': 'Hazardous Materials (HAZMAT)',
  'XMTO--': 'Towing Vessel',
  'XF----': 'Fishing',
  'XFDF--': 'Drifter',
  'XFDR--': 'Dredge',
  'XFTR--': 'Trawler',
  'XR----': 'Leisure Craft',
  'XL----': 'Law Enforcement Vessel',
  'XH----': 'Hovercraft',
  'O-----': 'Own Track'
} as const;

export const FunctionIdSeaSubsurface = {
  '------': 'Sea Subsurface Track',
  'S-----': 'Submarine',
  'SN----': 'Nuclear Propulsion',
  'SC----': 'Conventional Propulsion',
  'SO----': 'Other Submersible',
  'SU----': 'Unmanned Underwater Vehicle (UUV)',
  'SS----': 'Station',
  'SSA---': 'ASW Submarine',
  'W-----': 'Underwater Weapon',
  'WT----': 'Torpedo',
  'WM----': 'Sea Mine',
  'WMD---': 'Sea Mine Dealt',
  'WMG---': 'Sea Mine (Ground)',
  'WMGD--': 'Sea Mine (Ground) Dealt',
  'WMM---': 'Sea Mine (Moored)',
  'WMMD--': 'Sea Mine (Moored) Dealt',
  'WMF---': 'Sea Mine (Floating)',
  'WMFD---': 'Sea Mine (Floating) Dealt',
  'WMO---': 'Sea Mine (Other)',
  'WMOD--': 'Sea Mine (Other) Dealt',
  'WD----': 'Underwater Decoy',
  'WDM---': 'Sea Mine Decoy',
  'N-----': 'Non-Submarine',
  'ND----': 'Diver'
} as const;

export const FunctionIdSpecialForce = {
  '------': 'Special Operations Force Unit',
  'A-----': 'Aviation',
  'AF----': 'Fixed Wing',
  'AFA---': 'Fixed Wing Attack',
  'AFK---': 'Fixed Wing Refuel',
  'AFU---': 'Fixed Wing Utility',
  'AFUL--': 'Fixed Wing Utility Light',
  'AFUM--': 'Fixed Wing Utility Medium',
  'AFUH--': 'Fixed Wing Utility Heavy',

  'AV----': 'VSTOL',

  'AH----': 'Rotary Wing',
  'AHH---': 'Combart Search and Rescue',
  'AHA---': 'Rotary Wing Attack',
  'AHK---': 'Rotary Wing Refuel',
  'AHU---': 'Rotary Wing Utility',
  'AHUL--': 'Rotary Wing Utility Light',
  'AHUM--': 'Rotary Wing Utility Medium',
  'AHUH--': 'Rotary Wing Utility Heavy',

  'N-----': 'Naval',
  'NS----': 'Seal',
  'NU----': 'Underwater Demolition Team',
  'NB----': 'Special Boat',
  'NN----': 'Special SSNR',
  'G-----': 'Ground',
  'GS----': 'Special Forces',
  'GR----': 'Ranger',
  'GP----': 'Psychological Operations (PSYOP)',
  'GPA---': 'Fixed Aviation',
  'GC----': 'Civil Affairs',
  'B-----': 'Support'
} as const;

export type FunctionId = keyof typeof FunctionIdMappings;

export type FunctionDescription = (typeof FunctionIdMappings)[FunctionId];

export interface MILSTD2525B extends MILSTD2525 {
  description: FunctionDescription;
}

export type SymbolSetList = {
  code: string;
  dimension: milstd.BattleDimension;
  functionId: string;
  description: string;
  symbolInfo: MILSTD2525B;
};

export const getFunctionDescription = (code: string): FunctionDescription => {
  const functionId = code.slice(3, 9); // Function ID spans positions 4–9
  return FunctionIdMappings[functionId as FunctionId] || 'Unknown';
};

export const getSymbolInfo = (code: string): MILSTD2525B => {
  const symbolInfo = milstd.getSymbolInfo(code);
  return {
    ...symbolInfo,
    description: getFunctionDescription(code)
  };
};

/**
 * Generates all possible symbol combinations for all battle dimensions with their respective function IDs.
 * Uses default values: Warfighting symbol set, Unknown affiliation, Present status,
 * Unspecified echelon/mobility/operational condition, and Unspecified country code.
 *
 * @returns Array of objects containing the symbol code, dimension, function ID, and description
 */
export function generateAllDimensionFunctionCombinations() {
  const combinations: Array<{
    code: string;
    dimension: milstd.BattleDimension;
    functionId: string;
    description: string;
    symbolInfo: MILSTD2525B;
  }> = [];

  // Default values
  const defaultSymbolSet: milstd.SymbolSet = 'Warfighting';
  const defaultAffiliation: milstd.Affiliation = 'Unknown';
  const defaultStatus: milstd.Status = 'Unspecified';
  const defaultEchelon: milstd.Echelon = 'Unspecified';
  const defaultMobility: milstd.Mobility = 'Unspecified';
  const defaultCountryCode = 'Unspecified';
  const defaultOperationalCondition: milstd.OperationalCondition =
    'Unspecified';

  // Battle dimensions to process (excluding 'Other' as it has no specific function IDs)
  const dimensionsToProcess: milstd.BattleDimension[] = [
    'Space',
    'Air',
    'Ground',
    'Sea Surface',
    'Subsurface',
    'SOF (Special Operations Forces)'
  ];

  for (const dimension of dimensionsToProcess) {
    const functionIdMappings = FunctionIdMappingsByValue(dimension);

    for (const [functionId, description] of Object.entries(
      functionIdMappings
    )) {
      try {
        // Generate the MILSTD code
        const code = milstd.generateCode(
          defaultSymbolSet,
          defaultAffiliation,
          dimension,
          defaultStatus,
          defaultEchelon,
          defaultMobility,
          defaultCountryCode,
          defaultOperationalCondition,
          functionId
        );

        // Get complete symbol information
        const symbolInfo = getSymbolInfo(code);

        combinations.push({
          code,
          dimension,
          functionId,
          description,
          symbolInfo
        });
      } catch (error) {
        // Skip invalid combinations
        console.warn(
          `Failed to generate code for ${dimension} - ${functionId}: ${error}`
        );
      }
    }
  }

  return combinations;
}

/**
 * Generates symbol combinations for a specific battle dimension with all its function IDs.
 * Uses default values for other parameters.
 *
 * @param dimension - The battle dimension to generate combinations for
 * @returns Array of objects containing the symbol code, function ID, and description for the dimension
 */
export function generateDimensionFunctionCombinations(
  dimension: milstd.BattleDimension
) {
  const combinations: Array<{
    code: string;
    functionId: string;
    description: string;
    symbolInfo: MILSTD2525B;
  }> = [];

  // Default values
  const defaultSymbolSet: milstd.SymbolSet = 'Warfighting';
  const defaultAffiliation: milstd.Affiliation = 'Unknown';
  const defaultStatus: milstd.Status = 'Unspecified';
  const defaultEchelon: milstd.Echelon = 'Unspecified';
  const defaultMobility: milstd.Mobility = 'Unspecified';
  const defaultCountryCode = 'Unspecified';
  const defaultOperationalCondition: milstd.OperationalCondition =
    'Unspecified';

  const functionIdMappings = FunctionIdMappingsByValue(dimension);

  for (const [functionId, description] of Object.entries(functionIdMappings)) {
    try {
      // Generate the MILSTD code
      const code = milstd.generateCode(
        defaultSymbolSet,
        defaultAffiliation,
        dimension,
        defaultStatus,
        defaultEchelon,
        defaultMobility,
        defaultCountryCode,
        defaultOperationalCondition,
        functionId
      );

      // Get complete symbol information
      const symbolInfo = getSymbolInfo(code);

      combinations.push({
        code,
        functionId,
        description,
        symbolInfo
      });
    } catch (error) {
      console.warn(
        `Failed to generate code for ${dimension} - ${functionId}: ${error}`
      );
    }
  }

  return combinations;
}
