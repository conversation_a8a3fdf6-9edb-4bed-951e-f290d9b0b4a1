<div class="button-wrapper bottom-margin">
  <button
    matTooltip="{{ expanded ? 'Minimize Toolbars' : 'Maximize Toolbars' }}"
    [ngClass]="{'active': expanded}"
    (click)="toggleToolbars()">
    <mat-icon class="button-icon" svgIcon="{{expanded ? 'collapse-r-tb' : 'expand-r-tb'}}"/>
  </button>
</div>

<div class="button-wrapper">
  <button
    matTooltip="{{ showLabels ? 'Hide Labels' : 'Show Labels' }}"
    [ngClass]="{'active': showLabels}"
    (click)="toggleLabels()">
    <mat-icon class="button-icon" svgIcon="labels-off-r-tb"/>
  </button>
</div>
