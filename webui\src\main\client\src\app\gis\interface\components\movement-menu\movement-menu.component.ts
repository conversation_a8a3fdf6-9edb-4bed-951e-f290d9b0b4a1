import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { DataService } from 'src/app/data/data.service';

@Component({
  selector: 'nexus-movement-menu',
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatMenuModule
  ],
  templateUrl: './movement-menu.component.html',
  styleUrl: './movement-menu.component.css'
})
export class MovementMenuComponent {
  data = inject(DataService);

  openMovementDialog(): void {
    this.data.movement.openMovementManagerDialog();
  }
}
