import { HttpClient } from '@angular/common/http';
import {
  Inject,
  Injectable,
  InjectionToken,
  NgZone,
  signal,
  computed
} from '@angular/core';

import { Store } from '@ngrx/store';

import { LatLngTuple } from 'leaflet';

import { map, Observable } from 'rxjs';
import { Settings } from 'src/app/data/settings/settings.model';

import {
  Coordinate,
  CursitController,
  semiRandomCoordinates
} from '../../../cursit/models/cursit.model';
import { Instruction } from '../../../data/endpoint/endpoint.model';
import {
  BoundingBox,
  Entity,
  EntityInfo,
  extractEntityInfo,
  getLayerIcon,
  isArcEntityInfo,
  isCorridorAreaEntityInfo,
  isPointEntityInfo,
  Layer,
  Shapes
} from '../../../data/entity/entity.model';
import { processLargeArrayByChunk } from '../../../shared/util/util.model';

import { LeafletEntity } from '../../leafletgis/LeafletEntity';
import { LeafletMilitaryMapElement } from '../../leafletgis/LeafletMilitaryMapElement';
import {
  Line,
  MilSymDTOItemList,
  ReceivedTacticalGraphics
} from '../../leafletgis/LeafletTacticalGraphics';

import {
  CursitFilter,
  isVisible
} from '../../../cursit/models/cursit-filter.model';
import { LeafletMapDisplay } from '../../leafletgis/LeafletMapDisplay';
import { mapActions } from '../actions/map.actions';
import {
  ElementFactory,
  FanAreaOptions,
  MilitaryElement
} from '../ElementFactory';
import { ElementHandler } from '../ElementHandler';
import { GISElement } from '../GISElement';
import { InputHandler } from '../InputHandler';
import { LayerFactory } from '../LayerFactory';
import { LayerHandler } from '../LayerHandler';
import { AbstractMapDisplay, MapDisplay } from '../MapDisplay';
import { MapLayer } from '../MapLayer';
import { GISCoordinateReference, Reference } from '../models/gis.model';
import { ShapeType } from '../models/map-element.model';
import { MouseCoordinateType } from '../MouseCoordinateLocator';
import {
  GridType,
  LayerInfo,
  LayerManagerActions,
  LayerVisibility
} from '../../../data/layer-manager/layer-manager.model';
import { LayerManagerFacade } from '../../../data/layer-manager/layer-manager.facade';
import { LeafletMap } from '../../leafletgis/LeafletMap';
import { LeafletLayerHandler } from '../../leafletgis/LeafletLayerHandler';

import { LeafletGraphics } from '../../leafletgis/LeafletGraphics';
import { NestedTreeItem } from '@simfront/common-libs';
import { forward } from 'mgrs';
export const GISENGINE = {
  LUCIAD_2D: 'LUCIAD_2D',
  LUCIAD_3D: 'LUCIAD_3D',
  LEAFLET: 'LEAFLET'
} as const;

type ObjectValues<T> = T[keyof T];

export type GISEngine = ObjectValues<typeof GISENGINE>;

export const GIS_ENGINE = new InjectionToken<GISEngine>('GIS_ENGINE');

export type RouteClickData = {
  latitude: number;
  longitude: number;
  containerX: number;
  containerY: number;
};

@Injectable({ providedIn: 'root' })
export class GisService {
  _mapDisplay: MapDisplay<object, object>;
  _reference: GISCoordinateReference = Reference.GeoCentric;
  milSymLayers: MapLayer<object>[] = [];
  milSymLayer: MapLayer<object> = undefined;
  drawingLayer: MapLayer<object> = undefined;
  _latLngLayer: MapLayer<object> = undefined;
  _mgrsLayer: MapLayer<object> = undefined;
  _currentFilters?: CursitFilter;
  _showEntityLabels: boolean = true;
  _layerQueue: LayerInfo[] = [];

  private _routePoints = signal<RouteClickData[]>([]);
  public readonly routePoints = this._routePoints.asReadonly();
  public readonly routePointsCount = computed(() => this._routePoints().length);

  constructor(
    @Inject(GIS_ENGINE) public gisEngine: GISEngine,
    private _store: Store,
    private http: HttpClient,
    private ngZone: NgZone
  ) {}

  async addElements(settings: Settings): Promise<void> {
    const dynamicLayersRoot = this.layerHandler.createParentLayer(
      'Layer',
      '-999',
      '-11',
      true
    ).parentLayer;
    const baseLayersRoot = this.layerHandler.createParentLayer(
      'Base',
      '-888',
      '-11',
      true
    ).parentLayer;
    const gridLayersRoot = this.layerHandler.createParentLayer(
      'Grid',
      '-777',
      '-11',
      true
    ).parentLayer;
    this.layerHandler.addLayer(dynamicLayersRoot);
    this.layerHandler.addLayer(baseLayersRoot);
    this.layerHandler.addLayer(gridLayersRoot);
    const wmtsLayer = this.createTiledLayer(
      'https://server.arcgisonline.com/arcgis/rest/services/NatGeo_World_Map/MapServer/WMTS',
      ['NatGeo_World_Map']
    );

    wmtsLayer
      ?.then((layer) => {
        layer.layerType = 'Base';
        layer.setVisible(true);
        layer.parentId = '-888'; // id for Base Layer
        this.layerHandler.addLayer(layer);
      })
      .catch((reason) => {
        console.log(reason);
      });

    this.createAndAddWorldBordersLayer();

    // const milSymLayer: Layer =
    // this.gisService.createMilitarySymbolLayer('Military Symbol Layer');
    this.milSymLayer = this.createMilitarySymbolLayer(
      'Manually Created Objects'
    );
    this.milSymLayer.setSelectable(true);
    this.milSymLayer.setEditable(true);
    this.milSymLayer.setVisible(true);
    this.milSymLayer.layerId = '-222'; // id for Manually Created Objects
    this.milSymLayer.parentId = '-999'; // id for Dynamic Shape Layers (pixi layers)
    this.layerHandler.addLayer(this.milSymLayer);

    this.drawingLayer = this.createShapeDrawingLayer('Geodetic Shape Layer');
    this.drawingLayer.setSelectable(true);
    this.drawingLayer.setEditable(true);
    this.drawingLayer.setVisible(true);
    this.drawingLayer.layerId = '-333'; // id for Geodetic Shape Layer
    this.drawingLayer.parentId = '-999';
    this.layerHandler.addLayer(this.drawingLayer);

    this._mgrsLayer = this.createGridLayer('MGRS', settings.cursit.gridColour);

    this._latLngLayer = this.createGridLayer(
      'LatLon',
      settings.cursit.gridColour
    );

    // TODO: there's a weird bug here this is the only sequence that works
    if (settings.cursit.locationFormat === 'Mgrs') {
      this._mgrsLayer.setVisible(true);
      this.layerHandler.addLayer(this._mgrsLayer);
      this.layerHandler.addLayer(this._latLngLayer);
      this._latLngLayer.setVisible(false);
      this.setGridType('MGRS');
    } else {
      this.layerHandler.addLayer(this._mgrsLayer);
      this._mgrsLayer.setVisible(false);
      this._latLngLayer.setVisible(true);
      this.layerHandler.addLayer(this._latLngLayer);
      this.setGridType('LatLon');
    }

    this.createLayersInQueue();

    return undefined;
  }

  createLayersInQueue() {
    this._layerQueue.forEach((layer) => {
      this.store.dispatch(LayerManagerActions.addLayer(layer));
      const newMapLayer = this.createMilitarySymbolLayer(layer.layerName);
      newMapLayer.setSelectable(true);
      newMapLayer.setEditable(true);
      newMapLayer.setVisible(true);
      this.layerHandler.addToLayerCache(layer.layerId, newMapLayer);
    });
  }

  addToLayerQueue(layerList?: LayerInfo[], layer?: LayerInfo) {
    if (layerList) {
      this._layerQueue.push(...layerList);
    }
    if (layer) {
      this._layerQueue.push(layer);
    }
  }

  updateFilters(filters: CursitFilter): void {
    this._currentFilters = filters;
    this.milSymLayers.forEach((layer) => {
      if (layer.isVisible()) {
        Object.values(layer.getElements()).forEach((element) => {
          if (!element.sidc) {
            return;
          }
          const visible = isVisible(element.sidc, filters);
          const le = (element as LeafletMilitaryMapElement)
            .gisObject as LeafletEntity;
          le.setElementVisibility(visible);
        });
      }
    });
  }

  shouldShowEntity(element: GISElement<object>): void {
    if (!this._currentFilters) {
      return;
    }
    if (!element.sidc) {
      return;
    }
    const visible = isVisible(element.sidc, this._currentFilters);
    const le = (element as LeafletMilitaryMapElement)
      .gisObject as LeafletEntity;
    le.setElementVisibility(visible);
  }

  updateSettings(settings: Partial<Settings>): void {
    if (!this.gridLayer) {
      return;
    }
    if (settings.cursit.gridColour) {
      this.gridLayer.getGISLayer().options = {
        ...this.gridLayer.getGISLayer().options,
        color: settings.cursit.gridColour
      };
      this.gridLayer.getGISLayer().reset();
    }
    if (settings.cursit.locationFormat) {
      const { layerId, layerType, layerName } =
        settings.cursit.locationFormat === 'Mgrs'
          ? this._mgrsLayer
          : this._latLngLayer;
      const gridType: GridType =
        settings.cursit.locationFormat === 'Mgrs' ? 'MGRS' : 'LatLon';
      this.store.dispatch(
        LayerManagerActions.setLayerVisibility({
          layerId,
          visibility: 'VISIBLE'
        })
      );
      this.setGridType(gridType);
    }
  }

  get gridLayer(): MapLayer<any> | undefined {
    return this.mapDisplay?.gridType === 'MGRS'
      ? this._mgrsLayer
      : this._latLngLayer;
  }

  requestTacticalGraphics(
    sentTacticalGraphics: MilSymDTOItemList
  ): Observable<string[]> {
    return this.http
      .post<string[]>('http://localhost:8080/tactical', sentTacticalGraphics)
      .pipe(map((response: string[]) => response.map((item) => item)));
  }

  loadAllEntities(numberOfEntities: number): void {
    this.addElementsToLayerAsync(
      numberOfEntities,
      this.layerHandler.getLayer('-222')
    );
  }

  static getRandomCode(): string {
    const infantry = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCIN--AF***`;
    const engineer = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCE---*****`;
    const medic = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSM---*****`;
    const maintenance = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSX---*****`;
    const nuclear = `G${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}MPNZ----****X`;
    return [infantry, engineer, medic, maintenance, nuclear][
      Math.floor(Math.random() * 5)
    ];
  }

  addElementsToLayerAsync(
    numberOfEntities: number,
    milSymLayer: MapLayer<object>
  ): void {
    console.log('Here in async');
    this.milSymLayers.push(milSymLayer);
    const coordinates1 = semiRandomCoordinates(
      numberOfEntities,
      [0, 0],
      1000000
    );
    this.processCoordinatesAsync(coordinates1, milSymLayer);
  }

  processCoordinatesAsync(
    coordinates: Coordinate[],
    layer: MapLayer<object>
  ): void {
    let countId = 0;

    // eslint-disable-next-line no-restricted-syntax
    for (const coordinate of coordinates) {
      const sidc = GisService.getRandomCode();

      const element = this.elementFactory.createMilitaryElement({
        sidc,
        id: `CLOCS__CLOCS__${countId.toString()}`,
        label: `CLOCS__CLOCS__${countId.toString()}`,
        type: ShapeType.POINT,
        catCode: 'Test',
        coordinates: coordinate,
        reference: this.reference
      });

      layer.addElement(element);
      // TODO: - Nebi - Just to try
      // element.updateLocation([0, 0]);
      countId += 1;

      const gisElement = this.elementFactory.createMilitaryElement({
        sidc,
        id: 'GHMPOADC--****X',
        label: '1023498234',
        type: ShapeType.POLYLINE,
        catCode: 'Test',
        coordinates: coordinate,
        reference: this.reference
      });

      layer.addElement(gisElement);
    }
  }

  processTacticalGraphics(receivedTacticalGraphicsList: string[]): void {
    receivedTacticalGraphicsList.forEach((item) => {
      const parsedTacticalGraphics = JSON.parse(
        item
      ) as ReceivedTacticalGraphics;
      const receivedTacticalGraphics: ReceivedTacticalGraphics = {
        type: parsedTacticalGraphics.type,
        polygons: parsedTacticalGraphics.polygons,
        lines: parsedTacticalGraphics.lines,
        labels: parsedTacticalGraphics.labels
      };

      receivedTacticalGraphics.lines.forEach((line: Line) => {
        line.line.forEach((coords: LatLngTuple) => {
          console.log(coords);
        });
      });
    });
    this.mapDisplay.redraw();
  }

  updateCursitController(newCursitController: CursitController): void {
    this.mapDisplay.updateCursitController(newCursitController);
  }

  get store(): Store {
    return this._store;
  }

  get layerFactory(): LayerFactory<object> {
    return this.mapDisplay?.layerFactory;
  }

  get elementFactory(): ElementFactory<object> {
    return this.mapDisplay?.elementFactory;
  }

  get layerHandler(): LayerHandler<object, object> | null {
    return this.mapDisplay?.layerHandler;
  }

  get layerList(): MapLayer<object>[] {
    return this.mapDisplay?.layerHandler.getLayerList();
  }

  get inputHandler(): InputHandler {
    return this.mapDisplay?.inputHandler;
  }

  get elementHandler(): ElementHandler {
    return this.mapDisplay?.elementHandler;
  }

  get reference(): GISCoordinateReference | null {
    return this._reference;
  }

  set reference(coordinateReference: GISCoordinateReference) {
    this._reference = coordinateReference;
  }

  get mapDisplay(): MapDisplay<object, object> {
    return this._mapDisplay;
  }

  set mapDisplay(mapDisplay: MapDisplay<object, object>) {
    this._mapDisplay = mapDisplay;
  }

  printCursitMap(): void {
    this.mapDisplay.printMap();
  }

  createElement(element: MilitaryElement): GISElement<object> {
    return this.elementFactory.createMilitaryElement(element);
  }

  createMilitarySymbolLayer(
    layerName: string,
    layerId?: string
  ): MapLayer<object> {
    return this.layerFactory?.createMilitarySymbolLayer(layerName, layerId);
  }

  createShapeDrawingLayer(
    layerName: string,
    layerId?: string
  ): MapLayer<object> {
    return this.layerFactory?.createShapeDrawingLayer(layerName, layerId);
  }

  createWorldBorders(): MapLayer<object> | Promise<MapLayer<object>> {
    return this.layerFactory?.createFeatureLayer(
      'assets/data/world.json',
      'Base'
    );
  }

  createGridLayer(gridType: GridType, colour: string): MapLayer<object> {
    return this.layerFactory?.createGridLayer(gridType, colour);
  }

  createTiledLayer(
    url: string,
    layers?: string[]
  ): Promise<MapLayer<object>> | null {
    return this.layerFactory?.createTiledLayer(url, layers);
  }

  setBackgroundMapOpacity(opacity: number): void {
    this.mapDisplay.layerHandler.mapOpacity(opacity);
  }

  setLayerOpacity(layer, opacity: number) {
    if (layer.layerType === 'Parent') {
      layer.layers.forEach((child) => {
        this.mapDisplay.layerHandler.layerOpacity(child.layerId, opacity);
      });
    } else {
      this.mapDisplay.layerHandler.layerOpacity(layer.layerId, opacity);
    }
  }

  async initMap(): Promise<void> {
    // ///////////// USING GENERIC GIS INTERFACE ///////////////////////

    switch (this.gisEngine) {
      default: {
        const leafletMapDisplay = await import(
          '../../leafletgis/LeafletMapDisplay'
        );
        this.mapDisplay = new leafletMapDisplay.LeafletMapDisplay(
          this,
          this.store,
          null,
          this.ngZone
        );
        break;
      }
    }
  }

  updateEntities(entities: Entity[]): void {
    if (this.layerHandler === undefined) {
      setTimeout(() => this.updateEntities(entities), 500);
    } else {
      processLargeArrayByChunk(entities, 1000, this.updateEntity.bind(this));
    }
  }

  // TODO: Handle partial updates.
  updateEntity(entity: Entity): void {
    if (this.layerHandler === undefined) {
      return;
    }
    entity.layers.forEach((layer) => this._updateEntity(entity, layer));
  }

  private _updateEntity(entity: Entity, layer: Layer): void {
    const layerIcon = getLayerIcon(entity, layer.layerId.toString());
    if (layerIcon.location !== undefined) {
      const entityInfo = extractEntityInfo(
        entity.uniqueId,
        entity.name,
        layerIcon
      );
      if (!!entityInfo) {
        const mapLayer = this._getLayer(layer);
        this._updateElement(mapLayer, entityInfo);
      }
    }
  }

  private _getLayer(layer: Layer): MapLayer<Object> {
    const mapLayer: MapLayer<Object> | undefined = this.layerHandler.getLayer(
      layer.layerId.toString()
    );
    if (!mapLayer) {
      return this._createLayer(layer);
    }
    return mapLayer;
  }

  private _createLayer(
    layer: Layer,
    editable: boolean = false,
    selectable: boolean = true
  ): MapLayer<Object> {
    const milLayer: MapLayer<Object> = this.createMilitarySymbolLayer(
      layer.layerName,
      layer.layerId.toString()
    );
    milLayer.setEditable(editable);
    milLayer.setSelectable(selectable);
    if (!!layer.parentId && layer.parentId != '-1') {
      milLayer.parentId = layer.parentId;
    } else {
      milLayer.parentId = this.layerHandler.getLayerByName('Layer').layerId;
    }
    this.layerHandler.addLayer(milLayer);
    this.milSymLayers.push(milLayer);
    return milLayer;
  }

  private _updateElement(layer: MapLayer<Object>, entity: EntityInfo): void {
    const element: GISElement<Object> | undefined = layer.getElement(entity.id);
    if (!element) {
      this._createElement(layer, entity);
    } else {
      element.updateElement(entity);
    }
  }

  updateMapElementSelection(
    id: string,
    latLong: { lat: number; lng: number }
  ): void {
    const display = this._mapDisplay as LeafletMapDisplay;
    const containerPoint = display.leafletMap.latLngToContainerPoint(latLong);
    this.mapDisplay.store.dispatch(
      mapActions.mapElementSelection({
        elementId: id,
        screenCoordinates: { x: containerPoint.x, y: containerPoint.y },
        coordinates: [[latLong.lat, latLong.lng]]
      })
    );
  }

  private _createElement(layer: MapLayer<Object>, entity: EntityInfo): void {
    let width: number | undefined;
    let maximumRange: number | undefined;
    let minimumRange: number | undefined;
    let orientationAngle: number | undefined;
    let sectorAngle: number | undefined;
    let speed: number | undefined;
    let bearing: number | undefined;

    // Extract properties based on shape type using type guards
    if (isPointEntityInfo(entity)) {
      speed = entity.speed;
      bearing = entity.bearing;
    } else if (isArcEntityInfo(entity)) {
      maximumRange = entity.maximumRange;
      minimumRange = entity.minimumRange;
      orientationAngle = entity.orientationAngle;
      sectorAngle = entity.sectorAngle;
    } else if (isCorridorAreaEntityInfo(entity)) {
      width = entity.width;
    }

    const fanAreaOptions: FanAreaOptions = {
      maximumRange,
      minimumRange,
      orientationAngle,
      sectorAngle
    };

    const cursitElement = this.elementFactory.createMilitaryElement({
      sidc: entity.symbolCode,
      id: entity.id,
      label: entity.name,
      type: entity.shapeType,
      catCode: entity.catCode,
      coordinates: entity.coordinates,
      reference: this.reference,
      width,
      fanAreaOptions
    });

    if (cursitElement) {
      layer.addElement(cursitElement);
      this.shouldShowEntity(cursitElement);
      if (cursitElement.gisObject._labelAdded) {
        cursitElement.gisObject.setLabelVisibility(this._showEntityLabels);
      }
      cursitElement.updateSpeedAndBearing(speed, bearing);
    } else {
      console.error(
        `Failed to create element: ${entity.name}`,
        entity.shapeType
      );
    }
  }

  removeLayerFromMap(layer) {
    (this.mapDisplay.map as unknown as LeafletMap).removeLayer(layer);
  }

  getRandomLatLon(): [number, number] {
    const minLat = -90;
    const maxLat = 90;
    const minLon = -180;
    const maxLon = 180;
    const lat = Math.random() * (maxLat - minLat) + minLat;
    const lon = Math.random() * (maxLon - minLon) + minLon;
    return [lon, lat];
  }

  setLayerVisibility(
    layerNode: NestedTreeItem<LayerInfo>,
    visibility: boolean
  ): void {
    const { layerId, layerType, layerName } = layerNode.value;
    const layer: MapLayer<object> = this.layerHandler.getLayer(
      layerNode.value.layerId
    );
    if (layer) {
      this.store.dispatch(
        LayerManagerActions.setLayerVisibility({
          layerId,
          visibility: visibility ? 'VISIBLE' : 'HIDDEN'
        })
      );
      if (layerNode.children) {
        this.setLayerGroupVisibility(layerNode.children, visibility);
      }
      layer.setVisible(visibility);
    }
  }

  setGridLayerVisibility(
    layerNode: NestedTreeItem<LayerInfo>,
    visibility: boolean
  ): void {
    const { layerId, layerType, layerName } = layerNode.value;
    const layer: MapLayer<object> = this.layerHandler.getLayer(
      layerNode.value.layerId
    );
    if (layer) {
      this.store.dispatch(
        LayerManagerActions.setLayerVisibility({
          layerId,
          visibility: visibility ? 'VISIBLE' : 'HIDDEN'
        })
      );
      layer.setVisible(visibility);
    }
  }

  setLayerGroupVisibility(
    children: NestedTreeItem<LayerInfo>[],
    visibility: boolean
  ): void {
    children.forEach((layerNode) => {
      const layer: MapLayer<object> = this.layerHandler.getLayer(layerNode.id);
      layer.setVisible(visibility);
      if (layerNode.children.length !== 0) {
        this.setLayerGroupVisibility(layerNode.children, visibility);
      }
    });
  }

  setGridType(gridType: GridType): void {
    this.mapDisplay.gridType = gridType;
    this.mapDisplay.setMouseCoordinateType(gridType);
    switch (gridType) {
      case 'MGRS':
        this._mgrsLayer.setVisible(true);
        this._latLngLayer.setVisible(false);
        break;
      case 'LatLon':
        this._mgrsLayer.setVisible(false);
        this._latLngLayer.setVisible(true);
        break;
      case 'GARS':
        break;
    }
  }

  removeLayer(layerId: string): void {
    const layer: MapLayer<object> = this.layerHandler.getLayer(layerId);
    if (layer) {
      this.mapDisplay.layerHandler.removeLayer(layer);
    }
  }

  updateLayers(): void {
    this.milSymLayers.forEach((layer) => {
      layer.redraw();
    });
  }

  setLayerRedrawState(layerName: string, instruction: Instruction): void {
    this.milSymLayers
      .filter((layer) => layerName.includes(layer.layerName.split('_')[0]))
      .forEach((layer) => layer.setRedrawLayer(instruction === 'PLAY'));
  }

  setMouseCoordinateType(mouseCoordinateType: MouseCoordinateType): void {
    this.mapDisplay.setMouseCoordinateType(mouseCoordinateType);
  }

  toggleLayerManager(): void {
    this.mapDisplay.toggleLayerManagerVisibility();
  }

  mapZoomIn(): void {
    this.mapDisplay.zoomIn();
  }

  mapZoomOut(): void {
    this.mapDisplay.zoomOut();
  }

  mapFitBounds(boundingBox: BoundingBox): void {
    this.mapDisplay.fitBounds(boundingBox);
  }
  resizeTrigger() {
    const mapDisplay = this.mapDisplay as LeafletMapDisplay;
    if (!mapDisplay || !mapDisplay.map) return;
    mapDisplay.map.invalidateSize(true);
  }
  createAndAddWorldBordersLayer(): void {
    const worldBorders = this.createWorldBorders();
    if (worldBorders instanceof Promise) {
      worldBorders.then((layer) => {
        layer.layerType = 'Base';
        layer.setVisible(true);
        layer.parentId = '-888';
        this.layerHandler.addLayer(layer);
      });
    } else {
      worldBorders.layerType = 'Base';
      worldBorders.setVisible(true);
      worldBorders.parentId = '-888';
      this.layerHandler.addLayer(worldBorders);
    }
  }

  startRouteCreation(): void {
    this.mapDisplay.inputHandler.startRouteCreation();
  }

  cancelRouteCreation(): void {
    this.mapDisplay.inputHandler.cancelRouteCreation();
  }

  setRouteClickData(clickData: RouteClickData): void {
    this._routePoints.update((points) => [...points, clickData]);
  }

  clearRoutePoints(): void {
    this._routePoints.set([]);
  }

  removeRoutePoint(index: number): void {
    this._routePoints.update((points) => {
      const newPoints = [...points];
      newPoints.splice(index, 1);
      return newPoints;
    });
  }

  /**
   * Takes an entity ID,
   * finds the entity,
   * sets entity as selected,
   * selects and centers entity on the cursit
   * @param entityID
   */
  selectAndCenter(entityID: string): void {
    const display = this._mapDisplay as LeafletMapDisplay;
    display.unselect();
    let foundEntity: LeafletEntity | LeafletGraphics | undefined = undefined;
    for (const layer of this.milSymLayers) {
      const elements = layer.getElements();
      if (elements) {
        for (const e of Object.values(elements)) {
          const le = (e as LeafletMilitaryMapElement)
            .gisObject as LeafletEntity;
          if (le.id === entityID) {
            foundEntity = le;
            break;
          }
        }
      }
      if (foundEntity) {
        break;
      }
    }
    if (!foundEntity) {
      console.log('Could not find entity!');
      return;
    }

    let latLong: { lat: number; lng: number } | undefined = undefined;
    if (foundEntity instanceof LeafletEntity) {
      latLong = {
        lat: foundEntity.location[0],
        lng: foundEntity.location[1]
      };
    } else if (foundEntity instanceof LeafletGraphics) {
      latLong = {
        lat: foundEntity._locations[0][1],
        lng: foundEntity._locations[0][0]
      };
    }

    this.updateMapElementSelection(foundEntity.id, latLong);
    display.entitySelected(foundEntity);
  }

  toggleEntityLabelVisibility(showLabels?: boolean): boolean {
    this._showEntityLabels = showLabels ?? !this._showEntityLabels;
    this.milSymLayers.forEach((layer) => {
      const elements = layer.getElements();
      if (elements) {
        Object.values(elements).forEach((e) => {
          const le = (e as LeafletMilitaryMapElement)
            .gisObject as LeafletEntity;
          if (le._labelAdded) {
            le.setLabelVisibility(this._showEntityLabels);
          }
        });
      }
    });
    this.mapDisplay.redraw();
    return this._showEntityLabels;
  }

  hideShowElementType(type: 'MA' | 'OR', visible: boolean): void {
    this.milSymLayers.forEach((msl) => {
      const elements = msl.getElements();
      if (elements) {
        Object.values(elements).forEach((e) => {
          const le = (e as LeafletMilitaryMapElement)
            .gisObject as LeafletEntity;
          if (type === le._catCode) {
            le.setElementVisibility(visible);
          }
        });
      }
    });
  }

  convertToMgrs(latitude: number, longitude: number): string {
    return forward([latitude, longitude], 5);
  }
}
