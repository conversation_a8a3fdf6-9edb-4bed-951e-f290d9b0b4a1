/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/16/2024, 10:27 AM
 */

import { GridLayer } from 'leaflet';

export interface VectorTileOptions {
  maxZoom: number;
  tolerance: number;
  debug: number;
  style: {
    fillColor: string,
    color: string
  };
}

declare class GeoJSONVectorTileLayer extends GridLayer {
  constructor(geoJSONPath: string, options?: VectorTileOptions);
}
