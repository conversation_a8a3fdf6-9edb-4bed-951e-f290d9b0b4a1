@let disableRoot = crud !== 'Create' || (crud === 'Create' && model === 'Endpoint');
<mat-form-field>
  <mat-label>System/Protocol</mat-label>
  <mat-select
    (selectionChange)="onNodeChange($event.value)"
    [value]="selectedNode$ | async"
    [disabled]="disableRoot"
  >
    @for (node of nodes; track node.name) {
      <mat-option [value]="node.name">
        {{ node.name }}
      </mat-option>
    }
  </mat-select>
</mat-form-field>

@if (showDirections$ | async) {
  <mat-form-field>
    <mat-label>Direction</mat-label>
    <mat-select
      [value]="direction$ | async"
      (selectionChange)="directionSelectionChange($event.value)"
      [disabled]="disableRoot"
    >
      @if (crud !== 'Create') {
        @let direction = direction$ | async;
        <mat-option [value]="direction">{{ direction }}</mat-option>
      }
      @for (option of availableDirections$ | async; track option) {
        <mat-option [value]="option">{{ option }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
}

<vcci-data-param-form
  #conForm
  [formType]="'Connection'"
  [usingDefault]="settings.configDialog.useConDefaults && crud !== 'Update'"
  [parameters]="connParams$ | async"
  [sections]="connSections$ | async"
  [pages]="connPages$ | async"
  (getNext)="onGetNext($event, 'connectionMessage')"
  (identifierChanged)="onIdentifierChanged({ type: 'Connection', value: $event })"
  (networkInterfaceChanges)="onNetworkInterfaceChanges($event)"
/>

<vcci-data-param-form
  #endForm
  [formType]="'Endpoint'"
  [usingDefault]="settings.configDialog.useEndDefaults && crud !== 'Update'"
  [parameters]="endParams$ | async"
  [sections]="endSections$ | async"
  [pages]="endPages$ | async"
  (getNext)="onGetNext($event, 'endpointMessage')"
  (networkInterfaceChanges)="onNetworkInterfaceChanges($event)"
/>

<!--<vcci-dataparam-form-->
<!--  #conForm-->
<!--  [usingDefault]="settings.configDialog.useConDefaults && crud !== 'Update'"-->
<!--  [parameters]="connParams$ | async"-->
<!--  [formType]="'Connection'"-->
<!--  (getNext)="onGetNext($event, 'connectionMessage')"-->
<!--  (identifierChanged)="onIdentifierChanged($event)"-->
<!--  (onCreateConnection)="onCreateConnection()"-->
<!--  (onUpdateConnection)="onUpdateConnection()"-->
<!--  (networkInterfaceChanges)="onNetworkInterfaceChanges($event)"-->
<!--&gt;-->
<!--</vcci-dataparam-form>-->

<!--<vcci-dataparam-form-->
<!--  #endForm-->
<!--  [usingDefault]="settings.configDialog.useEndDefaults && crud !== 'Update'"-->
<!--  [parameters]="endParams$ | async"-->
<!--  [formType]="'Endpoint'"-->
<!--  (getNext)="onGetNext($event, 'endpointMessage')"-->
<!--  (identifierChanged)="onIdentifierChanged($event)"-->
<!--  (onUpdateEndpoint)="onUpdateEndpoint()"-->
<!--  (networkInterfaceChanges)="onNetworkInterfaceChanges($event)"-->
<!--&gt;-->
<!--</vcci-dataparam-form>-->
