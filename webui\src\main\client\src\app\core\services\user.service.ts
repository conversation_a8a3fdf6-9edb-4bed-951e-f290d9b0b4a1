import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';

@Injectable({ providedIn: 'root' })
export class UserService {
  trackActivity(e: Event): void {
    // throw new Error('Method not implemented.', { cause: e });
  }

  public networkRequest(arg: string): Observable<string> {
    return this.http.get<string>(
      `${environment.origin}/myEndpoint`,
      { params: { arg } }
    );
  }

  constructor(private http: HttpClient) {}
}
