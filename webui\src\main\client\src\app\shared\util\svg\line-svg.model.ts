/**
 * Available start / end connectors for the
 * line object.
 */
import { ElementRef } from '@angular/core';

/**
 * All the available marker type for
 * the current line-svg component.
 */
export enum MarkerType {
  Circle = 'circle',
  Arrow = 'arrow',
  Square = 'square',
  None = ''
}

/**
 * Rectangle Position x, y, h, w.
 */
export interface RectPos {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Marker color attributes.
 */
export interface MarkerColor {
  fill?: string;
  stroke?: string;
}

/**
 * Style for an SVG marker, so you can
 * modify the line markers styles.
 */
export interface MarkerStyle {
  type: MarkerType;
  id?: string;
  viewBox?: RectPos;
  refPos?: Point;
  color?: MarkerColor;
  size?: number;
  strokeWidth?: number;
}

/**
 * X, Y point coordinates to draw line.
 */
export interface Point {
  x: number;
  y: number;
}

/**
 * Basic line style - pass extras with extra style ie: {'stroke-dashed': '10,10'}
 * Specify the start and end connector type.
 */
export interface LineStyle {
  fill: string;
  stroke: string;
  strokeWidth: string;
  extraStyle?: { [k: string]: string };
  markers?: LineMarker;
  command?: (p: Point, i: number, a: Point[]) => string;
}

/**
 * Used to specify an connector for start and end of a line.
 */
export interface LineMarker {
  start: MarkerType | MarkerStyle;
  end: MarkerType | MarkerStyle;
}

export const isMarkerStyle = (m: MarkerType | MarkerStyle): m is MarkerStyle => typeof m === 'object';

/**
 * A line svg item that will be drawn using
 * svgPath function, pass command to determine line type.
 */
export interface Line extends LineStyle {
  points: Point[];
}

/**
 * Line connection between ElementRefs
 * can create a stylized line between 2 html element.
 */
export interface LineConnection extends LineStyle {
  start: ElementRef;
  end: ElementRef;
  offsetY?: number; // Move the line on y axis.
  offsetX?: number; // Move the line on x axis.
  offsetR?: number; // Use radius from center for offset.
}

/**
 * Two points together used mainly for element connection.
 */
export interface PointPair {
  a: Point;
  b: Point;
}

/**
 * A default arrow marker.
 */
export const defaultArrowMarker: MarkerStyle = {
  type: MarkerType.Arrow,
  id: MarkerType.Arrow,
  viewBox: { x: 0, y: 0, width: 10, height: 10 },
  refPos: { x: 5, y: 5 },
  color: { fill: 'var(--mat-divider-color)', stroke: 'var(--mat-divider-color)' },
  size: 4
};

/**
 * A default circle marker.
 */
export const defaultCircleMarker: MarkerStyle = {
  type: MarkerType.Circle,
  id: MarkerType.Circle,
  viewBox: { x: 0, y: 0, width: 10, height: 10 },
  refPos: { x: 5, y: 5 },
  color: { fill: '#706e6e' },
  size: 4
};
