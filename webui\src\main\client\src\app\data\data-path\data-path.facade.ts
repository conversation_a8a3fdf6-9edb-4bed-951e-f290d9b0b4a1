import { inject, Injectable } from '@angular/core';

import { EMPTY, filter, map, switchMap, withLatestFrom } from 'rxjs';

import { DataPathDialog } from './data-path.dialog';
import { DataPathFacadeBase, getDataPathRoutes } from './data-path.model';
import { createEffect, ofType } from '@ngrx/effects';
import { EndpointFacade } from '../endpoint/endpoint.facade';
import { SelectionModel } from '@angular/cdk/collections';
import {
  ConfigureInstruction,
  ConfigurePayload,
  Endpoint,
  EndpointActions,
  getEndpointId
} from '../endpoint/endpoint.model';
import { FilterGroup, isEntityFilter } from '../endpoint/filter.model';

@Injectable({ providedIn: 'root' })
export class DataPathFacade extends DataPathFacadeBase {
  override facadeDialogContent = DataPathDialog;
  endpointFacade: EndpointFacade = inject(EndpointFacade);
  dependencies = new SelectionModel<string>(true);

  routes$ = this.all$.pipe(map(getDataPathRoutes));

  onDeleteRoute$ = createEffect(() =>
    this.actions$.pipe(
      ofType(this.actions.deleteOneReceived),
      map((action) => action.payload),
      withLatestFrom(this.endpointFacade.all$),
      map(([path, endpoints]) =>
        this.updateEndpointEntityFilter(path, endpoints)
      ),
      filter((payload) => !!payload),
      map((payload) => EndpointActions.configure(payload))
    )
  );

  // Effect to handle endpoint deletion and delete the associated routes if any exist
  onEndpointDeleted$ = createEffect(() =>
    this.endpointFacade.actions$.pipe(
      ofType(this.endpointFacade.actions.deleteOneReceived),
      map((action) => action.payload),
      withLatestFrom(this.routes$),
      switchMap(([endpointId, routes]) => {
        const routesToDelete = routes.filter(
          (route) => route.input === endpointId || route.output === endpointId
        );

        return routesToDelete.length > 0
          ? routesToDelete.map((route) =>
              this.actions.deleteOneReceived(route.name)
            )
          : [];
      })
    )
  );

  //Effect is called whenever the user selects either an endpoint from connection panel or endpoint from router view
  //Find all routes associated with the endpoint that was clicked and select them
  selectRoutes$ = createEffect(
    () =>
      this.endpointFacade.actions$.pipe(
        ofType(this.endpointFacade.actions.selectOne),
        withLatestFrom(this.routes$),
        switchMap(([params, routes]) => {
          return params.payload.ids.map((endpointId) => {
            const dependencies = routes
              .filter((r) => r.input === endpointId || r.output === endpointId)
              .flatMap((r) => [r.input, r.output])
              .filter((v) => v === endpointId);
            this.dependencies.select(...dependencies);
            if (dependencies) {
              this.selectMany(dependencies);
            }
          });
        })
      ),
    { dispatch: false }
  );

  //Effect is called whenever the user deselects either an endpoint from connection panel or endpoint from router view
  deselectRoutes$ = createEffect(
    () =>
      this.endpointFacade.actions$.pipe(
        ofType(this.endpointFacade.actions.deselectOne),
        switchMap(() => {
          if (this.dependencies) {
            this.deselectMany(this.dependencies.selected);
            this.dependencies.clear();
          }
          return EMPTY;
        })
      ),
    { dispatch: false }
  );

  onCreateRoute$ = createEffect(() =>
    this.actions$.pipe(
      ofType(this.actions.createOneReceived),
      withLatestFrom(this.endpointFacade.selected$),
      filter(
        ([{ payload }, selected]) =>
          payload.pathEnd.endpoint === selected?.name ||
          payload.pathStart.endpoint === selected?.name
      ),
      map(([_, selected]) =>
        this.endpointFacade.actions.selectOne({
          ids: [getEndpointId(selected)],
          clearSelection: false
        })
      )
    )
  );

  updateEndpointEntityFilter(
    path: string,
    endpoints: Endpoint[]
  ): undefined | ConfigurePayload {
    const paths = path.split('___');
    if (paths.length < 3) {
      return undefined;
    }
    const inRoute = paths[0];
    const outRoute = paths[2];
    const endpoint = endpoints.find((endpoint) => endpoint.name === outRoute);
    const filters = endpoint?.filters;
    if (!endpoint || !filters || filters.length === 0) {
      return undefined;
    }
    let hasChanges = false;
    const updatedFilters = filters.reduce<FilterGroup[]>((acc, filter) => {
      const entityFilters = filter?.['ENTITIES'];
      if (!entityFilters || !isEntityFilter(entityFilters)) {
        acc.push(filter);
        return acc;
      }
      const updatedExcluded = entityFilters.fullRouteExcludedEntities.filter(
        (e) => e.split('__')?.[1] !== inRoute
      );
      const updatedIncluded = entityFilters.partialRouteIncludedEntities.filter(
        (e) => e.split('__')?.[1] !== inRoute
      );
      if (
        updatedIncluded.length ===
          entityFilters.partialRouteIncludedEntities.length &&
        updatedExcluded.length ===
          entityFilters.fullRouteExcludedEntities.length
      ) {
        acc.push(filter);
        return acc;
      }
      // There's changes...
      hasChanges = true;
      // There's no more entity filter so let's remove it.
      if (updatedExcluded.length === 0 && updatedIncluded.length === 0) {
        const { ['ENTITIES']: _, ...updated } = filter;
        // There's still other filters so let's keep it.
        if (Object.keys(updated).length !== 0) {
          acc.push(updated);
        }
        return acc;
      } else {
        // Update the entity filter.
        const updated: FilterGroup = {
          ...filter,
          ['ENTITIES']: {
            ...entityFilters,
            fullRouteExcludedEntities: updatedExcluded,
            partialRouteIncludedEntities: updatedIncluded
          }
        };
        acc.push(updated);
        return acc;
      }
    }, []);
    return hasChanges
      ? {
          id: getEndpointId(endpoint),
          instruction: ConfigureInstruction.UpdateFilters,
          endpoint: {
            name: endpoint.name,
            node: endpoint.node,
            filters: updatedFilters
          }
        }
      : undefined;
  }
}
