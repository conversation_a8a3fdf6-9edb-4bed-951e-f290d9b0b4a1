import { ShapeType } from '../../gis/interface/models/map-element.model';

export type Coordinate = [number, number] |
[number, number, number]; // [longitude, latitude, altitude]

export type EntityInfo = {
  symbolCode: string,
  coordinates: Coordinate | Coordinate[],
  speed?: number,
  bearing?: number,
  width?: number
};

export const semiRandomCoordinates = (
  length: number,
  [centerLng, centerLat]: Coordinate,
  radius: number
): Coordinate[] => Array.from({ length }).map(() => {
  const offsetLng = Math.random() * (radius * 2) - radius;
  const offsetLat = Math.random() * (radius * 2) - radius;

  const offsetLngDeg = offsetLng / (111111 * Math.cos((centerLat * Math.PI) / 180));
  const offsetLatDeg = offsetLat / 111111; // 1 degree of latitude is approximately 111111 meters

  const lng = centerLng + offsetLngDeg;
  const lat = centerLat + offsetLatDeg;

  return [lng, lat, 0];
});

export enum CursitControllerType {
  Pan = 'PanController',
  Measuring = 'MeasuringController',
  BulkSelect = 'BulkSelectController',
  CreateEntityController = 'CreateEntityController',
  // CreateFe
  UnitSelected = 'UnitSelectedController',
  Create = 'CreateController',
  GraphicSelected = 'GraphicSelectedController',
  EditController = 'EditController',
  SelectController = 'SelectController',
}

export interface CursitController {
  controllerType: CursitControllerType;
  controllerPayload?: {
    shapeType?: ShapeType,
    code?: string
  };
}

