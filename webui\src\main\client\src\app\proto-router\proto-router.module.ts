import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { MaterialModule } from '../material/material.module';
import { DraggableDirective } from '../shared/directives/draggable.directive';
import { FlexBlockDirective } from '../shared/directives/flex-block.directive';
import { IconButtonSizeDirective } from '../shared/directives/icon-button-size.directive';
import { RouteColorPipe } from '../shared/pipes/route-color.pipe';
import { SvgPointPipe } from '../shared/pipes/svg-point.pipe';
import { SharedModule } from '../shared/shared.module';
import { ProtoRouterEndpointComponent } from './components/proto-router-endpoint/proto-router-endpoint.component';
import { ProtoRouterHeaderComponent } from './components/proto-router-header.component';
import { ProtoRouterSvgComponent } from './components/proto-router-svg/proto-router-svg.component';
import { RouteSvgButtonComponent } from './components/proto-router-svg/route-svg-button.component';
import { ProtoRouterComponent } from './components/proto-router.component';
import { RouterLegendsComponent } from './components/router-legends.component';

import { ProtoRouterContainerComponent } from './containers/proto-router-container.component';
import { ProtoRouterRoutingModule } from './proto-router-routing.module';
import { CommonLibsModule } from '@simfront/common-libs';
import { SvgShowButtonPipe } from '../shared/pipes/svg-show-button.pipe';
import { HasFilterPipe } from '../shared/pipes/has-filter.pipe';
import { RouteSvgFilterComponent } from './components/proto-router-svg/route-svg-filter.component';
import { PageComponent } from "../core/components/page.component";

export const CONTAINERS = [ProtoRouterContainerComponent];

@NgModule({
  imports: [
    CommonModule,
    MaterialModule,
    ProtoRouterRoutingModule,
    SharedModule,
    SvgPointPipe,
    DraggableDirective,
    RouteColorPipe,
    FlexBlockDirective,
    IconButtonSizeDirective,
    RouterLegendsComponent,
    CommonLibsModule,
    SvgShowButtonPipe,
    HasFilterPipe,
    RouteSvgFilterComponent,
    PageComponent
],
  declarations: [
    CONTAINERS,
    ProtoRouterComponent,
    ProtoRouterEndpointComponent,
    ProtoRouterSvgComponent,
    RouteSvgButtonComponent,
    ProtoRouterHeaderComponent
  ]
})
export class ProtoRouterModule {}
