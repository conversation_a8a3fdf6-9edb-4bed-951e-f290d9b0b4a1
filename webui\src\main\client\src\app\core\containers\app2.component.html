<ng-container *ngrxLet="{mode: sidePanelMode$, pos: sidePanelPos$} as context">
  <!-- @if (showArc$ | async) { -->
  @if (!(hideArc$ | async)) {
    <nexus-arc style="z-index: 0;" />
  }
  <vcci-toolbar />
  <div class="app-content-grp">
    <ng-container [ngTemplateOutlet]="connectionPanel" [ngTemplateOutletContext]="{
          connections: data.nonExchangeWithEndpoint$ | async,
          show: context.mode === 'Consolidated' && context.pos === 'left',
          title: 'CONNECTIONS',
          left: true,
          open: showLeftSideNav$ | async,
          expanded: connectionsExpanded$ | async,
          position: context.pos
        }" />
    <ng-container [ngTemplateOutlet]="connectionPanel" [ngTemplateOutletContext]="{
          connections: data.nonExchangeInputWithEndpoints$ | async,
          show: context.mode === 'Routing',
          title: 'INPUTS',
          left: true,
          open: showLeftSideNav$ | async,
          expanded: connectionsExpanded$ | async,
        }" />
    <div class="app-outlet custom-mat-container">
      <router-outlet style="display: none" />
    </div>
    <ng-container [ngTemplateOutlet]="connectionPanel" [ngTemplateOutletContext]="{
          connections: data.nonExchangeWithEndpoint$ | async,
          show: context.mode === 'Consolidated' && context.pos === 'right',
          title: 'CONNECTIONS',
          left: false,
          open: showRightSideNav$ | async,
          expanded: connectionsExpanded$ | async,
          position: context.pos
        }" />
    <ng-container [ngTemplateOutlet]="connectionPanel" [ngTemplateOutletContext]="{
          connections: data.nonExchangeOutputWithEndpoints$ | async,
          show: context.mode === 'Routing',
          title: 'OUTPUTS',
          left: false,
          open: showRightSideNav$ | async,
          expanded: connectionsExpanded$ | async,
        }" />
  </div>
</ng-container>

<ng-template #connectionPanel let-connections="connections" let-show="show" let-title="title" let-left="left"
  let-open="open" let-expanded="expanded" let-position="position">
  <vcci-popout-window #connectionPopout *ngIf="show" (opened)="connectionItem.isPopout = true"
    (closed)="connectionItem.isPopout = false" [style]="{ display: connectionItem.isPopout ? 'none' : 'block' }">
    <mat-card class="conn-sidenav custom-mat-container" [@translate]="open">
      <vcci-connection #connectionItem
        [expanded]="open"
        [connectionsExpanded]="expanded"
        [connections]="connections"
        [title]="title"
        [position]="position"
        [isLeft]="left"
        [displayType]="displayType$ | async"
        (onExpanded)="handleSideNavToggle($event, left)"
        (popout)="connectionPopout.open(undefined, connectionItem.getPopoutWindowConfig())"
        (changeDisplayType)="changeConnectionDisplayType($event)"
        [selectedEndpointsByConn]="data.selectedEndpointsByConn$ | async" />
    </mat-card>
  </vcci-popout-window>

</ng-template>
