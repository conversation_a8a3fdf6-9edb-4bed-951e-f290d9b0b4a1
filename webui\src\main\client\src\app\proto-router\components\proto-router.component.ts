import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  QueryList,
  SimpleChanges,
  ViewChildren
} from '@angular/core';
import { SFMap } from '@simfront/common-libs';
import {
  animationFrameScheduler,
  debounceTime,
  distinct,
  Subscription,
  withLatestFrom
} from 'rxjs';
import { DataPathRoute } from '../../data/data-path/data-path.model';
import { DataService, EndpointWithConn } from '../../data/data.service';
import { getEndpointId } from '../../data/endpoint/endpoint.model';
import { Point } from '../../shared/util/svg/line-svg.model';
import { ProtoRouterEndpointComponent } from './proto-router-endpoint/proto-router-endpoint.component';
import { RouteItem } from './proto-router-svg/proto-router-svg.component';

@Component({
  selector: 'vcci-proto-router',
  templateUrl: './proto-router.component.html',
  styleUrls: ['./proto-router.component.css'],
  standalone: false
})
export class ProtoRouterComponent
  implements OnChanges, OnDestroy, AfterViewInit
{
  @ViewChildren('inputs') inputsBlock: QueryList<ProtoRouterEndpointComponent>;
  @ViewChildren('outputs')
  outputsBlock: QueryList<ProtoRouterEndpointComponent>;

  @Input({ required: true }) inputs: EndpointWithConn[];
  @Input({ required: true }) outputs: EndpointWithConn[];
  @Input({ required: true }) routes: DataPathRoute[] = [];
  @Input() selectedEndpointIds: string[] = [];
  @Input() selectedConnectionId: string | undefined = undefined;
  @Input() selectedRoutes: string[] = [];
  filteredInputs: EndpointWithConn[] = [];
  filteredOutputs: EndpointWithConn[] = [];

  private _observer: ResizeObserver;
  private _blocksSub: Subscription | undefined = undefined;
  protected readonly getEndpointId = getEndpointId;

  inputNodes: string[] = [];
  outputNodes: string[] = [];
  inputFilterNodes: string[] = [];
  outputFilterNodes: string[] = [];

  routeItems: RouteItem[] = [];

  constructor(
    private data: DataService,
    private elementRef: ElementRef,
    private cd: ChangeDetectorRef,
    private ngZone: NgZone
  ) {
    this.data.onRefresh$.subscribe(this.ngOnDestroy.bind(this));
    this._observer = new ResizeObserver(() =>
      this.ngZone.run(() => this.buildRouteItems())
    );
    this._observer.observe(this.elementRef.nativeElement);
  }

  ngAfterViewInit() {
    this._blocksSub = this.inputsBlock.changes
      .pipe(
        withLatestFrom(this.outputsBlock.changes),
        debounceTime(50, animationFrameScheduler),
        distinct()
      )
      .subscribe(() => this.buildRouteItems());
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['routes']) {
      this.buildRouteItems();
    }
    if (changes['inputs'] || changes['outputs']) {
      if (changes['inputs']) {
        this.inputNodes = Array.from(
          new Set(this.inputs.map((e) => e.node)).values()
        ).sort();
        this.filteredInputs = this.filterNodes(
          this.inputs,
          this.inputFilterNodes
        );
      }
      if (changes['outputs']) {
        this.outputNodes = Array.from(
          new Set(this.outputs.map((e) => e.node)).values()
        ).sort();
        this.filteredOutputs = this.filterNodes(
          this.outputs,
          this.outputFilterNodes
        );
      }
    }
    if (changes['selectedConnectionId']) {
      this.selectedConnectionUpdate(this.selectedConnectionId);
    }
  }

  buildRouteItems(): void {
    if (this.inputsBlock === undefined || this.outputsBlock === undefined) {
      return;
    }
    const inMap = this.inputsBlock.reduce<SFMap<ProtoRouterEndpointComponent>>(
      (acc, t) => ({
        ...acc,
        [t.endpointId]: t
      }),
      {}
    );
    const outMap = this.outputsBlock.reduce<
      SFMap<ProtoRouterEndpointComponent>
    >(
      (acc, t) => ({
        ...acc,
        [t.endpointId]: t
      }),
      {}
    );
    this.routeItems = this.routes.reduce<RouteItem[]>((acc, r) => {
      const inT = inMap[r.input];
      const outT = outMap[r.output];
      if (inT && outT) {
        acc.push(
          this.getRouteInfo(r.name, r.isTransferAllDataRoute, inT, outT)
        );
      }
      return acc;
    }, []);
    this.cd.detectChanges();
  }

  private getRouteInfo(
    name: string,
    isFullRoute: boolean,
    a: ProtoRouterEndpointComponent,
    b: ProtoRouterEndpointComponent
  ): RouteItem {
    return {
      name,
      isFullRoute,
      input: {
        point: this.getPoint(a.getContainer()),
        id: a.endpointId,
        endpoint: a.endpoint
      },
      output: {
        point: this.getPoint(b.getContainer(), false),
        id: b.endpointId,
        endpoint: b.endpoint
      }
    };
  }

  private getPoint(elementRef: ElementRef, isInput = true): Point {
    const x =
      elementRef.nativeElement.offsetLeft +
      (isInput ? elementRef.nativeElement.offsetWidth : 0);
    const y = elementRef.nativeElement.offsetTop + 50;
    return { x, y };
  }

  handleEndpointClicked(endpointId: string): void {
    this.data.endpoint.endpointSelected(endpointId);
  }

  // //THIS SHOULD BE HANDLED IN ENDPOINT FACADE => HOW DO WE GET ROUTES THERE THOUGH
  // handleDependencies(endpointId: string): void {
  //
  //   //get all dependencies (routes) associated with endpointId
  //   const dependencies = this.routes
  //     .filter(r => r.input === endpointId || r.output === endpointId)
  //     .flatMap(r => [r.input, r.output])
  //     .filter(v => v !== endpointId);
  //
  //   //select routes
  //   if (dependencies) {
  //     if (this.data.endpoint.selected.isSelected(endpointId)) {
  //       this.data.dataPath.deselectMany(dependencies);
  //     } else {
  //       this.data.dataPath.selectMany(dependencies);
  //     }
  //   }
  // }

  //Called when a user selects a connection in connection panel
  //NOTE: Multi selection is disabled at the moment so only one endpoint per connection can be highlighted in
  //the router panel. It is prioritizing input endpoints at the moment.
  //TODO: enable mutli selection so more than one endpoint can be selected
  private selectedConnectionUpdate(connId: string) {
    const associatedEndpoints = [];

    if (this.inputs) {
      this.inputs
        .filter((endpt) => endpt.connection === connId)
        .forEach((ep) => associatedEndpoints.push(getEndpointId(ep)));
    }
    if (this.outputs && associatedEndpoints.length === 0) {
      this.outputs
        .filter((endpt) => endpt.connection === connId)
        .forEach((ep) => associatedEndpoints.push(getEndpointId(ep)));
    }
    this.handleEndpointClicked(associatedEndpoints[0]);
  }

  onFilterChange(type: 'inputs' | 'outputs', nodes: string[]): void {
    if (type === 'inputs') {
      this.inputFilterNodes = nodes;
      this.filteredInputs = this.filterNodes(
        this.inputs,
        this.inputFilterNodes
      );
    } else {
      this.outputFilterNodes = nodes;
      this.filteredOutputs = this.filterNodes(
        this.outputs,
        this.outputFilterNodes
      );
    }
  }

  filterNodes(
    endpoints: EndpointWithConn[],
    nodes: string[]
  ): EndpointWithConn[] {
    return endpoints
      .filter((e) => nodes.includes(e.node))
      .sort((a, b) =>
        `${a.node}:${a.name}`.localeCompare(`${b.node}:${b.name}`)
      );
  }

  ngOnDestroy() {
    this._observer.unobserve(this.elementRef.nativeElement);
    this._blocksSub.unsubscribe();
  }
}
