<h2 mat-dialog-title>CURSIT Filters</h2>
<mat-dialog-content>
  <sf-core-nested-tree [data]="filterListData()"></sf-core-nested-tree>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button
    mat-button
    color="primary"
    [mat-dialog-close]="filterForm.getRawValue()"
  >
    Apply
  </button>
  <button mat-button mat-dialog-close>Cancel</button>
</mat-dialog-actions>

<ng-template #custom let-node>
  <form [formGroup]="filterForm">
    <mat-checkbox
      [checked]="isNodeChecked(node)"
      [indeterminate]="isNodeIndeterminate(node)"
      (change)="onNodeCheckChange(node, $event.checked)"
    >
      {{ node.label || node.value?.name || node.name }}
    </mat-checkbox>
  </form>
</ng-template>
