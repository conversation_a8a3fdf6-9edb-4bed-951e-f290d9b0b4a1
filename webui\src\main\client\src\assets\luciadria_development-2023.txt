# #
# # Development license.
# #
version                   = 2023.0
productName               = LuciadRIA
copyright                 = Powered by Hexagon.
message                   = Powered by Hexagon.
licensee                  = Canadian Department of National Defence
licenseType               = development
optionalModules           = Geometry,Panoramic,MilitarySymbology
expiryDate                = 2023/10/17
serialNumber              = ca9000
key                       = 171de944e9fb7f5fa453bf323c8be812ec4d516f2efd8a3cb2b1af753fe225f35dd45647ad05fdc116abafa2b1d76169fefb94e60f491c1fe39980dc0c5d42d1
#
ADMIN/1-FileReference         = 145343
ADMIN/2-MaintenanceInfo       = 
ADMIN/3-CustomerAccount       = Canadian Department of National Defence
ADMIN/4-CustomerNumber        = C00030
ADMIN/5-ProjectName           = VCCI (Virtual C2 Interface)
ADMIN/6-ProjectNumber         = P100345
ADMIN/7-DispatcherName        = VCCI End User Licenses
ADMIN/8-GenerationInstruction = L1690295043750
ADMIN/9-LicenceDefinition     = * *LuciadLRIA Pro EUL Department of National Defence (20 users) 1 2023
ENTITLES/0-DISCLAIMER-0        = The entitlement information in this file is for information only
ENTITLES/0-DISCLAIMER-1        = Entitlements are determined by the agreements between <PERSON><PERSON> and the Licensee in those agreements
ENTITLES/1-Product and Tier    = LuciadRIA Pro
ENTITLES/2-License             = 20 SEATS
ENTITLES/3-EndUserSystem       = VCCI (Virtual C2 Interface) for Organization  in Territory Canada and abroad
