import { createActionGroup, emptyProps } from '@ngrx/store';
import { createEverything, payload } from '@simfront/common-libs';
import { environment } from 'src/environments/environment';
import { RootState } from '..';
import { IconLocation, Layer, Line } from '../entity/entity.model';

export interface Route {
  uniqueId: string;
  name: string;
  defaultLayerIcon: IconLocation;
  layerHash: any;
  layerIconHash: any;
  layers: Layer[];
}

export interface CreateRoute {
  jRouteNameText: string;
  jRouteLine: Line;
}

export const {
  reducer: routeReducer,
  facade: RouteFacadeBase,
  initialState
} = createEverything<RootState, Route, 'route', never, string>(
  'route',
  (e) => e.uniqueId,
  `${environment.origin}/routes`,
  (state) => state.route ?? initialState
);

export const RouteActions = createActionGroup({
  source: 'Route',
  events: {
    'Save Route': payload<CreateRoute>(),
    'Save Route Success': emptyProps(),
    'Open Route Creation Dialog': emptyProps(),
    'Close Route Creation Dialog': emptyProps()
  }
});
