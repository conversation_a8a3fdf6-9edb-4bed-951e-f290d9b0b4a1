import {
  animate,
  AnimationAnimateMetadata,
  AnimationTransitionMetadata,
  AnimationTriggerMetadata,
  state,
  style,
  transition,
  trigger
} from '@angular/animations';

type Token = '*' | {
  [key: string]: string | number
} | Array<'*' | {
  [key: string]: string | number
}>;

export type AnimationStyleParams = '*' | { [key: string]: string | number } | Array<'*' | {
  [key: string]: string | number
}>;
export const defaultTransition = (ms: number): AnimationTransitionMetadata => transition('on <=> off', defaultAnimation(ms));
export const defaultAnimation = (ms: number): AnimationAnimateMetadata => animate(`${ms}ms cubic-bezier(0.4, 0.0, 0.2, 1)`);
export const twoStateAnimation = (triggerName: string, onTokens: Token, offToken: Token): AnimationTriggerMetadata => trigger(triggerName, [
  state('true', style(onTokens)),
  state('false', style(offToken)),
  transition('true <=> false', defaultAnimation(225))
]);
export const customExpandCollapse = (height: string, minHeight: string): AnimationTriggerMetadata => trigger('expand', [
  state('collapsed', style({ height: `${height}`, minHeight: `${minHeight}` })),
  state('expanded', style({ height: '*' })),
  transition('expanded <=> collapsed', defaultAnimation(225))
]);
export const customWidthExpandCollapse = (key: string, width: string, minWidth: string): AnimationTriggerMetadata => trigger(key, [
  state('false', style({ width: `${width}`, minWidth: `${minWidth}` })),
  state('true', style({ width: '*' })),
  transition('true <=> false', defaultAnimation(225))
]);
export const defaultExpandCollapse = customExpandCollapse('0px', '0px');

export const defaultRotate = (triggerName: string, startDeg: number, endDeg: number): AnimationTriggerMetadata => trigger(triggerName, [
  state('false', style({ transform: `rotate(${startDeg}deg)` })),
  state('true', style({ transform: `rotate(${endDeg}deg)` })),
  transition('true <=> false', defaultAnimation(300))
]);
