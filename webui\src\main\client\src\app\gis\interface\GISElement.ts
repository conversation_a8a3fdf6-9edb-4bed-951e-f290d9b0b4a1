import { Coordinate, EntityInfo } from '../../cursit/models/cursit.model';

import { MilSymLayer } from '../leafletgis/leaflet-symbology-layer/MilitarySymbologyLayer';

import { GISObjectContainer } from './LocationSettable';
import { MapLayer } from './MapLayer';
import { GisService } from './service/gis.service';
import { getGlobalInjector } from '../../../main';

export class GISElement<T> implements GISObjectContainer {
  private _id: string;
  private gisRefObject: any;
  private _mapLayer: MapLayer<T> | MilSymLayer<T>;
  private _selected: boolean = false;
  private _sidc?: string | null = null;
  private _gisService: GisService;

  constructor(id: string) {
    this.id = id;
    const injector = getGlobalInjector();
    this._gisService = injector.get(GisService);
  }

  set gisObject(gisObject: any) {
    this.gisRefObject = gisObject;
  }

  get gisObject(): any {
    return this.gisRefObject;
  }

  set mapLayer(layer: MapLayer<T> | MilSymLayer<T>) {
    this._mapLayer = layer;
  }

  get mapLayer(): MapLayer<T> | MilSymLayer<T> {
    return this._mapLayer;
  }

  set id(id: string) {
    this._id = id;
  }

  get id(): string {
    return this._id;
  }

  get sidc(): string | undefined | null {
    return this._sidc;
  }

  set sidc(sidc: string) {
    this._sidc = sidc;
  }

  set selected(selected: boolean) {
    if (typeof (this.gisRefObject as any).selected === 'boolean') {
      (this.gisRefObject as any).selected = selected;
    }
  }

  get selected(): boolean {
    if (typeof (this.gisRefObject as any).selected === 'boolean') {
      return (this.gisRefObject as any).selected;
    }

    return false;
  }

  removeLocation(index: number): void {
    if (typeof (this.gisRefObject as any).removeLocation === 'function') {
      (this.gisRefObject as any).removeLocation(index);
    }
  }

  addLocation(coordinate: Coordinate): void {
    if (typeof (this.gisRefObject as any).addLocation === 'function') {
      (this.gisRefObject as any).addLocation(coordinate);
    }
  }

  updateLocation(coordinate: Coordinate | Coordinate[]): void {
    if (typeof (this.gisRefObject as any).setLocation === 'function') {
      (this.gisRefObject as any).setLocation(coordinate);
      if (this.selected) {
        const latLong = {
          lat: coordinate[0] as number,
          lng: coordinate[1] as number
        }
      }
    }
  }

  updateSpeedAndBearing(speed: number, bearing: number): void {
    if (typeof (this.gisRefObject as any).setSpeedAndBearing === 'function') {
      (this.gisRefObject as any).setSpeedAndBearing(speed, bearing);
    }
  }

  updateSymbol(symbolCode: string): void {
    if (typeof (this.gisRefObject as any).setSymbolCode === 'function') {
      (this.gisRefObject as any).setSymbolCode(symbolCode);
    }
  }

  updateElement(entityInfo: EntityInfo): void {
    this.updateSymbol(entityInfo.symbolCode);
    this.updateLocation(entityInfo.coordinates);
    this.updateSpeedAndBearing(entityInfo.speed, entityInfo.bearing);
  }
}
