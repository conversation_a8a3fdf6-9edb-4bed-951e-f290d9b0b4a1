<div class="fle-con">
  <sf-core-table
    class="tbl"
    identifier="candidateEntities"
    includeSearch
    includePaginator
    [data]="candidateEntities"
    [columns]="columns"
    [trackBy]="trackBy"
    [selectionMode]="selectionMode"
    (tableSelectionChange)="onSelectionChanged($event)"
  />
  <form [formGroup]="form" class="fle-info">
<!--    <div class="fle-id">-->
<!--      <mat-form-field >-->
<!--        <mat-label>ID</mat-label>-->
<!--        <input formControlName="uniqueId" matInput />-->
<!--      </mat-form-field>-->
<!--    </div>-->
    <mat-form-field>
      <mat-label>North / South (Meters)</mat-label>
      <input formControlName="northSouthDistanceMetres" matInput type="number" />
    </mat-form-field>
    <mat-form-field>
      <mat-label>East / West (Meters)</mat-label>
      <input formControlName="eastWestDistanceMetres" matInput type="number" />
    </mat-form-field>
  </form>
</div>
