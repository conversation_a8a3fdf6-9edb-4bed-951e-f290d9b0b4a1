import { Component, Inject, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { map } from 'rxjs';

import { BS_STORE_PROVIDER } from '../../data/bs-params/bs-params.model';
import {
  BSParamDialogPayload,
  BSParamsStore
} from '../../data/bs-params/bs-params.store';
import { DataService } from '../../data/data.service';

@Component({
  selector: 'vcci-bs-dialog',
  templateUrl: 'bs-dialog.component.html',
  styles: [
    `
      .error-msg {
        margin: 10px;
        font-weight: 500;
        font-style: italic;
        color: var(--error);
        max-width: 90%;
        word-break: break-all;
      }
    `
  ],
  providers: [BS_STORE_PROVIDER],
  standalone: false
})
export class BSDialogComponent implements OnDestroy {
  title$ = this.store.title$;
  crud$ = this.store.crud$;
  model$ = this.store.model$;
  nodes$ = this.store.nodes$.pipe(
    map((nodes) => nodes.filter((n) => 'ROUTING' === n.serviceType))
  );
  busy$ = this.store.busy$;
  error$ = this.store.error$;
  invalid$ = this.store.invalid$;
  showConnectionButton$ = this.store.showConnectionButton$;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: BSParamDialogPayload | null,
    private readonly store: BSParamsStore,
    protected ds: DataService
  ) {
    if (this.data) {
      this.store.patchState(this.data);
      this.store.updateSelection(this.data);
    }
  }

  onNetworkInterfaceChanges(value: string) {
    this.ds.settings.updateOne({
      id: 'user',
      changes: { configDialog: { networkInterface: value } }
    });
  }

  ngOnDestroy() {
    this.store.ngOnDestroy();
  }
}
