import { HandleEventResult } from '@luciad/ria/view/controller/HandleEventResult';
import { SelectController } from '@luciad/ria/view/controller/SelectController';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { LuciadMapDisplay } from '../LuciadMapDisplay';

export class SelectionController extends SelectController {
  constructor(private mapDisplay: LuciadMapDisplay) {
    super();
  }

  // override getCandidates(
  //   viewPoint: Point | null,
  //   sensitivity: number,
  //   paintRepresentations: PaintRepresentation[]
  // ): PickInfo[] {
  //   const pickInfo = super.getCandidates(viewPoint, sensitivity, paintRepresentations);
  //   if (pickInfo) {
  //     // console.log(pickInfo[0].objects);
  //   }
  //   return pickInfo;
  // }
  //
  // override handleCandidates(event: GestureEvent, candidates: PickInfo[]): HandleEventResult {
  //   console.log(candidates);
  //   return EVENT_HANDLED;
  // }

  override onGestureEvent(gestureEvent: GestureEvent): HandleEventResult {
    console.log(gestureEvent.type);
    return super.onGestureEvent(gestureEvent);
  }
}
