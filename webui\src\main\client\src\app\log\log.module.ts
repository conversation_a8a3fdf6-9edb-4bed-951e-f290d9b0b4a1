import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonLibsModule, CoreModule } from '@simfront/common-libs';
import { MaterialModule } from '../material/material.module';
import { FlexBlockDirective } from '../shared/directives/flex-block.directive';
import { LogComponent } from './components/log.component';
import { MsLogComponent } from './components/ms-log.component';
import { LogContainerComponent } from './containers/log-container.component';
import { LogRouterModule } from './log-router.module';
import {
  MatPseudoCheckboxModule,
  MatRippleModule
} from '@angular/material/core';
import { PageComponent } from '../core/components/page.component';

const COMPONENTS = [LogContainerComponent, LogComponent, MsLogComponent];

@NgModule({
  declarations: COMPONENTS,
  exports: COMPONENTS,
  imports: [
    LogRouterModule,
    MaterialModule,
    CommonModule,
    ReactiveFormsModule,
    CommonLibsModule,
    CoreModule,
    FormsModule,
    FlexBlockDirective,
    MatPseudoCheckboxModule,
    MatRippleModule,
    PageComponent
  ]
})
export class LogModule {}
