import {
  Component,
  inject,
  Input,
  signal,
  WritableSignal
} from '@angular/core';
import {
  DataPage,
  DataParameter,
  DataSection,
  PAGE_FORM,
  PARENT_FORM
} from 'src/app/data/bs-params/bs-params.model';
import { BsDataParamsComponent } from '../bs-data-params/bs-data-params.component';
import { BsDataSectionsComponent } from '../bs-data-sections/bs-data-sections.component';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { BsDataParamBase } from '../bs-data-param/bs-data-param.base';

@Component({
  selector: 'vcci-bs-data-page',
  templateUrl: 'bs-data-page.component.html',
  styleUrl: 'bs-data-page.component.css',
  imports: [
    BsDataParamsComponent,
    BsDataSectionsComponent,
    ReactiveFormsModule
  ],
  providers: [
    {
      provide: PAGE_FORM,
      useFactory: () => signal(new FormGroup({}))
    }
  ]
})
export class BsDataPageComponent extends BsDataParamBase {
  parentForm$: WritableSignal<FormGroup> = inject(PARENT_FORM, {
    skipSelf: true
  });
  pageForm$: WritableSignal<FormGroup> = inject(PAGE_FORM, { self: true });

  @Input({ required: true }) set dataPage(dataPage: DataPage) {
    this.pageName = dataPage.pageName;
    this.parameters = Object.values(dataPage.parameters);
    this.sections = Object.values(dataPage.sections);
    const pageForm = this.parentForm$().get('pages').get(this.pageName);
    if (pageForm instanceof FormGroup) {
      this.pageForm$.set(pageForm);
    }
  }

  pageName: string = '';
  parameters: DataParameter[] = [];
  sections: DataSection[] = [];
}
