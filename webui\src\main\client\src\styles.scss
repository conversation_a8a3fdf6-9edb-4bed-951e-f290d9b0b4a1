@use '@angular/material' as mat;

// M3-Theme Setup
// @use './theme-colors';
// @include mat.core();

// html {
//   @include mat.core-theme(theme-colors.$dark-theme);
//   @include mat.all-component-themes(theme-colors.$dark-theme);
// }
// M3-Theme End

// M2-Theme Setup
@include mat.elevation-classes();
@include mat.app-background();

$my-primary: mat.m2-define-palette(mat.$m2-blue-palette, 500, 300, 700);
$my-accent: mat.m2-define-palette(mat.$m2-grey-palette, 50, 100, 200);
$my-warn: mat.m2-define-palette(mat.$m2-red-palette);

$dark-theme: mat.m2-define-dark-theme(
  (
    color: (
      primary: $my-primary,
      accent: $my-accent,
      warn: $my-warn
    ),
    typography: mat.m2-define-typography-config(),
    density: 0
  )
);
@include mat.all-component-themes($dark-theme);
// M2-Theme End

:root {
  --calian-defense: #c34600;
  --calian-primary: #0574b9;
  --calian-dialog: #135781;
  // --calian-primary: #C34600;
  --calian-secondary: #2308c9;
  --calian-neutral: #67696c;
  --calian-gradient: radial-gradient(
    ellipse at bottom right,
    #000000 0%,
    var(--calian-primary) 100%
  );
  --calian-gradient-light: radial-gradient(
    ellipse at bottom right,
    #000000 0%,
    var(--calian-primary) 30%,
    var(--calian-primary) 100%
  );

  --background: #424242;
  --background-rgb: 66, 66, 66;
  --secondary-background-dark: #333333;
  --secondary-background-dark-rgb: 51, 51, 51;
  --secondary-background-light: #85848b;
  --secondary-background-light-rgb: 133, 132, 139;
  --map-background: #415568;
  --map-background-rgb: 65, 85, 104;
  --background-gradient: var(--calian-gradient);
  --background-gradient-light: var(--calian-gradient-light);

  --text: #f2f4f4;
  --text-invert: #303030;
  --text-secondary: #cdcdcd;
  --text-description: #aaaaaa;
  --divider-light: var(--secondary-background-light);
  --divider-light-rgb: var(--secondary-background-light-rgb);
  --divider-dark: var(--secondary-background-dark);
  --divider-dark-rgb: var(--secondary-background-dark-rgb);
  --opacity: 0.6;
  --opacity-low: 0.2;
  --opacity-high: 0.9;

  --success: #00bf00;
  --success-rgb: 0, 191, 0;
  // --active: #6AA5D9;
  --active: #5183ea;
  --active-rgb: 81, 131, 234;
  --pending: #ff9800;
  --pending-rgb: 255, 152, 0;
  --error: #f44336;
  --error-rgb: 244, 67, 54;
  --error-dark: #8b0000;
  --error-dark-rgb: 139, 0, 0;

  // Overrides
  --mdc-elevated-card-container-color: var(--background);
  --mdc-dialog-container-color: var(--calian-dialog);
  --mat-divider-color: var(--divider-light);
  --mdc-filled-text-field-container-color: rgba(
    var(--calian-primary),
    var(--opacity)
  );
  --mdc-filled-text-field-disabled-container-color: rgba(
    var(--calian-primary),
    var(--opacity)
  );
  --mdc-filled-text-field-disabled-input-text-color: var(--text-description);
  --mdc-filled-text-field-disabled-label-text-color: var(--text-description);
  --mat-select-panel-background-color: var(--calian-primary);
  --mat-tab-label-active-text-color: var(--text);
  @include mat.snack-bar-overrides(
    (
      container-color: '#333333',
      button-color: '#f2f4f4',
      supporting-text-color: '#aaaaaa'
    )
  );

  @include mat.stepper-overrides(
    (
      container-color: var(--calian-gradient)
    )
  );

  @include mat.expansion-overrides(
    (
      actions-divider-color: var(--calian-primary),
      container-background-color: var(--calian-dialog)
    )
  );
}

.mat-toolbar {
  &.error {
    --mdc-snackbar-container-color: var(--secondary-background-dark);
    --mat-mdc-snack-bar-button-color: var(--text);
    --mdc-snackbar-supporting-text-color: var(--text-description);
  }
}

html,
body {
  height: 100%;
  width: 100%;
  color: var(--text);
}

body {
  margin: 0;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  overflow-y: scroll;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 0 4px #707070;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 10px;
}

button:disabled,
select:disabled,
input:disabled,
.mat-mdc-select-disabled {
  opacity: 0.3;
}

.selectable {
  outline: var(--active) solid 2px;
}

.custom-mat-container {
  background-color: rgba(var(--background-rgb), var(--opacity)) !important;
  color: var(--text);
  backdrop-filter: blur(5px);
}

.line-broken-tooltip {
  white-space: pre-line;
}

.system-text {
  font-family: 'Segoe UI', 'Noto Sans', sans-serif !important;
  font-weight: 500;
}

.data-param-grp {
  display: flex;
  flex-direction: row;

  > mat-checkbox {
    margin-top: 10px;
  }
}

.data-param-form-field,
.data-param-form-field > mat-form-field,
.data-param-form-field > .container > mat-form-field {
  width: 100%;
}

.data-param-form-field > mat-form-field {
  width: 100%;
}

.custom-font {
  font-family:
    Segoe UI,
    Helvetica Neue,
    Arial,
    sans-serif;
  line-height: normal;
  font-style: normal;
  letter-spacing: normal;
}

sf-core-table mat-card {
  background-color: transparent !important;
}

// THE LOADING EFFECT
.skeleton {
  // The shine that's going to move across the skeleton:
  background-image: linear-gradient(
    90deg,
    rgba(#fff, 0),
    rgba(#fff, 0.2),
    rgba(#fff, 0)
  );
  background-size: 40px 100%; // width of the shine
  background-repeat: no-repeat; // No need to repeat the shine effect
  background-position: left -40px top 0; // Place shine on the left side, with offset on the left based on the width of the shine - see background-size
  animation: shine 1s ease infinite; // increase animation time to see effect in 'slow-mo'
}

@keyframes shine {
  to {
    // Move shine from left to right, with offset on the right based on the width of the shine - see background-size
    background-position: right -40px top 0;
  }
}
