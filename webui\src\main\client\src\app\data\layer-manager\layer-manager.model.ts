import { createEverything, payload } from '@simfront/common-libs';
import { RootState } from '../index';
import { environment } from '../../../environments/environment';
import { createActionGroup } from '@ngrx/store';

export type LayerType = 'Feature' | 'Grid' | 'Web Service' | 'Parent' | 'Base';
export type LayerSource = 'System' | 'Web Service' | 'MicroService';
export type GridType = 'MGRS' | 'LatLon' | 'GARS';
export type LayerVisibility = 'PARTIAL' | 'VISIBLE' | 'HIDDEN';
export type LayerLockStatus = 'locked' | 'partiallyLocked' | 'unlocked';

export interface LayerInfo {
  layerId: string;
  layerName: string;
  visibility: LayerVisibility;
  layerSource?: LayerSource;
  layerType?: LayerType;
  locked: LayerLockStatus;
  children: string[];
  opacity?: number;
  parentId?: string;
}

export type LayerNode = LayerInfo & Record<'layers', LayerNode[]>;

export interface LayerLockState {
  layerId: string;
  layerLockStatus: LayerLockStatus;
  layerType: LayerType;
}

export const {
  initialState,
  reducer: LayerReducer,
  facade: LayerFacadeBase
} = createEverything<RootState, LayerInfo, 'layer', never, string>(
  'layer',
  (l) => l.layerId,
  `${environment.origin}/layers`,
  (state) => state.layer
);

export const LayerManagerActions = createActionGroup({
  source: 'layer',
  events: {
    'Add Layer': payload<LayerInfo>(),
    'Update Layer': payload<LayerInfo>(),
    'Set Layer Visibility': payload<{
      layerId: string;
      visibility: LayerVisibility;
    }>(),
    'Update Children Visibility': payload<{
      layerId: string;
      visibility: LayerVisibility;
    }>(),
    'Update Parent Visibility': payload<{
      layerId: string;
      visibility: LayerVisibility;
    }>(),
    'Set Layer Opacity': payload<{ layerId: string; opacity: number }>(),
    'Set Layer Lock Status': payload<LayerLockState>(),
    'Remove Layer': payload<string>()
  }
});
