<button
  matTooltip="Drawing"
  mat-button
  class="menu-button"
  [matMenuTriggerFor]="drawingMenu"
>
  Drawing
  <mat-icon svgIcon="draw-shape" style="width: 24px; height: 24px"></mat-icon>
</button>
<mat-menu #drawingMenu="matMenu">
  <button
    mat-menu-item
    (click)="openShapeStyleDialog()"
    [disabled]="shapeStyleDialogOpen()"
  >
    Shape Style
  </button>
  <button mat-menu-item>Point</button>
  <button mat-menu-item>Bounds</button>
  <button mat-menu-item>Circle</button>
  <button mat-menu-item>Sector</button>
  <button mat-menu-item>Geo Buffer</button>

  <button mat-menu-item [matMenuTriggerFor]="polyMenu">Poly</button>
  <mat-menu #polyMenu="matMenu"
    ><button mat-menu-item>Bounds</button>
    <button mat-menu-item>Polyline</button></mat-menu
  >
  <button mat-menu-item [matMenuTriggerFor]="threePointMenu">3-Point</button>
  <mat-menu #threePointMenu="matMenu">
    <button mat-menu-item>3-Point Circle</button>
    <button mat-menu-item>3-Point Arc</button>
  </mat-menu>
  <button mat-menu-item [matMenuTriggerFor]="ellipseMenu">Ellipse</button>
  <mat-menu #ellipseMenu="matMenu">
    <button mat-menu-item>Ellipse</button>
    <button mat-menu-item>Elliptic Arc</button>
  </mat-menu>
  <button mat-menu-item [matMenuTriggerFor]="arcMenu">Arc</button>
  <mat-menu #arcMenu="matMenu">
    <button mat-menu-item>Arc by Bulge</button>
    <button mat-menu-item>Arc by Centre</button>
    <button mat-menu-item>Arc Band</button>
  </mat-menu>
</mat-menu>
