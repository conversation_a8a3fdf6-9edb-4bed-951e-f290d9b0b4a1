import {
  Component,
  ElementRef,
  OnInit,
  Renderer2,
  ViewChild,
  inject,
  signal
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { SymbologyComponent } from '@simfront/common-libs';
import { LeafletMouseEvent } from 'leaflet';
import {
  EchelonsMappingsByDimension,
  FunctionIdMappingsByValue,
  generateAllDimensionFunctionCombinations,
  SymbolSetList
} from '../../../../milstd/models/milstd-2525b.model';
import {
  EntityTypes,
  generateCode,
  milsymFormFields,
  milsymCodeFormFields,
  Echelon,
  getSymbolInfo,
  isValidCode
} from '../../../../milstd/models/milstd-2525.model';
import { LeafletMapDisplay } from '../../../leafletgis/LeafletMapDisplay';
import { GisService } from '../../service/gis.service';
import { MilitaryElement } from '../../ElementFactory';
import { ShapeType } from '../../models/map-element.model';
import {
  CodeType,
  createEntityData,
  CreateEntity,
  Shapes
} from '../../../../data/entity/entity.model';
import { CreateEntityTabComponent } from '../create-entity-tab/create-entity-tab.component';
import { CreateEntityCodeComponent } from '../create-entity-code/create-entity-code.component';
import { EntityFacade } from 'src/app/data/entity/entity.facade';
import { toolbarExpanded } from '../top-toolbar/top-toolbar.component';
import { forward } from 'mgrs';
import { DataService } from 'src/app/data/data.service';
import { LocationFormat } from 'src/app/data/settings/settings.model';
import { CreateEntitySymbolListComponent } from '../create-entity-symbol-list/create-entity-symbol-list.component';

@Component({
  selector: 'nexus-create-entity-dialog',
  templateUrl: './create-entity-dialog.html',
  styleUrls: ['./create-entity-dialog.css'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatButtonModule,
    MatProgressBarModule,
    MatTooltipModule,
    MatIconModule,
    CreateEntityTabComponent,
    SymbologyComponent,
    CreateEntityCodeComponent
  ]
})
export class CreateEntityDialogComponent implements OnInit {
  private dialogRef = inject(MatDialogRef<CreateEntityDialogComponent>);
  private dialog = inject(MatDialog);
  private gisService = inject(GisService);
  private renderer = inject(Renderer2);
  private entityFacade = inject(EntityFacade);
  private ds = inject(DataService);
  private data: Partial<createEntityData> & { milStd?: string } =
    inject(MAT_DIALOG_DATA);

  milsymFormFields = milsymFormFields;
  milsymCodeFormFields = milsymCodeFormFields;
  symbolCode: string = '';

  createEntityForm: FormGroup = new FormGroup({
    entityType: new FormControl('Unit', Validators.required),
    name: new FormControl('', Validators.required),
    entitySuffix: new FormControl(''),
    country: new FormControl('Unspecified', Validators.required),
    affiliation: new FormControl('Unknown', Validators.required),
    symbolSet: new FormControl('Warfighting', Validators.required),
    dimension: new FormControl('Other', Validators.required),
    functionID: new FormControl('Unknown', Validators.required),
    status: new FormControl('Unspecified', Validators.required),
    echelon: new FormControl('Unspecified', Validators.required),
    mobility: new FormControl('Unspecified', Validators.required),
    operationalCondition: new FormControl('Unspecified', Validators.required),
    longitude: new FormControl(''),
    latitude: new FormControl(''),
    mgrs: new FormControl(''),
    speed: new FormControl(0.0, Validators.required),
    bearing: new FormControl(0.0, Validators.required),
    altitude: new FormControl(0.0, Validators.required),
    milsymCode: new FormControl('', [
      Validators.required,
      Validators.minLength(15)
    ])
  });

  readonly toolbarExpanded = toolbarExpanded;

  EntityTypes = EntityTypes;
  settingLocation = signal<boolean>(false);
  locationSelected = signal<{
    selected: boolean;
    longitude: number | null;
    latitude: number | null;
    mgrs: string | null;
  }>({
    selected: false,
    longitude: null,
    latitude: null,
    mgrs: null
  });
  entityType = signal<string>('Unit');
  functionIdOptions = signal<Record<string, string>>({});
  echelonOptions = signal<Echelon[]>([]);
  locationFormat = signal<LocationFormat>('Lat Lon');

  @ViewChild('selectedLocation') selectedLocation: ElementRef;

  ngOnInit() {
    this.ds.settings.get$('user').subscribe((settings) => {
      this.locationFormat.set(settings.cursit.locationFormat);
      this.updateLocationValidators();
    });

    if (!!this.data) {
      this.createEntityForm.patchValue(this.data);
    }

    // Setup form subscriptions
    this.setupFormSubscriptions();

    // Set initial location validators
    this.updateLocationValidators();
  }

  private setupFormSubscriptions() {
    this.createEntityForm.get('entityType')?.valueChanges.subscribe((value) => {
      this.entityType.set(value);
    });

    // Subscribe to dimension changes to update Function ID options
    this.createEntityForm
      .get('dimension')
      ?.valueChanges.subscribe((dimension) => {
        if (dimension) {
          // Update function ID options based on the selected dimension
          const functionIdMapping = FunctionIdMappingsByValue(dimension);
          this.functionIdOptions.set(functionIdMapping);

          const echelonOptions = EchelonsMappingsByDimension(dimension);
          this.echelonOptions.set(echelonOptions as Echelon[]);

          // Update the Function ID field in the milsymFormFields array
          const functionIdFieldIndex = milsymFormFields.findIndex(
            (field) => field.name === 'functionID'
          );

          const echelonFieldIndex = milsymFormFields.findIndex(
            (field) => field.name === 'echelon'
          );

          if (functionIdFieldIndex !== -1) {
            milsymFormFields[functionIdFieldIndex] = {
              ...milsymFormFields[functionIdFieldIndex],
              options: Object.values(functionIdMapping)
            };
          }

          if (echelonFieldIndex !== -1) {
            milsymFormFields[echelonFieldIndex] = {
              ...milsymFormFields[echelonFieldIndex],
              options: echelonOptions
            };
          }
          // Reset Function ID to the first available option if the current one is not valid
          const currentFunctionId =
            this.createEntityForm.get('functionID')?.value;

          const currentEchelon = this.createEntityForm.get('echelon')?.value;

          const validOptions = Object.values(functionIdMapping);
          const validEchelons = echelonOptions;

          if (!validOptions.includes(currentFunctionId)) {
            // Set to the first available option or 'Unknown'
            const defaultOption =
              validOptions.length > 0 ? validOptions[0] : 'Unknown';
            this.createEntityForm.get('functionID')?.setValue(defaultOption);
          }

          if (!validEchelons.includes(currentEchelon)) {
            // Set to the first available option or 'Unspecified'
            const defaultOption =
              validEchelons.length > 0 ? validEchelons[0] : 'Unspecified';
            this.createEntityForm.get('echelon')?.setValue(defaultOption);
          }
        }
      });

    // Subscribe to the form value changes and check if the milsymCode is valid
    this.createEntityForm.valueChanges.subscribe((formValues) => {
      if (formValues.milsymCode && formValues.milsymCode.length >= 15) {
        this.symbolCode = formValues.milsymCode;
      }
    });

    // Initialize with current value
    const currentCode = this.createEntityForm.get('milsymCode')?.value;
    if (currentCode && currentCode.length >= 15) {
      this.symbolCode = currentCode;
    }

    this.generateInitialMilsymCode();
  }

  private updateLocationValidators() {
    const longitudeControl = this.createEntityForm.get('longitude');
    const latitudeControl = this.createEntityForm.get('latitude');
    const mgrsControl = this.createEntityForm.get('mgrs');

    if (this.locationFormat() === 'Lat Lon') {
      longitudeControl?.setValidators([Validators.required]);
      latitudeControl?.setValidators([Validators.required]);
      mgrsControl?.clearValidators();
      mgrsControl?.setValue('');
    } else {
      mgrsControl?.setValidators([Validators.required]);
      longitudeControl?.clearValidators();
      latitudeControl?.clearValidators();
      longitudeControl?.setValue('');
      latitudeControl?.setValue('');
    }

    longitudeControl?.updateValueAndValidity();
    latitudeControl?.updateValueAndValidity();
    mgrsControl?.updateValueAndValidity();
  }

  private generateInitialMilsymCode() {
    // If we have a milStd value, use that instead of generating from scratch
    if (this.data.milStd) {
      const milStdCode = this.data.milStd;

      // Validate the code first
      if (isValidCode(milStdCode)) {
        try {
          const symbolInfo = getSymbolInfo(milStdCode);

          this.createEntityForm.patchValue({
            name: this.data.name,
            latitude: this.data.latitude,
            longitude: this.data.longitude,
            symbolSet: symbolInfo.symbolSet,
            affiliation: symbolInfo.affiliation,
            dimension: symbolInfo.dimension,
            status: symbolInfo.status,
            echelon: symbolInfo.echelon,
            mobility: symbolInfo.mobility,
            country: symbolInfo.countryCode,
            operationalCondition: symbolInfo.operationalCondition,
            functionID: symbolInfo.functionId,
            milsymCode: milStdCode
          });

          this.symbolCode = milStdCode;
          return;
        } catch (error) {
          console.error('Error parsing existing MILSTD code:', error);
        }
      } else {
        console.warn('Invalid MILSTD code provided:', milStdCode);
      }
    }

    const values = this.createEntityForm.value;
    let functionIDValue: string;

    Object.entries(FunctionIdMappingsByValue(values.dimension)).forEach(
      (pair) => {
        if (pair[1] === values.functionID) {
          functionIDValue = pair[0];
        }
      }
    );

    if (functionIDValue) {
      try {
        const iconCode = generateCode(
          values.symbolSet,
          values.affiliation,
          values.dimension,
          values.status,
          values.echelon,
          values.mobility,
          values.country,
          values.operationalCondition,
          functionIDValue
        );

        this.createEntityForm.get('milsymCode')?.setValue(iconCode);
      } catch (error) {
        console.error('Error generating initial MILSYM code:', error);
      }
    }
  }

  submitCreateEntityForm() {
    const values = this.createEntityForm.value;
    this.dialogRef.close();
    let functionIDValue: string;

    Object.entries(FunctionIdMappingsByValue(values.dimension)).forEach(
      (pair) => {
        if (pair[1] === values.functionID) {
          functionIDValue = pair[0];
        }
      }
    );

    const iconCode = generateCode(
      values.symbolSet,
      values.affiliation,
      values.dimension,
      values.status,
      values.echelon,
      values.mobility,
      values.country,
      values.operationalCondition,
      functionIDValue
    );
    // const entity: MilitaryElement = {
    //   sidc: iconCode,
    //   id: values.name,
    //   label: values.name,
    //   type: ShapeType.POINT,
    //   catCode: 'MA',
    //   coordinates: [values.latitude, values.longitude]
    // };
    // const layer = this.gisService.layerHandler.getLayer(
    //   'ManuallyCreatedObjects'
    // );
    // layer.addElement(
    //   this.gisService.elementFactory.createMilitaryElement(entity)
    // );
    // layer.redraw();
    const saveEntity: CreateEntity = {
      jEntityNameText: values.name,
      jIconCode: iconCode,
      jIconType: CodeType.MILSTD_2525B,
      jLocation: {
        '@class': Shapes.PointLocation,
        latitude: values.latitude,
        longitude: values.longitude,
        bearing: values.bearing || 0,
        altitude: values.altitude || 0,
        speed: values.speed || 0
      }
    };

    this.entityFacade.saveEntity(saveEntity);
  }

  openSymbolSetDialog() {
    const dialogRef = this.dialog.open(CreateEntitySymbolListComponent, {
      width: '500px',
      disableClose: false,
      hasBackdrop: true
    });

    dialogRef.afterClosed().subscribe((result: SymbolSetList) => {
      if (result && result.code) {
        this.createEntityForm.get('milsymCode')?.setValue(result.code);
        this.symbolCode = result.code;
      }
    });
  }

  openSelectLocation() {
    this.toolbarExpanded.update((v) => !v);
    this.settingLocation.set(true);
    this.dialogRef.updateSize('300px');
    this.dialogRef.updatePosition({ top: '100px', right: '50px' });
    this.dialogRef.disableClose = true;
    const dialogContainer = document.querySelector('.mat-mdc-dialog-surface');
    if (dialogContainer) {
      this.renderer.setStyle(dialogContainer, 'overflow-y', 'hidden');
    }
    const mapContainer = (
      this.gisService.mapDisplay as LeafletMapDisplay
    ).map.getContainer();
    this.renderer.setStyle(mapContainer, 'cursor', 'pointer');
    (this.gisService.mapDisplay as LeafletMapDisplay).map.on(
      'click',
      this.locationOnClick
    );
  }

  locationOnClick = (e: LeafletMouseEvent) => {
    if (this.locationFormat() === 'Lat Lon') {
      this.locationSelected.set({
        selected: true,
        latitude: e.latlng.lat,
        longitude: e.latlng.lng,
        mgrs: null
      });
    } else {
      const mgrs = forward([e.latlng.lat, e.latlng.lng], 5);
      this.locationSelected.set({
        selected: true,
        latitude: e.latlng.lat,
        longitude: e.latlng.lng,
        mgrs: mgrs
      });
    }
  };

  selectLocation() {
    const latitude = this.locationSelected().latitude;
    const longitude = this.locationSelected().longitude;
    const mgrs = this.locationSelected().mgrs;

    this.restoreForm();

    this.createEntityForm.patchValue({
      latitude: latitude,
      longitude: longitude,
      mgrs: mgrs
    });
  }

  restoreForm() {
    this.toolbarExpanded.update((v) => !v);
    this.settingLocation.set(false);
    this.locationSelected.set({
      selected: false,
      latitude: null,
      longitude: null,
      mgrs: null
    });
    this.dialogRef.updatePosition();
    this.dialogRef.updateSize('1000px');
    this.dialogRef.disableClose = false;
    const dialogContainer = document.querySelector('.mat-mdc-dialog-surface');
    if (dialogContainer) {
      this.renderer.setStyle(dialogContainer, 'overflow-y', 'auto');
    }
    const mapContainer = (
      this.gisService.mapDisplay as LeafletMapDisplay
    ).map.getContainer();
    this.renderer.setStyle(mapContainer, 'cursor', '');
    (this.gisService.mapDisplay as LeafletMapDisplay).map.off(
      'click',
      this.locationOnClick
    );
  }

  protected readonly Object = Object;
}
