<div class="toolbar-wrapper">
  <div class="button-wrapper">
    <button
      (click)="toggleEntityToolbar()"
      [ngClass]="{'expanded': expanded}" class="shape-toolbar-button"
      matTooltip="{{expanded ? 'Collapse Entity Toolbar' : 'Expand Entity Toolbar'}}">
      <mat-icon class="button-icon" svgIcon="entity-tb"></mat-icon>
    </button>
  </div>
  <div *ngIf="expanded" class="toolbar">
    <div *ngFor="let buttonInfo of buttonInfoList">
      @if (!buttonInfo.hidden) {
        <div class="button-wrapper">
          <button
            [matTooltip]="buttonInfo.label"
            class="shape-toolbar-button" (click)="buttonAction(buttonInfo)"
            [ngClass]="{'expanded':buttonInfo.selected}">
            <mat-icon class="button-icon" svgIcon="{{ buttonInfo.svgIcon }}"></mat-icon>
          </button>
        </div>
      }

    </div>
  </div>
</div>
