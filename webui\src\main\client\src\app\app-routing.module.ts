import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { environment } from '../environments/environment';

const routes: Routes = [
  { path: '', redirectTo: environment.cursit ? '/cursit' : '/router', pathMatch: 'full' },
  {
    path: 'router',
    loadChildren: () => import('./proto-router/proto-router.module').then(m => m.ProtoRouterModule)
  },
  {
    path: 'cursit',
    loadChildren: () => import('./cursit/cursit.module').then(m => m.CursitModule)
  },
  {
    path: 'logs',
    loadChildren: () => import('./log/log.module').then(m => m.LogModule)
  },
  {
    path: 'settings',
    loadChildren: () => import('./data/settings/settings.module').then(m => m.SettingsModule)
  },
  {
    path: 'aco-ato',
    loadChildren: ()=> import('./aco-ato/aco-ato.module').then(m => m.AcoAtoModule)
  },
  {
    path: 'services',
    loadChildren: () => import('./battlespace/bsn.module').then(m => m.BsnModule)
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: true })],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
