/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/20/2023, 1:43 PM
 */

import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { GISEngine, GISENGINE } from './gis/interface/service/gis.service';

@Component({
    selector: 'vcci-select-gisengine-dialog',
    template: `
      <h2 mat-dialog-title>Select a GIS Engine</h2>
      <mat-dialog-content>
          <!-- Add your options here, for example, using radio buttons or checkboxes -->
          <mat-radio-group [(ngModel)]="selectedGISEngine">
              <mat-radio-button  [value]="GISENGINE.LUCIAD_2D">LUCIAD_2D</mat-radio-button>
              <mat-radio-button  [value]="GISENGINE.LUCIAD_3D">LUCIAD_3D</mat-radio-button>
              <mat-radio-button  [value]="GISENGINE.LEAFLET">LEAFLET</mat-radio-button>
          </mat-radio-group>
      </mat-dialog-content>
      <mat-dialog-actions>
          <button mat-button (click)="onCancel()">Cancel</button>
          <button mat-button [mat-dialog-close]="selectedGISEngine" cdkFocusInitial>OK</button>
      </mat-dialog-actions>
  `,
    standalone: false
})
export class SelectGisengineDialogComponent {
  selectedGISEngine: GISEngine;

  constructor(public dialogRef: MatDialogRef<SelectGisengineDialogComponent>) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  protected readonly GISENGINE = GISENGINE;
}
