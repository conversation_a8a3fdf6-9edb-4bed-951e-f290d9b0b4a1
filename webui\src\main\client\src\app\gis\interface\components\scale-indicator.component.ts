import { Component, Input, OnInit } from '@angular/core';
import { ScaleIndicator, ScaleIndicatorBackgroundStyle } from '../models/scale-indicator.model';

@Component({
    selector: 'vcci-scale-indicator[scaleIndicator]',
    styleUrls: ['./scale-indicator.component.css'],
    templateUrl: './scale-indicator.component.html',
    standalone: false
})
export class ScaleIndicatorComponent implements OnInit {
  @Input() scaleIndicator!: ScaleIndicator;
  @Input() numberOfForegroundTicks = 2;

  foregroundTicks: ScaleIndicatorBackgroundStyle[] = [];

  ngOnInit(): void {
    for (let i = 0; i < this.numberOfForegroundTicks; i += 1) {
      this.foregroundTicks.push({
        left: `${i * (100 / this.numberOfForegroundTicks)}%`,
        width: `${50 / this.numberOfForegroundTicks}%`
      });
    }
  }
}
