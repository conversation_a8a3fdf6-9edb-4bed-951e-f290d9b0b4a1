/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/20/2023, 11:42 AM
 */

import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'vcci-app-add-entities-dialog',
    templateUrl: './add-entities-dialog.component.html',
    styleUrls: ['./add-entities-dialog.component.css'],
    standalone: false
})
export class AddEntitiesDialogComponent {
  numberOfEntities: number;
  addLabels: boolean;

  constructor(
    public dialogRef: MatDialogRef<AddEntitiesDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: number
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }
}
