import {
  Component,
  EventEmitter,
  forwardRef,
  inject,
  Input,
  OnInit,
  Output,
  WritableSignal
} from '@angular/core';
import {
  ControlContainer,
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR
} from '@angular/forms';
import {
  compareValue,
  DataParameter,
  DEFAULT_NETWORK_INTERFACE,
  GROUP_CONTAINER,
  normalizeParamVal,
  ParameterValidator
} from '../bs-params.model';
import { nullishOrEmpty } from '../../../shared/util/util.model';
import { ParamListComponent } from './param-list.component';
import { ParamCheckboxComponent } from './param-checkbox.component';
import { ParamFileComponent } from './param-file.component';
import { ParamInputComponent } from './param-input.component';
import { updateFormValidators } from '../../../shared/form.model';
import { SFMap } from '@simfront/common-libs';
import { SelectionModel } from '@angular/cdk/collections';

@Component({
  selector: 'vcci-data-param',
  template: `
    @if (parameter.visible) {
      @if (parameter.list) {
        <vcci-param-list />
      } @else {
        @switch (parameter.type) {
          @case ('BOOLEAN') {
            <vcci-param-checkbox />
          }
          @case ('FILE') {
            <vcci-param-file />
          }
          @default {
            <vcci-param-input />
          }
        }
      }
    }
  `,
  styles: `
    :host {
      width: 100%;
    }
  `,
  imports: [
    ParamListComponent,
    ParamCheckboxComponent,
    ParamFileComponent,
    ParamInputComponent
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DataParameterComponent),
      multi: true
    }
  ]
})
export class DataParameterComponent implements OnInit, ControlValueAccessor {
  private networkInterface = inject(DEFAULT_NETWORK_INTERFACE, {
    optional: true
  });

  orGroups: WritableSignal<SFMap<SelectionModel<string>>> = inject(
    GROUP_CONTAINER,
    { skipSelf: true }
  );
  private _controlContainer: ControlContainer = inject(ControlContainer);
  private _parameter!: DataParameter;
  private _usingDefault = false;
  private _initialValue: string[] | string | boolean = '';

  value!: string[] | string | boolean;
  onChange: (value: string[] | string | boolean) => void = () => {};
  onTouched: () => void = () => {};

  get formControl(): FormControl {
    return this._controlContainer.control?.get(
      this.parameter.name
    ) as FormControl;
  }

  get container(): ControlContainer {
    return this._controlContainer;
  }

  get parameter() {
    return this._parameter;
  }

  get usingDefault(): boolean {
    return this._usingDefault;
  }

  get group() {
    return this.orGroups?.()[this.parameter.group?.name];
  }

  @Input({ required: true }) set parameter(parameter: DataParameter) {
    this._parameter = parameter;
    if (!this.parameter.editable) {
      this.formControl.disable();
    }
    if (!nullishOrEmpty(this.parameter)) {
      this.setValue(normalizeParamVal(this.parameter), false);
    }
    this.formControl.setValidators([ParameterValidator(this.parameter)]);
    this.formControl.updateValueAndValidity();
  }

  @Input() set usingDefault(value: boolean) {
    this._usingDefault = value;
  }

  @Output() getNext: EventEmitter<DataParameter> = new EventEmitter();
  @Output() networkInterfaceChanges = new EventEmitter<string>();

  ngOnInit(): void {
    if (this.formControl) {
      if (
        this.parameter.type === 'NETWORK_INTERFACE' &&
        this.networkInterface()
      ) {
        this.setValue(this.networkInterface());
      } else {
        this.setValue(normalizeParamVal(this.parameter, this.usingDefault));
      }
      this._initialValue = Array.isArray(this.value)
        ? [...this.value]
        : this.value;
      this._updateValidators();
      this.formControl.updateValueAndValidity();
    }
  }

  onGetNext() {
    this.getNext.emit(this.parameter);
  }

  private _updateValidators(): void {
    updateFormValidators(this.formControl, [
      ParameterValidator(this.parameter)
    ]);
  }

  writeValue(value: string[] | string | boolean): void {
    this.value = value;
    if (compareValue(this.value, this._initialValue)) {
      this.formControl.markAsPristine();
    } else {
      this.formControl.markAsDirty();
    }
  }

  registerOnChange(fn: (value: string | boolean) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setValue(value: string[] | string | boolean, emit = true): void {
    this.value = value;
    this._initialValue = this.value;
    if (!this.formControl) {
      return;
    }
    this.formControl.patchValue(value, { emitEvent: false });
    if (this.parameter.hasNext && !nullishOrEmpty(this.value) && emit) {
      this.getNext.emit(this.parameter);
    }
    if (this.parameter.group && this.parameter.type === 'BOOLEAN' && !!value) {
      this.setGroupValue(!!value);
    }
    this.onChange(value);
    this.onTouched();
  }

  setGroupValue(checked: boolean): void {
    if (checked) {
      this.group.select(this.parameter.name);
    } else {
      this.group.deselect(this.parameter.name);
    }
  }

  useDefault(): void {
    if (nullishOrEmpty(this.value) && !!this.parameter?.defaultValue) {
      const value = normalizeParamVal(this.parameter, true);
      this.setValue(value);
    }
  }
}
