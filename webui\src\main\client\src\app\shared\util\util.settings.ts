export class Colour {
  static readonly Blue        = new Colour('rgb(  0,   0, 255)', 'rgb(0,     0, 128)', 'Blue');
  static readonly Cyan        = new Colour('rgb(  0, 255, 255)', 'rgb(0,   128, 128)', 'Cyan');
  static readonly CrystalBlue = new Colour('rgb(128, 224, 255)', 'rgb(64,  112, 128)', 'Crystal Blue');
  static readonly Yellow      = new Colour('rgb(255, 255,   0)', 'rgb(128, 128,   0)', 'Yellow');
  static readonly LightYellow = new Colour('rgb(255, 255, 128)', 'rgb(128, 128,  64)', 'Light Yellow');
  static readonly NeonGreen   = new Colour('rgb(  0, 255,   0)', 'rgb(  0, 128,   0)', 'Neon Green');
  static readonly BambooGreen = new Colour('rgb(170, 255, 170)', 'rgb( 85, 128,  85)', 'Bamboo Green');
  static readonly Red         = new Colour('rgb(255,   0,   0)', 'rgb(128,   0,   0)', 'Red');
  static readonly Salmon      = new Colour('rgb(255, 128, 128)', 'rgb(128,  64,  64)', 'Salmon');
  static readonly Magenta     = new Colour('rgb(255,   2, 205)', 'rgb(128,   1, 103)', 'Magenta');
  static readonly PlumRed     = new Colour('rgb(128,   0, 128)', 'rgb( 64,   0,  64)', 'Plum Red');
  static readonly LightOrchid = new Colour('rgb(226, 159, 255)', 'rgb(113,  80, 128)', 'Light Orchid');
  static readonly Safari      = new Colour('rgb(128,  98,  16)', 'rgb( 64,  49,   8)', 'Safari');
  static readonly Khaki       = new Colour('rgb(210, 176, 106)', 'rgb(105,  88,  53)', 'Khaki');
  static readonly Black       = new Colour('rgb(  0,   0,   0)', 'rgb(128, 128, 128)', 'Black');
  static readonly White       = new Colour('rgb(255, 255, 255)', 'rgb(128, 128, 128)', 'White');
  static readonly OffWhite    = new Colour('rgb(239, 239, 239)', 'rgb(119, 119, 119)', 'Off White');
  static readonly BaliHai     = new Colour('rgb(131, 149, 170)', 'rgb( 66,  75,  85)', 'Bali Hai');

  private constructor(
    public readonly primary: string,
    public readonly secondary: string,
    public readonly display: string
  ) {}
}

export interface FeatureProperties {
  code:       string;
  type:       ObjectTypeCategoryCode;
  modifiers:  any;
  id:         number;
  layer:      string;
  visible:    boolean;
  contextId:  number;
  sandboxed?: boolean;
}
export enum ObjectTypeCategoryCode {
  Unit            = 'OR',
  Materiel        = 'MA',
  ControlFeature  = 'FE'
}

