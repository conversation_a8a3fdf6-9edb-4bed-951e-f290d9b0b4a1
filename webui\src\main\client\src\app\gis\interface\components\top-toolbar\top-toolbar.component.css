.top-toolbar {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: auto;
  transition: all 0.3s ease;
  pointer-events: none;
}

.toolbar-container {
  display: flex;
  align-items: center;
  height: 40px;
  position: relative;
  width: 100%;
  overflow: hidden;
  background: var(--background-gradient-light);
  border-radius: 4px;
  transform-origin: top center;
  transition: transform 0.3s ease;
  pointer-events: auto;
}

.collapsed {
  transform: scaleY(0);
}

.expanded {
  transform: scaleY(1);
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.icon-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.menu-item {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
  border-radius: 0;
  min-width: 0;
  white-space: nowrap;
  transition: font-size 0.3s ease;
}

.menu-button:hover {
  background-color: var(--calian-gradient);
}

.menu-button .mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  margin-left: 4px;
  flex-shrink: 0;
}

::ng-deep .mat-mdc-menu-panel {
  margin-top: 4px !important;
}

.toggle-button {
  margin-top: -30px;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  pointer-events: auto;
}

.toggle-button > mat-icon {
  cursor: pointer;
  pointer-events: all;
  background-color: var(--calian-primary) !important;
  fill: var(--calian-primary) !important;
  border-radius: 50%;
}

.collapsedToggle {
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.expandedToggle {
  transform: translateY(50%);
  transition: transform 0.3s ease;
}

::ng-deep .mat-mdc-menu-content {
  background-color: var(--calian-primary);
}

.toolbar-container .mat-divider.mat-divider-vertical {
  height: 24px;
  border-right-width: 1px;
  color: rgba(255, 255, 255, 0.3);
}

@media (max-width: 1366px) {
  .menu-button {
    font-size: 0;
  }

  .menu-button .mat-icon {
    margin: 0;
  }
}

@media (max-width: 1027px) {
  .top-toolbar {
    top: 15%;
    left: 40px;
    flex-direction: row;
  }

  .toolbar-container {
    flex-direction: column;
    height: 100%;
    max-width: 50px;
    transform-origin: left center;
    transition: transform 0.3s ease;
  }

  .toolbar-container .mat-divider.mat-divider-vertical {
    height: 1px;
    width: 80%;
    margin: 4px auto;
    border-right-width: 0;
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: rgba(255, 255, 255, 0.3);
  }

  .collapsed {
    transform: scaleX(0);
  }

  .expanded {
    transform: scaleX(1);
  }

  .toggle-button {
    margin-left: -30px;
    margin-top: 0;
  }

  .collapsedToggle {
    transform: translateX(-50%) rotate(-90deg);
    transition: transform 0.3s ease;
  }

  .expandedToggle {
    transform: translateX(50%) rotate(-90deg);
    transition: transform 0.3s ease;
  }
}
