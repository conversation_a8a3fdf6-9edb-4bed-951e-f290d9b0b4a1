.component {
  margin: 0;
  border-top: 2px solid #404040;
  color: var(--text);
  background-color: rgba(var(--secondary-background-dark-rgb), var(--opacity));
}

.spinner {
  max-width: 24px;
  max-height: 24px;
}


.selected {
  border: var(--active) solid 2px;
}

.active:hover {
  cursor: pointer;
}

.header {
  display: flex;
  align-items: center;
  width: calc(100% - 10px);
  height: 28px;
  margin-top: 2px;
}

.header > button:first-child {
  margin-left: 5px;
}

.header p {
  flex-grow: 1;
}

.name {
  margin-left: 10px;
  margin-bottom: 0;
}

.content {
  display: flex;
  justify-content: center;
  padding-left: 8px;
  padding-right: 8px;
  gap: 2px;
  margin: 5px 0;
}

.content button {
  width: 36px !important;
  height: 36px !important;
  padding: 6px !important;
  background-color: var(--secondary-background-light);
}

.footer {
  margin-top: 4px;
  padding-left: 10px;
  font-size: 10px;
  font-style: italic;
}

mat-card {
  padding: 0;
  margin: 0;
  box-shadow: none;
  border: none;
  background: transparent;
}

@container (max-width: 100px) {
  .header > button:first-child {
    margin-left: 13px;
  }

  .content, .footer, .name, .icon {
    display: none;
  }
}

.content > button {
  border: thin solid var(--divider-light);
}

.divider {
  border: thin solid var(--divider-light);
  margin: 0 5px;
}
