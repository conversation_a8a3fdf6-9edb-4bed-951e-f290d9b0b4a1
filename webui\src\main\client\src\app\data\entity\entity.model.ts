import { createActionGroup, emptyProps } from '@ngrx/store';

import {
  createEverything,
  optionalPayload,
  payload
} from '@simfront/common-libs';

import { environment } from '../../../environments/environment';

import { DataParameters } from '../bs-params/bs-params.model';
import { RootState } from '../index';

import { UniqueId } from './id.model';
import { ShapeType } from '../../gis/interface/models/map-element.model';
import { Coordinate } from '../../cursit/models/cursit.model';
import { Polygon, Polyline } from 'leaflet';
import { PopoutWindowConfig } from '../../shared/popout-window/popout-window.model';

//<editor-fold desc="Geo">
export enum Shapes {
  Point = 'com.simfront.appobjmodel.obj.location.BattleSpacePoint',
  PolygonArea = 'com.simfront.appobjmodel.obj.location.BattleSpacePolygonArea',
  PointLocation = 'com.simfront.appobjmodel.obj.location.BattleSpacePointLocation',
  Line = 'com.simfront.appobjmodel.obj.location.BattleSpaceLine',
  FanArea = 'com.simfront.appobjmodel.obj.location.BattleSpaceFanArea',
  CorridorArea = 'com.simfront.appobjmodel.obj.location.BattleSpaceCorridorArea',
  Ellipse = 'com.simfront.appobjmodel.obj.location.BattleSpaceEllipse'
}

/**
 * Latitude and Longitude point.
 */
export interface LatLng {
  latitude: number;
  longitude: number;
}

/**
 * Simple point.
 */
export interface Point extends LatLng {
  '@class': Shapes.Point;
}

/**
 * Point extends {@link LatLng}
 * adds altitude, bearing and speed.
 */
export interface PointLocation extends Omit<Point, '@class'> {
  '@class': Shapes.PointLocation;
  altitude: number;
  bearing: number;
  speed: number;
}

/**
 * Basic line, multiple {@link LatLng} points.
 */
export interface Line {
  '@class': Shapes.Line;
  linePoints: LatLng[];
}

export interface EntityDetails {
  key: string;
  val: string | number;
}

export interface BoundingBox {
  north: number;
  south: number;
  east: number;
  west: number;
}

/**
 * Geometric Fan Area.
 */
export interface FanArea {
  '@class': Shapes.FanArea;
  vertexPoint: LatLng;
  minimumRange: number;
  maximumRange: number;
  orientationAngle: number;
  sectorAngle: number;
}

/**
 * Geometric Corridor Area.
 */
export interface CorridorArea {
  '@class': Shapes.CorridorArea;
  centerLinePoints: LatLng[];
  width: number;
}

/**
 * Geometric Ellipse.
 */
export interface Ellipse {
  '@class': Shapes.Ellipse;
  centerPoint: LatLng;
  firstDiameterPoint: LatLng;
  secondDiameterPoint: LatLng;
}

export interface PolygonArea {
  '@class': Shapes.PolygonArea;
  linePoints: LatLng[];
}

export interface LayerIconLocation {
  battleSpaceObjectId: number;
  layerId: number;
  time: number;
}

export type Shape =
  | Point
  | PointLocation
  | Line
  | FanArea
  | CorridorArea
  | Ellipse
  | PolygonArea;
/**
 * Location or geometric shape that is included in entity.
 */
export type Location = Shape & LayerIconLocation;
//</editor-fold>

//<editor-fold desc="Entity">
/**
 * Basic layer.
 */
export interface Layer {
  layerId: number;
  layerName: string;
  parentId: string;
}

/**
 * Possible type of mil symbol.
 */
export interface IconCodeTypeMap {
  MILSTD_2525B: string;
  APP6A: string;
}

/**
 * Icon location,
 * Use this to draw the mil symbol on the cursit.
 */
export interface IconLocation {
  catCode: string;
  iconCodeTypeMap: IconCodeTypeMap;
  location: Location;
}

/**
 * Mil symbol entity.
 * Logic to draw is:
 *  1. Use the icon in layerIcons: [layerName]: IconLocation, go to step 2 only if there's no key matching. Null is valid.
 *  2. Use the default layer icon.
 */
export interface Entity {
  uniqueId: UniqueId;
  name: string;
  defaultLayerIcon: IconLocation;
  layers: Layer[];
  layerIcons: { [key: number]: IconLocation };
}

/**
 * External Object returned from '/{id}/externalobjects'
 */
export interface ExternalObject {
  iconCodeType: IconCodeTypeMap | null;
  objectId: UniqueId;
  objectName: string;
  objectType: string;
  parameters: DataParameters;
}

/**
 * Used by the CURSIT to draw the GIS Element.
 */
export interface BaseEntityInfo {
  id: string;
  name: string;
  symbolCode: string;
  catCode: string;
  shapeType: ShapeType;
}

export interface PointEntityInfo extends BaseEntityInfo {
  shapeType: ShapeType.POINT;
  coordinates: Coordinate;
  speed?: number;
  bearing?: number;
}

export interface PolylineEntityInfo extends BaseEntityInfo {
  shapeType: ShapeType.POLYLINE;
  coordinates: Coordinate[];
}

export interface PolygonEntityInfo extends BaseEntityInfo {
  shapeType: ShapeType.POLYGON;
  coordinates: Coordinate[];
}

export interface CorridorAreaInfo extends BaseEntityInfo {
  shapeType: ShapeType.GEO_BUFFER;
  coordinates: Coordinate[];
  width: number;
}

export interface EllipseEntityInfo extends BaseEntityInfo {
  shapeType: ShapeType.ELLIPSE;
  coordinates: Coordinate[];
}

export interface ArcEntityInfo extends BaseEntityInfo {
  shapeType: ShapeType.ARC_BAND;
  coordinates: Coordinate;
  maximumRange: number;
  minimumRange: number;
  orientationAngle: number;
  sectorAngle: number;
}

export type EntityInfo =
  | PointEntityInfo
  | PolylineEntityInfo
  | PolygonEntityInfo
  | CorridorAreaInfo
  | EllipseEntityInfo
  | ArcEntityInfo;

export interface createEntityData {
  name: string;
  symbolSet: string;
  affiliation: string;
  dimension: string;
  functionID: string;
  status: string;
  echelon: string;
  mobility: string;
  operationalCondition: string;
  longitude: string;
  latitude: string;
  speed: number;
  bearing: number;
  altitude: number;
}

export enum CodeType {
  MILSTD_2525B,
  MILSTD_2525C,
  APP6A
}

export interface CreateEntity {
  jEntityNameText: string;
  jIconCode: string;
  jIconType: CodeType;
  jLocation: PointLocation;
}

/**
 * Type guards for EntityInfo variants
 */
export const isPointEntityInfo = (
  entity: EntityInfo
): entity is PointEntityInfo => entity.shapeType === ShapeType.POINT;

export const isPolylineEntityInfo = (
  entity: EntityInfo
): entity is PolylineEntityInfo => entity.shapeType === ShapeType.POLYLINE;

export const isPolygonEntityInfo = (
  entity: EntityInfo
): entity is PolygonEntityInfo => entity.shapeType === ShapeType.POLYGON;

export const isCorridorAreaEntityInfo = (
  entity: EntityInfo
): entity is CorridorAreaInfo => entity.shapeType === ShapeType.GEO_BUFFER;

export const isEllipseEntityInfo = (
  entity: EntityInfo
): entity is EllipseEntityInfo => entity.shapeType === ShapeType.ELLIPSE;

export const isArcEntityInfo = (entity: EntityInfo): entity is ArcEntityInfo =>
  entity.shapeType === ShapeType.ARC_BAND;

export const isPointLocation = (obj: unknown): obj is PointLocation => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'latitude' in obj &&
    'longitude' in obj &&
    'bearing' in obj &&
    'altitude' in obj
  );
};

export const isFanArea = (obj: unknown): obj is FanArea => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'vertexPoint' in obj &&
    'minimumRange' in obj &&
    'maximumRange' in obj &&
    'orientationAngle' in obj &&
    'sectorAngle' in obj
  );
};

export const isEllipse = (obj: unknown): obj is Ellipse => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'centerPoint' in obj &&
    'firstDiameterPoint' in obj &&
    'secondDiameterPoint' in obj
  );
};

export const isCorridorArea = (obj: unknown): obj is CorridorArea => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'centerLinePoints' in obj &&
    'width' in obj
  );
};

export const isPoly = (obj: unknown): obj is Polyline | Polygon => {
  return typeof obj === 'object' && obj !== null && 'linePoints' in obj;
};

//</editor-fold>

//<editor-fold desc="Type Guard">
/**
 * Check if is entity or icon location object.
 * @param val Entity or IconLocation object.
 */
export const isLayerIcon = (val: Entity | IconLocation): val is IconLocation =>
  val['catCode'] !== undefined &&
  val['iconCodeTypeMap'] !== undefined &&
  val['location'] !== undefined;
//</editor-fold>

//<editor-fold desc="Helpers">
/**
 * Convert single Lat Long to coordinate.
 * @param latLng Lat long object.
 * @param altitude Altitude, can be undefined and will get ignored.
 */
const latLongToCoordinate = (latLng: LatLng, altitude?: number): Coordinate =>
  !!altitude
    ? [latLng.longitude, latLng.latitude, altitude]
    : [latLng.longitude, latLng.latitude];

/**
 * Convert Lat Long(s) to coordinates.
 * @param latLng Lat Long(s)
 * @param altitude Altitude, can be undefined to ignore.
 */
export const latLongToCoordinates = (
  latLng: LatLng[] | LatLng,
  altitude?: number
): Coordinate | Coordinate[] => {
  if (Array.isArray(latLng)) {
    return latLng.map((ll) => latLongToCoordinate(ll, altitude));
  } else {
    return latLongToCoordinate(latLng, altitude);
  }
};

/**
 * Get the layer icon based on the layer id, will use defaultLayerIcon if no id matches.
 * This might return null... nothing should be drawn in this case.
 * @param entity Entity
 * @param layerId Layer id, can be undefined if you want to get default.
 */
export const getLayerIcon = (
  entity: Entity | IconLocation,
  layerId?: string
): IconLocation | null =>
  isLayerIcon(entity)
    ? entity
    : !!layerId && entity.layerIcons.hasOwnProperty(layerId)
      ? entity.layerIcons[layerId]
      : entity.defaultLayerIcon;

/**
 * Return the icon code. This can be null.
 * @param entity Entity
 * @param layerId The layer id, this can undefined (will use default).
 * @param iconType 'MILSTD_2525B' | 'APP6A'
 */
export const extractSymbolCode = (
  entity: Entity | IconLocation,
  layerId?: string,
  iconType: 'MILSTD_2525B' | 'APP6A' = 'MILSTD_2525B'
): string | null => {
  const layerIcon: IconLocation | null = getLayerIcon(entity, layerId);
  return layerIcon?.iconCodeTypeMap[iconType];
};

/**
 * Get the shape type and coordinates.
 * @param location Icon location.
 */
export const getShapeInfo = (
  location: Location
): {
  coordinates: Coordinate | Coordinate[];
  shapeType: ShapeType;
  speed?: number;
  bearing?: number;
  width?: number;
  maximumRange?: number;
  minimumRange?: number;
  orientationAngle?: number;
  sectorAngle?: number;
} | null => {
  switch (location['@class']) {
    case Shapes.CorridorArea:
      return {
        coordinates: location.centerLinePoints.map(
          (point) => [point.latitude, point.longitude] as Coordinate
        ),
        shapeType: ShapeType.GEO_BUFFER,
        width: location.width
      };
    case Shapes.Ellipse:
      return {
        coordinates: [
          [location.centerPoint.latitude, location.centerPoint.longitude],
          [
            location.firstDiameterPoint.latitude,
            location.firstDiameterPoint.longitude
          ],
          [
            location.secondDiameterPoint.latitude,
            location.secondDiameterPoint.longitude
          ]
        ],
        shapeType: ShapeType.ELLIPSE
      };
    case Shapes.FanArea:
      return {
        coordinates: [
          [location.vertexPoint.latitude, location.vertexPoint.longitude]
        ],
        shapeType: ShapeType.ARC_BAND,
        maximumRange: location.maximumRange,
        minimumRange: location.minimumRange,
        orientationAngle: location.orientationAngle,
        sectorAngle: location.sectorAngle
      };
    case Shapes.Line:
      return {
        coordinates: location.linePoints.map(
          (point) => [point.latitude, point.longitude] as Coordinate
        ),
        shapeType: ShapeType.POLYLINE
      };
    case Shapes.PolygonArea:
      return {
        coordinates: location.linePoints.map(
          (point) => [point.latitude, point.longitude] as Coordinate
        ),
        shapeType: ShapeType.POLYGON
      };
    case Shapes.PointLocation:
      return {
        coordinates: [location.latitude, location.longitude, location.altitude],
        shapeType: ShapeType.POINT,
        bearing: location.bearing,
        speed: location.speed
      };
    case Shapes.Point:
      return {
        coordinates: [location.latitude, location.longitude],
        shapeType: ShapeType.POINT
      };
    default:
      return null;
  }
};

/**
 * Get the entity info in one go.
 * Everything required to create element.
 * @param id Unique ID of entity
 * @param name Entity name
 * @param layerIcon Layer Icon
 * @param iconType 'MILSTD_2525B' | 'APP6A'
 */
export const extractEntityInfo = (
  id: UniqueId,
  name: string,
  layerIcon: IconLocation | null,
  iconType: 'MILSTD_2525B' | 'APP6A' = 'MILSTD_2525B'
): null | EntityInfo => {
  if (!layerIcon) {
    console.error('Failed to extract entity info. No layer icon.');
    return null;
  }
  const symbolCode = layerIcon.iconCodeTypeMap[iconType];
  if (!symbolCode) {
    console.error('Failed to extract entity info. No symbol code.');
    return null;
  }
  const shapeInfo = getShapeInfo(layerIcon.location);
  if (!shapeInfo) {
    console.error('Failed to extract entity info. No shape information.');
    return null;
  }

  return {
    id,
    name,
    symbolCode,
    catCode: layerIcon.catCode,
    ...shapeInfo
  } as EntityInfo;
};

export const externalObjToEntity = (obj: ExternalObject): Entity => ({
  name: obj.objectName,
  uniqueId: obj.objectId,
  layers: [],
  defaultLayerIcon: {
    iconCodeTypeMap: {
      MILSTD_2525B: '',
      APP6A: ''
    },
    catCode: '',
    location: {
      '@class': Shapes.PointLocation,
      bearing: 0,
      altitude: 0,
      speed: 0,
      longitude: 0,
      latitude: 0,
      battleSpaceObjectId: 0,
      layerId: 0,
      time: 0
    }
  },
  layerIcons: {}
});
//</editor-fold>

export const {
  reducer: entityReducer,
  facade: EntityFacadeBase,
  initialState
} = createEverything<RootState, Entity, 'entity', never, string>(
  'entity',
  (e) => e.uniqueId,
  `${environment.origin}/entities`,
  (state) => state.entity ?? initialState
);

export const EntityActions = createActionGroup({
  source: 'entity',
  events: {
    Notification: payload<Entity>(),
    'Open Create Entity Dialog': (
      payload?: Partial<createEntityData>,
      milStd?: string
    ) => ({ payload, milStd }),
    'Create Entity Dialog Dismissed': emptyProps(),
    'Open Entity Details Dialog': (
      id?: UniqueId,
      popout?: PopoutWindowConfig
    ) => ({ id, popout }),
    'Save Entity': payload<CreateEntity>(),
    'Save Entity Success': emptyProps()
  }
});
