import { NgForOf } from '@angular/common';
import { Directive, Host, Input, NgIterable } from '@angular/core';

// eslint-disable-next-line @angular-eslint/directive-selector
@Directive({
    selector: '[ngForTrackByProperty]',
    standalone: false
})
export class NgForTrackByPropertyDirective<T> {
  @Input() ngForOf!: NgIterable<T>;
  @Input() ngForTrackByProperty!: keyof T;

  constructor(@Host() ngForOfDir: NgForOf<T>) {
    ngForOfDir.ngForTrackBy = (_, item: T): T[keyof T] => item[this.ngForTrackByProperty];
  }
}
