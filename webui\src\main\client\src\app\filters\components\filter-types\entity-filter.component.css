:host {
  position: relative;
}

.filter-button {
  position: absolute;
  right: 10px;
  top: 3px;
  z-index: 1;
}

::ng-deep .line-shadow {
  box-shadow: none !important;
}

::ng-deep .line-shadow > td {
  border-bottom-width: 1px !important;
  border-bottom-style: solid !important;
  border-bottom-color: var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12))) !important;
}

::ng-deep .route-desc {
  margin-right: 10px;
}

::ng-deep .filter-menu {
  max-width: 200px;
}

::ng-deep .filter-menu-item {
  max-width: 200px;
}
