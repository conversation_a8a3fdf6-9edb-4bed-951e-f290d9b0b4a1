import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { createEffect, ofType } from '@ngrx/effects';

import { PayloadTypedAction, SFMap } from '@simfront/common-libs';

import {
  combineLatestWith,
  concatMap,
  filter,
  map,
  mergeMap,
  Observable,
  switchMap,
  tap,
  withLatestFrom
} from 'rxjs';

import { ConnectionActions } from '../connection/connection.model';
import {
  Entity,
  ExternalObject,
  externalObjToEntity
} from '../entity/entity.model';

import { AssociationDialog } from './association/association.dialog';
import {
  Candidate,
  OutboundObject,
  RequestType
} from './association/association.model';
import { EndpointForm } from './endpoint-form.component';
import {
  ConfigurePayload,
  Endpoint,
  EndpointActions,
  EndpointFacadeBase,
  EntityMetrics,
  getEndpointId,
  Instruction,
  instructionEqualState,
  selectEndpointAssociation
} from './endpoint.model';
import { FilterGroup } from './filter.model';
import { MetricsDialog } from './metrics/metrics.dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { BSParamDialogPayload } from '../bs-params/bs-params.store';
import { BSDialogComponent } from 'src/app/battlespace/node/bs-dialog.component';

@Injectable({ providedIn: 'root' })
export class EndpointFacade extends EndpointFacadeBase {
  override facadeDialogContent = EndpointForm;
  selected = new SelectionModel<string>(false);

  constructor(private http: HttpClient) {
    super();
  }

  // <editor-fold desc="Selectors">
  endpointByConnection$ = this.all$.pipe(
    map((endpoints) =>
      endpoints.reduce<SFMap<Endpoint[]>>(
        (acc, e) => ({
          ...acc,
          [e.connection]: [...(acc[e.connection] || []), e]
        }),
        {}
      )
    )
  );

  selectedEndpoints$ = this.selectedIds$.pipe(
    combineLatestWith(this.entities$),
    map(([selected, entities]) =>
      selected.map((s) => entities[s]).filter((s) => !!s)
    )
  );

  //called when either an endpoint is selected in the router window or a connection is selected in the connection panel
  endpointSelected(endpointId: string): void {
    if (this.selected.isSelected(endpointId)) {
      this.deselectOne(endpointId);
    } else {
      this.selectOne(endpointId);
    }
    this.selected.toggle(endpointId);
  }

  candidates$ = this.store
    .select(selectEndpointAssociation)
    .pipe(map((assoc) => assoc?.candidates));
  associations$ = this.store
    .select(selectEndpointAssociation)
    .pipe(map((assoc) => assoc?.associations));

  assocBusy$ = this.store
    .select(selectEndpointAssociation)
    .pipe(map((assoc) => assoc?.busy));

  unassociatedCandidates$ = this.candidates$.pipe(
    combineLatestWith(this.associations$),
    map(([candidates, associations]) =>
      candidates.filter(
        (c) => !associations.some((a) => a.dstId === c.objectId)
      )
    )
  );

  endpoint$ = (id: string): Observable<Endpoint> =>
    this.entities$.pipe(map((entities) => entities[id]));

  // </editor-fold>

  // <editor-fold desc="Actions">
  restart(id: string): void {
    this.store.dispatch(EndpointActions.restart(id));
  }

  openCreateEndpointDialog(payload?: BSParamDialogPayload): void {
    this.store.dispatch(EndpointActions.openCreateDialog(payload));
  }

  openDeleteDialog(id: string): void {
    this.openDialog({ crud: 'Delete', quantity: 'One', id });
  }

  openMetricsDialog(id: string): void {
    this.store.dispatch(EndpointActions.openMetricsDialog(id));
  }

  instruct(id: string, instruction: Instruction): void {
    this.store.dispatch(EndpointActions.instruct({ id, instruction }));
  }

  openAssocDialog(id: string): void {
    this.store.dispatch(EndpointActions.openAssocDialog(id));
  }

  configureEndpoint(configurePayload: ConfigurePayload): void {
    this.store.dispatch(EndpointActions.configure(configurePayload));
  }
  // </editor-fold>

  // <editor-fold desc="API">
  readCandidates(id: string, type: RequestType): void {
    this.store.dispatch(EndpointActions.readCandidates({ id, type }));
  }

  readAssociations(id: string): void {
    this.store.dispatch(EndpointActions.readAssociations(id));
  }

  applyAssociations(id: string): void {
    this.store.dispatch(EndpointActions.applyAssociations(id));
  }

  associate(oo: OutboundObject): void {
    this.store.dispatch(EndpointActions.associate(oo));
  }

  dissociate(id: string): void {
    this.store.dispatch(EndpointActions.dissociate(id));
  }

  public getDetailedMetrics(id: string): Observable<EntityMetrics[]> {
    return this.http.get<EntityMetrics[]>(`${this.endpoint}/${id}/metrics`);
  }

  public getExternalObjects(
    id: string,
    type: 'SINGLE_SENDER' | 'ALL_SENDERS' | 'SINGLE_LISTENER' | 'ALL_LISTENERS'
  ): Observable<Entity[]> {
    return this.http
      .get<
        ExternalObject[]
      >(`${this.endpoint}/${id}/externalobjects?type=${type}`)
      .pipe(map((objs) => objs.map(externalObjToEntity)));
  }

  public readCandidatesAPI(
    id: string,
    type: RequestType
  ): Observable<Candidate[]> {
    return this.http.get<Candidate[]>(
      `${this.endpoint}/${id}/candidates?type=${type}`
    );
  }

  public readAssociationsAPI(id: string): Observable<OutboundObject[]> {
    return this.http.get<OutboundObject[]>(
      `${this.endpoint}/${id}/associations`
    );
  }

  public getEndpointFilters(id: string): Observable<FilterGroup[]> {
    return this.http.get<FilterGroup[]>(`${this.endpoint}/${id}/filters`);
  }

  private _endpointInstruction(
    id: string,
    instruction: Instruction
  ): Observable<Endpoint> {
    return this.http.post<Endpoint>(
      `${this.endpoint}/${id}/transfercmd?cmd=${instruction}`,
      undefined
    );
  }

  private _configureEndpoint(payload: ConfigurePayload): Observable<Endpoint> {
    return this.http.post<Endpoint>(
      `${this.endpoint}/${payload.id}/configure`,
      payload
    );
  }

  // strip off packets passed/failed from data receiver endpoints
  override inboundMapOne<P, Type extends string>(
    t: Endpoint,
    pta: PayloadTypedAction<P, Type>
  ): Endpoint {
    if (!t.metrics || t.capability === 'DATA_SENDER') {
      return t;
    }
    const { packetsPassed, packetsFailed, ...restMetrics } = t.metrics;
    return {
      ...t,
      metrics: restMetrics
    };
  }

  // </editor-fold>

  // <editor-fold desc="Effects">
  openCreateDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.openCreateDialog),
      switchMap((action) =>
        this.dialog
          .open(BSDialogComponent, { width: '500px', data: action.payload })
          .afterClosed()
      ),
      map(() => EndpointActions.createDialogDismissed())
    )
  );

  restart$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.restart),
      map((action) => action.payload),
      switchMap((id) =>
        this._endpointInstruction(id, 'PAUSE').pipe(
          concatMap((endpoint) => [
            this.actions.updateOneReceived(endpoint),
            EndpointActions.instruct({
              id: getEndpointId(endpoint),
              instruction: 'PLAY'
            })
          ])
        )
      )
    )
  );

  openMetricsDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.openMetricsDialog),
      mergeMap((action) =>
        this.dialog
          .open(MetricsDialog, { data: action.payload, autoFocus: false })
          .afterClosed()
      ),
      map(() => EndpointActions.dismissMetricsDialog())
    )
  );

  onConnectionCreate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ConnectionActions.createSpecialSuccess),
      map((action) => action.payload.battleSpaceEndpoint),
      filter((bse) => !!bse),
      map((bse) => this.actions.createOne(bse))
    )
  );

  instruct$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.instruct),
      map((action) => action.payload),
      switchMap(({ id, instruction }) =>
        this._endpointInstruction(id, instruction).pipe(
          concatMap((result) => [
            EndpointActions.instructSuccess({ id, instruction }),
            this.actions.updateOneReceived(result)
          ])
        )
      )
    )
  );

  onInstruct$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.instruct),
      map((action) => action.payload),
      withLatestFrom(this.entities$),
      map(([payload, entities]) => ({
        ...payload,
        endpoint: entities[payload.id]
      })),
      filter(
        ({ endpoint, instruction }) =>
          !!endpoint && !instructionEqualState(endpoint, instruction)
      ),
      map(({ endpoint }) =>
        this.actions.updateOneReceived({ ...endpoint, state: 'REQUESTING' })
      )
    )
  );

  openAssocDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.openAssocDialog),
      map((action) => action.payload),
      tap((id) => {
        this.readCandidates(id, 'ALL_LISTENERS');
        this.readAssociations(id);
      }),
      mergeMap((data) =>
        this.dialog.open(AssociationDialog, { data }).afterClosed()
      ),
      map(() => EndpointActions.assocDialogDismissed())
    )
  );

  readCandidates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.readCandidates),
      map((action) => action.payload),
      switchMap(({ type, id }) =>
        this.readCandidatesAPI(id, type).pipe(
          map((result) => EndpointActions.readCandidatesSuccess(result))
        )
      )
    )
  );

  readAssociations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.readAssociations),
      map((action) => action.payload),
      switchMap((id) =>
        this.readAssociationsAPI(id).pipe(
          map((result) => EndpointActions.readAssociationsSuccess(result))
        )
      )
    )
  );

  applyAssociations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.applyAssociations),
      map((action) => action.payload),
      withLatestFrom(this.associations$),
      switchMap(([id, oos]) =>
        this.http
          .post<unknown>(
            `${this.endpoint}/${id}/associations`,
            oos.map((oo) => ({ ...oo, srcId: oo.srcId }))
          )
          .pipe(map(() => EndpointActions.applyAssociationsSuccess()))
      )
    )
  );

  configureEndpoint$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EndpointActions.configure),
      map((action) => action.payload),
      switchMap((payload) => this._configureEndpoint(payload)),
      map((result) => this.actions.readOneReceived(result))
    )
  );

  // </editor-fold>
}
