import { createFeature, createReducer, on } from '@ngrx/store';
import { mapActions } from '../actions/map.actions';

export interface MouseCoordinateState {
  mouseCoordinate: string;
}

const initialState: MouseCoordinateState = { mouseCoordinate: '' };

export const scaleMouseCoordinateFeature = createFeature({
  name: 'mousemove',
  reducer: createReducer(
    initialState,
    on(mapActions.mouseMove, (state, action) => ({ ...state, mouseCoordinate: action.payload }))
  )
});

export const {
  name,
  reducer,
  selectMousemoveState,
  selectMouseCoordinate
} = scaleMouseCoordinateFeature;
