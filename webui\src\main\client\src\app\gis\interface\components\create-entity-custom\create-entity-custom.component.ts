import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { EntityFormField } from '../../../../milstd/models/milstd-2525.model';

@Component({
  selector: 'nexus-create-entity-custom',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatRadioModule
  ],
  templateUrl: './create-entity-custom.component.html',
  styleUrl: './create-entity-custom.component.css'
})
export class CreateEntityCustomComponent implements OnInit {
  @Input() parentForm: FormGroup;
  @Input() entityType: string = 'Unit';
  @Input() milsymFormFields: EntityFormField<string>[] = [];

  ngOnInit() {
    // Additional initialization if needed
  }
}
