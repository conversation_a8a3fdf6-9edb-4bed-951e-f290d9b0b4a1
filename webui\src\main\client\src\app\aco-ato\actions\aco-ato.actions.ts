import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { FilePayload } from '../../shared/directives/file-payload.directive';

export const acoAtoActions = createActionGroup({
  source: 'ACO/ATO',
  events: {
    'Open Import Dialog': emptyProps(),
    'Execute Rap Files': props<{ filePayload: Record<string, FilePayload> }>(),
    'Save File Success': emptyProps(),
    'Save File fail': props<{ error: any }>(),
    'Open Rap Times Dialog': emptyProps(),
    'Save Rap time': (time: number) => ({ time }),
    'Save Rap time Success': emptyProps(),
    'Save Rap time fail': props<{ error: any }>()
  }
});
