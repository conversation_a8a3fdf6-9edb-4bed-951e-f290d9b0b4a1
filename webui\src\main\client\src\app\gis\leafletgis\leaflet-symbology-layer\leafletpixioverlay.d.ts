// import * as L from 'leaflet';
// import { LatLng, LatLngExpression, Point } from 'leaflet';
// import * as PIXI from 'pixi.js';
// import { LeafletEntity } from '../LeafletEntity';
// import { Container } from 'pixi.js';
//
// // declare function setEventSystem(
// renderer: PIXI.Renderer, destroyInteractionManager: boolean, autoPreventDefault: boolean): void;
//
// // declare function projectionZoom(map: L.Map): number;
//
// interface Utils {
//   latLngToLayerPoint(latLng: LatLngExpression, zoom?: number): L.Point;
//   layerPointToLatLng(point: L.Point, zoom?: number): L.LatLng;
//   getScale(zoom: number): number;
//   getRenderer(): PIXI.Renderer;
//   getContainer(): Container;
//   getMap(): L.Map;
//   getSymbolsArray(): Container[];
//   getFirstRenderingArray(): Container[];
//   setMarkerScale(invScale: number): void;
// }
//
// interface PixiOverlayOptions {
//   padding?: number;
//   forceCanvas?: boolean;
//   doubleBuffering?: boolean;
//   resolution?: number;
//   projectionZoom?(map: L.Map): number;
//   destroyInteractionManager?: boolean;
//   autoPreventDefault?: boolean;
//   preserveDrawingBuffer?: boolean;
//   clearBeforeRender?: boolean;
//   shouldRedrawOnMove?(): boolean;
//   pane?: 'markerPane' | 'overlayPane' | 'shadowPane' | 'tilePane'
//           | 'mapPane' | 'tooltipPane' | 'popupPane';
//   zIndex?: number;
// }
//
// declare class PixiOverlay extends L.Layer {
//
//   constructor(drawCallback: (utils: Utils) => void, options?: PixiOverlayOptions);
//   redraw(data?: any): Promise<void>;
//   destroy(): void;
//   bringToFront(): this;
//   getSymbolsArray(): Container[];
//   getFirstRenderingArray: () => Container[];
//   addGraphics(graphics: Container): void;
//   removeGraphicsAt(index: number): void;
//   removeGraphics(graphics: Container): void;
//   bringToBack(): this;
//   setOptions(options: PixiOverlayOptions): void;
//   getOptions(): PixiOverlayOptions;
//   project(latLng: LatLng, zoom?: number): Point;
//   unProject(point: Point, zoom?: number): LatLng;
// }
