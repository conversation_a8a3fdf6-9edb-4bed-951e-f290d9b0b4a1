<h2 mat-dialog-title>RAP Time</h2>
<form [formGroup]="rapTime" class="rap-form" mat-dialog-content>
  <div class="rap-time-box">
    <mat-form-field appearance="fill">
      <mat-label>Select Date</mat-label>
      <input [matDatepicker]="picker" formControlName="date" matInput />
      <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
    </mat-form-field>
    <mat-form-field appearance="fill">
      <mat-label>Hours</mat-label>
      <input formControlName="hour"
             matInput
             max="23"
             min="0"
             type="number" />
      <mat-hint>Enter value between 0-23</mat-hint>
      @if (rapTime.get('hour')?.hasError('min') || rapTime.get('hour')?.hasError('max')) {
        <mat-error>Entered value is not in specified limit</mat-error>
      }
    </mat-form-field>
    <mat-form-field appearance="fill">
      <mat-label>Minutes</mat-label>
      <input formControlName="minute"
             matInput
             max="59"
             min="0"
             type="number" />
      <mat-hint>Enter value between 0-59</mat-hint>
      @if (rapTime.get('minute')?.hasError('min') || rapTime.get('minute')?.hasError('max')) {
        <mat-error>Entered value is not in specified limit</mat-error>
      }
    </mat-form-field>
    <mat-form-field appearance="fill">
      <mat-label>Seconds</mat-label>
      <input formControlName="second"
             matInput
             max="59"
             min="0"
             type="number" />
      <mat-hint>Enter value between 0-59</mat-hint>
      @if (rapTime.get('second')?.hasError('min') || rapTime.get('second')?.hasError('max')) {
        <mat-error>Entered value is not in specified limit</mat-error>
      }
    </mat-form-field>
  </div>
</form>
<mat-dialog-actions align="end">
  <button [disabled]="rapTime.invalid" [mat-dialog-close]="fullDateTime()" color="primary" mat-raised-button>Save
  </button>
  <button mat-button mat-dialog-close>Cancel</button>
</mat-dialog-actions>
