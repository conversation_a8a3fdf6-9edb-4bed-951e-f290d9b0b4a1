import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn
} from '@angular/forms';
import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { createEverything, payload } from '@simfront/common-libs';
import { environment } from 'src/environments/environment';
import { RootState } from '..';
import { Entity } from '../entity/entity.model';

export interface CreateMovementForm {
  name: FormControl<string>;
  movementEngine: FormControl<string>;
  selectMission: FormControl<number>;
  startMovements: FormControl<string>;
  startHours: FormControl<number>;
  startMinutes: FormControl<number>;
  startSeconds: FormControl<number>;
  entityType: FormControl<string>;
  stepIndex: FormControl<number>;
  entitySelection: FormGroup<EntitySelectionForm>;
  routeSelection: FormGroup<RouteSelectionForm>;
  routeInformation: FormGroup<RouteInformationForm>;
}

export interface EntitySelectionForm {
  selectedEntities: FormControl<Entity[]>;
}

export interface RouteSelectionForm {
  routeType: FormControl<string>;
  routeName: FormControl<string>;
  routePointsInformation: FormArray<FormGroup<RoutePointInformation>>;
}

export interface RouteInformationForm {
  callsign: FormControl<string>;
  speed: FormControl<number>;
  altitude: FormControl<number>;
  minHeight: FormControl<number>;
  maxHeight: FormControl<number>;
  moveToStart: FormControl<boolean>;
  returnToStart: FormControl<boolean>;
}

export interface Movement {
  movementEngine: string;
  name: string;
  startType: string;
  startTime: number;
  entities: Entity[];
  existingRoute: any;
  routeName: string;
  routePoints: { latitude: number; longitude: number }[];
  type: string;
  routePointInfo: RoutePointInformation[];
  callsign: string;
  speed: number;
  altitude: number;
  minHeight: number;
  maxHeight: number;
  moveToStart: boolean;
  returnToStart: boolean;
}

export interface RoutePointInformation {
  speed: FormControl<number>;
  altitude: FormControl<number>;
  pointType: FormControl<string>;
  majorAxis: FormControl<number>;
  minorAxis: FormControl<number>;
  startOffset: FormControl<number>;
  loopStart: FormControl<boolean>;
  loopEnd: FormControl<boolean>;
  numberOfLoops: FormControl<number>;
  duration: FormControl<number>;
  continous: FormControl<number>;
  pause?: FormControl<number>;
  patrol?: FormControl<number>;
  hover?: FormControl<number>;
  orbit?: FormControl<number>;
}

export interface RoutePoint {
  label: string;
  name: string;
  type: string;
  options?: { value: string; label: string }[];
}

export function getPointTypeOptions(
  entityType: string
): { value: string; label: string }[] {
  if (entityType === 'air') {
    return [
      { value: 'normal', label: 'Normal' },
      { value: 'hover', label: 'Hover' },
      { value: 'orbit', label: 'Orbit' },
      { value: 'pause', label: 'Pause' }
    ];
  } else {
    return [
      { value: 'normal', label: 'Normal' },
      { value: 'pause', label: 'Pause' },
      { value: 'patrol', label: 'Patrol' }
    ];
  }
}

export const routeInformationFormFields = [
  {
    label: 'Callsign',
    name: 'callsign',
    type: 'text'
  },
  {
    label: 'Speed',
    name: 'speed',
    type: 'number'
  },
  {
    label: 'Altitude (ft)',
    name: 'altitude',
    type: 'number'
  },
  {
    label: 'Min Height (ft)',
    name: 'minHeight',
    type: 'number'
  },
  {
    label: 'Max Height (ft)',
    name: 'maxHeight',
    type: 'number'
  },
  {
    label: 'Move to Start',
    name: 'moveToStart',
    type: 'checkbox'
  },
  {
    label: 'Return to Start',
    name: 'returnToStart',
    type: 'checkbox'
  }
];

export const routePointFormFields = (entityType: string) => {
  const allFields = [
    {
      label: 'Speed',
      name: 'speed',
      type: 'number'
    },
    {
      label: 'Altitude (ft) ASL',
      name: 'altitude',
      type: 'number'
    },
    {
      label: 'Point Type',
      name: 'pointType',
      type: 'select',
      options: getPointTypeOptions(entityType)
    },
    {
      label: 'Pause (min)',
      name: 'pause',
      type: 'number'
    },
    {
      label: 'Patrol (min)',
      name: 'patrol',
      type: 'number'
    },
    {
      label: 'Hover (min)',
      name: 'hover',
      type: 'number'
    },
    {
      label: 'Orbit (min)',
      name: 'orbit',
      type: 'number'
    },
    {
      label: 'Major Axis (km)',
      name: 'majorAxis',
      type: 'number'
    },
    {
      label: 'Minor Axis (km)',
      name: 'minorAxis',
      type: 'number'
    },
    {
      label: 'Start Offset (min)',
      name: 'startOffset',
      type: 'number'
    },
    {
      label: 'Num Loops',
      name: 'numLoops',
      type: 'number'
    },
    {
      label: 'Duration',
      name: 'duration',
      type: 'number'
    }
  ];

  return allFields.filter((field) => {
    // Hide hover and orbit fields for land entities
    if (
      entityType === 'land' &&
      (field.name === 'hover' || field.name === 'orbit')
    ) {
      return false;
    }
    // Hide patrol field for air entities
    if (entityType === 'air' && field.name === 'patrol') {
      return false;
    }
    return true;
  });
};

export const routePointCheckboxFields = [
  {
    label: 'Is Loop Start',
    name: 'isLoopStart',
    type: 'checkbox'
  },
  {
    label: 'Is Loop End',
    name: 'isLoopEnd',
    type: 'checkbox'
  },
  {
    label: 'Continuous',
    name: 'continuous',
    type: 'checkbox'
  }
];

export function routeNameRequiredValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const routeType = control.get('routeType')?.value;
    const routeName = control.get('routeName');

    if (routeType === 'NEW_ROUTE' && !routeName?.value) {
      routeName?.setErrors({ required: true });
    } else {
      routeName?.setErrors(null);
    }
    return null;
  };
}

export const {
  reducer: movementReducer,
  facade: MovementFacadeBase,
  initialState
} = createEverything<RootState, Movement, 'movement', never, string>(
  'movement',
  (e) => e.name,
  `${environment.origin}/simulation/move`,
  (state) => state.movement ?? initialState
);

export const MovementActions = createActionGroup({
  source: 'Movement',
  events: {
    'Open Movement Manager Dialog': emptyProps(),
    'Close Movement Manager Dialog': emptyProps(),
    'Save Movement': payload<Movement>(),
    'Save Movement Success': emptyProps()
  }
});
