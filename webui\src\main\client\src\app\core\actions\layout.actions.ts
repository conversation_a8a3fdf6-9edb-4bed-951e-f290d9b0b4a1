import { createActionGroup, emptyProps } from '@ngrx/store';
import { payload } from '@simfront/common-libs';
import {
  ConnectionPanelDisplay,
  ConnectionPanelPosition,
  LayoutState,
  SidePanelState
} from '../reducers/layout.reducer';

export const LayoutActions = createActionGroup({
  source: 'Layout',
  events: {
    'Set Initial Layout State': payload<Partial<LayoutState>>(),
    'Toggle Left Sidenav': payload<boolean>(),
    'Toggle Right Sidenav': payload<boolean>(),
    'Toggle Connections Expanded': payload<boolean>(),
    'Toggle Dataman Status': payload<boolean>(),
    'Toggle Background Arc Effect': payload<boolean>(),
    'Change Side Panel': payload<SidePanelState>(),
    'Change Connection Panel Display': payload<ConnectionPanelDisplay>(),
    'Change Connection Panel Position': payload<ConnectionPanelPosition>()
  }
});
