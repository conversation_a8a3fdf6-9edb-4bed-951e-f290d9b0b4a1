import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSliderModule } from '@angular/material/slider';
import { FormsModule } from '@angular/forms';
import { GisService } from '../../service/gis.service';

@Component({
  selector: 'nexus-settings-slider',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSliderModule,
    FormsModule
  ],
  templateUrl: './settings-slider.component.html',
  styleUrls: ['./settings-slider.component.css']
})
export class SettingsSliderComponent {
  gisService = inject(GisService);
  mapOpacitySliderValue = 1.0;
  elementSizeValue = 50;
  showLabels: boolean = this.gisService._showEntityLabels;

  setBackgroundMapOpacity(opacity: number): void {
    this.mapOpacitySliderValue = opacity;
    this.gisService.setBackgroundMapOpacity(opacity);
  }

  setElementSize(iconSize: number): void {
    this.elementSizeValue = iconSize;
    this.gisService.mapDisplay.setMilitaryElementIconSize(iconSize);
  }

  toggleLabels(event: Event): void {
    event.stopPropagation();
    this.showLabels = this.gisService.toggleEntityLabelVisibility();
  }
}
