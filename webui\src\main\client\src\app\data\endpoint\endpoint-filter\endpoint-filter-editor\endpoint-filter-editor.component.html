<div>
  <div class="panel-title"><b>FILTER EDITORS</b></div>
  @if (filter !== undefined) {
    <p>{{ info.description }}</p>
    @switch (info?.type) {
      @case (FilterType.Geo) {
        <ng-container [ngTemplateOutlet]="geoFilter" />
      }
      @case (FilterType.Entity) {
        <ng-container [ngTemplateOutlet]="entityFilter" />
      }
      @case (FilterType.Options) {
        <ng-container [ngTemplateOutlet]="optionFilter" />
      }
      @case (FilterType.FollowEntity) {
        <ng-container [ngTemplateOutlet]="followEntityFilter" />
      }
      @default {
        <ng-container
          [ngTemplateOutletContext]="{
            type: info.type === FilterType.String ? 'text' : 'number'
          }"
          [ngTemplateOutlet]="attributeFilter"
        />
      }
    }
  }
</div>

<ng-template #optionFilter>
  @if (isOptionFilter(filter)) {
    <mat-label>{{ info.attributeName }}</mat-label>
    @for (option of info.options; track option) {
      <mat-checkbox
        [checked]="filter.selectedOptions.includes(option)"
        (change)="onOptionFilterChange(filter, option, $event.checked)"
      >
        {{ option }}
      </mat-checkbox>
    }
  }
</ng-template>

<ng-template #attributeFilter let-type="type">
  @if (isAttributeFilter(filter)) {
    <mat-form-field>
      <mat-label>{{ info.attributeName }}</mat-label>
      @if (filter.valueOptions === undefined) {
        <input
          [value]="'' + filter.value"
          matInput
          (change)="onAttributeFilterChange(filter, $event.target['value'])"
          [type]="type"
        />
      } @else {
        <mat-select>
          @for (option of filter.valueOptions; track option) {
            <mat-option>{{ option }}</mat-option>
          }
        </mat-select>
      }
    </mat-form-field>
  }
</ng-template>

<ng-template #geoFilter>
  @if (isGeoFilter(filter)) {
    <vcci-geo-filter
      [filter]="filter"
      [newGeoAreaDrawn]="newGeoAreaDrawn$ | async"
      [isGeoDrawn]="isGeoDrawn$ | async"
      (filterChanged)="onGeoFilterChange($event)"
    ></vcci-geo-filter>
  }
</ng-template>

<ng-template #entityFilter>
  @if (isEntityFilter(filter)) {
    <vcci-entity-filter
      [entityFilter]="filter"
      [entities]="treeData$ | async"
    />
  }
</ng-template>

<ng-template #followEntityFilter>
  @if (isFollowEntityFilter(filter)) {
    <vcci-follow-entity-filter
      [filter]="filter"
      [candidateEntities]="entities$ | async"
    />
  }
</ng-template>
