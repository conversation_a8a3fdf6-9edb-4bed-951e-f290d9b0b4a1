import {
  Component,
  inject,
  TemplateRef,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
  signal
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  CursitFilter,
  CursitFilterForm,
  FORM_FIELDS
} from '../../models/cursit-filter.model';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CommonModule } from '@angular/common';
import {
  CoreModule,
  buildTreeData,
  NestedTreeItem,
  TreeUtilFunctions
} from '@simfront/common-libs';

interface CursitFilterNode {
  name: string;
  checked: boolean;
  children: CursitFilterNode[];
}

@Component({
  selector: 'vcci-cursit-filter',
  templateUrl: 'cursit-filter.component.html',
  styleUrls: ['cursit-filter.component.css'],
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatFormFieldModule,
    MatCheckboxModule,
    CoreModule
  ]
})
export class CursitFilterComponent implements AfterViewInit {
  @ViewChild('custom') custom!: TemplateRef<any>;

  data: CursitFilter = inject(MAT_DIALOG_DATA);
  FORM_FIELDS = FORM_FIELDS;

  private cdr = inject(ChangeDetectorRef);

  readonly filterListData = signal<NestedTreeItem<CursitFilterNode>[]>([]);

  filterForm: FormGroup<CursitFilterForm> = new FormGroup({
    symbolSet: new FormControl(this.data.symbolSet),
    affiliation: new FormControl(this.data.affiliation),
    dimension: new FormControl(this.data.dimension),
    echelon: new FormControl(this.data.echelon),
    mobility: new FormControl(this.data.mobility),
    operationalCondition: new FormControl(this.data.operationalCondition),
    description: new FormControl(this.data.description)
  });

  private TreeUtilFunctions: TreeUtilFunctions<CursitFilterNode> = {
    id: (node) => node.name,
    children: (node) => node.children,
    hasMenu: () => false,
    customTemplate: (
      node: CursitFilterNode,
      templates: TemplateRef<unknown>[]
    ): TemplateRef<unknown> | undefined =>
      templates.length > 0 ? templates[0] : undefined
  };

  ngAfterViewInit(): void {
    this.filterListData.set(
      buildTreeData<CursitFilterNode>(
        FORM_FIELDS.map((field) => ({
          name: field.label,
          checked: this.isFieldFullySelected(field),
          children: field.options.map((option: any) => ({
            name: option.name || option,
            checked: this.isOptionSelected(field, option),
            children: []
          }))
        })),
        this.TreeUtilFunctions,
        [this.custom]
      )
    );

    this.cdr.detectChanges();
  }

  isNodeChecked(node: NestedTreeItem<CursitFilterNode>): boolean {
    return node.value.checked;
  }

  isNodeIndeterminate(node: NestedTreeItem<CursitFilterNode>): boolean {
    // Only parent nodes can be indeterminate
    if (node.children.length === 0) {
      return false;
    }

    const checkedChildren = node.children.filter(
      (child) => child.value.checked
    );
    // Indeterminate if some but not all children are checked
    return (
      checkedChildren.length > 0 &&
      checkedChildren.length < node.children.length
    );
  }

  onNodeCheckChange(
    node: NestedTreeItem<CursitFilterNode>,
    checked: boolean
  ): void {
    node.value.checked = checked;

    // If this is a parent node, update all children
    if (node.children.length > 0) {
      node.children.forEach((child) => {
        child.value.checked = checked;
      });
    }

    // Update parent state if this is a child node
    this.updateParentState(node);

    // Update form controls based on checked state
    this.updateFormControls();
  }

  private isFieldFullySelected(field: any): boolean {
    const currentValues = this.data[field.name] || [];
    return currentValues.length === field.options.length;
  }

  private isOptionSelected(field: any, option: any): boolean {
    const currentValues = this.data[field.name] || [];
    return currentValues.some(
      (value: any) =>
        (value.name && value.name === option.name) ||
        (typeof value === 'string' && value === option) ||
        value === option
    );
  }

  private updateParentState(node: NestedTreeItem<CursitFilterNode>): void {
    // Find parent node if this is a child
    const parentNode = this.findParentNode(node);
    if (parentNode) {
      const checkedChildren = parentNode.children.filter(
        (child) => child.value.checked
      );

      if (checkedChildren.length === 0) {
        // No children checked, uncheck parent
        parentNode.value.checked = false;
      } else if (checkedChildren.length === parentNode.children.length) {
        // All children checked, check parent
        parentNode.value.checked = true;
      }
    }
  }

  private findParentNode(
    childNode: NestedTreeItem<CursitFilterNode>
  ): NestedTreeItem<CursitFilterNode> | null {
    for (const parentNode of this.filterListData()) {
      if (parentNode.children.includes(childNode)) {
        return parentNode;
      }
    }
    return null;
  }

  private updateFormControls(): void {
    FORM_FIELDS.forEach((field, fieldIndex) => {
      const parentNode = this.filterListData()[fieldIndex];
      if (parentNode) {
        const checkedOptions = parentNode.children
          .filter((child) => child.value.checked)
          .map((child) => {
            return field.options.find(
              (option: any) =>
                (option.name && option.name === child.value.name) ||
                (typeof option === 'string' && option === child.value.name) ||
                option === child.value.name
            );
          })
          .filter((option) => option !== undefined);

        const formControl = this.filterForm.get(field.name);
        if (formControl) {
          type FieldType = keyof CursitFilter;
          const fieldName = field.name as FieldType;
          const typedOptions = checkedOptions as CursitFilter[typeof fieldName];
          formControl.setValue(typedOptions);
        }
      }
    });
  }
}
