<div class="detail-row">
  <div style="margin: auto">DETAILS</div>
  <button mat-icon-button (click)="expanded = !expanded">
    <mat-icon *ngIf="!expanded; else notVisible">visibility</mat-icon>
    <ng-template #notVisible>
      <mat-icon>visibility_off</mat-icon>
    </ng-template>
  </button>
</div>
<div class="content" [@expand]="expanded ? 'expanded' : 'collapsed'">
  <div>{{filterDetails}}</div>
</div>
