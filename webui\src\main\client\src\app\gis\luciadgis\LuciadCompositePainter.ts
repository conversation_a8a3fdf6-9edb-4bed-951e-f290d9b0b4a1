/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/6/2024, 11:12 AM
 */
import { MilitarySymbologyPainter } from '@luciad/ria-milsym/symbology/military/MilitarySymbologyPainter';
import { Feature } from '@luciad/ria/model/feature/Feature';
import { Polygon } from '@luciad/ria/shape/Polygon';
import { Polyline } from '@luciad/ria/shape/Polyline';
import { Shape } from '@luciad/ria/shape/Shape';
import { BasicFeaturePainter } from '@luciad/ria/view/feature/BasicFeaturePainter';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { PaintState } from '@luciad/ria/view/feature/FeaturePainter';
import { clusteredFeatures, isCluster } from '@luciad/ria/view/feature/transformation/ClusteringTransformer';
import { Layer } from '@luciad/ria/view/Layer';
import { Map } from '@luciad/ria/view/Map';
import { DrapeTarget } from '@luciad/ria/view/style/DrapeTarget';
import { GeoCanvas } from '@luciad/ria/view/style/GeoCanvas';
import { IconStyle } from '@luciad/ria/view/style/IconStyle';
import { LabelCanvas } from '@luciad/ria/view/style/LabelCanvas';
import { PointLabelPosition } from '@luciad/ria/view/style/PointLabelPosition';
import { IconFactory } from './LuciadIconFactory';
import { LuciadMilitarySymbologyClassifier } from './LuciadMilitarySymbologyClassifier';
import { LuciadShape } from './LuciadShape';

function getClusterLabel(feature: Feature): string {
  return `<span style='color: black; background: rgba(255, 255, 255, 0.8); padding: 2px; font-size: medium; font-weight: bold; border-radius: 70px;'>${
    clusteredFeatures(feature).length}</span>`;
}

function allIconsInClusterAreTheSame(cluster: Feature): boolean {
  const clusteredElements = clusteredFeatures(cluster);
  const firstElementCode = clusteredElements[0].properties['code'];
  for (let i = 1; i < clusteredElements.length; i += 1) {
    const feature = clusteredElements[i];
    if (feature.properties['code'] !== firstElementCode) {
      return false;
    }
  }
  return true;
}

function getClusterStyle(fillColor: string, hovered: boolean): IconStyle {
  const iconStyle: IconStyle = {
    width: '60px',
    height: '60px',
    drapeTarget: DrapeTarget.NOT_DRAPED,
    image: IconFactory.circle({
      width: 60,
      height: 60,
      fill: fillColor,
      stroke: '#000000'
    })
  };
  if (hovered) {
    iconStyle.bloom = { intensity: 1.5 };
  }
  return iconStyle;
}

const HOSTILE_COLOR = '#FF8080';
const FRIEND_COLOR = 'rgb(128, 224, 255)';

export class LuciadCompositePainter extends BasicFeaturePainter {
  private readonly _symbologyPainter: MilitarySymbologyPainter;
  private readonly _classifier: LuciadMilitarySymbologyClassifier;

  constructor(
    symbologyPainter: MilitarySymbologyPainter,
    classifier: LuciadMilitarySymbologyClassifier
  ) {
    super();
    this._symbologyPainter = symbologyPainter;
    this._classifier = classifier;

    this._symbologyPainter.on('InvalidateById', (id: string) => this.invalidateById(id));
    this._symbologyPainter.on('InvalidateAll', () => this.invalidateAll());
    this._symbologyPainter.on('Invalidate', (feature: Feature) => this.invalidate(feature));
  }

  override paintLabel(
    labelCanvas: LabelCanvas,
    feature: Feature,
    shape: Shape,
    layer: Layer,
    map: Map,
    paintState: PaintState
  ): void {
    if (feature.properties['code']) {
      const lbl = feature.properties['label'];
      labelCanvas.drawLabel(
        `<div style="color: black; font-weight: bold;">${lbl}</div>`,
        shape,
        { positions: PointLabelPosition.NORTH_EAST, offset: 30 }
      );
    } else if (isCluster(feature)) {
      // Cluster painting
      if (shape.focusPoint) {
        labelCanvas.drawLabel(getClusterLabel(feature), shape.focusPoint, {
          group: 'NON_DECLUTTERED',
          positions: PointLabelPosition.SOUTH,
          offset: 40
        });
      }
      if (paintState.selected) {
        const elements = clusteredFeatures(feature);
        for (let i = 0; i < elements.length; i += 1) {
          const symbol = elements[i];
          this._symbologyPainter.paintLabel(
            labelCanvas,
            symbol,
            (symbol as any).geometry,
            layer,
            map,
            paintState
          );
        }
      }
    } else if (feature instanceof LuciadShape) {
      const { label } = feature;
      const labelElement: string =  label ? `<div style="color: black; font-weight: bold;">${label}</div>` : '';
      if (shape instanceof Polyline) {
        labelCanvas.drawLabelOnPath(
          labelElement,
          shape,
          {}
        );
      } else if (shape instanceof Polygon) {
        labelCanvas.drawLabelInPath(
          labelElement,
          shape,
          {}
        );
      }
    }
  }

  override paintBody(
    geoCanvas: GeoCanvas,
    feature: Feature,
    shape: Shape,
    layer: FeatureLayer,
    map: Map,
    paintState: PaintState
  ): void {
    if (feature.properties['code']) {
      this._symbologyPainter.paintBody(geoCanvas, feature, shape, layer, map, paintState);
    } else if (isCluster(feature) && clusteredFeatures(feature).length > 0) {
      // Cluster painting
      if (paintState.selected) {
        const elements = clusteredFeatures(feature);
        for (let i = 0; i < elements.length; i += 1) {
          const symbol = elements[i];
          this._symbologyPainter.paintBody(
            geoCanvas,
            symbol,
            (symbol as any).geometry,
            layer,
            map,
            paintState
          );
        }
      }

      const firstElementInCluster = clusteredFeatures(feature)[0];
      if (allIconsInClusterAreTheSame(feature)) {
        // Use same icon
        const clusterVisualization = new Feature((feature as any).geometry, {
          code: firstElementInCluster.properties['code'],
          iconSize: 96
        }, feature.id);

        this._symbologyPainter.paintBody(
          geoCanvas,
          clusterVisualization,
          (clusterVisualization as any).geometry,
          layer,
          map,
          paintState
        );
      } else if (shape.focusPoint) {
        if (this._classifier.getClassification(firstElementInCluster).indexOf('Hostile') !== -1) {
          geoCanvas.drawIcon(shape.focusPoint, getClusterStyle(HOSTILE_COLOR, paintState.hovered));
        } else if (this._classifier.getClassification(firstElementInCluster).indexOf('Friend') !== 1) {
          geoCanvas.drawIcon(shape.focusPoint, getClusterStyle(FRIEND_COLOR, paintState.hovered));
        } else {
          geoCanvas.drawIcon(shape.focusPoint, getClusterStyle('rgba(165, 189, 41, 0.9)', paintState.hovered));
        }
      }
    } else {
      super.paintBody(geoCanvas, feature, shape, layer, map, paintState);
    }
  }
}
