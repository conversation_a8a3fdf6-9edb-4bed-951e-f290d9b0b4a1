<svg class="svg-container" width="100%" height="100%">
  @for (route of connectionBlocks; track route.name) {
    @let connectedTo = [route.input.id, route.output.id];
    <polyline
      fill="none"
      stroke-width="3"
      [attr.points]="route.points | svgPoint"
      [attr.stroke-dasharray]="route.isFullRoute ? null : 4"
      [attr.stroke]="selected | routeColor : connectedTo"/>
    <polyline
      fill="none"
      stroke-width="3"
      class="dash-animation"
      [attr.points]="route.points | svgPoint"
      stroke="white"
      stroke-opacity="0.2"/>
  }
  <ng-container *ngFor="let route of otherBlocks | keyvalue; trackBy: otherBlocksTrackBy">
    <polyline
      fill="none"
      stroke-width="3"
      [attr.points]="route.value.points | svgPoint"
      [attr.stroke]="selected | routeColor : route.value.connectedTo"/>
    <g
      routeSvgButton
      svgIconHref="assets/images/svg/bundled.svg#running-full"
      [iconPoint]="route.value.points[0]"
      [isInput]="route.value.isInput"
      [connectedTo]="route.value.connectedTo"
      [iconClasses]="'svg-status-icon ' + route.value.endpoint.state"
      [matTooltip]="'Endpoint Status: ' + route.value.endpoint.state"
      [selected]="selected"
      [height]="28"
    />
    @let hasFilter = route.value.endpoint | hasFilters;
    @if (hasFilter) {
      <g
        routeSvgFilter
        [iconPoint]="route.value.points[0]"
        [isInput]="route.value.isInput"
        [connectedTo]="route.value.connectedTo"
        [height]="16"
        [width]="11"
        [initialOffset]="62"
        [selected]="selected"
        matTooltip="Some entities are being filtered."
      />
    }
  </ng-container>
  @for (route of connectionBlocks; track route.name) {
    @let connectedTo = [route.input.id, route.output.id];
    @if (selected | svgShowButton : connectedTo) {
      <g
        routeSvgButton
        svgIconHref="assets/images/svg/bundled.svg#delete"
        [iconPoint]="route.points[1]"
        [iconClasses]="'increase-stroke'"
        [connectedTo]="connectedTo"
        [initialOffset]="0"
        [selected]="selected"
        [height]="28"
        (svgClick)="deleteRoute(route.name)"
        matTooltip="Delete Route"
      />
    }
  }
</svg>
