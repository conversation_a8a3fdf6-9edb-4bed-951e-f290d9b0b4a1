import { LatLngExpression, MapOptions, MarkerClusterGroupOptions, TileLayerOptions } from 'leaflet';
import * as L from 'leaflet';

export const MAX_BOUNDS = L.latLngBounds(
  L.latLng(-90, -180), // Southwest corner
  L.latLng(90, 180)    // Northeast corner
);

export const MAP_OPTIONS: MapOptions = {
  center: [51.505, -0.09],
  zoom: 2,
  maxBounds: MAX_BOUNDS,
  maxZoom: 21,
  minZoom: 2,
  bounceAtZoomLimits: true,
  maxBoundsViscosity: 1,
  attributionControl: false
};

export const CLUSTER_GROUP_OPTIONS: MarkerClusterGroupOptions = {
  spiderfyOnEveryZoom: true,
  zoomToBoundsOnClick: false, // Disable default zoom into cluster on click
  spiderfyDistanceMultiplier: 1, // Adjust spider leg length
  animate: true,
  chunkedLoading: true,
  maxClusterRadius: (zoom: number): number => {
    if (zoom >= 2 && zoom <= 4) {
      return 80;
    } else if (zoom > 4 && zoom <= 8) {
      return 40;
    } else {
      return 10;
    }
  },
  disableClusteringAtZoom: 12,
  removeOutsideVisibleBounds: true
};

export const TILE_LAYERS_OPTIONS: TileLayerOptions = {
  noWrap: true,
  bounds: MAX_BOUNDS,
  maxZoom: 21
}

export interface LeafletEntity {
  setLatLng(latLng: LatLngExpression): this;
  setIcon(icon: L.DivIcon | L.Icon): this;
}
