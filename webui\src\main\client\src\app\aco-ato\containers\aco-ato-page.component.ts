import { Component } from '@angular/core';
import { Mat<PERSON><PERSON>on } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { acoAtoActions } from '../actions/aco-ato.actions';
import { PageComponent } from 'src/app/core/components/page.component';

@Component({
  selector: 'vcci-aco-ato-page',
  imports: [MatButton, PageComponent],
  templateUrl: './aco-ato-page.component.html',
  styleUrl: './aco-ato-page.component.css'
})
export class AcoAtoPageComponent {
  constructor(
    private store: Store,
    private dialog: MatDialog
  ) {}

  onImportClick() {
    this.store.dispatch(acoAtoActions.openImportDialog());
  }

  onSetRapTimeClick() {
    this.store.dispatch(acoAtoActions.openRapTimesDialog());
  }
}
