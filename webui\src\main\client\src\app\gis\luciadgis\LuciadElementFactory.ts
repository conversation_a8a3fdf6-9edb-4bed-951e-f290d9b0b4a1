import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { Layer } from '@luciad/ria/view/Layer';
import { Coordinate } from '../../cursit/models/cursit.model';
import { reverseCoordinates } from '../../shared/helpers/helpers';
import { MapElementFactory } from '../interface/ElementFactory';
import { ShapeType } from '../interface/models/map-element.model';
import { LuciadMapDisplay } from './LuciadMapDisplay';
import { LuciadMilitaryMapElement } from './LuciadMilitaryMapElement';
import { MS2525Symbol } from './MS2525Symbol';

export class LuciadElementFactory extends MapElementFactory<Layer> {
  luciadMapDisplay: LuciadMapDisplay;

  constructor(luciadMapDisplay: LuciadMapDisplay) {
    super();
    this.luciadMapDisplay = luciadMapDisplay;
  }

  createMilitaryElement(
    sidc: string,
    id: string,
    label: string,
    shapeType: ShapeType,
    catCode:string,
    coordinate?: Coordinate | Coordinate[],
    coordinateReference?: string,
    addLabel?: boolean
  ): LuciadMilitaryMapElement {
    const feature = new MS2525Symbol(
      this.luciadMapDisplay,
      sidc,
      label,
      getReference(coordinateReference),
      shapeType,
      reverseCoordinates(coordinate)
    );

    feature.id = id;

    const luciadMilitaryMapElement = new LuciadMilitaryMapElement(id);
    luciadMilitaryMapElement.id = id;
    luciadMilitaryMapElement.sidc = sidc;
    luciadMilitaryMapElement.gisObject = feature;
    luciadMilitaryMapElement.mapLayer = null;

    return luciadMilitaryMapElement;
  }
}
