import { HatchedImageBuilder } from './HatchedImageBuilder';

/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/9/2024, 11:52 AM
 */

export function hatchedFillBuilder(color: string): HTMLImageElement {
  return new HatchedImageBuilder()
    .patterns([HatchedImageBuilder.Pattern.SLASH, HatchedImageBuilder.Pattern.BACKGROUND])
    .lineColor(color)
    .lineWidth(1)
    .patternSize(10, 10)
    .backgroundColor('rgba(0 ,0 , 0, 0)')
    .build();
}
