<button
  matTooltip="Entity"
  mat-button
  class="menu-button"
  [matMenuTriggerFor]="entityMenu"
>
  Entity
  <mat-icon svgIcon="entity-tb" style="width: 24px; height: 24px"></mat-icon>
</button>

<mat-menu #entityMenu="matMenu">
  <!-- Create submenu -->
  <button
    mat-menu-item
    #createMenuTrigger="matMenuTrigger"
    [matMenuTriggerFor]="createMenu"
  >
    Create
  </button>
  <mat-menu #createMenu="matMenu">
    <button mat-menu-item (click)="activateEntityCreation()">Entity</button>
    <button mat-menu-item (click)="openRouteCreation()">Route</button>
    @if (!isProduction) {
      <button mat-menu-item>Radar</button>
      <button mat-menu-item>Tactical Graphics</button>
    }
  </mat-menu>

  <!-- Find submenu -->
  <button
    mat-menu-item
    #findMenuTrigger="matMenuTrigger"
    [matMenuTriggerFor]="findMenu"
  >
    Find
  </button>
  <mat-menu #findMenu="matMenu">
    <button mat-menu-item (click)="findEntity()">Entity</button>
    @if (!isProduction) {
      <button mat-menu-item>In Orbat</button>
    }
  </mat-menu>

  <!-- Hide submenu -->
  <button
    mat-menu-item
    #hideMenuTrigger="matMenuTrigger"
    [matMenuTriggerFor]="hideMenu"
  >
    Hide
  </button>
  <mat-menu #hideMenu="matMenu">
    <button mat-menu-item (click)="hideAllUnits()">All Units</button>
    <button mat-menu-item (click)="hideAllMateriels()">All Materiels</button>
  </mat-menu>

  <!-- Regular menu items -->
  @if (!isProduction) {
    <button mat-menu-item>Disaggregation</button>
    <button mat-menu-item>Add Last</button>
  }

  <button
    mat-menu-item
    (click)="createFromSelection()"
    [disabled]="entitySelected()?.length === 0"
  >
    Copy Entity
  </button>
</mat-menu>
