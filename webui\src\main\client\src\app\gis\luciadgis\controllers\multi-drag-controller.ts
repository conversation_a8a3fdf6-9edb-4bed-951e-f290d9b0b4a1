import { Feature } from '@luciad/ria/model/feature/Feature';
import { WithIdentifier } from '@luciad/ria/model/WithIdentifier';
import { CoordinateReference } from '@luciad/ria/reference/CoordinateReference';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { Point } from '@luciad/ria/shape/Point';
import { Transformation } from '@luciad/ria/transformation/Transformation';
import { createTransformation } from '@luciad/ria/transformation/TransformationFactory';
import { Controller } from '@luciad/ria/view/controller/Controller';
import { HandleEventResult } from '@luciad/ria/view/controller/HandleEventResult';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { GestureEventType } from '@luciad/ria/view/input/GestureEventType';
import { ModifierType } from '@luciad/ria/view/input/ModifierType';
import { Layer } from '@luciad/ria/view/Layer';
import { LayerType } from '@luciad/ria/view/LayerType';
import { Map } from '@luciad/ria/view/Map';

type EditingEntry = [FeatureLayer, { features: Feature[] }];

export class MoveController extends Controller {
  private readonly _editingList: EditingEntry[];
  private _lastDragPoint: Point | null;

  _mapToModel: Transformation | null = null;
  found = false;

  constructor(worldRef: CoordinateReference, featuresPerLayerArray: {
    layer: Layer,
    selected: WithIdentifier[]
  }[]) {
    super();
    this._editingList = [];
    featuresPerLayerArray.forEach(selected => {
      this._editingList.push(
        [selected.layer as FeatureLayer, { features: selected.selected as Feature[] }]
      );
    });
  }

  override onActivate(map: Map): void {
    super.onActivate(map);
    this._mapToModel = createTransformation(map.reference, getReference('CRS:84'));
  }

  override onGestureEvent(gestureEvent: GestureEvent): HandleEventResult {
    if (!this.map) {
      return HandleEventResult.EVENT_IGNORED;
    }

    if (gestureEvent.type === GestureEventType.DOWN) {
      this.found = false;
      const point = gestureEvent.viewPoint;
      const pickAt = this.map.pickAt(point.x, point.y, 0);
      const selection: { layer: FeatureLayer, objects: Feature[] }[] = [];
      const pickAtDynamic = pickAt?.filter(e => e.layer.type === LayerType.DYNAMIC);
      if (pickAtDynamic) {
        pickAtDynamic.forEach(pickInfo => {
          const { layer } = pickInfo;
          if (layer instanceof FeatureLayer && layer.editable && layer.selectable) {
            selection.push({
              layer,
              objects: [pickInfo.objects[0]]
            });
          }
        });

        if (selection.length > 0) {
          const selectedObjectsList = this.map.selectedObjects;
          selectedObjectsList?.some(selectedObjects => selectedObjects.selected?.some(selected => {
            if (selected === selection[0].objects[0]) {
              this.found = true;
              return true;
            }
            return false;
          }));
        }
      }

      if (!this.found) {
        this.map.clearSelection();
      }
    }

    if (gestureEvent.type === GestureEventType.DRAG) {
      // If Control key is pressed and hold, skip dragging and do pan
      if (gestureEvent.modifier === ModifierType.CTRL) {
        return HandleEventResult.EVENT_IGNORED;
      }
      if (this.found) {
        const currentDragPoint = this.map.viewToMapTransformation.transform(gestureEvent.viewPoint);

        if (!this._lastDragPoint) {
          this._lastDragPoint = currentDragPoint;
          return HandleEventResult.EVENT_HANDLED;
        }

        this._editingList.forEach(([layer, { features }]) => {
          const startPoint = this._mapToModel.transform(this._lastDragPoint);
          const endPoint = this._mapToModel.transform(currentDragPoint);

          const dx = endPoint.x - startPoint.x;
          const dy = endPoint.y - startPoint.y;

          if (dx !== 0 || dy !== 0) {
            if (layer.editable) {
              features.forEach(feature => {
                feature.shape?.translate2D(dx, dy);
                layer.painter.invalidate(feature);
              });
            }
          }
        });
        this._lastDragPoint = currentDragPoint;

        return HandleEventResult.EVENT_HANDLED;
      }
    }
    if (gestureEvent.type === GestureEventType.DRAG_END) {
      this._lastDragPoint = null;
    }
    return super.onGestureEvent(gestureEvent);
  }
}

