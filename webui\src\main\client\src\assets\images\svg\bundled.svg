<svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<symbol id="abacus-icon" viewBox="195.959 140.909 107.381 106.801">

  <ellipse style="stroke-width: 0px; stroke-opacity: 0;" cx="242.253" cy="154.723" rx="8.611" ry="8.325"/>
  <ellipse style="stroke: rgb(0, 0, 0); stroke-width: 0px; paint-order: fill; stroke-opacity: 0;" cx="219.288" cy="205.115" rx="8.404" ry="8.404"/>
  <ellipse style="stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px;" cx="277.372" cy="230.555" rx="12.279" ry="11.871"/>
  <path style="fill: rgb(255, 255, 255); paint-order: fill; fill-opacity: 0; stroke-width: 10px;" d="M 219.761 204.374 L 242.457 158.765 L 277.373 230.888"/>
  <path style="fill: rgb(255, 255, 255); paint-order: fill; fill-opacity: 0; stroke-width: 10px;" d="M 228.054 188.552 L 256.205 188.225"/>

</symbol>
<symbol id="add-endpoint" viewBox="0 0 10 10">
<path class="foregroundColor" d="M5,1A4,4,0,1,0,9,5,4,4,0,0,0,5,1ZM5,8.53A3.53,3.53,0,1,1,8.53,5,3.53,3.53,0,0,1,5,8.53Z"/><path class="foregroundColor" d="M7,4.61H5.39V3a.39.39,0,1,0-.78,0V4.61H3a.39.39,0,1,0,0,.78H4.61V7a.39.39,0,0,0,.78,0V5.39H7a.39.39,0,0,0,0-.78Z"/>
</symbol>
<symbol id="add-input" viewBox="0 0 30 30">
<path class="foregroundColor" d="M20.55,13.2a7.16,7.16,0,0,1,2,.29l-5.72-5v3h-6V16H15A7,7,0,0,1,20.55,13.2Z"/><path class="foregroundColor" d="M13.57,19.71a6,6,0,0,1-1.27.15A6.13,6.13,0,1,1,15.17,8.33V7a7.26,7.26,0,0,0-2.87-.59,7.38,7.38,0,1,0,0,14.75A8.06,8.06,0,0,0,13.6,21a6.84,6.84,0,0,1,0-.79C13.55,20,13.56,19.88,13.57,19.71Z"/><path class="foregroundColor" d="M20.55,14.2a6,6,0,1,0,6,6A6,6,0,0,0,20.55,14.2Zm0,11.24a5.24,5.24,0,1,1,5.24-5.24A5.25,5.25,0,0,1,20.55,25.44Z"/><path class="foregroundColor" d="M23.57,19.62H21.13V17.18a.58.58,0,0,0-.58-.59.59.59,0,0,0-.59.59v2.44H17.52a.59.59,0,0,0,0,1.17H20v2.44a.59.59,0,0,0,.59.58.58.58,0,0,0,.58-.58V20.79h2.44a.59.59,0,1,0,0-1.17Z"/>
</symbol>
<symbol id="add-layer-layer-mngr" viewBox="0 0 16 16">
<path class="foregroundColor" d="M11.57,7.5H8.5V4.43a.5.5,0,0,0-1,0V7.5H4.43a.5.5,0,1,0,0,1H7.5v3.07a.5.5,0,0,0,1,0V8.5h3.07a.5.5,0,0,0,0-1Z"/><path class="foregroundColor" d="M8,14.57A6.57,6.57,0,1,1,14.57,8,6.58,6.58,0,0,1,8,14.57ZM8,2.43A5.57,5.57,0,1,0,13.57,8,5.58,5.58,0,0,0,8,2.43Z"/>
</symbol>
<symbol id="add-output" viewBox="0 0 30 30">
<path class="foregroundColor" d="M20.55,13.75a6,6,0,1,0,6,6A6,6,0,0,0,20.55,13.75Zm0,11.24a5.24,5.24,0,1,1,5.24-5.24A5.25,5.25,0,0,1,20.55,25Z"/><path class="foregroundColor" d="M23.57,19.16H21.13V16.72a.58.58,0,0,0-.58-.58.59.59,0,0,0-.59.58v2.44H17.52a.59.59,0,0,0-.58.59.58.58,0,0,0,.58.58H20v2.44a.59.59,0,0,0,.59.59.58.58,0,0,0,.58-.59V20.33h2.44a.58.58,0,0,0,.59-.58A.59.59,0,0,0,23.57,19.16Z"/><polygon class="foregroundColor" points="16.92 13.06 10.92 7.82 10.92 10.81 4.92 10.81 4.92 15.31 10.92 15.31 10.92 18.32 16.92 13.06"/><path class="foregroundColor" d="M13.6,18.91a6.62,6.62,0,0,1-.62-.24l-1,.9a8,8,0,0,0,1.62.63c0-.15,0-.3,0-.45A7.58,7.58,0,0,1,13.6,18.91Z"/><path class="foregroundColor" d="M22.8,13.06A7.38,7.38,0,0,0,12,6.56l1,.89a6.11,6.11,0,0,1,8.55,5.38,6.44,6.44,0,0,1,1.26.3S22.8,13.09,22.8,13.06Z"/>
</symbol>
<symbol id="add-route" viewBox="0 0 30 30">
<path class="foregroundColor" d="M20.55,14.29a6,6,0,1,0,6,6A6,6,0,0,0,20.55,14.29Zm4.34,8.93a6,6,0,0,1-.38.5,5.2,5.2,0,0,1-8.06-.21A5.08,5.08,0,0,1,15.61,22a5.2,5.2,0,0,1-.3-1.73,5.14,5.14,0,0,1,.2-1.36,5.2,5.2,0,0,1,1.33-2.34,5.41,5.41,0,0,1,1.09-.81,5.22,5.22,0,0,1,7.86,4.51,5.34,5.34,0,0,1-.41,2A5.92,5.92,0,0,1,24.89,23.22Z"/><path class="foregroundColor" d="M24,19.88a.59.59,0,0,0-.42-.17H21.13V17.27a.57.57,0,0,0-.35-.54.55.55,0,0,0-.46,0,.58.58,0,0,0-.36.54v2.44H17.52a.55.55,0,0,0-.41.17.59.59,0,0,0-.13.18.68.68,0,0,0,0,.46.59.59,0,0,0,.54.36H20v2.44a.54.54,0,0,0,.05.22.58.58,0,0,0,1.08,0,.54.54,0,0,0,0-.22V20.88h2.44a.58.58,0,0,0,.54-.36.55.55,0,0,0,0-.46A.57.57,0,0,0,24,19.88Z"/><path class="foregroundColor" d="M13.7,17.19,7.27,12.75a2,2,0,0,0,.13-.69,1.91,1.91,0,0,0-.13-.68l6.26-4.32a2,2,0,0,0,2.94,0l6.26,4.32a1.91,1.91,0,0,0-.13.68,2,2,0,0,0,.13.69l-1,.66a6.49,6.49,0,0,1,1.26.34l.27-.18a2,2,0,1,0,1.3-3.51,2,2,0,0,0-1.3.5L16.94,6.17A1.76,1.76,0,0,0,17,5.71a2,2,0,0,0-4,0,1.76,1.76,0,0,0,.06.46L6.7,10.56a2,2,0,0,0-1.3-.5,2,2,0,1,0,1.3,3.51L13.13,18a1.91,1.91,0,0,0-.13.68,2,2,0,0,0,.56,1.38,7,7,0,0,1,.95-3.31A1.93,1.93,0,0,0,13.7,17.19Zm9.83-5.26a1.11,1.11,0,0,1,1.07-1,1.1,1.1,0,0,1,0,2.2,1,1,0,0,1-.51-.14,1,1,0,0,1-.56-.82.7.7,0,0,1,0-.14A.61.61,0,0,1,23.53,11.93Zm-9.6-6.36a1.08,1.08,0,0,1,2.14,0,.7.7,0,0,1,0,.14A1.14,1.14,0,0,1,15,6.81a1.07,1.07,0,0,1-.71-.28,1.06,1.06,0,0,1-.39-.82A.7.7,0,0,1,13.93,5.57ZM6.47,12.2a1,1,0,0,1-.56.82,1,1,0,0,1-.51.14,1.1,1.1,0,0,1,0-2.2,1.11,1.11,0,0,1,1.07,1,.61.61,0,0,1,0,.13A.7.7,0,0,1,6.47,12.2Z"/>
</symbol>
<symbol id="add-same-as-last-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.56,13V10.27h-.91V12.4l-4-1.22-4,1.22V10.27h-.91v9.46h9.91Zm-.91,5.81H12.56V13.35l4-1.22,4,1.22Z"/><path class="foregroundColor" d="M4.39,15.92A9.14,9.14,0,0,0,5,18.28L5.8,18a8.09,8.09,0,0,1-.5-2.12Z"/><path class="foregroundColor" d="M16.61,6h0a8.57,8.57,0,0,0-1.44.13,8.84,8.84,0,0,0-2.23.66L12.5,7a9,9,0,0,0,2.17,16.78l.33.07h.08c.25,0,.5.08.75.1a6.79,6.79,0,0,0,.78,0h0a9,9,0,0,0,9-8.08c0-.3.05-.61.05-.92A9,9,0,0,0,16.61,6ZM15,22.93l-.45-.12-.4-.11a8,8,0,0,1-2.29-1.16A6.66,6.66,0,0,1,11.2,21a8.28,8.28,0,0,1-1.6-2c-.12-.2-.22-.4-.32-.61a9.2,9.2,0,0,1-.43-1.14,8.08,8.08,0,0,1-.2-.85A7.46,7.46,0,0,1,8.52,15v0a7.2,7.2,0,0,1,.12-1.32,8.26,8.26,0,0,1,.24-1,8,8,0,0,1,2.47-3.77,8.14,8.14,0,0,1,3.78-1.82c.24,0,.48-.08.73-.1a6.28,6.28,0,0,1,.75,0c.18,0,.36,0,.53,0h.14a8.06,8.06,0,0,1,5.26,2.56,6.57,6.57,0,0,1,.48.57,8.4,8.4,0,0,1,.79,1.24c.11.22.22.45.31.68a8.09,8.09,0,0,1,.58,3,7.84,7.84,0,0,1-.35,2.34,7.72,7.72,0,0,1-.59,1.42,8,8,0,0,1-6.82,4.31l-.33,0A8.53,8.53,0,0,1,15,22.93Z"/><path class="foregroundColor" d="M8.85,8.3l-.51-.75a9.69,9.69,0,0,0-1,.78h0v0h0l0,0H7v0H7l0,0h0l-.42.46.7.59A8.06,8.06,0,0,1,8.85,8.3Z"/><path class="foregroundColor" d="M12,6.13a8.19,8.19,0,0,0-1.5.35l.16.49A9.16,9.16,0,0,1,12,6.13Z"/><path class="foregroundColor" d="M9.92,23.35l.18.07h.44a8.66,8.66,0,0,0,1.62.36,9.84,9.84,0,0,1-1.95-1.29Z"/><path class="foregroundColor" d="M6.86,19.86l-.73.54.17.23h0l0,0h.13v0h0l0,0h0a8.34,8.34,0,0,0,1.32,1.27l.56-.72A8.26,8.26,0,0,1,6.86,19.86Z"/><path class="foregroundColor" d="M5.12,11.37h0v0H5.06v0h0v0H5A9.56,9.56,0,0,0,4.58,13h0v0h0v0h0v0h0v0c0,.1,0,.21,0,.31l.89.15A8.46,8.46,0,0,1,6,11.58l-.83-.39Z"/>
</symbol>
<symbol id="admin" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="15.74 25.48 14.26 25.48 14.26 28.03 7.41 28.03 7.41 29.5 22.59 29.5 22.59 28.03 15.74 28.03 15.74 25.48"/><path class="foregroundColor" d="M25,28V29.5h1.43a.74.74,0,0,0,.74-.74h0a.74.74,0,0,0-.74-.73Z"/><path class="foregroundColor" d="M3.54,29.5H5V28H3.54a.74.74,0,0,0-.74.73h0A.74.74,0,0,0,3.54,29.5Z"/><path class="foregroundColor" d="M13.89,3.83,10.57.5H1.7V22.67H28.3V3.83ZM7.41,14.08H22.59v1.63H7.41Zm15.18-2.17H7.41V10.28H22.59Z"/>
</symbol>
<symbol id="arc-by-3-pts" viewBox="0 0 30 30">
<path class="foregroundColor" d="M19,5.38V7.11a10.14,10.14,0,0,0-3.36.67V7H11.22v3.6l-.1.1a11.2,11.2,0,0,0-3.51,9.43H6.5v4.46H11V20.16H8.63c-.52-2.77.65-6.14,3.14-8.67h3.91V8.84A9.33,9.33,0,0,1,19,8.11V9.84H23.5V5.38ZM8.9,21.16H10v2.46H7.5V21.16H8.9Zm5.78-10.67H12.22V8h2.46v2.46ZM22.5,8.84H20V6.38H22.5V8.84Z"/>
</symbol>
<symbol id="arc-by-bulge" viewBox="0 0 30 30">
<path class="foregroundColor" d="M19.75,5.79V7.21a12.48,12.48,0,0,0-8.7,3.89,12.44,12.44,0,0,0-3.89,8.65H5.79v4.46h4.46V19.75H8.18a11.58,11.58,0,0,1,3.54-7.9l.68.68.36-.36-.67-.66a11.47,11.47,0,0,1,7.66-3.28v2h4.46V5.79Zm-11.52,15h1v2.46H6.79V20.75H8.23Zm15-11.5H20.75V6.79h2.46Z"/><rect class="foregroundColor" x="16.68" y="15.82" width="0.5" height="1.76" transform="translate(-6.85 16.86) rotate(-45)"/><rect class="foregroundColor" x="14.19" y="13.33" width="0.5" height="1.76" transform="translate(-5.82 14.37) rotate(-45)"/><path class="foregroundColor" d="M20.1,19.52,19,18.38l-.35.35,1.14,1.14V24h4.46V19.52ZM23.21,23H20.75V20.52h2.46Z"/>
</symbol>
<symbol id="arc-by-center" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="24.79 21.78 24.44 21.43 23.19 22.67 21.94 21.43 21.59 21.78 22.84 23.03 21.59 24.27 21.94 24.63 23.19 23.38 24.44 24.63 24.79 24.27 23.55 23.03 24.79 21.78"/><rect class="foregroundColor" x="22.94" y="19.69" width="0.5" height="1"/><rect class="foregroundColor" x="22.94" y="15.69" width="0.5" height="1"/><rect class="foregroundColor" x="22.94" y="11.69" width="0.5" height="1"/><rect class="foregroundColor" x="22.94" y="13.69" width="0.5" height="1"/><rect class="foregroundColor" x="22.94" y="9.69" width="0.5" height="1"/><rect class="foregroundColor" x="22.94" y="17.69" width="0.5" height="1"/><path class="foregroundColor" d="M21,4.55V6a14.43,14.43,0,0,0-10.17,4.47A14.16,14.16,0,0,0,6.32,21H4.58v4.46H9V21H7.33a13.25,13.25,0,0,1,4.17-9.77A13.35,13.35,0,0,1,21,7.05V9h4.46V4.55ZM8,22v2.46H5.58V22ZM24.42,8H22V5.55h2.46Z"/>
</symbol>
<symbol id="arcband" viewBox="0 0 30 30">
<rect class="foregroundColor" x="22.95" y="6.99" width="0.5" height="1"/><polygon class="foregroundColor" points="24.45 20.72 23.2 21.97 21.95 20.72 21.6 21.08 22.84 22.32 21.6 23.57 21.95 23.93 23.2 22.68 24.45 23.93 24.8 23.57 23.55 22.32 24.8 21.08 24.45 20.72"/><rect class="foregroundColor" x="22.95" y="18.99" width="0.5" height="1"/><rect class="foregroundColor" x="22.95" y="14.99" width="0.5" height="1"/><rect class="foregroundColor" x="22.95" y="16.99" width="0.5" height="1"/><rect class="foregroundColor" x="12.46" y="22.16" width="1" height="0.5" transform="translate(-0.15 0.09) rotate(-0.39)"/><rect class="foregroundColor" x="14.46" y="22.14" width="1" height="0.5" transform="translate(-0.15 0.1) rotate(-0.39)"/><rect class="foregroundColor" x="20.46" y="22.1" width="1" height="0.5" transform="translate(-0.17 0.16) rotate(-0.45)"/><rect class="foregroundColor" x="10.46" y="22.17" width="1" height="0.5" transform="translate(-0.15 0.08) rotate(-0.39)"/><rect class="foregroundColor" x="18.46" y="22.11" width="1" height="0.5" transform="translate(-0.17 0.15) rotate(-0.45)"/><rect class="foregroundColor" x="16.46" y="22.13" width="1" height="0.5" transform="translate(-0.15 0.12) rotate(-0.39)"/><path class="foregroundColor" d="M22.25,8.76v0L20,6.36a8.93,8.93,0,0,1,2.26.13l.48.07a.49.49,0,0,0,.6-.36A.5.5,0,0,0,23,5.59c-3.88-.94-8.66.71-12.17,4.22a14.14,14.14,0,0,0-4.48,10.4H4.59v4.46H9.05v-2h.42v-.5H9.05v-2H7.33a10,10,0,0,1,.15-2.3l2.67,2.79h.49l.1-.1L7.61,17.31a13,13,0,0,1,.82-2.39l3,3.16a7,7,0,0,0-.09,2.62s0,.08,0,.12a.49.49,0,0,0,.49.4H12a.5.5,0,0,0,.4-.58A7.84,7.84,0,0,1,14.86,14,8.14,8.14,0,0,1,21,11.43v1.79h2V14h.5v-.77h2V8.76ZM8.05,21.21v2.46H5.59V21.21Zm3.55-3.7L8.67,14.43a13.74,13.74,0,0,1,1.21-2l2.71,2.84A9.17,9.17,0,0,0,11.6,17.51Zm2.55-4.23a11.2,11.2,0,0,0-1.29,1.57L10.18,12a15.51,15.51,0,0,1,1.32-1.51l.2-.18,2.64,2.77Zm.56-.5L12.07,10a16.46,16.46,0,0,1,1.77-1.4l2.73,2.87A10.77,10.77,0,0,0,14.71,12.78ZM17,11.23l-2.77-2.9a14.84,14.84,0,0,1,2.1-1.07l3.07,3.22A8.44,8.44,0,0,0,17,11.23ZM21,8.76v1.66a6.61,6.61,0,0,0-.89,0L16.85,7.06a11.74,11.74,0,0,1,2.52-.62l2.22,2.32Zm3.46,3.46H22V9.76h2.46Z"/>
</symbol>
<symbol id="arrow-collapse" viewBox="0 0 10 10">
<polygon class="foregroundColor" points="5 3.23 8.54 6.77 1.46 6.77 5 3.23"/>
</symbol>
<symbol id="arrow-expand" viewBox="0 0 10 10">
<polygon class="foregroundColor" points="5 6.77 1.46 3.23 8.54 3.23 5 6.77"/>
</symbol>
<symbol id="associate-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M20,6.46V4H17.54v.86H10V4H4v6h.86v7.54H4V20H6.46v-.86H14V20h6V14h-.86V6.46ZM18.39,14H14v4.39H6.46v-.85H5.61V10H9.19l1.19,1.19.71-.7L10,9.4V5.61h7.54v.85h.85Z"/><rect class="foregroundColor" x="12.21" y="11.88" width="1" height="1.86" transform="translate(-5.34 12.74) rotate(-45)"/>
</symbol>
<symbol id="audit-metrics" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.3,6c-1.11,0-2-1-2.7-1.8H11.4C10.66,5,9.81,6,8.7,6H6V25.8H24V6ZM15,5.1a.9.9,0,1,1-.9.9A.9.9,0,0,1,15,5.1Zm8.1,19.8H6.9V6.9h4.5l1.89,1.8H16.8l1.8-1.8h4.5Z"/><rect class="foregroundColor" x="9.13" y="19.51" width="2" height="3"/><rect class="foregroundColor" x="12.38" y="16.51" width="2" height="6"/><rect class="foregroundColor" x="15.62" y="18.51" width="2" height="4"/><rect class="foregroundColor" x="18.87" y="15.51" width="2" height="7"/><path class="foregroundColor" d="M9.13,15.5a.51.51,0,0,1-.42-.23.5.5,0,0,1,.15-.69l4.53-3,3.15,2.25,4-3.21a.5.5,0,0,1,.7.08.49.49,0,0,1-.08.7l-4.61,3.69-3.2-2.29-4,2.6A.57.57,0,0,1,9.13,15.5Z"/>
</symbol>
<symbol id="battle-field-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M6.77,20.78,4.58,23a.88.88,0,0,0,0,1.24.87.87,0,0,0,.62.26.89.89,0,0,0,.62-.26L8,22a.87.87,0,0,0-1.23-1.23Z"/><path class="foregroundColor" d="M6.44,17.52l4.82,4.82a3.41,3.41,0,0,0-4.82-4.82Z"/><path class="foregroundColor" d="M25.42,23l-2.19-2.18A.87.87,0,0,0,22,22l2.18,2.19a.89.89,0,0,0,.62.26.87.87,0,0,0,.62-.26A.88.88,0,0,0,25.42,23Z"/><path class="foregroundColor" d="M18,15.86l-2.08-2.08,7.18-7.17a.63.63,0,1,0-.89-.89L15,12.9,7.83,5.72a.63.63,0,1,0-.89.89l7.18,7.17L12,15.86a.61.61,0,0,0,0,.88.6.6,0,0,0,.88,0L15,14.67l2.08,2.07a.6.6,0,0,0,.88,0A.61.61,0,0,0,18,15.86Z"/><path class="foregroundColor" d="M18.74,17.52a3.41,3.41,0,0,0,0,4.82l4.82-4.82A3.41,3.41,0,0,0,18.74,17.52Z"/>
</symbol>
<symbol id="bounds" viewBox="0 0 30 30">
<path class="foregroundColor" d="M22.27,19.54V7.73H10.46V6H6v4.46H7.73V22.27H19.54V24H24V19.54ZM7,9.46V7H9.46V9.46ZM8.73,21.27V10.46h1.73V8.73H21.27V19.54H19.54v1.73ZM23,23H20.54V20.54H23Z"/>
</symbol>
<symbol id="buffre-polygon" viewBox="0 0 30 30">
<path class="foregroundColor" d="M7.84,4.8A4.5,4.5,0,0,0,6.25,6.54L8.14,4.65C8,4.71,7.93,4.74,7.84,4.8Z"/><path class="foregroundColor" d="M9,5.92h.18l1.73-1.73a4.74,4.74,0,0,0-.65-.05L8.44,5.92Z"/><polygon class="foregroundColor" points="8.22 6.67 8.22 6.15 5.61 8.76 5.44 9.63 8.22 6.86 8.22 6.67"/><polygon class="foregroundColor" points="11.43 5.92 12.72 4.63 12.16 4.48 10.72 5.92 11.43 5.92"/><polygon class="foregroundColor" points="8.22 8.43 5.06 11.58 4.89 12.46 8.22 9.13 8.22 8.43"/><polygon class="foregroundColor" points="8.34 10.59 4.51 14.41 4.34 15.29 8.17 11.47 8.34 10.59"/><polygon class="foregroundColor" points="12.18 7.01 12.53 7.11 14.52 5.12 13.96 4.97 12.18 6.75 12.18 7.01"/><polygon class="foregroundColor" points="14.32 7.59 16.31 5.6 15.75 5.45 13.76 7.44 14.32 7.59"/><polygon class="foregroundColor" points="12.28 9.63 12.18 9.6 12.18 9.73 12.28 9.63"/><polygon class="foregroundColor" points="7.79 13.41 3.97 17.24 3.8 18.11 7.62 14.29 7.79 13.41"/><polygon class="foregroundColor" points="11.43 9.88 11.32 9.88 10.95 10.26 10.78 11.13 12.03 9.88 11.43 9.88"/><path class="foregroundColor" d="M5.76,18.26h.17l1.14-1.14.17-.88L3.42,20.06l-.09.47c0,.12,0,.25,0,.37l2.47-2.47Z"/><polygon class="foregroundColor" points="16.11 8.08 18.1 6.09 17.55 5.94 15.56 7.93 16.11 8.08"/><polygon class="foregroundColor" points="10.23 13.96 14.07 10.12 13.52 9.96 10.4 13.08 10.23 13.96"/><path class="foregroundColor" d="M5.76,20,3.37,22.39a4.26,4.26,0,0,0,.16.54l2.23-2.22Z"/><polygon class="foregroundColor" points="17.91 8.56 19.9 6.57 19.34 6.42 17.35 8.41 17.91 8.56"/><polygon class="foregroundColor" points="9.68 16.79 15.87 10.6 15.31 10.45 9.85 15.91 9.68 16.79"/><polygon class="foregroundColor" points="17.11 10.93 11.52 16.52 13.57 15.18 17.66 11.08 17.11 10.93"/><path class="foregroundColor" d="M6.51,22.23H5.82L4.08,24a3.9,3.9,0,0,0,.31.4l2.13-2.13Z"/><polygon class="foregroundColor" points="19.7 9.04 21.69 7.05 21.14 6.91 19.15 8.89 19.7 9.04"/><polygon class="foregroundColor" points="9.72 21.31 11.51 19.51 9.72 20.69 9.72 21.31"/><path class="foregroundColor" d="M22,9.07l1.54-1.53h0l-.52-.14L21.25,9.07Z"/><path class="foregroundColor" d="M8.09,22.23,5.22,25.1l0,0a3.62,3.62,0,0,0,.43.24L8.8,22.23Z"/><polygon class="foregroundColor" points="19.2 11.5 18.9 11.42 18.12 12.2 19.2 11.5"/><path class="foregroundColor" d="M16.07,16.53,6.82,25.77a3.42,3.42,0,0,0,.63.09L18.11,15.19Z"/><path class="foregroundColor" d="M24.08,9.07v.16L25,8.31A2.42,2.42,0,0,0,24.58,8l-1,1.05Z"/><path class="foregroundColor" d="M24.08,11.5l2-2a3.08,3.08,0,0,0-.3-.41L24.08,10.8Z"/><path class="foregroundColor" d="M21.84,13,9.27,25.6a4.07,4.07,0,0,0,.94-.45l.65-.43L22.55,13Z"/><path class="foregroundColor" d="M17.46,20.4l9.25-9.25a3.39,3.39,0,0,0-.13-.57L15.41,21.74Z"/><path class="foregroundColor" d="M24.06,16.08l2.26-2.26a4.26,4.26,0,0,0,.35-1.06L22,17.42Z"/><polygon class="foregroundColor" points="9.75 9.88 9.24 9.88 9.12 10.52 8.74 12.47 8.57 13.34 8.19 15.29 8.02 16.17 7.64 18.12 7.61 18.26 8.21 18.26 8.63 18.26 8.73 17.73 8.9 16.86 9.28 14.91 9.45 14.03 9.83 12.08 10 11.2 10.26 9.88 9.75 9.88"/><polygon class="foregroundColor" points="19.09 9.66 18.53 9.51 17.3 9.17 16.74 9.02 15.5 8.69 14.95 8.54 13.71 8.2 13.15 8.05 12.18 7.79 12.18 8.83 12.34 8.87 12.89 9.02 14.13 9.35 14.69 9.5 15.92 9.84 16.48 9.99 17.72 10.32 18.28 10.47 19.51 10.81 20.07 10.96 20.12 10.97 20.12 10.91 20.12 10.2 20.12 9.94 19.09 9.66"/><polygon class="foregroundColor" points="17.57 13.46 15.52 14.8 10.97 17.78 9.72 18.6 9.72 19.01 9.72 19.03 9.72 19.79 12.06 18.26 14.11 16.92 18.66 13.94 20.12 12.98 20.12 12.48 20.12 12.28 20.12 11.79 17.57 13.46"/><rect class="foregroundColor" x="8.97" y="6.67" width="2.46" height="2.46"/><rect class="foregroundColor" x="20.87" y="9.82" width="2.46" height="2.46"/><rect class="foregroundColor" x="6.51" y="19.01" width="2.46" height="2.46"/>
</symbol>
<symbol id="center-selected-tr-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.56,11.5v-3h-3v1h-7v-1h-3v3h1v7h-1v3h3v-1h7v1h3v-3h-1v-7Zm-2,7h-1v1h-7v-1h-1v-7h1v-1h7v1h1Z"/><path class="foregroundColor" d="M14.7,21.78l-2.07,2.07a.43.43,0,0,0,.61.61l1.33-1.34v4.64a.43.43,0,1,0,.86,0V23.12l1.33,1.34a.44.44,0,0,0,.74-.31.39.39,0,0,0-.13-.3l-2.06-2.07A.43.43,0,0,0,14.7,21.78Z"/><path class="foregroundColor" d="M8.22,14.59,6.15,12.53a.43.43,0,0,0-.61.61l1.34,1.33H2.24a.43.43,0,0,0,0,.86H6.88L5.54,16.66a.44.44,0,0,0,.31.74.39.39,0,0,0,.3-.13L8.22,15.2A.43.43,0,0,0,8.22,14.59Z"/><path class="foregroundColor" d="M15.3,8.22l2.07-2.07a.43.43,0,0,0-.61-.61L15.43,6.88V2.24a.43.43,0,1,0-.86,0V6.88L13.24,5.54a.44.44,0,0,0-.74.31.39.39,0,0,0,.13.3l2.06,2.07A.43.43,0,0,0,15.3,8.22Z"/><path class="foregroundColor" d="M21.78,15.2l2.07,2.07a.43.43,0,0,0,.61-.61l-1.34-1.33h4.64a.43.43,0,0,0,0-.86H23.12l1.34-1.33a.44.44,0,0,0-.31-.74.39.39,0,0,0-.3.13l-2.07,2.06A.43.43,0,0,0,21.78,15.2Z"/>
</symbol>
<symbol id="chat" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,6C9.49,6,5,9.67,5,14.19A7.18,7.18,0,0,0,7,19.1L5.6,23.47l4.78-2.06a17.27,17.27,0,0,0,4.5.63C20.65,22,25,18.66,25,14.19S20.51,6,15,6Z"/>
</symbol>
<symbol id="circle-by-3-pts" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15.87,6.24V4.77H11.4V7.29a9.49,9.49,0,0,0-5.15,8.27H4.76V20H7.27a9.5,9.5,0,0,0,8.47,5.21A9.33,9.33,0,0,0,19,24.64v.55h4.46V21.25a9.48,9.48,0,0,0-7.59-15ZM12.4,5.77h2.47V8.23H12.4V5.77ZM6.84,19H5.76V16.56H8.22V19H6.84Zm8.9,5.21A8.52,8.52,0,0,1,8.41,20h.81V15.56h-2A8.5,8.5,0,0,1,11.4,8.43v.8h4.47v-2A8.48,8.48,0,0,1,22.6,20.73H19v2.85A8.47,8.47,0,0,1,15.74,24.23Zm6.72,0H20V21.73h2.46v2.46Z"/>
</symbol>
<symbol id="clear-analysis-emtb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M8,11.66h5.18a.55.55,0,0,0,0-1.1H8a.55.55,0,0,0,0,1.1Z"/><path class="foregroundColor" d="M25.45,17.55c-.06-.29-.14-.59-.23-.88a7.44,7.44,0,0,0-1.83-3,6.87,6.87,0,0,0-1-.82V5.31h-2.7c-1.11,0-2-1-2.7-1.8H9.81c-.74.83-1.59,1.8-2.7,1.8H4.41v19.8h9.36a7,7,0,0,1-1-.82l-.07-.08H5.31v-18h4.5L11.7,8h3.51L17,6.21h4.5v6.1h0a7.52,7.52,0,0,0-7.64.48,5.92,5.92,0,0,0-.48.35H8a.55.55,0,0,0,0,1.1h4.24a7.59,7.59,0,0,0-1,1.55H8a.55.55,0,0,0,0,1.1h2.85c-.06.22-.12.44-.16.66a6.76,6.76,0,0,0-.11.89H8a.55.55,0,0,0-.55.55.54.54,0,0,0,.55.54h2.58a6.56,6.56,0,0,0,.11.89,6.7,6.7,0,0,0,.23.88,7.49,7.49,0,0,0,1.75,2.91l.07.08a8.06,8.06,0,0,0,2.49,1.65,7.5,7.5,0,0,0,5.64,0,7.48,7.48,0,0,0,2.48-1.65,7.31,7.31,0,0,0,1.83-3,8.75,8.75,0,0,0,.23-.88A7.3,7.3,0,0,0,25.45,17.55Zm-12-11.34a.9.9,0,1,1,.9-.9A.9.9,0,0,1,13.41,6.21Zm9.28,17.38-.28.25a6.37,6.37,0,0,1-2.15,1.27,6.39,6.39,0,0,1-4.34,0,6.94,6.94,0,0,1-2.43-1.52,6.4,6.4,0,0,1-1.87-4.06,5,5,0,0,1,0-1.09,6.1,6.1,0,0,1,.32-1.55,6.36,6.36,0,0,1,.49-1.1,6.54,6.54,0,0,1,1.06-1.4,1.22,1.22,0,0,1,.17-.15,6.19,6.19,0,0,1,1.44-1,6.39,6.39,0,0,1,3-.73,6.47,6.47,0,0,1,3.42,1,5.91,5.91,0,0,1,.9.68l.28.25a6.52,6.52,0,0,1,0,9.2Z"/><path class="foregroundColor" d="M18.09,14.36a4.61,4.61,0,0,0-3.7,1.85v-.73a.47.47,0,0,0-.93,0V17.3a.46.46,0,0,0,.47.46h1.81a.46.46,0,0,0,.47-.46.47.47,0,0,0-.47-.46h-.66a3.65,3.65,0,0,1,3-1.55,3.7,3.7,0,1,1-3.69,4,.46.46,0,0,0-.46-.43.44.44,0,0,0-.33.14.47.47,0,0,0-.13.36,4.62,4.62,0,1,0,4.61-5Z"/>
</symbol>
<symbol id="clocs-icon" viewBox="0 0 32 32">

  <g transform="matrix(0.04948600381612778, 0, 0, 0.04948600381612778, 2.7856149673461914, 3.9044981002807617)">
    <g style="" transform="matrix(1, 0, 0, 1, 1.557832, 20.441587)">
      <path class="st0" d="M 449.245 215.216 C 450.97 209.729 472.434 139.076 472.434 139.076 C 472.434 139.076 417.854 151.242 409.556 153.006 C 401.258 154.795 398.582 151.839 397.39 149.161 C 396.222 146.485 389.093 115.362 389.093 115.362 C 389.093 115.362 343.126 170.527 333.32 179.409 C 323.562 188.314 318.209 177.048 319.985 168.447 C 321.762 159.844 341.934 42.095 341.934 42.095 C 341.934 42.095 319.692 53.665 310.496 58.715 C 301.298 63.752 294.971 60.49 290.226 52.583 C 285.48 44.673 252.265 -23.339 252.265 -23.339 C 252.265 -23.339 219.05 44.673 214.306 52.582 C 209.56 60.49 203.234 63.752 194.06 58.714 C 184.837 53.664 162.62 42.094 162.62 42.094 C 162.62 42.094 182.768 159.843 184.545 168.446 C 186.345 177.047 180.992 188.314 171.211 179.408 C 161.428 170.526 115.463 115.361 115.463 115.361 C 115.463 115.361 108.333 146.484 107.141 149.16 C 105.973 151.838 103.296 154.794 94.999 153.005 C 86.701 151.241 32.121 139.075 32.121 139.075 C 32.121 139.075 49.617 206.697 50.809 212.33 C 52.001 217.976 53.875 224.582 42.803 230.118 C 30.929 236.055 23.215 239.316 23.215 239.316 C 23.215 239.316 130.281 330.664 135.027 335.408 C 139.771 340.154 140.672 344.315 138.579 350.532 C 136.511 356.762 115.809 381.97 115.809 381.97 C 115.809 381.97 224.325 370.157 230.7 372.493 C 237.05 374.817 242.258 374.233 244.01 390.999 C 245.714 407.776 240.701 488.661 240.701 488.661 L 251.742 488.661 L 252.782 488.661 L 263.854 488.661 C 263.854 488.661 258.81 407.777 260.562 390.999" style="fill-opacity: 0; stroke-width: 35.0766px; stroke-linejoin: round; stroke-linecap: round;"/>
    </g>
    <path d="M 365.358 262.149 L 365.358 333.513 L 401.031 312.116 M 493.809 333.513 C 493.809 404.455 436.301 461.981 365.358 461.981 C 294.416 461.981 236.89 404.455 236.89 333.513 C 236.89 262.57 294.416 205.062 365.358 205.062 C 436.301 205.062 493.809 262.57 493.809 333.513 Z" stroke-linecap="round" stroke-linejoin="round" style="fill-opacity: 0; stroke-width: 35.0766px;"/>
  </g>


</symbol>
<symbol id="cluster-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M20.9,11.18l-.63.63A5.26,5.26,0,1,1,14.08,18l-.63.62a6,6,0,1,0,7.45-7.44Z"/><path class="foregroundColor" d="M22.43,20.13v-6h-.76V15.3l-2.44-.74-2.45.74-.76.76v4.07Zm-3.2-4.77,2.44.74v3.27H16.78V16.1Z"/><path class="foregroundColor" d="M14.2,24.28a5.94,5.94,0,0,0,3.18-.91,5.23,5.23,0,0,1-1.12-.27,5.25,5.25,0,0,1-6-1.32l-.54.54A6,6,0,0,0,14.2,24.28Z"/><path class="foregroundColor" d="M9.81,20.11,13,16.89V12.51a1.55,1.55,0,0,1-.28.2,2.53,2.53,0,0,1-.33.16l-.37.13a2.79,2.79,0,0,1-.38.07V11.88a5.82,5.82,0,0,0,1-.41,5.75,5.75,0,0,0,.88-.55h.86v4.56l6.7-6.7-.31-.31A9.44,9.44,0,1,0,7.5,21.82l.3.3,1.39-1.38Z"/>
</symbol>
<symbol id="collapse-all-r-tb" viewBox="0 0 30 30">
<rect class="foregroundColor" x="20.1" y="4" width="6" height="6"/><path class="foregroundColor" d="M23.81,14.28l1.39,1.39a.61.61,0,0,0,1-.43.61.61,0,0,0-.18-.43l-2.43-2.43a.6.6,0,0,0-.86,0l-2.43,2.43a.61.61,0,0,0,.86.86l1.39-1.39V25.92a.61.61,0,0,0,.61.61.61.61,0,0,0,.61-.61Z"/><path class="foregroundColor" d="M15.61,7.63,14.22,9a.61.61,0,1,0,.86.86l2.43-2.43a.6.6,0,0,0,0-.86L15.08,4.16a.61.61,0,0,0-.86.86l1.39,1.39H4A.61.61,0,0,0,3.36,7,.61.61,0,0,0,4,7.63Z"/><path class="foregroundColor" d="M16.47,14.28v2a.62.62,0,0,0,.61.61.61.61,0,0,0,.61-.61V12.81a.61.61,0,0,0-.61-.61H13.64a.61.61,0,0,0,0,1.22h2L7.38,21.65a.61.61,0,0,0,.86.86Z"/>
</symbol>
<symbol id="collapse-r-tb" viewBox="0 0 30 30">
<rect class="foregroundColor" x="20.1" y="11.13" width="6" height="6"/><rect class="foregroundColor" x="20.1" y="4.13" width="6" height="6"/><rect class="foregroundColor" x="20.1" y="18.13" width="6" height="6"/><path class="foregroundColor" d="M15.61,14.76l-1.39,1.4a.6.6,0,0,0,0,.86.63.63,0,0,0,.86,0l2.43-2.44a.6.6,0,0,0,0-.86l-2.43-2.43a.62.62,0,0,0-.86,0,.6.6,0,0,0,0,.86l1.39,1.4H4a.61.61,0,1,0,0,1.21Z"/>
</symbol>
<symbol id="collapse-up-r-tb-up" viewBox="0 0 30 30">
<rect class="foregroundColor" x="13.1" y="4" width="6" height="6"/><rect class="foregroundColor" x="6.1" y="4" width="6" height="6"/><rect class="foregroundColor" x="20.1" y="4" width="6" height="6"/><path class="foregroundColor" d="M16.73,14.48l1.39,1.4a.6.6,0,0,0,.86,0,.61.61,0,0,0,.18-.43A.6.6,0,0,0,19,15l-2.43-2.43a.6.6,0,0,0-.86,0L13.26,15a.61.61,0,0,0,.86.86l1.39-1.4V26.13a.61.61,0,0,0,.61.61.61.61,0,0,0,.61-.61Z"/>
</symbol>
<symbol id="comment-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M12,5.3c-4.07,0-7.37,2.75-7.37,6.13a5.33,5.33,0,0,0,1.29,3.48L4.73,18.7l4.14-1.78a12.4,12.4,0,0,0,3.05.4c4.25,0,7.46-2.53,7.46-5.89S16.07,5.3,12,5.3Z"/>
</symbol>
<symbol id="connect-all" viewBox="0 0 30 30">
<path class="foregroundColor" d="M16.18,17.36a.13.13,0,0,0-.09,0,.12.12,0,0,0-.09,0l-3.07,3.07a2.38,2.38,0,0,1-3.36-3.36L12.64,14a.18.18,0,0,0,0-.09.13.13,0,0,0,0-.09l-.88-.89a.15.15,0,0,0-.18,0L8.51,16a3.89,3.89,0,0,0,0,5.49,3.9,3.9,0,0,0,5.49,0l3.06-3.07a.13.13,0,0,0,0-.18Z"/><path class="foregroundColor" d="M21.49,8.51a3.89,3.89,0,0,0-5.49,0l-3.09,3.1a.12.12,0,0,0,0,.17l.88.89a.18.18,0,0,0,.09,0,.15.15,0,0,0,.09,0l3.1-3.1a2.37,2.37,0,0,1,4.05,1.68,2.33,2.33,0,0,1-.69,1.68L17.33,16a.13.13,0,0,0,0,.18l.89.88a.09.09,0,0,0,.08,0,.1.1,0,0,0,.09,0L21.49,14A3.89,3.89,0,0,0,21.49,8.51Z"/>
</symbol>
<symbol id="connection" viewBox="0 0 18 12">
<rect class="foregroundColor" x="1" y="5" width="16" height="2"/>
</symbol>
<symbol id="create-control-feature-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.25,15.72a9.87,9.87,0,0,0,.09-1.22,9.5,9.5,0,1,0-9.5,9.5c.26,0,.51,0,.77,0a6,6,0,1,0,8.64-8.24ZM14.84,23a8.5,8.5,0,1,1,8.5-8.5c0,.18,0,.36,0,.54a5.68,5.68,0,0,0-.78-.39c0-.05,0-.1,0-.15a7.7,7.7,0,1,0-7.91,7.69A5.2,5.2,0,0,0,15,23Zm0-11.92a3.42,3.42,0,0,0-.1,6.83,6,6,0,0,0-.46,2.3,6.25,6.25,0,0,0,.09,1,6.7,6.7,0,1,1,7.16-6.84,5.7,5.7,0,0,0-1.25-.13,5.94,5.94,0,0,0-2,.36V14.5A3.41,3.41,0,0,0,14.84,11.08Zm5.44,14.37a5.19,5.19,0,0,1-3.76-1.61,5.51,5.51,0,0,1-.69-.9,5.62,5.62,0,0,1-.4-.77,4.54,4.54,0,0,1-.29-1,4.95,4.95,0,0,1,.51-3.37,5.24,5.24,0,0,1,2.45-2.36A5.31,5.31,0,0,1,20.28,15a5.52,5.52,0,0,1,1.23.16,6.44,6.44,0,0,1,1,.34,5.46,5.46,0,0,1,.75.41,5.27,5.27,0,0,1,.87.76,5.23,5.23,0,0,1-3.81,8.82Z"/><path class="foregroundColor" d="M23.31,19.62H20.87V17.18a.59.59,0,0,0-.59-.58.58.58,0,0,0-.58.58v2.44H17.26a.59.59,0,1,0,0,1.17H19.7v2.44a.59.59,0,0,0,.58.59.6.6,0,0,0,.59-.59V20.79h2.44a.59.59,0,0,0,0-1.17Z"/>
</symbol>
<symbol id="create-entity-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23.88,16.14V9.88l-9-5.18-9,5.18V20.25l9,5.18,1-.56a6,6,0,1,0,8-8.73Zm-1-5.68V15.6a6,6,0,0,0-6.07.85l-1.24-1.38,5.22-5.82Zm-8-4.61,5,2.89-5,5.58-5-5.58Zm-8,13.82V10.46L9,9.25l5.22,5.82L9,20.88Zm8,4.61-5-2.89,5-5.58,1.19,1.33a6,6,0,0,0-.78,6.9Zm5.66,2.09a5.23,5.23,0,0,1-4-1.89,5.33,5.33,0,0,1-1.24-3.35,5.16,5.16,0,0,1,1.29-3.41,5.87,5.87,0,0,1,.72-.69,5.09,5.09,0,0,1,5.55-.57,5.53,5.53,0,0,1,1,.65,5.22,5.22,0,0,1-3.32,9.26Z"/><path class="foregroundColor" d="M23.58,20.55H21.14V18.1a.59.59,0,0,0-1.17,0v2.45H17.53a.58.58,0,0,0-.58.58.59.59,0,0,0,.58.59H20v2.44a.59.59,0,0,0,1.17,0V21.72h2.44a.59.59,0,0,0,.59-.59A.58.58,0,0,0,23.58,20.55Z"/><path class="foregroundColor" d="M24.64,7.13l-.06-.23a2.32,2.32,0,0,1,0-.24,1.15,1.15,0,0,1,0-.33.94.94,0,0,1,.1-.28,1.44,1.44,0,0,1,.18-.25,2.72,2.72,0,0,1,.24-.24l.26-.23a1.2,1.2,0,0,0,.19-.21,1.27,1.27,0,0,0,.12-.23.66.66,0,0,0,0-.25.85.85,0,0,0,0-.23.43.43,0,0,0-.13-.17.62.62,0,0,0-.19-.12.71.71,0,0,0-.26,0,1.84,1.84,0,0,0-.63.12,2.15,2.15,0,0,0-.62.39V3.41a2.48,2.48,0,0,1,.65-.27,3.06,3.06,0,0,1,.74-.09,2.8,2.8,0,0,1,.67.08,1.85,1.85,0,0,1,.55.25,1.19,1.19,0,0,1,.36.44,1.45,1.45,0,0,1,.13.65,1.88,1.88,0,0,1-.05.43,1.46,1.46,0,0,1-.16.36,1.64,1.64,0,0,1-.26.33l-.36.33-.24.21-.18.19a1,1,0,0,0-.1.21.88.88,0,0,0,0,.24.62.62,0,0,0,0,.19.67.67,0,0,0,.07.17Zm.57,1.71a.73.73,0,0,1-.52-.19.63.63,0,0,1-.2-.46.61.61,0,0,1,.2-.46.77.77,0,0,1,.52-.18.72.72,0,0,1,.51.18.57.57,0,0,1,.21.46.6.6,0,0,1-.2.46A.73.73,0,0,1,25.21,8.84Z"/>
</symbol>
<symbol id="create-materiel-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.24,7.13a.86.86,0,0,1-.06-.23,2.32,2.32,0,0,1,0-.24,1.69,1.69,0,0,1,0-.33,1.36,1.36,0,0,1,.11-.28,1.38,1.38,0,0,1,.17-.25,2.89,2.89,0,0,1,.25-.24L25,5.33a1.8,1.8,0,0,0,.19-.21,1,1,0,0,0,.12-.23.93.93,0,0,0,0-.25.59.59,0,0,0,0-.23.52.52,0,0,0-.12-.17.56.56,0,0,0-.2-.12.66.66,0,0,0-.25,0,1.8,1.8,0,0,0-.63.12,2.15,2.15,0,0,0-.62.39V3.41a2.35,2.35,0,0,1,.65-.27,3,3,0,0,1,.74-.09,2.86,2.86,0,0,1,.67.08,1.85,1.85,0,0,1,.55.25,1.39,1.39,0,0,1,.36.44,1.45,1.45,0,0,1,.13.65,1.88,1.88,0,0,1,0,.43,1.46,1.46,0,0,1-.16.36,1.4,1.4,0,0,1-.27.33,2.94,2.94,0,0,1-.36.33l-.24.21-.17.19a1,1,0,0,0-.1.21.86.86,0,0,0,0,.24.63.63,0,0,0,0,.19.67.67,0,0,0,.07.17Zm.57,1.71a.73.73,0,0,1-.52-.19.6.6,0,0,1-.21-.46.59.59,0,0,1,.21-.46.77.77,0,0,1,.52-.18.72.72,0,0,1,.51.18.57.57,0,0,1,.21.46.6.6,0,0,1-.2.46A.73.73,0,0,1,24.81,8.84Z"/><path class="foregroundColor" d="M23.69,16.09a.36.36,0,0,1,0-.1c0-.31,0-.61,0-.92a9,9,0,0,0-9-9h0a8.57,8.57,0,0,0-1.44.13,8.45,8.45,0,0,0-2.23.66,3.55,3.55,0,0,0-.44.21,9,9,0,0,0,2.17,16.78l.33.06.08,0L14,24c.25,0,.51,0,.77,0h0c.16,0,.3,0,.46,0a6,6,0,1,0,8.48-7.95Zm-2.53-5.95A7.74,7.74,0,0,1,22,11.39a5.93,5.93,0,0,1,.31.68,7.9,7.9,0,0,1,.58,3c0,.18,0,.36,0,.54a6,6,0,0,0-6,.78l-1.32-1.32L20.8,9.72Q21,9.93,21.16,10.14ZM9.49,8.93a8.12,8.12,0,0,1,3.79-1.81A5.93,5.93,0,0,1,14,7a6.28,6.28,0,0,1,.75,0l.54,0h.13A8.08,8.08,0,0,1,20.1,9l-5.35,5.35L9.41,9ZM7.74,19.09c-.11-.19-.22-.4-.32-.6A7.79,7.79,0,0,1,7,17.35a6.21,6.21,0,0,1-.21-.85,8.54,8.54,0,0,1-.13-1.43v0a9.52,9.52,0,0,1,.12-1.32,8.56,8.56,0,0,1,.24-1,8,8,0,0,1,1.68-3L14,15.07,8.71,20.4A7.41,7.41,0,0,1,7.74,19.09Zm7,4.06A7.78,7.78,0,0,1,13.14,23l-.45-.11-.39-.11A8.1,8.1,0,0,1,10,21.61c-.21-.15-.41-.32-.61-.49l5.35-5.35L16,17.07a6,6,0,0,0-1.59,4.06,6.12,6.12,0,0,0,.35,2Zm5.7,3.22A5.22,5.22,0,0,1,16.05,24a5.66,5.66,0,0,1-.46-.88,5.26,5.26,0,0,1,4.86-7.19,5.49,5.49,0,0,1,3.1,1,5.22,5.22,0,0,1-3.1,9.44Z"/><path class="foregroundColor" d="M23.47,20.55H21V18.1a.59.59,0,0,0-1.17,0v2.45H17.42a.58.58,0,0,0-.58.58.59.59,0,0,0,.58.59h2.44v2.44a.59.59,0,0,0,1.17,0V21.72h2.44a.6.6,0,0,0,.59-.59A.59.59,0,0,0,23.47,20.55Z"/>
</symbol>
<symbol id="create-radar-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23.77,15.66a9,9,0,0,0-.72-5.24l-.15-.32-.1-.22h0a9,9,0,0,0-5-4.18,9.14,9.14,0,0,0-2.93-.49,8.87,8.87,0,0,0-3.78.84A9,9,0,0,0,6.74,18a9,9,0,0,0,8.16,5.2h0a6,6,0,1,0,8.82-7.56Zm-1.1-.61a6.08,6.08,0,0,0-1.6-.42v0a1.09,1.09,0,0,0,0-.18v-.06c0-.11,0-.23,0-.34s0-.14,0-.21,0-.26,0-.37,0-.17,0-.25l-.06-.33a2.17,2.17,0,0,0-.07-.24l-.09-.32-.06-.16,1.49-.7A7.75,7.75,0,0,1,22.67,15.05Zm-4.58,0c0-.09.05-.17.07-.26s0-.09,0-.14v-.06c0-.13,0-.27,0-.4s0-.13,0-.19,0-.29-.06-.42,0-.12,0-.17l1.56-.73v0a2.58,2.58,0,0,1,.08.26c0,.07,0,.14.05.2s0,.18.05.26l0,.21a2.28,2.28,0,0,1,0,.26c0,.07,0,.14,0,.21a2.44,2.44,0,0,1,0,.27v.19s0,0,0,.06A6,6,0,0,0,18.09,15Zm-4.41-.75a.54.54,0,0,1,0,.08.9.9,0,0,0,0,.13l0,.1,0,.13.05.09a1.15,1.15,0,0,0,.07.11L14,15l.06.07.11.09.08.06.12.06.08,0,.14,0,.09,0h.22l.18,0h.08l.1,0,.13,0h0l.06,0a1.23,1.23,0,0,0,.22-.15l.05,0a1.55,1.55,0,0,0,.19-.24h0l0,0a1.24,1.24,0,0,0,.11-.28c0-.09,0-.15,0-.22l.89-.41a1,1,0,0,1,0,.16.66.66,0,0,1,0,.14,2.26,2.26,0,0,1,0,.26h0v.05a.15.15,0,0,1,0,.07,1.93,1.93,0,0,1-.09.38,2.11,2.11,0,0,1-1.11,1.2,2,2,0,0,1-.9.2,2.13,2.13,0,0,1-.9-4.07,2.09,2.09,0,0,1,.4-.13h.13a1.36,1.36,0,0,1,.29,0h0l-.35,1-.11,0-.09.05-.12.07-.09.08-.08.07-.07.09-.08.1a.3.3,0,0,0,0,.08l-.07.13a.64.64,0,0,1,0,.07l0,.16a.28.28,0,0,0,0,.09h0v.16Zm2.16.17h0Zm0-.26h0v0Zm-3.64,7.39a7.82,7.82,0,0,1-.64-14.44,7.7,7.7,0,0,1,3.29-.73,7.93,7.93,0,0,1,2.55.42,7.78,7.78,0,0,1,4.28,3.57l-2.29,1.07-2.62,1.22-1.07.5.62-1.71.08-.21a.6.6,0,0,0-.35-.76l-.2,0a.59.59,0,0,0-.56.39l0,.08h-.71l-.4.06h0L14,11a3.24,3.24,0,0,0-2.13,4.59,3.39,3.39,0,0,0,.3.51.64.64,0,0,0,.1.13,2.56,2.56,0,0,0,.31.34l.11.11a3.22,3.22,0,0,0,.45.33.2.2,0,0,0,.08,0,3.34,3.34,0,0,0,.43.21l.18.07a3.06,3.06,0,0,0,.41.1h.05l.1,0a2.77,2.77,0,0,0,.49,0h0a1.75,1.75,0,0,0,.24,0,5.9,5.9,0,0,0-.69,1.7,4.7,4.7,0,0,1-1.33-.27,5,5,0,0,1-2.83-2.59,5,5,0,0,1,6.12-6.88.63.63,0,0,0,.19,0,.59.59,0,0,0,.56-.4.59.59,0,0,0,0-.45.62.62,0,0,0-.34-.3,6.29,6.29,0,0,0-2-.32,6.2,6.2,0,0,0-2.11,12c.19.07.37.12.56.17l.13,0,.06,0,.37.07.17,0h.06l.22,0c0,.07,0,.13,0,.2A5.82,5.82,0,0,0,14.52,22,7.9,7.9,0,0,1,12.22,21.57Zm8.11,4.25a5.22,5.22,0,0,1-4.52-2.65A5.11,5.11,0,0,1,15.32,22a5.29,5.29,0,0,1-.23-1.44c0-.06,0-.12,0-.17a5.1,5.1,0,0,1,.18-1.19,5.2,5.2,0,0,1,1.1-2.06,5.48,5.48,0,0,1,1.18-1,5.33,5.33,0,0,1,2.2-.75,5.07,5.07,0,0,1,.56-.05c.22,0,.43,0,.64.06a5.3,5.3,0,0,1,1.58.44,5.9,5.9,0,0,1,1,.66,5.22,5.22,0,0,1-3.26,9.32Z"/><path class="foregroundColor" d="M23.36,20H20.92V17.55a.59.59,0,0,0-1.17,0V20H17.31a.6.6,0,0,0-.59.59.59.59,0,0,0,.59.58h2.44V23.6a.59.59,0,1,0,1.17,0V21.16h2.44a.58.58,0,0,0,.58-.58A.59.59,0,0,0,23.36,20Z"/>
</symbol>
<symbol id="create-route-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M25.09,7.91v-3h-3v1H7.91v-1h-3v3h1V22.09h-1v3h3v-1H22.09V25h3V22h-1v-14Zm-2,14h-1v1.14H7.91v-1h-1V7.91h1v-1H22.09v1h1Z"/><path class="foregroundColor" d="M13,14.15a7.62,7.62,0,0,0-1.22-.5,6.84,6.84,0,0,0,3.13-3L13.55,10l2.94-1.23.56,3.16-1.3-.71A8,8,0,0,1,13,14.15Zm1.91,4.61-1.33.73,2.94,1.24.56-3.17-1.3.72a7.46,7.46,0,0,0-6.81-4.05v1A6.52,6.52,0,0,1,14.88,18.76Zm6.06-4-2.5-2v1.5H14.55a7.57,7.57,0,0,1-.63.49c.22.16.43.33.64.51h3.88v1.5Z"/>
</symbol>
<symbol id="create-unit-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23.59,20.55H21.15V18.1a.58.58,0,0,0-.58-.58.59.59,0,0,0-.59.58v2.45H17.54a.58.58,0,0,0-.58.58.59.59,0,0,0,.58.59H20v2.44a.59.59,0,0,0,.59.58.58.58,0,0,0,.58-.58V21.72h2.44a.59.59,0,0,0,.59-.59A.58.58,0,0,0,23.59,20.55Z"/><path class="foregroundColor" d="M23.92,9.2a1.38,1.38,0,0,1-.44-1V8.13H4.82v14h9.84A6,6,0,1,0,25,17.08V9.58h0A1.49,1.49,0,0,1,23.92,9.2Zm.06.37V16.2a6,6,0,0,0-6.72-.07l-1.47-1Zm-1.13-.44-8,5.4-8-5.4Zm-17,.44L14,15.13,5.82,20.69Zm8.75,11.56H6.94l8-5.39,1.54,1a6,6,0,0,0-1.87,4.35Zm11.24,0a5.24,5.24,0,0,1-10.38,1,5.38,5.38,0,0,1-.1-1h0a5.21,5.21,0,0,1,1.77-3.9,5.29,5.29,0,0,1,.87-.62,5.13,5.13,0,0,1,2.6-.72,5.28,5.28,0,0,1,5.24,5.24Z"/><path class="foregroundColor" d="M26.21,3.38a1.85,1.85,0,0,0-.55-.25A2.8,2.8,0,0,0,25,3.05a3,3,0,0,0-.74.09,2.48,2.48,0,0,0-.65.27V4.59a2.15,2.15,0,0,1,.62-.39,1.84,1.84,0,0,1,.63-.12.75.75,0,0,1,.26,0,.62.62,0,0,1,.19.12.33.33,0,0,1,.12.17.59.59,0,0,1,0,.23.66.66,0,0,1,0,.25,1,1,0,0,1-.12.23,1.2,1.2,0,0,1-.19.21l-.26.23a2.72,2.72,0,0,0-.24.24,1.44,1.44,0,0,0-.18.25,1.36,1.36,0,0,0-.11.28,1.69,1.69,0,0,0,0,.33,2.32,2.32,0,0,0,0,.24l.06.23h1A.67.67,0,0,1,25.34,7a.62.62,0,0,1,0-.19.88.88,0,0,1,0-.24,1,1,0,0,1,.1-.21l.18-.19.24-.21.36-.33a1.64,1.64,0,0,0,.26-.33,1.46,1.46,0,0,0,.16-.36,1.88,1.88,0,0,0,.05-.43,1.45,1.45,0,0,0-.13-.65A1.39,1.39,0,0,0,26.21,3.38Z"/><path class="foregroundColor" d="M25.46,7.73A.72.72,0,0,0,25,7.55a.77.77,0,0,0-.52.18.56.56,0,0,0-.19.4s0,0,0,.06a.59.59,0,0,0,.2.46.73.73,0,0,0,.52.19h0a.69.69,0,0,0,.49-.18.6.6,0,0,0,.2-.46A.57.57,0,0,0,25.46,7.73Z"/>
</symbol>
<symbol id="cursit" viewBox="0 0 30 30">
<path class="foregroundColor" d="M13.37,7.21c.14.2,1.18-.28,1-.25.32-.17,0-.18-.14-.28a2.18,2.18,0,0,0-.31-1l.13-.15c-.3-.43-.52.52-.52.52l.16,0-.08.19a.54.54,0,0,1,0,.48l-.22.13c-.07.1.37.11.38.12S13.27,7.09,13.37,7.21Z"/><path class="foregroundColor" d="M12.81,6.86c.18-.08.43-.07.45-.31l.11-.15c.05-.07-.09-.19-.15-.21L13,6.34l-.1,0-.1.12v.07l-.12.16C12.57,6.84,12.69,6.92,12.81,6.86Z"/><path class="foregroundColor" d="M15,1A14,14,0,1,0,29,15,14,14,0,0,0,15,1Zm5.57,6.54c.1,0-.14.22-.14.22,0,.32.28.5.74.67s0,.58-.27.49-1.09-.32-1.12,0c0,.21-.87,0-.74-.23s.07-.49.24-.72.49-.15.49,0C19.77,8.56,20.28,7.58,20.57,7.54ZM15,27.92a12.87,12.87,0,0,1-9.34-4c-.28-.29-.49-1,0-1.27l.47-.12c.36-.31-.34-1.57-.06-1.77.87-.62.45-1.39-.08-2-.23-.26-1.48-1.55-1.61-1.32a2.91,2.91,0,0,0-.43-1.53,6.74,6.74,0,0,1-1-1.53c-.07-.24-.07-1-.22-1.17s-.5-.25-.49-.36a13,13,0,0,1,3.34-6.7l1.16-.47c.73-1.09.77-.24,1.42-.53.22,0,.47-.89.71-1,.4-.27.09-.27,0-.38s3.4-1.56,3.46-1.33S10.6,3.54,10.8,3.51c-.47.06-.55.89-.47.89.23,0,1.67-1.1,2.34-.87S14.51,3,15,2.63c.23-.19.44-.53.75-.53a11.43,11.43,0,0,1,4,.87c.74.43-.32.4-.43.69a3.08,3.08,0,0,1-.39.64c-.15.2-.62.12-.52-.13s.53-.36-.14-.42-.88-.53-1.57,0a2.54,2.54,0,0,0-.5.68c-.19.32-.65.31-.84.61s.1.8.47.58c.09-.06,1.18,1,1,.14-.09-.56.31-.95.31-1.37s.44-.11.32,0-.31.83.08.86c.17,0,.66-.35.72-.08-.08-.26-.55.55-.52.54-.17.07-.36,0-.24.32s-.63.31-.76.39-.65-.18-.64,0c-.2-.17.06-.59-.11-.65-.17.26-.09.79-.46.79a1.62,1.62,0,0,0-.93.58,3.69,3.69,0,0,1-1,.46c.47.05.45.39.41.68-.09.67-1.55,0-1.49.31a3.83,3.83,0,0,1-.21,1.12c0,.19.62.32.58.42a10.58,10.58,0,0,0,1-.39l.2-.46A3.45,3.45,0,0,1,14.6,9l.23-.4A6.68,6.68,0,0,1,15.9,8.5a3.64,3.64,0,0,1,.82.75c.06.08.36.18.36.29v.35c.16.3.2-.62.13-.45,0-.29.22.11.27.08l-1-1c-.32-.53.84.28,1,.38s.45,1,.84.78c.24-.12,0-.21.16-.35l.7-.14c-.5.36.25.82.29.86.24.1.39-.09.54,0s1,0,.88-.13c.23.12.13,1.16-.07,1.32a3.25,3.25,0,0,1-2.17-.09c-.56-.4-.46.43-.74.55-.53.22-1.33-.72-1.89-.75.28,0,0-.69,0-.74-.22-.27-1.59,0-2,.07a3.67,3.67,0,0,0-1.9.51c-.36.29-.36.77-.74,1-.23.15-.5.1-.7.29a4.82,4.82,0,0,0-1,1.31c-.08.19.11.65.06.89-.47,1.51.12,3.59,1.94,3.82.44.05.9.29,1.35.16a4.88,4.88,0,0,1,1.05-.38c.53,0,.31.75,1,.54.36-.11.52.24.52.5-.12.55-.38.86.12,1.25a1.74,1.74,0,0,1,.69,1.1c0,.26.27.7,0,.85s-.36.71-.36.91.52.68.66.88.05.61.22.93a6.48,6.48,0,0,1,.46.91c.16.46,1.35,0,1.63,0,1,0,1.43-1.33,2.1-1.74.38-.23.27-.87.72-1.19s.86-.49.9-1.05-.37-1.5-.15-1.91,3.06-4.44,2.23-4.63l-1.18.56c-.23,0-1.11-.93-1.34-1.17a10.85,10.85,0,0,1-1-1.71c-.21-.31-.83-.91-.83-1.3,0,.07.29.51.41.42l0-.22a4.52,4.52,0,0,0,.89,1.06c.36.2.36.78.71,1,.68.52.5,2,1.49,1.1.66-.6,2-1.74,1.62-2.78-.18-.55-1-.14-1.29.05-.27-.14-1.28-1.25-.89-1.39.2-.07.63.51.79.59h.74c.21.45,1.61-.45,1.94-.32a.75.75,0,0,1,.46.49,12,12,0,0,1,.47,3.4A12.92,12.92,0,0,1,15,27.92Z"/>
</symbol>
<symbol id="delete-all" viewBox="0 0 30 30">
<path class="foregroundColor" d="M17.92,8.75V7.67A.92.92,0,0,0,17,6.75H13a.92.92,0,0,0-.92.92V8.75H7.42V9.92H8.78l1.3,11.75a1.59,1.59,0,0,0,1.59,1.58h6.66a1.57,1.57,0,0,0,1.58-1.56L21.22,9.92h1.36V8.75Zm-4.67,0V7.92h3.5v.83Zm5.08,13.33H11.67a.42.42,0,0,1-.42-.44L10,9.92h10.1l-1.3,11.75A.41.41,0,0,1,18.33,22.08Z"/><path class="foregroundColor" d="M13.33,11.67A.33.33,0,0,0,13,12v7.33a.34.34,0,1,0,.67,0V12A.34.34,0,0,0,13.33,11.67Z"/><path class="foregroundColor" d="M16.67,11.67a.34.34,0,0,0-.34.33v7.33a.34.34,0,1,0,.67,0V12A.33.33,0,0,0,16.67,11.67Z"/>
</symbol>
<symbol id="delete-layer-layer-mngr" viewBox="0 0 16 16">
<path class="foregroundColor" d="M10.25,3.25V2.5a.76.76,0,0,0-.75-.75h-3a.76.76,0,0,0-.75.75v.75H2.25v1h1l1,8.75A1.25,1.25,0,0,0,5.5,14.25h5A1.24,1.24,0,0,0,11.75,13l1-8.78h1v-1Zm.5,9.75a.25.25,0,0,1-.25.25h-5A.26.26,0,0,1,5.25,13l-1-8.72h7.44Zm-4-9.75v-.5h2.5v.5Z"/>
</symbol>
<symbol id="delete-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M14.36,6.75V5.58A.58.58,0,0,0,13.77,5H10.23a.58.58,0,0,0-.59.58V6.75H5.5v.58H6.68l1.18,10.5A1.18,1.18,0,0,0,9.05,19H15a1.18,1.18,0,0,0,1.19-1.17l1.18-10.5H18.5V6.75Zm-4.13,9V9.38a.3.3,0,1,1,.59,0v6.41a.3.3,0,0,1-.59,0Zm3.54,0a.3.3,0,0,1-.59,0V9.38a.3.3,0,1,1,.59,0ZM10.23,5.73a.12.12,0,0,1,0-.1.16.16,0,0,1,.11,0h3.25a.16.16,0,0,1,.1,0,.12.12,0,0,1,0,.1v1H10.23Z"/>
</symbol>
<symbol id="delete" viewBox="0 0 18 18">
<path class="foregroundColor" d="M11.07,4.66V4a.7.7,0,0,0-.71-.7H7.64a.7.7,0,0,0-.71.7v.66H3.75v1h.94l.88,7.94A1.16,1.16,0,0,0,6.73,14.7h4.54a1.15,1.15,0,0,0,1.16-1.13l.88-8h.94v-1ZM7.89,4.25h2.22v.41H7.89Zm3.59,9.27v0a.2.2,0,0,1-.21.2H6.73a.21.21,0,0,1-.21-.23L5.64,5.61h6.72Z"/>
</symbol>
<symbol id="delete_item_small" viewBox="0 0 20 20">
<path class="foregroundColor" d="M5.76,5.76a6,6,0,1,0,8.48,0A6,6,0,0,0,5.76,5.76Zm8,8a5.24,5.24,0,1,1,0-7.41A5.26,5.26,0,0,1,13.71,13.71Z" transform="translate(0 0)"/><polygon class="foregroundColor" points="11.72 7.45 10 9.17 8.27 7.45 7.45 8.28 9.17 10 7.45 11.72 8.28 12.55 10 10.83 11.72 12.55 12.55 11.73 10.83 10 12.55 8.28 11.72 7.45"/>
</symbol>
<symbol id="detail-tr-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M12.9,12.81h5.47a.67.67,0,1,0,0-1.34H12.9a.67.67,0,1,0,0,1.34Z"/><path class="foregroundColor" d="M21.29,15.12H12.9a.67.67,0,1,0,0,1.34h8.39a.67.67,0,1,0,0-1.34Z"/><path class="foregroundColor" d="M12.9,9.23h1.82a.67.67,0,0,0,0-1.34H12.9a.67.67,0,1,0,0,1.34Z"/><path class="foregroundColor" d="M14.72,18.77H12.9a.67.67,0,1,0,0,1.34h1.82a.67.67,0,0,0,0-1.34Z"/><rect class="foregroundColor" x="8.16" y="5.7" width="1.1" height="18.59"/>
</symbol>
<symbol id="detail" viewBox="0 0 18 12">
<rect class="foregroundColor" x="3.13" y="6.5" width="2" height="3"/><rect class="foregroundColor" x="6.38" y="3.5" width="2" height="6"/><rect class="foregroundColor" x="9.62" y="5.5" width="2" height="4"/><rect class="foregroundColor" x="12.87" y="2.5" width="2" height="7"/>
</symbol>
<symbol id="details-hide" viewBox="0 0 20 20">
<path class="foregroundColor" d="M18.47,9.63a11.59,11.59,0,0,0-3.15-3.5l-.09-.06-.69.69.12.08a11.59,11.59,0,0,1,2.76,2.88c-.58,1-3.18,5-7.41,5a6.35,6.35,0,0,1-2.7-.64l-.08,0-.71.71.16.07a7.37,7.37,0,0,0,3.33.84c4,0,7.07-3.2,8.46-5.94l0-.06Z"/><path class="foregroundColor" d="M13.18,8.34l-.07-.15-.69.69,0,.08A2.66,2.66,0,0,1,9,12.45l-.08,0-.69.69.15.08a3.71,3.71,0,0,0,1.66.42A3.61,3.61,0,0,0,13.6,10,3.56,3.56,0,0,0,13.18,8.34Z"/><path class="foregroundColor" d="M16.47,2.86,14,5.32a8.69,8.69,0,0,0-4-1A10.3,10.3,0,0,0,1.54,9.64l0,.06,0,.07A14,14,0,0,0,5.31,14L2.86,16.47l.67.67L17.05,3.62l.09-.09ZM7.15,12.18,6,13.33A14,14,0,0,1,2.61,9.71c.6-.89,3.28-4.46,7.4-4.46A7.43,7.43,0,0,1,13.3,6L12.18,7.15A3.48,3.48,0,0,0,10,6.39,3.61,3.61,0,0,0,6.39,10,3.46,3.46,0,0,0,7.15,12.18Z"/>
</symbol>
<symbol id="details-show" viewBox="0 0 20 20">
<path class="foregroundColor" d="M12.44,10A2.44,2.44,0,1,1,10,7.56,2.44,2.44,0,0,1,12.44,10Zm6.06-.37s-3,6.06-8.49,6.06c-5.09,0-8.51-6.06-8.51-6.06S4.65,4.31,10,4.31,18.5,9.63,18.5,9.63ZM14.07,10A4.07,4.07,0,1,0,10,14.07,4.07,4.07,0,0,0,14.07,10Z"/>
</symbol>
<symbol id="diagnostics" viewBox="0 0 18 18">
<path class="foregroundColor" d="M8.17,13.88,6.86,8.44l-.57,2.43H3.5a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h2L6.84,4.12l1.46,6L9.72,5.62l1.21,4.73.61-.48h3a.5.5,0,0,1,.5.5.5.5,0,0,1-.5.5H11.88l-1.55,1.19L9.62,9.28Z"/>
</symbol>
<symbol id="different-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M20,6.46V4H17.54V5H6.46V4H4V6.46H5V17.54H4V20H6.46V19H17.54v1H20V17.54H19V6.46ZM18.52,17.54h-1v1H6.46v-1h-1V6.46h1v-1H17.54v1h1Z"/><rect class="foregroundColor" x="11.07" y="10.59" width="4.76" height="1"/><rect class="foregroundColor" x="11.07" y="12.72" width="4.76" height="0.99"/><polygon class="foregroundColor" points="10.71 7.6 9.48 7.6 8.57 12.53 9.53 12.53 10.71 7.6"/><path class="foregroundColor" d="M8.26,14.52a.76.76,0,0,0,.51.19.7.7,0,0,0,.52-.21A.64.64,0,0,0,9.5,14a.59.59,0,0,0-.21-.46.67.67,0,0,0-.49-.19.76.76,0,0,0-.53.2.66.66,0,0,0-.21.5A.63.63,0,0,0,8.26,14.52Z"/>
</symbol>
<symbol id="dis-icon" viewBox="195.959 140.909 107.381 106.801">

  <path style="paint-order: fill; stroke-width: 7px; fill-opacity: 0; stroke-linecap: round; stroke-miterlimit: 1;" d="M 210.14 195.434 L 255.031 195.345 L 277.299 158.233"/>
  <g transform="matrix(0.9034830331802368, 0, 0, 0.9034830331802368, 18.796384811401367, 15.284887313842773)" style="">
    <ellipse style="stroke-opacity: 0; stroke-width: 0px;" cx="212.01" cy="199.697" rx="9.623" ry="9.623"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 217.783 192.013 C 217.783 192.013 221.805 195.275 224.021 196.283 C 226.237 197.291 217.32 198.175 217.32 198.175"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 218.262 206.988 C 218.262 206.988 221.466 203.881 223.682 202.873 C 225.898 201.865 216.981 200.981 216.981 200.981"/>
  </g>
  <g transform="matrix(-0.49148, 0.758109, -0.758109, -0.49148, 532.641297, 95.964733)" style="">
    <ellipse style="stroke-opacity: 0; stroke-width: 0px;" cx="212.01" cy="199.697" rx="9.623" ry="9.623"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 217.783 192.013 C 217.783 192.013 221.805 195.275 224.021 196.283 C 226.237 197.291 217.32 198.175 217.32 198.175"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 218.262 206.988 C 218.262 206.988 221.466 203.881 223.682 202.873 C 225.898 201.865 216.981 200.981 216.981 200.981"/>
  </g>
  <g transform="matrix(0.426265, 0.730357, -0.730357, 0.426265, 310.237077, -44.743385)" style="">
    <ellipse style="stroke-opacity: 0; stroke-width: 0px;" cx="212.01" cy="199.697" rx="9.623" ry="9.623"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 217.783 192.013 C 217.783 192.013 221.805 195.275 224.021 196.283 C 226.237 197.291 217.32 198.175 217.32 198.175"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 218.262 206.988 C 218.262 206.988 221.466 203.881 223.682 202.873 C 225.898 201.865 216.981 200.981 216.981 200.981"/>
  </g>
  <g transform="matrix(0.252614, 0.456611, -0.456611, 0.252614, 276.566787, 19.355396)" style="">
    <ellipse style="stroke-opacity: 0; stroke-width: 0px;" cx="212.01" cy="199.697" rx="9.623" ry="9.623"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 217.783 192.013 C 217.783 192.013 221.805 195.275 224.021 196.283 C 226.237 197.291 217.32 198.175 217.32 198.175"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 218.262 206.988 C 218.262 206.988 221.466 203.881 223.682 202.873 C 225.898 201.865 216.981 200.981 216.981 200.981"/>
  </g>
  <g transform="matrix(0.274002, -0.427739, 0.427739, 0.274002, 93.140038, 258.862985)" style="">
    <ellipse style="stroke-opacity: 0; stroke-width: 0px;" cx="212.01" cy="199.697" rx="9.623" ry="9.623"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 217.783 192.013 C 217.783 192.013 221.805 195.275 224.021 196.283 C 226.237 197.291 217.32 198.175 217.32 198.175"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 218.262 206.988 C 218.262 206.988 221.466 203.881 223.682 202.873 C 225.898 201.865 216.981 200.981 216.981 200.981"/>
  </g>
  <ellipse style="fill-opacity: 0; stroke-width: 6px;" cx="255.524" cy="195.875" rx="20.093" ry="20.093"/>
  <path style="paint-order: fill; stroke-width: 4px;" d="M 239.252 167.116 L 245.275 177.955"/>
  <path style="paint-order: fill; stroke-width: 4px;" d="M 238.208 209.817 L 244.23 220.656" transform="matrix(0.488791, 0.872401, -0.872401, 0.488791, 311.086272, -100.40861)"/>
  <g transform="matrix(-0.400895, -0.672537, 0.672537, -0.400895, 224.091891, 448.900692)" style="">
    <ellipse style="stroke-opacity: 0; stroke-width: 0px;" cx="212.01" cy="199.697" rx="9.623" ry="9.623"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 217.783 192.013 C 217.783 192.013 221.805 195.275 224.021 196.283 C 226.237 197.291 217.32 198.175 217.32 198.175"/>
    <path style="stroke-opacity: 0; stroke-width: 0px; paint-order: fill;" d="M 218.262 206.988 C 218.262 206.988 221.466 203.881 223.682 202.873 C 225.898 201.865 216.981 200.981 216.981 200.981"/>
  </g>
  <path style="paint-order: fill; stroke-width: 6px;" d="M 255.089 195.576 L 273.616 226.968"/>

</symbol>
<symbol id="disaggregation-etb" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="9.22 9.76 7.97 10.14 6.78 10.5 6.78 12.5 6.78 13.77 9.22 13.77 11.24 13.77 11.67 13.35 11.67 10.5 9.22 9.76"/><polygon class="foregroundColor" points="20.63 16.98 18.18 17.72 18.18 20.98 21.32 20.98 22.62 20.98 23.07 20.98 23.07 20.34 23.07 18.18 23.07 17.72 20.63 16.98"/><path class="foregroundColor" d="M24.55,14a6.06,6.06,0,0,0-1.11-.76,6.71,6.71,0,0,0-1.14-.48l-.63.63L17.9,17.2l.28-.28,2.44-.74,2.45.74V15.71h.76v6H17.42V17.68l-1.94,1.94-.63.62a6.06,6.06,0,0,0,3.48,3.89,5.87,5.87,0,0,0,2.29.48,6,6,0,0,0,6-6A6,6,0,0,0,24.55,14Z"/><path class="foregroundColor" d="M15,5.5h0a9.91,9.91,0,0,0-1.54.14l-.37.09a6.69,6.69,0,0,1,1,.8h.07a7,7,0,0,1,.8,0l.57,0h.14A8.67,8.67,0,0,1,20,8.05l.7-.7A9.55,9.55,0,0,0,15,5.5Z"/><path class="foregroundColor" d="M22.07,10.17a9,9,0,0,1,.62,1,6.89,6.89,0,0,1,.34.73l0,.11a7.1,7.1,0,0,1,1.21.58,9.74,9.74,0,0,0-1.5-3.13Z"/><path class="foregroundColor" d="M15.35,23.74l-.35,0a8.88,8.88,0,0,1-1.72-.17l-.48-.12-.42-.12a8.91,8.91,0,0,1-2.33-1.16l-.7.7a9.47,9.47,0,0,0,3.58,1.61l.35.07h.08a7.5,7.5,0,0,0,.81.11,7.56,7.56,0,0,0,.83,0h0a9.72,9.72,0,0,0,2-.2,7.51,7.51,0,0,1-1.1-.85A4.43,4.43,0,0,1,15.35,23.74Z"/><path class="foregroundColor" d="M7.51,19.42a6.65,6.65,0,0,1-.34-.65c-.08-.18-.14-.37-.21-.55a7.62,7.62,0,0,1-1.25-.61,9.42,9.42,0,0,0,1.53,3.15l.7-.71C7.8,19.85,7.64,19.65,7.51,19.42Z"/><path class="foregroundColor" d="M5.45,16.19A5.71,5.71,0,0,0,6.57,17a6.36,6.36,0,0,0,1,.44l.62-.62,2.25-2.25H6v-6h.76V9.7l.39-.11,1.69-.51h0L9.23,9l2.44.73V8.5h.76v4.08l2.06-2.05.62-.63a6,6,0,0,0-3.42-3.78,5.93,5.93,0,0,0-2.31-.49,6,6,0,0,0-6,6A5.92,5.92,0,0,0,5.45,16.19Z"/><rect class="foregroundColor" x="5.82" y="22.8" width="2" height="1" transform="translate(-14.48 11.65) rotate(-45)"/><rect class="foregroundColor" x="8.65" y="19.97" width="2" height="1" transform="translate(-11.65 12.82) rotate(-45)"/><rect class="foregroundColor" x="22.79" y="5.83" width="2" height="1" transform="translate(2.5 18.68) rotate(-45)"/><rect class="foregroundColor" x="17.14" y="11.48" width="2" height="1" transform="translate(-3.16 16.33) rotate(-45)"/><rect class="foregroundColor" x="14.31" y="14.31" width="2" height="1" transform="translate(-5.99 15.16) rotate(-45)"/><rect class="foregroundColor" x="11.48" y="17.14" width="2" height="1" transform="translate(-8.82 13.99) rotate(-45)"/><polygon class="foregroundColor" points="21.32 8.09 20.74 8.67 19.9 9.51 20.61 10.21 21.45 9.38 22.02 8.8 21.32 8.09"/>
</symbol>
<symbol id="disconnect-all" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.45,8.23a3.54,3.54,0,0,0-2.51-1A3.4,3.4,0,0,0,16.52,8.3l-2.4,2.44A.36.36,0,0,0,14,11a.39.39,0,0,0,.11.27l.45.43a.38.38,0,0,0,.53,0l2.4-2.44h0A2.1,2.1,0,0,1,19,8.62a2,2,0,0,1,1.5.6,2,2,0,0,1,.64,1.49,2.09,2.09,0,0,1-.59,1.5l-2.4,2.43a.39.39,0,0,0,0,.54l.45.43a.36.36,0,0,0,.26.11.37.37,0,0,0,.27-.12l2.4-2.43a3.51,3.51,0,0,0-.07-4.94Z"/><path class="foregroundColor" d="M15.51,18.12a.39.39,0,0,0-.27-.11h0a.36.36,0,0,0-.27.12l-2.4,2.43a2.16,2.16,0,0,1-3,0A2.07,2.07,0,0,1,9,19a2.11,2.11,0,0,1,.62-1.46L12,15.13a.39.39,0,0,0,0-.53l-.44-.44a.39.39,0,0,0-.27-.11.59.59,0,0,0-.27.11l-2.4,2.43a3.52,3.52,0,0,0,0,4.94,3.49,3.49,0,0,0,4.94,0L16,19.09a.39.39,0,0,0,0-.53Z"/><path class="foregroundColor" d="M9.79,12l-2-.52a.47.47,0,0,0-.35.05.43.43,0,0,0-.21.28l0,.05a.48.48,0,0,0,0,.35.46.46,0,0,0,.28.21l2,.52h.12a.46.46,0,0,0,.23-.06.43.43,0,0,0,.21-.28v-.05a.45.45,0,0,0,0-.34A.44.44,0,0,0,9.79,12Z"/><path class="foregroundColor" d="M9.75,9.32a.46.46,0,0,0-.65,0l0,0a.45.45,0,0,0-.14.32.45.45,0,0,0,.14.33l1.44,1.43a.46.46,0,0,0,.32.13h0a.47.47,0,0,0,.33-.14l0,0a.46.46,0,0,0,0-.65Z"/><path class="foregroundColor" d="M12.44,7.83a.47.47,0,0,0-.57-.32h0a.46.46,0,0,0-.32.56L12,10a.45.45,0,0,0,.44.34.25.25,0,0,0,.12,0h.05a.49.49,0,0,0,.28-.21.47.47,0,0,0,0-.35Z"/><path class="foregroundColor" d="M22.7,18a.43.43,0,0,0-.28-.21l-2-.52a.46.46,0,0,0-.56.33v0a.43.43,0,0,0,0,.35.46.46,0,0,0,.28.21l2,.52.12,0a.48.48,0,0,0,.23-.07.43.43,0,0,0,.21-.28l0,0A.48.48,0,0,0,22.7,18Z"/><path class="foregroundColor" d="M19.49,18.81a.42.42,0,0,0-.32-.13h0a.46.46,0,0,0-.33.13l0,0a.45.45,0,0,0,0,.64l1.44,1.43a.45.45,0,0,0,.32.14h0a.45.45,0,0,0,.32-.14l0,0a.45.45,0,0,0,.14-.33.45.45,0,0,0-.14-.32Z"/><path class="foregroundColor" d="M18,20.2a.49.49,0,0,0-.21-.28.47.47,0,0,0-.35,0h0a.46.46,0,0,0-.32.57l.53,1.95h0a.44.44,0,0,0,.22.28.43.43,0,0,0,.22.06h.12l.05,0a.45.45,0,0,0,.32-.56Z"/>
</symbol>
<symbol id="disrupted" viewBox="0 0 18 12">
<path class="foregroundColor" d="M9.36,2.75,8.85,4.28l-.3.9h3.88L8.64,9.25l.51-1.53.3-.9H5.57L9.36,2.75M11,0,4,7.5H8.5L7,12l7-7.5H9.5L11,0Z"/>
</symbol>
<symbol id="dissociate-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M20,6.46V4H17.54v.86H10V4H4v6h.86v7.54H4V20H6.46v-.86H14V20h6V14h-.86V6.46ZM18.39,14H14v4.39H6.46v-.85H5.61V10H10V5.61h7.54v.85h.85Z"/><path class="foregroundColor" d="M9.5,14.6a.5.5,0,0,1-.35-.85l5-5a.48.48,0,0,1,.7,0,.5.5,0,0,1,0,.71l-5,5A.5.5,0,0,1,9.5,14.6Z"/>
</symbol>
<symbol id="dots-area-resizing" viewBox="0 0 30 10">
<rect class="backgroundColor" width="30" height="10"/><circle class="foregroundColor" cx="5" cy="5" r="2"/><circle class="foregroundColor" cx="15" cy="5" r="2"/><circle class="foregroundColor" cx="25" cy="5" r="2"/>
</symbol>
<symbol id="double-arrow-move-layer" viewBox="0 0 16 16">
<path class="foregroundColor" d="M10.68,9.41h0a.57.57,0,0,0-.42.19l-1.69,2V4.43l1.69,2a.56.56,0,0,0,.41.19.52.52,0,0,0,.43-.19A.72.72,0,0,0,11.25,6a.64.64,0,0,0-.15-.43L8.42,2.44a.56.56,0,0,0-.84,0L4.91,5.53a.66.66,0,0,0,0,.86.55.55,0,0,0,.41.19h0a.57.57,0,0,0,.42-.19l1.69-2v7.13l-1.69-2a.57.57,0,0,0-.42-.19h0a.56.56,0,0,0-.42.2.67.67,0,0,0-.15.42.62.62,0,0,0,.15.42l2.68,3.1a.58.58,0,0,0,.42.2.55.55,0,0,0,.42-.2l2.67-3.09a.62.62,0,0,0,.16-.43.67.67,0,0,0-.16-.43A.55.55,0,0,0,10.68,9.41Z"/>
</symbol>
<symbol id="draw-crcl" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,24.5A9.5,9.5,0,1,1,24.5,15,9.51,9.51,0,0,1,15,24.5Zm0-18A8.5,8.5,0,1,0,23.5,15,8.51,8.51,0,0,0,15,6.5Z"/>
</symbol>
<symbol id="draw-ellps" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,21.5C9.21,21.5,4.5,18.58,4.5,15S9.21,8.5,15,8.5,25.5,11.42,25.5,15,20.79,21.5,15,21.5Zm0-12C9.76,9.5,5.5,12,5.5,15s4.26,5.5,9.5,5.5S24.5,18,24.5,15,20.24,9.5,15,9.5Z"/>
</symbol>
<symbol id="draw-plgn-pnts" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.4,3.69v1.8H9.1V3.69H4.64V8.15H6.37v5.1H4.64v4.46h2l.44,3.71H5.59v4.46h4.46V24.15h2.2v1.73h4.46V21.42H15.12l-.38-4H16V15.53l4.17-.42v1.5h4.46V12.15h-1.4l.59-4h2V3.69ZM5.64,7.15V4.69H8.1V7.15Zm0,9.56V14.25H8.1v2.46Zm3.41,8.17H6.59V22.42H9.05Zm6.66-2.46v2.46H13.25V22.42Zm-.71-6H12.54V14H15Zm8.63-3.31v2.46H21.17V13.15Zm-1.41-1h-2V14.1L16,14.53V13H11.54v4.46h2.19l.39,4H12.25v1.73h-2.2V21.42h-2l-.43-3.71H9.1V13.25H7.37V8.15H9.1V6.49H21.4V8.15h1.41Zm2.64-5H22.4V4.69h2.46Z"/>
</symbol>
<symbol id="draw-shape" viewBox="0 0 30 30">
<rect class="foregroundColor" x="16.16" y="15.16" width="11.34" height="2.25" transform="translate(-5.12 20.21) rotate(-45)"/><polygon class="foregroundColor" points="17.79 21.92 15.31 22.81 16.2 20.33 17.79 21.92"/><path class="foregroundColor" d="M11.93,24.84a2,2,0,0,1-1.7-.7c-1.32-1.65.87-5.52,1.12-5.95,1.56-2.61,3.08-5.77,3.06-6.62-.37.1-1.56.75-5.12,4.35-3,3-4.76,4-5.78,3.35-2.15-1.47,2-11.11,3.29-14a.5.5,0,1,1,.91.41c-2.54,5.64-4.6,12.13-3.63,12.8.08.05.88.43,4.5-3.24,4.91-5,5.89-4.94,6.47-4.49.87.67-.09,3.35-2.83,8-.89,1.51-1.85,4-1.21,4.82.34.43,1.27.43,2.61,0a.5.5,0,0,1,.63.33.49.49,0,0,1-.33.62A6.84,6.84,0,0,1,11.93,24.84Z"/>
</symbol>
<symbol id="duplicate-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M19.75,19.75H9.11V9.11H19.75Z"/><polygon class="foregroundColor" points="4.98 15.16 4.98 4.98 15.16 4.98 15.16 7.86 15.89 7.86 15.89 4.25 4.25 4.25 4.25 15.89 7.86 15.89 7.86 15.16 4.98 15.16"/>
</symbol>
<symbol id="edit-map-tr-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M9.23,3.68A5.35,5.35,0,0,0,3.87,8.73c0,2.64,1.92,5.7,5.36,11.11,3.39-5.34,5.35-8.47,5.35-11.11A5.34,5.34,0,0,0,9.23,3.68Zm0,7.65A2.3,2.3,0,1,1,11.52,9,2.3,2.3,0,0,1,9.23,11.33Z"/><path class="foregroundColor" d="M19.13,26.32a7,7,0,1,1,7-7A7,7,0,0,1,19.13,26.32Zm0-13a6,6,0,1,0,6,6A6,6,0,0,0,19.13,13.32Z"/><rect class="foregroundColor" x="16.3" y="18" width="6.79" height="1.5" transform="translate(-7.49 19.42) rotate(-45)"/><polygon class="foregroundColor" points="17.28 22.23 15.63 22.82 16.22 21.17 17.28 22.23"/>
</symbol>
<symbol id="edit" viewBox="0 0 18 18">
<path class="foregroundColor" d="M10.24,9.1A1.24,1.24,0,1,0,9,10.34,1.24,1.24,0,0,0,10.24,9.1Z"/><path class="foregroundColor" d="M8.46,14.1H9.54c.19-.56.32-1.12,1-1.38s1.09,0,1.65.3l.77-.77a1.85,1.85,0,0,1-.3-1.65c.25-.63.78-.75,1.38-1V8.56c-.56-.19-1.12-.32-1.38-1a1.28,1.28,0,0,1-.11-.49A2.7,2.7,0,0,1,12.92,6l-.77-.77a1.87,1.87,0,0,1-1.65.31c-.63-.26-.76-.79-1-1.39H8.46c-.19.56-.32,1.12-1,1.39s-1.1,0-1.65-.31L5.08,6c.33.66.55,1.07.31,1.66s-.79.75-1.39,1V9.64c.5.18,1.11.31,1.39,1a1.85,1.85,0,0,1-.31,1.65l.77.77a1.88,1.88,0,0,1,1.65-.3C8.13,13,8.26,13.51,8.46,14.1ZM7,9.1a2,2,0,1,1,2,2A2,2,0,0,1,7,9.1Z"/>
</symbol>
<symbol id="element-actions-tr-tb" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="21.04 11.45 21.73 12.19 25.08 9.14 21.73 6.1 21.04 6.83 23.06 8.64 14.8 8.64 14.8 9.65 23.06 9.65 21.04 11.45"/><rect class="foregroundColor" x="4.92" y="5.64" width="7" height="7"/><path class="foregroundColor" d="M25.08,14.58H4.92v9.78h1V16l8.19,5.56L10,24.36h1.78L15,22.19l3.19,2.17H20l-4.08-2.77L24.08,16v8.33h1ZM15,21l-8-5.4H23Z"/>
</symbol>
<symbol id="element-resize-cursor-tr-tb" viewBox="0 0 20 20">
<circle class="backgroundColor" cx="10" cy="10" r="6.5"/><path class="foregroundColor" d="M10,2.5A7.5,7.5,0,1,0,17.5,10,7.5,7.5,0,0,0,10,2.5ZM3.5,10A6.5,6.5,0,1,1,10,16.5,6.51,6.51,0,0,1,3.5,10Z"/><polygon class="foregroundColor" points="11 6.15 11.03 7.01 12.14 6.88 9.92 9.1 10.53 9.71 12.75 7.49 12.62 8.6 13.48 8.63 13.67 5.96 11 6.15"/><polygon class="foregroundColor" points="8.77 13.39 8.73 12.53 7.63 12.66 9.85 10.44 9.23 9.83 7.01 12.05 7.15 10.94 6.28 10.91 6.09 13.58 8.77 13.39"/><polygon class="foregroundColor" points="6.72 6.59 9.91 6.59 9.91 5.96 6.09 5.96 6.09 9.82 6.72 9.82 6.72 6.59"/><polygon class="foregroundColor" points="13.04 12.95 9.85 12.95 9.85 13.58 13.67 13.58 13.67 9.71 13.04 9.71 13.04 12.95"/>
</symbol>
<symbol id="element-resize-tr-tb" viewBox="0 0 100 30">
<path class="foregroundColor" d="M5,18.7c-1,0-1.5-.7-1.5-2.11a2.77,2.77,0,0,1,.41-1.66A1.33,1.33,0,0,1,5,14.35c1,0,1.47.72,1.47,2.14a2.87,2.87,0,0,1-.4,1.64A1.32,1.32,0,0,1,5,18.7Zm0-3.64c-.4,0-.6.5-.6,1.51S4.59,18,5,18s.57-.49.57-1.47S5.37,15.06,5,15.06Z"/><path class="foregroundColor" d="M92.3,14.33v4.29h-.93V15.37a.83.83,0,0,1-.18.13l-.22.11-.24.08-.25.05V15a3.71,3.71,0,0,0,.68-.27,4.08,4.08,0,0,0,.58-.36Z"/><path class="foregroundColor" d="M94.5,18.7c-1,0-1.5-.7-1.5-2.11a2.85,2.85,0,0,1,.4-1.66,1.36,1.36,0,0,1,1.18-.58c1,0,1.47.72,1.47,2.14a2.87,2.87,0,0,1-.4,1.64A1.32,1.32,0,0,1,94.5,18.7Zm0-3.64c-.4,0-.6.5-.6,1.51s.2,1.42.59,1.42.57-.49.57-1.47S94.92,15.06,94.54,15.06Z"/><path class="foregroundColor" d="M98,18.7c-1,0-1.5-.7-1.5-2.11a2.84,2.84,0,0,1,.41-1.66A1.33,1.33,0,0,1,98,14.35c1,0,1.47.72,1.47,2.14a2.87,2.87,0,0,1-.4,1.64A1.32,1.32,0,0,1,98,18.7Zm0-3.64c-.4,0-.6.5-.6,1.51S97.59,18,98,18s.57-.49.57-1.47S98.37,15.06,98,15.06Z"/><path class="foregroundColor" d="M95,27.38H5a1,1,0,1,1,0-2H95a1,1,0,0,1,0,2Z"/><path class="foregroundColor" d="M5,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,5,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,5,24Z"/><path class="foregroundColor" d="M20,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,20,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,20,24Z"/><path class="foregroundColor" d="M35,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,35,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,35,24Z"/><path class="foregroundColor" d="M50,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,50,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,50,24Z"/><path class="foregroundColor" d="M65,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,65,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,65,24Z"/><path class="foregroundColor" d="M80,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,80,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,80,24Z"/><path class="foregroundColor" d="M95,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,95,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,95,24Z"/><polygon class="foregroundColor" points="42.42 5.25 42.42 14.64 49.33 9.94 42.42 5.25"/><polygon class="foregroundColor" points="57.58 14.64 57.58 5.25 50.67 9.94 57.58 14.64"/><polygon class="foregroundColor" points="56.74 4.92 43.26 4.92 50 9.49 56.74 4.92"/><polygon class="foregroundColor" points="43.26 14.97 56.74 14.97 50 10.4 43.26 14.97"/><path class="foregroundColor" d="M41.67,4.17V15.72H58.33V4.17ZM58,5V15.34H42V4.54H58Z"/>
</symbol>
<symbol id="ellipse" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,21.5C9.21,21.5,4.5,18.58,4.5,15S9.21,8.5,15,8.5,25.5,11.42,25.5,15,20.79,21.5,15,21.5Zm0-12C9.76,9.5,5.5,12,5.5,15s4.26,5.5,9.5,5.5S24.5,18,24.5,15,20.24,9.5,15,9.5Z"/>
</symbol>
<symbol id="elliptic-arc" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15.19,9.21H14.9V7.3H10.44V9.53C7,10.62,4.69,12.84,4.69,15.33c0,1.83,1.18,3.5,3.24,4.71V22.7h4.46V21.22a17.06,17.06,0,0,0,2.8.24c5.58,0,10.12-2.75,10.12-6.13S20.77,9.21,15.19,9.21ZM11.44,8.3H13.9v2.46H11.44V8.3Zm0,13.4H8.93V19.24h2.46V21.7Zm3.8-.49a16,16,0,0,1-2.8-.25V18.24H7.93v.6a4.49,4.49,0,0,1-2.24-3.51c0-2,1.87-3.78,4.75-4.75v1.18H14.9V9.47h.29c5.44,0,9.87,2.63,9.87,5.87S20.63,21.21,15.19,21.21Z"/>
</symbol>
<symbol id="endpoint-filter" viewBox="0 0 18 18">
<path class="foregroundColor" d="M12,9.5h5.43c0-.17,0-.33,0-.5s0-.33,0-.5H12.7Z"/><path class="foregroundColor" d="M5.3,8.5H.53c0,.17,0,.33,0,.5s0,.33,0,.5H6Z"/><polygon class="foregroundColor" points="4.48 4.93 6.8 8.5 7.45 9.5 8.18 10.62 8.18 13.97 9.82 13.97 9.82 10.62 10.55 9.5 11.2 8.5 11.3 8.35 13.52 4.93 4.48 4.93"/>
</symbol>
<symbol id="endpoint-flow-arrow" viewBox="0 0 10 10">
<path class="foregroundColor" d="M8.77,4.63,6.29,2.15a.52.52,0,1,0-.73.73l1.6,1.6H1.59a.52.52,0,0,0,0,1H7.16l-1.6,1.6a.52.52,0,0,0,0,.73A.51.51,0,0,0,5.93,8a.51.51,0,0,0,.36-.15L8.77,5.37A.51.51,0,0,0,8.77,4.63Z"/>
</symbol>
<symbol id="endpoint-flow-direction-in" viewBox="0 0 18 12">
<path class="foregroundColor" d="M5.23,6.37,7.71,8.85a.52.52,0,0,0,.73-.73l-1.6-1.6h5.57a.52.52,0,0,0,0-1H6.84l1.6-1.6a.52.52,0,0,0,0-.73A.51.51,0,0,0,8.07,3a.51.51,0,0,0-.36.15L5.23,5.63A.51.51,0,0,0,5.23,6.37Z"/>
</symbol>
<symbol id="endpoint-flow-direction-out" viewBox="0 0 18 12">
<path class="foregroundColor" d="M12.77,5.63,10.29,3.15a.52.52,0,0,0-.73.73l1.6,1.6H5.59a.52.52,0,0,0,0,1h5.57l-1.6,1.6a.52.52,0,0,0,0,.73A.51.51,0,0,0,9.93,9a.51.51,0,0,0,.36-.15l2.48-2.48A.51.51,0,0,0,12.77,5.63Z"/>
</symbol>
<symbol id="endpoint-indicator" viewBox="0 0 10 10">
<path class="foregroundColor" d="M8.77,4.63,6.29,2.15a.52.52,0,1,0-.73.73l1.6,1.6H1.59a.52.52,0,0,0,0,1H7.16l-1.6,1.6a.52.52,0,0,0,0,.73A.51.51,0,0,0,5.93,8a.51.51,0,0,0,.36-.15L8.77,5.37A.51.51,0,0,0,8.77,4.63Z"/>
</symbol>
<symbol id="endpoint-route-filter" viewBox="0 0 30 30">
<path class="foregroundColor" d="M9,5.1H21l-4.91,7.55V17.1H13.91V12.65Z"/><rect class="foregroundColor" x="4" y="21.9" width="1" height="1"/><rect class="foregroundColor" x="8.5" y="21.9" width="1.5" height="1"/><rect class="foregroundColor" x="6" y="21.9" width="1.5" height="1"/><path class="foregroundColor" d="M17.7,21.9a2.75,2.75,0,0,0-5.4,0H11v1h1.3a2.74,2.74,0,0,0,5.4,0H26v-1ZM15,24.65a2.25,2.25,0,1,1,2.19-2.75,2.11,2.11,0,0,1,0,1A2.25,2.25,0,0,1,15,24.65Z"/><circle class="foregroundColor" cx="15" cy="22.4" r="1.32"/>
</symbol>
<symbol id="entity-association" viewBox="0 0 18 18">
<path class="foregroundColor" d="M3.87,8.5H.53c0,.17,0,.33,0,.5s0,.33,0,.5H3a3.8,3.8,0,0,1,.51-.63Z"/><path class="foregroundColor" d="M17.5,9c0-.17,0-.33,0-.5H14.66c-.05.06-.1.14-.16.2l-.8.8h3.77C17.48,9.33,17.5,9.17,17.5,9Z"/><path class="foregroundColor" d="M9.72,11.78,8.21,13.29a2.47,2.47,0,1,1-3.5-3.5l2-2a2.49,2.49,0,0,1,3.5,0,2.6,2.6,0,0,1,.46.64L10,9.06a1.46,1.46,0,0,0-.4-.69,1.62,1.62,0,0,0-2.27,0h0l-2,2A1.61,1.61,0,0,0,7.6,12.68l1-1A3.15,3.15,0,0,0,9.72,11.78Zm.07-7.07L8.29,6.2a3.12,3.12,0,0,1,1.11.12l1-1A1.61,1.61,0,0,1,12.68,7.6l-2,2h0a1.62,1.62,0,0,1-2.28,0A1.57,1.57,0,0,1,8,8.93L8,9l-.64.63a2.42,2.42,0,0,0,.46.64,2.48,2.48,0,0,0,3.5,0l2-2a2.47,2.47,0,0,0-3.5-3.5Z"/><circle class="foregroundColor" cx="6.49" cy="11.5" r="0.5"/><circle class="foregroundColor" cx="11.51" cy="6.5" r="0.5"/>
</symbol>
<symbol id="entity-movement-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.3,6c-1.11,0-2-1-2.7-1.8H11.4C10.66,5,9.81,6,8.7,6H6V25.8H24V6ZM15,5.1a.9.9,0,1,1-.9.9A.9.9,0,0,1,15,5.1Zm8.1,19.8H6.9V6.9h4.5l1.89,1.8H16.8l1.8-1.8h4.5Z"/><path class="foregroundColor" d="M21.39,11.52a.43.43,0,0,0-.24-.24l-2.9-1.17a.46.46,0,0,0-.59.25.45.45,0,0,0,.25.58l1.89.77a14.15,14.15,0,0,0-7.08,5.4,3.19,3.19,0,0,1,.81.44,13.26,13.26,0,0,1,6.62-5l-.76,1.89a.45.45,0,0,0,.25.58.47.47,0,0,0,.17,0,.45.45,0,0,0,.42-.28l1.17-2.89A.47.47,0,0,0,21.39,11.52Z"/><path class="foregroundColor" d="M13.28,17.92a2.55,2.55,0,0,0-.82-.42,2.83,2.83,0,0,0-.83-.14,2.79,2.79,0,1,0,1.65.56Zm-1.65.34a2,2,0,0,1,.35,0,1.85,1.85,0,0,1,.82.4,1.83,1.83,0,0,1,.69,1.43,1.87,1.87,0,1,1-1.86-1.86Z"/><path class="foregroundColor" d="M11.33,11.77l.87-.86a.45.45,0,0,0-.64-.64l-.86.86-.86-.86a.47.47,0,0,0-.64,0,.45.45,0,0,0,0,.64l.86.86-.86.86a.45.45,0,0,0,0,.64.44.44,0,0,0,.32.13.45.45,0,0,0,.32-.13l.86-.86.86.86a.44.44,0,0,0,.32.13.45.45,0,0,0,.32-.13.47.47,0,0,0,0-.64Z"/><path class="foregroundColor" d="M18.54,21.23a2,2,0,1,1,2-2A2,2,0,0,1,18.54,21.23Zm0-3.11a1.11,1.11,0,1,0,1.1,1.1A1.11,1.11,0,0,0,18.54,18.12Z"/>
</symbol>
<symbol id="entity-route-association" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15.86,14.4l-1.8,1.81A3,3,0,1,1,9.87,12L12.3,9.59a3,3,0,0,1,4.19,0,2.92,2.92,0,0,1,.54.76l-.79.8a1.86,1.86,0,0,0-.49-.83,2,2,0,0,0-2.72,0h0L10.6,12.75a1.92,1.92,0,0,0,2.72,2.72l1.21-1.21A3.71,3.71,0,0,0,15.86,14.4ZM15.94,6,14.15,7.73a4,4,0,0,1,1.33.14l1.2-1.19A1.92,1.92,0,1,1,19.4,9.4L17,11.82h0a1.93,1.93,0,0,1-2.72,0,1.89,1.89,0,0,1-.48-.83l0,0-.76.76a3.06,3.06,0,0,0,.55.76,3,3,0,0,0,4.19,0l2.41-2.42A3,3,0,0,0,15.94,6Z"/><circle class="foregroundColor" cx="12" cy="14.07" r="0.6"/><circle class="foregroundColor" cx="18" cy="8.09" r="0.6"/><rect class="foregroundColor" x="6" y="21.67" width="1.5" height="1"/><rect class="foregroundColor" x="8.5" y="21.67" width="1.5" height="1"/><rect class="foregroundColor" x="4" y="21.67" width="1" height="1"/><path class="foregroundColor" d="M17.7,21.67a2.75,2.75,0,0,0-5.4,0H11v1h1.3a2.74,2.74,0,0,0,5.4,0H26v-1ZM15,24.42a2.25,2.25,0,1,1,2.19-2.75,2.07,2.07,0,0,1,.06.5,2,2,0,0,1-.06.5A2.25,2.25,0,0,1,15,24.42Z"/><circle class="foregroundColor" cx="15" cy="22.17" r="1.32"/>
</symbol>
<symbol id="entity-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,4.06,6,9.24V19.82H24V9.24Zm8,14.76H19.51l-3.84-4.28L21,8.65l2,1.17Zm-11.15,0L15,15.29l3.17,3.53ZM7,18.82v-9L9,8.65l5.29,5.89-3.84,4.28ZM20.07,8.14,15,13.79,9.93,8.14,15,5.21Z"/><path class="foregroundColor" d="M23.86,23.9H6.52a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5H23.86a.5.5,0,0,1,.5.5A.5.5,0,0,1,23.86,23.9Z"/><path class="foregroundColor" d="M20.54,21.86h-14a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h14a.5.5,0,0,1,.5.5A.51.51,0,0,1,20.54,21.86Z"/><path class="foregroundColor" d="M17.21,25.94H6.52a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5H17.21a.51.51,0,0,1,.5.5A.5.5,0,0,1,17.21,25.94Z"/>
</symbol>
<symbol id="event-flag" viewBox="0 0 12 12">
<rect class="foregroundColor" width="12" height="12"/><polygon class="cls-2" points="3.82 2.53 3.82 5.02 3.82 7.51 3.82 9.53 4.45 9.53 4.45 7.25 9.82 5.02 3.82 2.53"/>
</symbol>
<symbol id="expand-additional-act" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="18.91 15 12.91 21 12.91 9 18.91 15"/>
</symbol>
<symbol id="expand-all-r-tb" viewBox="0 0 30 30">
<rect class="foregroundColor" x="20.1" y="4" width="6" height="6"/><path class="foregroundColor" d="M22.59,24.46l-1.39-1.4a.62.62,0,0,0-.86,0,.61.61,0,0,0-.18.43.6.6,0,0,0,.18.43l2.43,2.43a.6.6,0,0,0,.86,0l2.43-2.43a.6.6,0,0,0,0-.86.62.62,0,0,0-.86,0l-1.39,1.4V12.81a.61.61,0,0,0-.61-.61.61.61,0,0,0-.61.61Z"/><path class="foregroundColor" d="M5.43,6.41,6.83,5A.61.61,0,1,0,6,4.16L3.54,6.59a.6.6,0,0,0,0,.86L6,9.88A.61.61,0,0,0,6.83,9L5.43,7.63H17.08A.61.61,0,0,0,17.69,7a.61.61,0,0,0-.61-.61Z"/><path class="foregroundColor" d="M8.42,20.62v-2a.61.61,0,0,0-.61-.6.6.6,0,0,0-.43.17.62.62,0,0,0-.18.43v3.44a.61.61,0,0,0,.61.61h3.44a.61.61,0,1,0,0-1.21h-2l8.23-8.24a.61.61,0,0,0-.86-.86Z"/>
</symbol>
<symbol id="expand-down-r-tb" viewBox="0 0 30 30">
<rect class="foregroundColor" x="12.76" y="4" width="6" height="6"/><rect class="foregroundColor" x="5.76" y="4" width="6" height="6"/><rect class="foregroundColor" x="19.76" y="4" width="6" height="6"/><path class="foregroundColor" d="M15.17,24.66l-1.4-1.39a.59.59,0,0,0-.85,0,.6.6,0,0,0,0,.86l2.43,2.43a.6.6,0,0,0,.86,0l2.43-2.43a.61.61,0,0,0-.86-.86l-1.4,1.39V13a.61.61,0,1,0-1.21,0Z"/>
</symbol>
<symbol id="expand-element-menu" viewBox="0 0 10 10">
<polygon class="foregroundColor" points="7.5 5 2.5 9 2.5 1 7.5 5"/>
</symbol>
<symbol id="expand-r-tb" viewBox="0 0 30 30">
<rect class="foregroundColor" x="20.37" y="12" width="6" height="6"/><rect class="foregroundColor" x="20.37" y="5" width="6" height="6"/><rect class="foregroundColor" x="20.37" y="19" width="6" height="6"/><path class="foregroundColor" d="M5.71,14.41,7.1,13a.61.61,0,1,0-.86-.86L3.81,14.59a.6.6,0,0,0,0,.86l2.43,2.43A.61.61,0,0,0,7.1,17L5.71,15.63H17.35A.61.61,0,0,0,18,15a.61.61,0,0,0-.61-.61Z"/>
</symbol>
<symbol id="export-csv" viewBox="0 0 30 30">
<path class="foregroundColor" d="M18,16.08h-5.9v1.18H18ZM12.05,12v3H18V12H21.5L15,5.45,8.5,12Z"/><path class="foregroundColor" d="M12.54,24.53a3.46,3.46,0,0,1-1.47.26,3.05,3.05,0,0,1-1-.16,2.19,2.19,0,0,1-.78-.48,2.13,2.13,0,0,1-.49-.74,2.8,2.8,0,0,1,0-2,2.24,2.24,0,0,1,.55-.8,2.39,2.39,0,0,1,.84-.52,3.21,3.21,0,0,1,1.1-.18,3.71,3.71,0,0,1,1.25.18V21a2.3,2.3,0,0,0-1.14-.29,1.9,1.9,0,0,0-.7.12,1.38,1.38,0,0,0-.53.34,1.42,1.42,0,0,0-.34.52,1.92,1.92,0,0,0-.12.69,1.78,1.78,0,0,0,.11.67,1.51,1.51,0,0,0,.31.5,1.34,1.34,0,0,0,.51.32,1.78,1.78,0,0,0,.67.11,2.44,2.44,0,0,0,1.23-.3Z"/><path class="foregroundColor" d="M13.32,23.58l.31.19.34.14a2,2,0,0,0,.35.09,2.28,2.28,0,0,0,.36,0,1.15,1.15,0,0,0,.66-.15.45.45,0,0,0,.22-.39.51.51,0,0,0-.05-.22.56.56,0,0,0-.17-.18A2.25,2.25,0,0,0,15,22.9l-.53-.24a2.43,2.43,0,0,1-.89-.61,1.25,1.25,0,0,1-.28-.83,1.2,1.2,0,0,1,.14-.57,1.3,1.3,0,0,1,.4-.41,1.84,1.84,0,0,1,.59-.25,2.9,2.9,0,0,1,.74-.08,4.47,4.47,0,0,1,.67,0,2,2,0,0,1,.49.13V21a1.66,1.66,0,0,0-.54-.23,2.49,2.49,0,0,0-.61-.08,1.23,1.23,0,0,0-.63.14.39.39,0,0,0-.23.36.41.41,0,0,0,0,.19.42.42,0,0,0,.14.17l.29.18.49.22a4.31,4.31,0,0,1,.57.29,2.24,2.24,0,0,1,.4.32,1.15,1.15,0,0,1,.24.39,1.4,1.4,0,0,1,.08.47,1.28,1.28,0,0,1-.13.59,1.17,1.17,0,0,1-.37.43,1.81,1.81,0,0,1-.61.28,3.23,3.23,0,0,1-.81.09,2.17,2.17,0,0,1-.36,0l-.37-.05a3.37,3.37,0,0,1-.34-.09,1.09,1.09,0,0,1-.27-.11Z"/><path class="foregroundColor" d="M21.73,20l-1.81,4.72H18.74L17,20h1.15l1,3c.06.17.1.31.13.43s0,.21.07.28h0c0-.07,0-.16.07-.28s.09-.26.15-.45l1-3Z"/>
</symbol>
<symbol id="export-events" viewBox="0 0 18 18">
<path class="foregroundColor" d="M11.08,13.17V14H6.92v-.83Zm0-1.67H6.92v.83h4.16ZM6.92,8.58v2.09h4.16V8.58h2.5L9,4,4.42,8.58Z" transform="translate(0 0)"/>
</symbol>
<symbol id="export-metrics" viewBox="0 0 30 30">
<rect class="foregroundColor" x="9.13" y="22.51" width="2" height="3"/><rect class="foregroundColor" x="12.38" y="19.51" width="2" height="6"/><rect class="foregroundColor" x="15.62" y="21.51" width="2" height="4"/><rect class="foregroundColor" x="18.87" y="18.51" width="2" height="7"/><path class="foregroundColor" d="M17,17.16v1.18h-3.9V17.16Zm0-2.36h-3.9V16H17Zm-3.9-4.13v2.95H17V10.67H20.5L15,4.17l-5.5,6.5Z"/>
</symbol>
<symbol id="export-routes" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.6,15.68a2,2,0,0,0-1.3.5L18,12.49V13.7L22.73,17a1.91,1.91,0,0,0-.13.68,2,2,0,0,0,.13.69l-6.26,4.32a2,2,0,0,0-2.94,0L7.27,18.37a2,2,0,0,0,.13-.69A1.91,1.91,0,0,0,7.27,17l4.78-3.3V12.49L6.7,16.18a2,2,0,0,0-1.3-.5,2,2,0,1,0,1.3,3.51l6.36,4.39A1.68,1.68,0,0,0,13,24a2,2,0,1,0,4,0,1.68,1.68,0,0,0-.06-.45l6.36-4.39a2,2,0,1,0,1.3-3.51ZM6.47,17.82a1,1,0,0,1-.56.82,1.08,1.08,0,0,1-.51.14,1.1,1.1,0,1,1,0-2.2,1.11,1.11,0,0,1,1.07,1,.5.5,0,0,1,0,.13A.7.7,0,0,1,6.47,17.82Zm9.6,6.36a1.08,1.08,0,0,1-2.14,0,.66.66,0,0,1,0-.15,1.14,1.14,0,0,1,1.1-1.1,1.07,1.07,0,0,1,.71.28,1.06,1.06,0,0,1,.39.82A.66.66,0,0,1,16.07,24.18Zm8.53-5.4a1.08,1.08,0,0,1-.51-.14,1,1,0,0,1-.56-.82.7.7,0,0,1,0-.14.5.5,0,0,1,0-.13,1.11,1.11,0,0,1,1.07-1,1.1,1.1,0,0,1,0,2.2Z"/><rect class="foregroundColor" x="13.05" y="14.6" width="3.91" height="1.18"/><polygon class="foregroundColor" points="13.05 10.63 13.05 11.45 13.05 11.79 13.05 13.01 13.05 13.42 16.95 13.42 16.95 13.01 16.95 11.79 16.95 11.45 16.95 10.63 16.95 10.46 20.5 10.46 20.5 10.46 18.66 8.3 15 3.96 11.34 8.3 9.5 10.46 9.5 10.46 13.05 10.46 13.05 10.63"/><rect class="foregroundColor" x="13.05" y="16.96" width="3.91" height="1.18"/>
</symbol>
<symbol id="fav-additional-act" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,9.84l1,3,.25.74h.77l3.2,0-2.56,1.92-.62.46.23.74.94,3.05L15.63,18,15,17.53l-.63.45-2.61,1.84.94-3.05.23-.74-.62-.46L9.75,13.65l3.2,0h.77l.25-.74,1-3m0-3.42-2.07,6.09L6.5,12.6l5.15,3.85-1.9,6.14L15,18.88l5.25,3.71-1.9-6.14L23.5,12.6l-6.43-.09L15,6.42Z"/>
</symbol>
<symbol id="fill-hatched-shp-opts-tb" viewBox="0 0 20 20">
<polygon class="foregroundColor" points="11.19 4 16 9.04 16 7.59 12.57 4 11.19 4"/><polygon class="foregroundColor" points="7.39 4 16 13.03 16 11.58 8.77 4 7.39 4"/><polygon class="foregroundColor" points="4 4 4 4.5 15.08 16 16 16 16 15.52 4.91 4 4 4"/><polygon class="foregroundColor" points="4 8.42 11.23 16 12.61 16 4 6.97 4 8.42"/><polygon class="foregroundColor" points="4 12.4 7.43 16 8.81 16 4 10.96 4 12.4"/>
</symbol>
<symbol id="fill-plain-shp-opts-tb" viewBox="0 0 20 20">
<rect class="foregroundColor" x="4" y="4" width="12" height="12"/>
</symbol>
<symbol id="filter-tr-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M8.5,8.5h13l-5.32,8.18V21.5H13.82V16.68Z"/>
</symbol>
<symbol id="filtered-full" viewBox="0 0 18 18">
<path class="foregroundColor" d="M4,4.94H14L9.91,11.23v3.71H8.09V11.23Z"/>
</symbol>
<symbol id="filtered-partial" viewBox="0 0 18 18">
<path class="foregroundColor" d="M12.16,5.94,9.07,10.69,9,10.8l-.07-.11L5.84,5.94h6.32m1.84-1H4l4.09,6.29v3.71H9.91V11.23L14,4.94Z"/>
</symbol>
<symbol id="find-entity-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M27.83,25.28,24.53,22A5.9,5.9,0,0,0,24,13.83V9L15,3.83,6,9V19.38l9,5.18,2.11-1.22a5.9,5.9,0,0,0,6.69-.62L27.09,26a.55.55,0,0,0,.37.15.52.52,0,0,0,.52-.52A.55.55,0,0,0,27.83,25.28ZM23,9.59V13.1a5.87,5.87,0,0,0-7.31,1.1h0l5.22-5.81ZM15,5l5,2.89-5,5.58L10,7.87ZM7,18.8V9.59L9.11,8.38l5.21,5.81L9.11,20Zm8,4.61-5-2.89,5-5.58.05.06a5.88,5.88,0,0,0,1.15,7.71Zm5-.35a4.83,4.83,0,0,1-2.83-.92,4.78,4.78,0,0,1-1.39-6.32,4,4,0,0,1,.59-.83,4.78,4.78,0,0,1,6.6-.62,5.14,5.14,0,0,1,1,1,4.81,4.81,0,0,1,.89,2.8A4.87,4.87,0,0,1,20,23.06Z"/>
</symbol>
<symbol id="find-in-orbat-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M22.69,24.47l-3.33-3.33a5.95,5.95,0,1,0-4.56,2.14,6,6,0,0,0,3.82-1.39l3.32,3.32a.55.55,0,0,0,.37.15.53.53,0,0,0,.53-.52A.51.51,0,0,0,22.69,24.47ZM14.8,22.22a4.91,4.91,0,1,1,4.9-4.9A4.9,4.9,0,0,1,14.8,22.22Z"/><path class="foregroundColor" d="M6.78,9a2.46,2.46,0,0,1-.88-.15A1.88,1.88,0,0,1,4.78,7.72a2.2,2.2,0,0,1-.16-.86A2.57,2.57,0,0,1,4.77,6a2.05,2.05,0,0,1,.45-.7,1.9,1.9,0,0,1,.7-.46,2.54,2.54,0,0,1,.94-.16,2.21,2.21,0,0,1,.87.16,1.82,1.82,0,0,1,.67.44,2.07,2.07,0,0,1,.43.68A2.28,2.28,0,0,1,9,6.76a2.65,2.65,0,0,1-.16.92A1.91,1.91,0,0,1,7.67,8.82,2.35,2.35,0,0,1,6.78,9Zm0-3.63a1.19,1.19,0,0,0-.51.11,1.1,1.1,0,0,0-.38.29,1.19,1.19,0,0,0-.24.46,1.89,1.89,0,0,0-.09.6,2.23,2.23,0,0,0,.08.6,1.34,1.34,0,0,0,.24.46,1.06,1.06,0,0,0,.37.29,1.15,1.15,0,0,0,.5.1,1.42,1.42,0,0,0,.52-.09,1.07,1.07,0,0,0,.38-.29,1.36,1.36,0,0,0,.23-.45A2.27,2.27,0,0,0,8,6.82a2.35,2.35,0,0,0-.07-.62,1.31,1.31,0,0,0-.22-.46,1,1,0,0,0-.37-.29A1.19,1.19,0,0,0,6.82,5.35Z"/><path class="foregroundColor" d="M13.48,8.91h-1.1l-.65-1.14-.16-.24a.78.78,0,0,0-.15-.16l-.15-.08-.18,0h-.21V8.91H9.94V4.71h1.54A2,2,0,0,1,12.65,5a1,1,0,0,1,.4.84,1.05,1.05,0,0,1-.07.4,1.08,1.08,0,0,1-.21.34,1.37,1.37,0,0,1-.32.25A2.19,2.19,0,0,1,12,7h0a.84.84,0,0,1,.33.21,2.66,2.66,0,0,1,.29.38Zm-1.41-3a.48.48,0,0,0-.19-.43,1,1,0,0,0-.57-.13h-.43V6.61h.31a2,2,0,0,0,.36,0,.85.85,0,0,0,.27-.13A.55.55,0,0,0,12,6.22.58.58,0,0,0,12.07,5.92Z"/><path class="foregroundColor" d="M17.39,7.67a1.07,1.07,0,0,1-.12.51,1.23,1.23,0,0,1-.33.39,1.56,1.56,0,0,1-.5.25,2.29,2.29,0,0,1-.66.09H14.17V4.71h1.55A1.91,1.91,0,0,1,16.81,5a.83.83,0,0,1,.37.72.86.86,0,0,1-.22.6,1.23,1.23,0,0,1-.63.37h0a1.48,1.48,0,0,1,.45.12,1.05,1.05,0,0,1,.34.22.92.92,0,0,1,.2.31A.89.89,0,0,1,17.39,7.67ZM16.2,5.82A.39.39,0,0,0,16,5.45a1.23,1.23,0,0,0-.54-.1h-.37V6.41h.35A.83.83,0,0,0,16,6.26.5.5,0,0,0,16.2,5.82Zm.21,1.85a.52.52,0,0,0-.21-.46,1.11,1.11,0,0,0-.64-.14h-.44V8.26h.5a1.09,1.09,0,0,0,.59-.14A.51.51,0,0,0,16.41,7.67Z"/><path class="foregroundColor" d="M22,8.91H21l-.35-1H19l-.33,1h-1l1.63-4.2h1.05ZM20.41,7.25l-.47-1.33-.06-.22a1,1,0,0,1,0-.21h0a1.77,1.77,0,0,1,0,.2,2.17,2.17,0,0,1-.07.24l-.44,1.32Z"/><path class="foregroundColor" d="M25.38,5.42H24.1V8.91h-.94V5.42H21.87V4.71h3.51Z"/>
</symbol>
<symbol id="fit-content" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="23 9 23 9 7 9 7 21 7 21 7 9 23 9"/><polygon class="foregroundColor" points="23 21 7 21 7 21 23 21 23 9 23 9 23 21"/><path class="foregroundColor" d="M23,9H7V21H23ZM21.5,19.5H8.5v-9h13Z"/><rect class="foregroundColor" x="24.5" y="9" width="1" height="12"/><rect class="foregroundColor" x="4.5" y="9" width="1" height="12"/>
</symbol>
<symbol id="flow-in" viewBox="0 0 10 10">
<path class="foregroundColor" d="M1.86,5.3,3.92,7.37a.43.43,0,0,0,.61-.61L3.2,5.43H7.84a.43.43,0,0,0,0-.86H3.2L4.53,3.24a.43.43,0,0,0,0-.61.39.39,0,0,0-.3-.13.43.43,0,0,0-.31.13L1.86,4.69A.43.43,0,0,0,1.86,5.3Z"/>
</symbol>
<symbol id="flow-out" viewBox="0 0 10 10">
<path class="foregroundColor" d="M8.14,4.7,6.08,2.63a.43.43,0,0,0-.61.61L6.8,4.57H2.16a.43.43,0,0,0,0,.86H6.8L5.47,6.76a.43.43,0,0,0,0,.61.39.39,0,0,0,.3.13.43.43,0,0,0,.31-.13L8.14,5.31A.43.43,0,0,0,8.14,4.7Z"/>
</symbol>
<symbol id="free-hand" viewBox="0 0 30 30">
<rect class="foregroundColor" x="16.39" y="15.27" width="11.34" height="2.25" transform="translate(-5.13 20.4) rotate(-45)"/><polygon class="foregroundColor" points="18.02 22.02 15.54 22.91 16.43 20.43 18.02 22.02"/><path class="foregroundColor" d="M12.16,25a2,2,0,0,1-1.7-.7c-1.32-1.66.87-5.52,1.13-6,1.55-2.61,3.07-5.76,3.05-6.62-.37.11-1.56.75-5.12,4.35-3,3-4.76,4.06-5.78,3.36C1.6,17.91,5.72,8.27,7,5.35a.5.5,0,1,1,.91.41C5.4,11.4,3.34,17.89,4.31,18.55c.08.06.89.43,4.5-3.23,4.9-5,5.89-5,6.47-4.49.87.67-.09,3.35-2.83,8-.89,1.52-1.85,4-1.21,4.82.34.43,1.27.43,2.61,0a.5.5,0,1,1,.3,1A6.84,6.84,0,0,1,12.16,25Z"/>
</symbol>
<symbol id="geo-buffer" viewBox="0 0 30 30">
<path class="foregroundColor" d="M2.81,10.59,5.74,7.66A4.49,4.49,0,0,0,2.81,10.59Z"/><path class="foregroundColor" d="M5.43,10h.28L8.15,7.53,7.5,7.47,2.62,12.35a4.1,4.1,0,0,0,.11.61l2.7-2.71Z"/><polygon class="foregroundColor" points="7.99 9.97 10.25 7.71 9.6 7.66 7.28 9.97 7.99 9.97"/><path class="foregroundColor" d="M5.6,13.94,4.17,15.36a3.91,3.91,0,0,0,.41.3L6.3,13.94Z"/><polygon class="foregroundColor" points="9.39 10.85 12.34 7.9 11.69 7.84 9.39 10.14 9.39 10.85"/><path class="foregroundColor" d="M7.87,13.94,5.62,16.19a5.12,5.12,0,0,0,.56.15l2.4-2.4Z"/><polygon class="foregroundColor" points="11.36 11.15 14.44 8.08 13.79 8.02 10.71 11.1 11.36 11.15"/><polygon class="foregroundColor" points="10.5 13.59 7.58 16.51 8.22 16.57 11.15 13.64 10.5 13.59"/><polygon class="foregroundColor" points="13.46 11.34 16.53 8.26 15.88 8.21 12.81 11.28 13.46 11.34"/><polygon class="foregroundColor" points="12.6 13.77 9.67 16.7 10.32 16.75 13.25 13.83 12.6 13.77"/><polygon class="foregroundColor" points="16.17 10.91 18.63 8.45 17.98 8.39 15.46 10.91 16.17 10.91"/><polygon class="foregroundColor" points="18.45 10.91 20.29 9.07 19.93 8.71 17.74 10.91 18.45 10.91"/><polygon class="foregroundColor" points="16.06 14.87 13.86 17.07 14.51 17.12 16.77 14.87 16.06 14.87"/><polygon class="foregroundColor" points="18.81 12.82 21.43 10.21 21.07 9.85 18.81 12.12 18.81 12.82"/><rect class="foregroundColor" x="15.64" y="16.36" width="2.61" height="0.5" transform="translate(-6.78 16.85) rotate(-45)"/><rect class="foregroundColor" x="19.06" y="12.3" width="3.89" height="0.5" transform="translate(-2.72 18.53) rotate(-45)"/><rect class="foregroundColor" x="16.78" y="17.5" width="2.61" height="0.5" transform="translate(-7.26 17.99) rotate(-45)"/><rect class="foregroundColor" x="20.2" y="13.44" width="3.89" height="0.5" transform="translate(-3.19 19.67) rotate(-45)"/><polygon class="foregroundColor" points="20.02 20.02 19.27 20.77 19.62 21.13 20.73 20.02 20.02 20.02"/><polygon class="foregroundColor" points="23.96 16.79 25.98 14.77 25.63 14.41 23.96 16.08 23.96 16.79"/><path class="foregroundColor" d="M22.3,20l-1.81,1.81a4.09,4.09,0,0,0,.46.25L23,20Z"/><path class="foregroundColor" d="M24,19.07l3-3a4.16,4.16,0,0,0-.24-.47L24,18.36Z"/><path class="foregroundColor" d="M27.32,17.28l-5.19,5.18a4.78,4.78,0,0,0,.65.07l4.6-4.6C27.37,17.71,27.35,17.49,27.32,17.28Z"/><path class="foregroundColor" d="M26.07,21.22A4.35,4.35,0,0,0,27,19.91l-2.19,2.2A4.49,4.49,0,0,0,26.07,21.22Z"/><polygon class="foregroundColor" points="9.39 11.73 9.39 12.74 14.85 13.22 14.85 12.23 14.85 12.21 9.39 11.73"/><polygon class="foregroundColor" points="18.81 14.16 18.81 14.87 18.1 14.87 20 16.76 20 16.06 20.71 16.06 18.81 14.16"/><path class="foregroundColor" d="M3.14,14.11c.09.15.19.28.29.42l2-2v-.71Z"/><polygon class="foregroundColor" points="11.77 16.88 12.42 16.94 14.85 14.51 14.85 13.8 11.77 16.88"/><polygon class="foregroundColor" points="24.49 13.27 21.71 16.06 22.41 16.06 24.84 13.63 24.49 13.27"/><polygon class="foregroundColor" points="18.13 19.64 18.48 19.99 20 18.47 20 17.76 18.13 19.64"/><rect class="foregroundColor" x="20.75" y="16.81" width="2.46" height="2.46"/><polygon class="foregroundColor" points="18.06 11.66 15.6 11.66 15.6 14.12 15.6 14.12 18.06 14.12 18.06 14.12 18.06 11.66"/><rect class="foregroundColor" x="6.18" y="10.72" width="2.46" height="2.46"/>
</symbol>
<symbol id="grip" viewBox="0 0 19 4">
<rect class="foregroundColor" width="1" height="4"/><rect class="foregroundColor" x="2" width="1" height="4"/><rect class="foregroundColor" x="4" width="1" height="4"/><rect class="foregroundColor" x="6" width="1" height="4"/><rect class="foregroundColor" x="8" width="1" height="4"/><rect class="foregroundColor" x="10" width="1" height="4"/><rect class="foregroundColor" x="12" width="1" height="4"/><rect class="foregroundColor" x="14" width="1" height="4"/><rect class="foregroundColor" x="16" width="1" height="4"/><rect class="foregroundColor" x="18" width="1" height="4"/>
</symbol>
<symbol id="help" viewBox="0 0 20 20">
<path class="foregroundColor" d="M9,11.9a3.91,3.91,0,0,1-.09-.39,2.59,2.59,0,0,1,0-.48A1.49,1.49,0,0,1,9,10.36a2.79,2.79,0,0,1,.37-.55,5.67,5.67,0,0,1,.48-.5l.47-.47a2.74,2.74,0,0,0,.37-.49,1.09,1.09,0,0,0,0-1,.94.94,0,0,0-.3-.33,1.22,1.22,0,0,0-.43-.2,1.77,1.77,0,0,0-.52-.07,2.47,2.47,0,0,0-1.72.8V6.13a4,4,0,0,1,2.05-.58,3.12,3.12,0,0,1,.93.13,2.15,2.15,0,0,1,.76.37,1.89,1.89,0,0,1,.51.61,2,2,0,0,1,.19.86,2.15,2.15,0,0,1-.16.83,2.77,2.77,0,0,1-.4.66,2.63,2.63,0,0,1-.51.54l-.51.47a3,3,0,0,0-.4.49,1.09,1.09,0,0,0-.15.56,1.68,1.68,0,0,0,.06.47,3.42,3.42,0,0,0,.14.36Zm.69,2.55a.93.93,0,0,1-.61-.23.75.75,0,0,1-.25-.57.72.72,0,0,1,.25-.57.89.89,0,0,1,.61-.24.85.85,0,0,1,.6.24.73.73,0,0,1,.26.57.76.76,0,0,1-.26.57A.88.88,0,0,1,9.7,14.45Z"/><path class="foregroundColor" d="M10,.5A9.5,9.5,0,1,1,.5,10,9.51,9.51,0,0,1,10,.5Zm0,1.43A8.07,8.07,0,1,0,18.08,10,8.07,8.07,0,0,0,10,1.93Z"/>
</symbol>
<symbol id="hidden-eye-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M22,8.73,21.27,8l-2,2A9.3,9.3,0,0,0,15,9c-5.25,0-8.6,4.59-9.3,5.64A17.44,17.44,0,0,0,10,19.27l-2,2,.71.71h0Zm-7,2.84a3.37,3.37,0,0,1,2,.69L12.25,17a3.37,3.37,0,0,1-.69-2A3.44,3.44,0,0,1,15,11.57Z"/><path class="foregroundColor" d="M21,11.12l-2.77,2.77a3.17,3.17,0,0,1,.2,1.11A3.44,3.44,0,0,1,15,18.43a3.36,3.36,0,0,1-1.11-.19l-2.06,2.05A7.67,7.67,0,0,0,15,21c5.4,0,8.66-5.23,9.3-6.35A14.46,14.46,0,0,0,21,11.12Z"/>
</symbol>
<symbol id="hide-all-materiels-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23.66,15.05c0-.23,0-.46,0-.69a9,9,0,0,0-9-9h0a8.57,8.57,0,0,0-1.44.13A9.34,9.34,0,0,0,11,6.15l-.43.21a9,9,0,0,0,2.17,16.79l.32.06h.09c.24,0,.5.08.75.1a6.79,6.79,0,0,0,.78,0h0a8.82,8.82,0,0,0,1.27-.1,6,6,0,1,0,7.69-8.21ZM21.11,9.44a8.4,8.4,0,0,1,.79,1.24c.11.23.22.45.31.68a8.09,8.09,0,0,1,.58,3c0,.13,0,.26,0,.4a5.66,5.66,0,0,0-1.43-.18,6,6,0,0,0-4.09,1.62L15.4,14.36,20.75,9ZM9.44,8.23a8.1,8.1,0,0,1,3.78-1.82c.24,0,.48-.08.73-.1s.49,0,.75,0,.35,0,.53,0h.13a8,8,0,0,1,4.68,2L14.7,13.65,9.35,8.31ZM7.69,18.39c-.12-.2-.22-.4-.32-.61a8.64,8.64,0,0,1-.43-1.14,8.08,8.08,0,0,1-.2-.85,7.46,7.46,0,0,1-.13-1.43v0A7.2,7.2,0,0,1,6.73,13,7.06,7.06,0,0,1,7,12,8.16,8.16,0,0,1,8.64,9L14,14.36,8.65,19.7A8.16,8.16,0,0,1,7.69,18.39Zm7.34,4-.33,0a8.53,8.53,0,0,1-1.61-.16l-.46-.12-.39-.11A8,8,0,0,1,10,20.91c-.21-.16-.41-.32-.6-.49l5.35-5.35,1.87,1.87a5.93,5.93,0,0,0-1,5.43Zm6.3,3.35a5.2,5.2,0,0,1-4.52-2.68,5.55,5.55,0,0,1-.68-2.52,5.16,5.16,0,0,1,1-3.06,5.28,5.28,0,0,1,.67-.75,5.12,5.12,0,0,1,3.51-1.39,5,5,0,0,1,1.36.2,5.33,5.33,0,0,1,.87.32,5.19,5.19,0,0,1-2.23,9.88Z"/><path class="foregroundColor" d="M23.83,18.65l1-1-.57-.56-1.14,1.13a4.31,4.31,0,0,0-1.92-.45c-2.44,0-4,2.14-4.33,2.63a8,8,0,0,0,2,2.12l-1,1,.57.56,1.1-1.1a3.79,3.79,0,0,0,1.67.42c2.53,0,4-2.44,4.34-3A6.52,6.52,0,0,0,23.83,18.65Zm-4.24,1.93a1.6,1.6,0,0,1,1.6-1.6,1.57,1.57,0,0,1,.9.28l-2.22,2.22A1.57,1.57,0,0,1,19.59,20.58Zm1.6,1.6a1.6,1.6,0,0,1-.72-.18l2.14-2.14a1.59,1.59,0,0,1-1.42,2.32Z"/>
</symbol>
<symbol id="hide-all-units-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.94,15.79V7.42H4.77v14H15.4a6,6,0,1,0,9.54-5.64Zm-1-.61a6,6,0,0,0-6.2.6l-2-1.35,8.2-5.56ZM22.81,8.42l-8,5.4L6.9,8.42Zm-17,.45L14,14.43,5.77,20ZM14.85,15,17,16.47a6,6,0,0,0-1.63,4H6.9Zm6.48,10.75a5.2,5.2,0,0,1-5.11-4.35,4.81,4.81,0,0,1-.09-.85s0-.1,0-.15a5.17,5.17,0,0,1,1.49-3.51,5.3,5.3,0,0,1,3.69-1.54,5.13,5.13,0,0,1,2.61.72,5.63,5.63,0,0,1,1,.74,5.2,5.2,0,0,1-3.61,8.94Z"/><path class="foregroundColor" d="M23.83,18.65l1-1-.57-.56-1.14,1.13a4.31,4.31,0,0,0-1.92-.45c-2.44,0-4,2.14-4.33,2.63a8,8,0,0,0,2,2.12l-1,1,.57.56,1.1-1.1a3.79,3.79,0,0,0,1.67.42c2.53,0,4-2.44,4.34-3A6.52,6.52,0,0,0,23.83,18.65Zm-4.24,1.93a1.6,1.6,0,0,1,1.6-1.6,1.57,1.57,0,0,1,.9.28l-2.22,2.22A1.57,1.57,0,0,1,19.59,20.58Zm1.6,1.6a1.6,1.6,0,0,1-.72-.18l2.14-2.14a1.59,1.59,0,0,1-1.42,2.32Z"/>
</symbol>
<symbol id="hide-labels-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,4.06,6,9.24V19.82H24V9.24Zm8,14.76H19.51l-3.84-4.28L21,8.65l2,1.17Zm-11.15,0L15,15.29l3.17,3.53ZM7,18.82v-9L9,8.65l5.29,5.89-3.84,4.28ZM20.07,8.14,15,13.79,9.93,8.14,15,5.21Z"/><path class="foregroundColor" d="M22.52,23.9h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,22.52,23.9Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,19.52,23.9Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,16.52,23.9Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,13.52,23.9Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,10.52,23.9Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,7.52,23.9Z"/><path class="foregroundColor" d="M19.52,21.86h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,19.52,21.86Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,16.52,21.86Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,13.52,21.86Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,10.52,21.86Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,7.52,21.86Z"/><path class="foregroundColor" d="M16.52,25.94h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,16.52,25.94Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,13.52,25.94Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,10.52,25.94Zm-3,0h-1a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5A.5.5,0,0,1,7.52,25.94Z"/>
</symbol>
<symbol id="hide-map-element-label" viewBox="0 0 24 24">
<path class="foregroundColor" d="M20.5,16.16h0a.25.25,0,0,0-.25.25v.25H20a.25.25,0,0,0,0,.5h.75v-.75A.25.25,0,0,0,20.5,16.16Z"/><path class="foregroundColor" d="M15.29,16.66h-.94a.25.25,0,1,0,0,.5h.94a.25.25,0,1,0,0-.5Z"/><path class="foregroundColor" d="M18.11,16.66h-.94a.25.25,0,0,0,0,.5h.94a.25.25,0,0,0,0-.5Z"/><path class="foregroundColor" d="M3.75,8.41v-1a.25.25,0,0,0-.25-.25h0a.25.25,0,0,0-.25.25v1a.25.25,0,0,0,.25.25h0A.25.25,0,0,0,3.75,8.41Z"/><path class="foregroundColor" d="M3.5,5.66h0a.25.25,0,0,0,.25-.25V5.16H4a.25.25,0,0,0,0-.5H3.25v.75A.25.25,0,0,0,3.5,5.66Z"/><path class="foregroundColor" d="M12.47,4.66h-.94a.25.25,0,1,0,0,.5h.94a.25.25,0,1,0,0-.5Z"/><path class="foregroundColor" d="M15.29,4.66h-.94a.25.25,0,1,0,0,.5h.94a.25.25,0,0,0,0-.5Z"/><path class="foregroundColor" d="M18.11,4.66h-.94a.25.25,0,0,0,0,.5h.94a.25.25,0,0,0,0-.5Z"/><path class="foregroundColor" d="M8.7,5.16h.94a.25.25,0,0,0,0-.5H8.7a.25.25,0,0,0,0,.5Z"/><path class="foregroundColor" d="M5.88,5.16h.94a.25.25,0,0,0,0-.5H5.88a.25.25,0,1,0,0,.5Z"/><path class="foregroundColor" d="M20,4.66a.25.25,0,0,0,0,.5h.25v.25a.25.25,0,0,0,.25.25h0a.25.25,0,0,0,.25-.25V4.66Z"/><path class="foregroundColor" d="M20.5,10.16h0a.25.25,0,0,0-.25.25v1a.25.25,0,0,0,.25.25h0a.25.25,0,0,0,.25-.25v-1A.25.25,0,0,0,20.5,10.16Z"/><path class="foregroundColor" d="M20.5,7.16h0a.25.25,0,0,0-.25.25v1a.25.25,0,0,0,.25.25h0a.25.25,0,0,0,.25-.25v-1A.25.25,0,0,0,20.5,7.16Z"/><path class="foregroundColor" d="M20.5,13.16h0a.25.25,0,0,0-.25.25v1a.25.25,0,0,0,.25.25h0a.25.25,0,0,0,.25-.25v-1A.25.25,0,0,0,20.5,13.16Z"/><path class="foregroundColor" d="M8.67,18.06V15.32L6.92,17.07v1c0,1.06-.11,1.14-1.19,1.22v.47H9.92v-.47C8.8,19.2,8.67,19.12,8.67,18.06Z"/><path class="foregroundColor" d="M10.88,13.11a4.48,4.48,0,0,1,.22.62l.47,0c0-.37-.05-.8-.07-1.21Z"/><path class="foregroundColor" d="M11.72,10.31l-1.23,1.23H5.3c-.37,0-.53,0-.72-.32H4.21c0,.61-.12,1.59-.21,2.52h.48A4.82,4.82,0,0,1,5,12.55c.24-.31.44-.42,1.15-.42h.77v3h0L3.51,18.52,4,19l2.93-2.93,1.75-1.75,1.86-1.86.93-.93.75-.73Zm-3.05,3h0V12.13h.81l.4,0h0Z"/>
</symbol>
<symbol id="hide-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M18.5,5,16.05,7.45A8.65,8.65,0,0,0,12,6.4c-3.54,0-6.71,2.43-8.4,5.3A13.74,13.74,0,0,0,7.48,16L5,18.51l.49.49L19,5.49ZM14.2,9.3A3.41,3.41,0,0,0,12,8.5,3.5,3.5,0,0,0,8.5,12a3.41,3.41,0,0,0,.8,2.2L8,15.51a14.31,14.31,0,0,1-3.58-3.8C5,10.85,7.72,7.1,12,7.1A7.77,7.77,0,0,1,15.53,8Z"/><path class="foregroundColor" d="M17.27,8.21l-.51.51a11.86,11.86,0,0,1,2.84,3c-.52.91-3.18,5.18-7.6,5.18a6.41,6.41,0,0,1-2.76-.66l-.53.53A7.33,7.33,0,0,0,12,17.6c3.7,0,6.83-2.8,8.4-5.91A11.39,11.39,0,0,0,17.27,8.21Z"/><path class="foregroundColor" d="M12,14.8a2.88,2.88,0,0,1-1.1-.22l-.51.51A3.46,3.46,0,0,0,12,15.5a3.47,3.47,0,0,0,3.09-5.11l-.52.52A2.63,2.63,0,0,1,14.8,12,2.8,2.8,0,0,1,12,14.8Z"/>
</symbol>
<symbol id="horizontal-tool-display-r-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M25.36,12.46v4h-4v-4h4m1-1h-6v6h6v-6Z"/><rect class="foregroundColor" x="20.36" y="4.46" width="6" height="6" transform="translate(30.82 -15.89) rotate(90)"/><rect class="foregroundColor" x="13.36" y="4.47" width="6" height="6" transform="translate(23.82 -8.89) rotate(90)"/><path class="foregroundColor" d="M25.36,19.46v4h-4v-4h4m1-1h-6v6h6v-6Z"/><rect class="foregroundColor" x="6.36" y="4.47" width="6" height="6" transform="translate(16.82 -1.89) rotate(90)"/><path class="foregroundColor" d="M18.26,24.41,15.83,22a.61.61,0,0,0-.86.86l1.39,1.4h-1a9,9,0,0,1-9-9v-1l1.4,1.4a.57.57,0,0,0,.42.17.57.57,0,0,0,.43-.17.6.6,0,0,0,0-.86L6.23,12.39a.6.6,0,0,0-.86,0L2.94,14.82a.6.6,0,0,0,.43,1,.59.59,0,0,0,.43-.17l1.4-1.4v1a10.21,10.21,0,0,0,10.2,10.2h1L15,26.85a.6.6,0,0,0,0,.86.63.63,0,0,0,.86,0l2.43-2.44A.6.6,0,0,0,18.26,24.41Z"/>
</symbol>
<symbol id="import-routes" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.6,10.32a2,2,0,0,0-1.3.49L16.94,6.42A1.68,1.68,0,0,0,17,6a2,2,0,1,0-4,0,1.68,1.68,0,0,0,.06.45L6.7,10.81a2,2,0,1,0-1.3,3.51,2,2,0,0,0,1.3-.5l5.35,3.69V16.3L7.27,13a1.91,1.91,0,0,0,.13-.68,2,2,0,0,0-.13-.69l6.26-4.32a2,2,0,0,0,2.94,0l6.26,4.32a2,2,0,0,0-.13.69,1.91,1.91,0,0,0,.13.68L18,16.3v1.21l5.35-3.69a2,2,0,0,0,1.3.5,2,2,0,0,0,0-4ZM6.47,12.45a1.11,1.11,0,0,1-1.07,1,1.1,1.1,0,0,1,0-2.2,1.08,1.08,0,0,1,.51.14,1,1,0,0,1,.56.82.7.7,0,0,1,0,.14A.5.5,0,0,1,6.47,12.45Zm9.24-5.66a1.05,1.05,0,0,1-.71.28,1.07,1.07,0,0,1-.71-.28A1.06,1.06,0,0,1,13.9,6a.66.66,0,0,1,0-.15,1.08,1.08,0,0,1,2.14,0,.66.66,0,0,1,0,.15A1.06,1.06,0,0,1,15.71,6.79Zm8.89,6.63a1.11,1.11,0,0,1-1.07-1,.5.5,0,0,1,0-.13.7.7,0,0,1,0-.14,1,1,0,0,1,.56-.82,1.08,1.08,0,0,1,.51-.14,1.1,1.1,0,0,1,0,2.2Z"/><rect class="foregroundColor" x="13.05" y="14.22" width="3.91" height="1.18"/><polygon class="foregroundColor" points="16.95 19.54 16.95 19.37 16.95 18.55 16.95 18.2 16.95 16.99 16.95 16.58 13.05 16.58 13.05 16.99 13.05 18.2 13.05 18.55 13.05 19.37 13.05 19.54 9.5 19.54 9.5 19.54 11.34 21.7 15 26.04 18.66 21.7 20.5 19.54 20.5 19.54 16.95 19.54"/><rect class="foregroundColor" x="13.05" y="11.86" width="3.91" height="1.18"/>
</symbol>
<symbol id="incoming" viewBox="0 0 22 7">
<rect class="foregroundColor" x="10.26" y="4" width="2" height="3"/><rect class="foregroundColor" x="13.51" y="1" width="2" height="6"/><rect class="foregroundColor" x="16.75" y="3" width="2" height="4"/><rect class="foregroundColor" x="20" width="2" height="7"/><path class="foregroundColor" d="M.64,4.57H0V2.43H.64Zm1.29,0V2.43H1.29V4.57ZM4.19,2.43H2.58V4.57H4.19V6.5l3.55-3L4.19.5Z"/>
</symbol>
<symbol id="info" viewBox="0 0 20 20">
<path class="foregroundColor" d="M10,.5A9.5,9.5,0,1,1,.5,10,9.51,9.51,0,0,1,10,.5Zm0,1.43A8.07,8.07,0,1,0,18.07,10,8.07,8.07,0,0,0,10,1.93Z"/><path class="foregroundColor" d="M10,7.06a.85.85,0,0,1-.59-.22.8.8,0,0,1,0-1.14A.81.81,0,0,1,10,5.47a.81.81,0,0,1,.6.23.72.72,0,0,1,.25.57.73.73,0,0,1-.25.56A.85.85,0,0,1,10,7.06Zm.69,7.47H9.29V8.36h1.39Z"/>
</symbol>
<symbol id="inputs" viewBox="0 0 16 16">
<path class="foregroundColor" d="M5.6,6.2V3.8L10.4,8,5.6,12.2V9.8H.8V6.2ZM9.2,2a5.91,5.91,0,0,0-2.95.78l.14.12.84.73a4.8,4.8,0,1,1,0,8.74l-.84.73-.14.12A5.91,5.91,0,0,0,9.2,14a6,6,0,0,0,0-12Z"/>
</symbol>
<symbol id="label-switch" viewBox="0 0 18 18">
<path class="foregroundColor" d="M12.17,7.05a3.65,3.65,0,0,0-.48-1.13c-.21-.29-.31-.38-1-.38H9.74v5.81c0,1,.11,1.09,1.22,1.18v.41H7.09v-.41c1.07-.09,1.17-.18,1.17-1.18V5.54H7.39c-.68,0-.87.1-1.1.42a4.83,4.83,0,0,0-.46,1.1H5.4c.08-.84.16-1.73.19-2.35h.34c.18.28.32.29.68.29h4.86a.73.73,0,0,0,.68-.29h.33c0,.53.06,1.54.12,2.3Z"/>
</symbol>
<symbol id="labels-off-r-tb" viewBox="0 0 20 20">
<path class="foregroundColor" d="M10,18.5A8.5,8.5,0,1,1,18.5,10,8.51,8.51,0,0,1,10,18.5Zm0-16A7.5,7.5,0,1,0,17.5,10,7.5,7.5,0,0,0,10,2.5Z"/><path class="foregroundColor" d="M13.14,8.33a3.75,3.75,0,0,0-.51-1.18c-.22-.25-.32-.35-1-.35h-.77v5.64c0,1,.12,1.08,1.19,1.16v.45h-4V13.6c1-.08,1.12-.15,1.12-1.16V6.8H8.44c-.67,0-.86.11-1.09.4a4.33,4.33,0,0,0-.49,1.14H6.4c.09-.88.17-1.81.19-2.39H7c.18.28.33.3.69.3h4.8a.78.78,0,0,0,.69-.3h.35c0,.51.06,1.56.12,2.34Z"/>
</symbol>
<symbol id="labels-on-r-tb" viewBox="0 0 20 20">
<path class="foregroundColor" d="M10,1.5A8.5,8.5,0,1,0,18.5,10,8.51,8.51,0,0,0,10,1.5Zm3.14,6.83a3.75,3.75,0,0,0-.51-1.18c-.22-.25-.32-.35-1-.35h-.77v5.64c0,1,.12,1.08,1.19,1.16v.45h-4V13.6c1-.08,1.12-.15,1.12-1.16V6.8H8.44c-.67,0-.86.11-1.09.4a4.33,4.33,0,0,0-.49,1.14H6.4c.09-.88.17-1.81.19-2.39H7c.18.28.33.3.69.3h4.79a.78.78,0,0,0,.7-.3h.35c0,.51.06,1.56.12,2.34Z"/>
</symbol>
<symbol id="layer-list-tr-tb" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="15 16.61 5.02 10.8 15 5 24.98 10.8 15 16.61"/><polygon class="foregroundColor" points="23.55 14.08 15.91 18.52 15.01 19.05 14.1 18.52 6.45 14.07 5 14.91 11.21 18.52 15.01 20.73 18.8 18.52 25 14.92 23.55 14.08"/><polygon class="foregroundColor" points="23.55 18.34 15.91 22.79 15.01 23.32 14.1 22.79 6.45 18.34 5 19.18 11.21 22.79 15.01 25 18.8 22.79 25 19.19 23.55 18.34"/>
</symbol>
<symbol id="layerlist-map-element-menu" viewBox="0 0 24 24">
<polygon class="foregroundColor" points="4.73 4.73 14.91 4.73 14.91 7.25 15.64 7.25 15.64 4 4 4 4 15.64 7.47 15.64 7.47 14.91 4.73 14.91 4.73 4.73"/><polygon class="foregroundColor" points="14.46 15.48 8.48 12 14.46 8.52 20.45 12 14.46 15.48"/><polygon class="foregroundColor" points="19.6 13.96 15.01 16.63 14.47 16.95 13.92 16.63 9.33 13.96 8.46 14.46 12.19 16.63 14.47 17.95 16.75 16.63 20.46 14.46 19.6 13.96"/><polygon class="foregroundColor" points="19.6 16.52 15.01 19.19 14.47 19.51 13.92 19.19 9.33 16.52 8.46 17.02 12.19 19.19 14.47 20.52 16.75 19.19 20.46 17.03 19.6 16.52"/>
</symbol>
<symbol id="linked-not" viewBox="0 0 18 12">
<path class="foregroundColor" d="M10.67,2.09a1.41,1.41,0,0,1,1-.43,1.39,1.39,0,0,1,1,.4,1.42,1.42,0,0,1,0,2l-1.78,1.8.66.64,1.77-1.8a2.33,2.33,0,0,0,0-3.29A2.4,2.4,0,0,0,11.62.75a2.33,2.33,0,0,0-1.61.7L8.24,3.25l.65.64Z"/><path class="foregroundColor" d="M7.38,9.62a1.44,1.44,0,0,1-2,0,1.41,1.41,0,0,1,0-2l1.78-1.8-.65-.65L4.74,7A2.33,2.33,0,0,0,8,10.27l1.78-1.8-.65-.65Z"/><path class="foregroundColor" d="M5.43,4.73a.42.42,0,0,0,.24-.07.44.44,0,0,0,.23-.29h0a.49.49,0,0,0-.35-.6L4.28,3.43A.49.49,0,0,0,4,4.38l1.27.33Z"/><path class="foregroundColor" d="M5.88,3.65a.48.48,0,0,0,.34.14h0a.52.52,0,0,0,.35-.14.5.5,0,0,0,.14-.35A.45.45,0,0,0,6.56,3L5.63,2a.49.49,0,0,0-.35-.15h0a.5.5,0,0,0-.34.84Z"/><path class="foregroundColor" d="M6.86,2.64a.53.53,0,0,0,.23.3A.5.5,0,0,0,7.33,3h.13a.49.49,0,0,0,.34-.6L7.46,1.11A.51.51,0,0,0,7.23.82a.44.44,0,0,0-.37,0,.46.46,0,0,0-.3.23.47.47,0,0,0,0,.37Z"/><path class="foregroundColor" d="M14.27,7.85a.46.46,0,0,0-.3-.22L12.7,7.29a.44.44,0,0,0-.37,0,.44.44,0,0,0-.23.29.51.51,0,0,0,.05.38.48.48,0,0,0,.3.22l1.27.34.13,0a.51.51,0,0,0,.24-.07.46.46,0,0,0,.23-.3A.48.48,0,0,0,14.27,7.85Z"/><path class="foregroundColor" d="M12.12,8.35a.48.48,0,0,0-.34-.14h0a.5.5,0,0,0-.35.14.52.52,0,0,0-.14.35.45.45,0,0,0,.15.34l.93.94a.52.52,0,0,0,.35.14.46.46,0,0,0,.34-.15.49.49,0,0,0,0-.69Z"/><path class="foregroundColor" d="M11.14,9.36a.53.53,0,0,0-.23-.3.48.48,0,0,0-.71.55l.34,1.28a.49.49,0,0,0,.47.36l.13,0a.46.46,0,0,0,.3-.23.47.47,0,0,0,0-.37Z"/>
</symbol>
<symbol id="linked" viewBox="0 0 18 12">
<path class="foregroundColor" d="M9.9,7.45a.26.26,0,0,0-.17-.07.29.29,0,0,0-.18.07l-2,2.05a1.42,1.42,0,1,1-2-2l2.05-2a.27.27,0,0,0,0-.36L7,4.51a.25.25,0,0,0-.36,0l-2,2a2.75,2.75,0,1,0,3.89,3.89l2-2a.25.25,0,0,0,0-.36Z"/><path class="foregroundColor" d="M13.44,1.56a2.74,2.74,0,0,0-3.89,0L7.49,3.62a.24.24,0,0,0,0,.35l.59.59a.23.23,0,0,0,.17.08.25.25,0,0,0,.18-.08L10.49,2.5a1.45,1.45,0,0,1,2,0,1.42,1.42,0,0,1,0,2L10.44,6.57a.25.25,0,0,0-.08.18.23.23,0,0,0,.08.17l.59.59a.25.25,0,0,0,.17.08.25.25,0,0,0,.18-.08l2.06-2.06A2.74,2.74,0,0,0,13.44,1.56Z"/>
</symbol>
<symbol id="lock-layer" viewBox="0 0 10 10">
<path class="foregroundColor" d="M8,4.18H7.29V2.86A2.33,2.33,0,0,0,5.3.52,2.29,2.29,0,0,0,2.71,2.79V4.18H2.05a.45.45,0,0,0-.45.45V9.05a.45.45,0,0,0,.45.45H8a.45.45,0,0,0,.45-.45V4.63A.45.45,0,0,0,8,4.18ZM3.24,2.79a1.76,1.76,0,0,1,3.52,0V4.18H3.24Z"/>
</symbol>
<symbol id="lock-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M16.67,10.33h-1V8.24a3.7,3.7,0,0,0-3.16-3.71,3.66,3.66,0,0,0-2.86.87A3.6,3.6,0,0,0,8.38,8.12v2.21H7.33a.7.7,0,0,0-.7.71v7a.7.7,0,0,0,.7.71h9.34a.71.71,0,0,0,.71-.71V11A.71.71,0,0,0,16.67,10.33Zm-7.46,0V8.12a2.79,2.79,0,1,1,5.58,0v2.21Z"/>
</symbol>
<symbol id="locked-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M20.74,13.18H19.56v-2.3A4.57,4.57,0,0,0,15,6.31h-.31A4.65,4.65,0,0,0,10.44,11v2.16H9.26a.89.89,0,0,0-.89.89v8.74a.89.89,0,0,0,.89.88H20.74a.89.89,0,0,0,.89-.88V14.07A.89.89,0,0,0,20.74,13.18Zm-8.74,0v-2.3a3,3,0,0,1,3-3h.2A3.07,3.07,0,0,1,18,11v2.18Z"/>
</symbol>
<symbol id="map-area_delete" viewBox="0 0 20 20">
<path class="foregroundColor" d="M4.34,4.34a8,8,0,1,0,11.32,0A8,8,0,0,0,4.34,4.34Zm10.6,10.6a7,7,0,1,1,0-9.88A7,7,0,0,1,14.94,14.94Z"/><polygon class="foregroundColor" points="12.37 6.5 10 8.87 7.63 6.5 6.5 7.63 8.87 10 6.5 12.37 7.63 13.5 10 11.13 12.37 13.5 13.5 12.37 11.13 10 13.5 7.63 12.37 6.5"/>
</symbol>
<symbol id="map-area_edit" viewBox="0 0 20 20">
<path class="foregroundColor" d="M4.34,4.34a8,8,0,1,0,11.32,0A8,8,0,0,0,4.34,4.34Zm10.6,10.6a7,7,0,1,1,0-9.88A7,7,0,0,1,14.94,14.94Z"/><rect class="foregroundColor" x="6.81" y="8.47" width="7.71" height="1.77" transform="translate(-3.49 10.28) rotate(-45)"/><polygon class="foregroundColor" points="6.71 12.06 7.96 13.31 5.99 14 6.71 12.06"/>
</symbol>
<symbol id="map-opacity-resize-cursor-tr-tb" viewBox="0 0 20 20">
<path class="backgroundColor" d="M10,3.5A6.5,6.5,0,1,0,16.5,10,6.51,6.51,0,0,0,10,3.5Zm-.17,10c-2.92,0-5-3-5.43-3.71C4.81,9.18,6.77,6.5,9.83,6.5s5,2.7,5.42,3.3C14.88,10.45,13,13.5,9.83,13.5Z"/><circle class="backgroundColor" cx="9.82" cy="10" r="2"/><path class="foregroundColor" d="M10,2.5A7.5,7.5,0,1,0,17.5,10,7.5,7.5,0,0,0,10,2.5Zm0,14A6.5,6.5,0,1,1,16.5,10,6.51,6.51,0,0,1,10,16.5Z"/><path class="foregroundColor" d="M9.83,6.5c-3.06,0-5,2.68-5.43,3.29.43.67,2.51,3.71,5.43,3.71,3.15,0,5.05-3.05,5.42-3.7C14.87,9.2,13,6.5,9.83,6.5Zm0,5.5a2,2,0,1,1,2-2A2,2,0,0,1,9.82,12Z"/>
</symbol>
<symbol id="map-opacity-scale-tr-tb" viewBox="0 0 100 30">
<path class="foregroundColor" d="M5,18.7c-1,0-1.5-.7-1.5-2.11a2.77,2.77,0,0,1,.41-1.66A1.33,1.33,0,0,1,5,14.35c1,0,1.47.72,1.47,2.14a2.87,2.87,0,0,1-.4,1.64A1.32,1.32,0,0,1,5,18.7Zm0-3.64c-.4,0-.6.5-.6,1.51S4.59,18,5,18s.57-.49.57-1.47S5.37,15.06,5,15.06Z"/><path class="foregroundColor" d="M92.3,14.33v4.29h-.93V15.37a.83.83,0,0,1-.18.13l-.22.11-.24.08-.25.05V15a3.71,3.71,0,0,0,.68-.27,4.08,4.08,0,0,0,.58-.36Z"/><path class="foregroundColor" d="M94.5,18.7c-1,0-1.5-.7-1.5-2.11a2.85,2.85,0,0,1,.4-1.66,1.36,1.36,0,0,1,1.18-.58c1,0,1.47.72,1.47,2.14a2.87,2.87,0,0,1-.4,1.64A1.32,1.32,0,0,1,94.5,18.7Zm0-3.64c-.4,0-.6.5-.6,1.51s.2,1.42.59,1.42.57-.49.57-1.47S94.92,15.06,94.54,15.06Z"/><path class="foregroundColor" d="M98,18.7c-1,0-1.5-.7-1.5-2.11a2.84,2.84,0,0,1,.41-1.66A1.33,1.33,0,0,1,98,14.35c1,0,1.47.72,1.47,2.14a2.87,2.87,0,0,1-.4,1.64A1.32,1.32,0,0,1,98,18.7Zm0-3.64c-.4,0-.6.5-.6,1.51S97.59,18,98,18s.57-.49.57-1.47S98.37,15.06,98,15.06Z"/><path class="foregroundColor" d="M95,27.38H5a1,1,0,1,1,0-2H95a1,1,0,0,1,0,2Z"/><path class="foregroundColor" d="M5,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,5,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,5,24Z"/><path class="foregroundColor" d="M20,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,20,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,20,24Z"/><path class="foregroundColor" d="M35,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,35,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,35,24Z"/><path class="foregroundColor" d="M50,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,50,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,50,24Z"/><path class="foregroundColor" d="M65,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,65,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,65,24Z"/><path class="foregroundColor" d="M80,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,80,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,80,24Z"/><path class="foregroundColor" d="M95,24a.5.5,0,0,1-.5-.5v-1A.5.5,0,0,1,95,22a.51.51,0,0,1,.5.5v1A.5.5,0,0,1,95,24Z"/>
</symbol>
<symbol id="map-opacity-tr-tb" viewBox="0 0 40 20">
<path class="foregroundColor" d="M29.8,12.59c-.15-.12-.15-.32-.25-.47a.65.65,0,0,1-.12-.47,2,2,0,0,0-.11-.57c0-.05-.06-.08-.12,0s-.2.05-.22-.12-.21-.31-.3-.48-.41.05-.46.1a4.45,4.45,0,0,1-.62.53.28.28,0,0,0-.13.25.78.78,0,0,1-.17.54c-.12.13-.2.13-.27,0a2.78,2.78,0,0,1-.38-1.34c-.2.11-.33.11-.38-.13a.68.68,0,0,0-.54-.29,1.13,1.13,0,0,1-.61-.05,1.55,1.55,0,0,0-.51-.14.37.37,0,0,1-.32-.25.19.19,0,0,0-.14-.12c-.09,0-.15,0-.11.11a.63.63,0,0,0,.3.36l0,0c.14.19.2.2.36,0s.16-.14.19,0,.11.1.18.14.16.16.06.32a1.71,1.71,0,0,1-.64.52,5.06,5.06,0,0,1-.89.4.68.68,0,0,1-.15,0,2.61,2.61,0,0,0-.25-.71c-.07-.11-.2-.18-.23-.32a2.13,2.13,0,0,0-.38-.65.07.07,0,0,0-.11,0c-.06,0,0,.07,0,.11.13.3.3.58.4.89a1.5,1.5,0,0,0,.6.7c0,.19.1.25.28.2s.27,0,.4-.08.21,0,.16.15a1.78,1.78,0,0,1-.66.93,2.56,2.56,0,0,0-.53.56.61.61,0,0,0,0,.65,1.4,1.4,0,0,1,.07.61.27.27,0,0,1-.17.22l-.17.08a.39.39,0,0,0-.21.48c0,.16,0,.3-.17.36s-.09.07-.1.14a1.18,1.18,0,0,1-1.05.94c-.1,0-.2,0-.3.06s-.3-.06-.28-.21a.77.77,0,0,0-.17-.47,1,1,0,0,1-.2-.51,1.08,1.08,0,0,0-.21-.58.58.58,0,0,1,0-.69c.16-.23.06-.43,0-.64a1,1,0,0,0-.3-.53.32.32,0,0,1-.08-.35c.1-.29,0-.37-.27-.41-.05,0-.11,0-.13-.06s-.27-.16-.43-.11-.38.18-.6.09a.17.17,0,0,0-.1,0c-.43.2-.64-.11-.88-.38a3,3,0,0,0-.29-.33.43.43,0,0,1-.11-.46.59.59,0,0,0,0-.56c0-.05,0-.11,0-.16a3,3,0,0,1,.35-.57.35.35,0,0,1,.14-.13.39.39,0,0,0,.26-.33A.37.37,0,0,1,18,9.06L18,9c.09-.25.28-.17.45-.15s.12,0,.17,0a1.72,1.72,0,0,1,1-.2h.11c.21,0,.33.08.25.28s0,.18.08.19c.27,0,.44.22.69.29.1,0,.16,0,.19-.09a.19.19,0,0,1,.29-.13,5.7,5.7,0,0,0,.55.16.36.36,0,0,0,.27,0,.19.19,0,0,1,.15,0c.09,0,.2.1.26,0a1,1,0,0,0,.17-.52c0-.1-.09-.09-.15-.06s-.34,0-.51,0a.27.27,0,0,1-.3-.3c0-.07,0-.21-.13-.23s-.16.09-.27.07c.05.11.2.2.06.34s-.19.11-.22,0-.29-.31-.28-.55c0,0-.05-.08-.09-.1s-.29-.24-.46-.33-.08-.08-.12,0,0,.09,0,.14a1.76,1.76,0,0,0,.49.41.77.77,0,0,1,.12.1c-.11,0-.16,0-.18.12a.5.5,0,0,1-.25.29c-.06,0-.16-.05-.23-.13s.16,0,.24,0,.16-.12.06-.22a1.92,1.92,0,0,0-.28-.18A.55.55,0,0,1,20,7.9c-.1-.17-.22-.11-.37-.09a1.21,1.21,0,0,0-.65.34c-.07.06-.15.11-.13.21a.19.19,0,0,1-.12.22.58.58,0,0,1-.63.1c-.28-.15-.28-.14-.22-.47a.72.72,0,0,0,0-.21c0-.17,0-.21.18-.21h.43c.2,0,.29-.14.18-.32a.36.36,0,0,0-.22-.2s-.11,0-.1-.1.09-.08.14-.08.26-.11.41-.13A.19.19,0,0,0,19,6.79h0a.48.48,0,0,0,.28-.17.46.46,0,0,1,.39-.24c.13,0,.07-.09.06-.15s-.12-.26.05-.35.13-.13.19-.06a.2.2,0,0,1,0,.24c-.08.11,0,.23.06.22s.39.11.58,0a.33.33,0,0,1,.24,0c.13,0,.22,0,.24-.17s0-.35.28-.25c.07,0,.08,0,0-.1s0-.22.15-.25.17,0,.25,0,.1,0,.09-.07-.06,0-.09,0a.45.45,0,0,0-.26,0,.28.28,0,0,1-.39-.11.38.38,0,0,1,.08-.47,1.56,1.56,0,0,0,.2-.16s.11-.08.07-.14a.17.17,0,0,0-.19,0c-.07,0-.14,0-.14.12s-.13.21-.24.28-.27.27-.1.49a.15.15,0,0,1,0,.23.28.28,0,0,0-.13.26.51.51,0,0,1-.38.29.11.11,0,0,1-.09-.11.7.7,0,0,0,0-.14c-.06-.1-.08-.24-.17-.29s-.18.08-.28.11-.24,0-.28-.15l-.06-.24A.28.28,0,0,1,19.49,5a3.52,3.52,0,0,0,.38-.24,2.13,2.13,0,0,0,.6-.82c.05-.15.22-.12.33-.17s.48-.14.72-.25a.75.75,0,0,1,.57,0c.12.05,0,.13,0,.18a2.33,2.33,0,0,1,1.07.38c.15.1.11.24-.06.3a.92.92,0,0,1-.66-.07c.17.15.06.43.34.45,0-.06-.08-.11,0-.17s.11,0,.16,0,.15.07.18-.09a.29.29,0,0,1,.31-.18c.11,0,.14,0,.11-.12s0-.14,0-.21a.06.06,0,0,1,.05-.08A.24.24,0,0,1,23.8,4s0,.06,0,.09-.1,0-.05.1a.13.13,0,0,0,.18,0A.74.74,0,0,1,24.21,4c.12-.06.33-.16.37-.12s.22,0,.33,0,.21-.1.33,0,.09,0,.05-.1a.22.22,0,0,0-.1-.09s-.08-.06,0-.11.07,0,.12,0a5.53,5.53,0,0,1,.92.31c-.07-.13-.21-.22-.18-.39,0,0,0,0,0,0s.36-.44.47-.42.22,0,.18.25a1.33,1.33,0,0,0,0,.52c0,.24,0,.33-.21.43l-.06,0a.33.33,0,0,0,.37-.15.26.26,0,0,0,0-.33.21.21,0,0,1,0-.19s0-.06,0-.07c-.19-.22.07-.31.14-.46.13,0,0,.22.1.27.07-.25.15-.27.35-.2s.17.11.3.1c0-.08-.12-.08-.15-.14s0-.2.12-.22l.29,0c.12,0,.22,0,.23-.16a.15.15,0,0,1,.11-.11,1.59,1.59,0,0,1,.93-.22.64.64,0,0,0,.46-.17C30,2,30,2,30.3,2.19a.67.67,0,0,0,.36.06.42.42,0,0,1,.32.11c.14.14.13.25,0,.33A5.61,5.61,0,0,0,30.4,3l0,0c.28-.23.6,0,.9-.09s.32.12.5.13.24.06.31-.1.08-.06.13,0l.13,0c.31,0,.31,0,.34.35,0,.09.08.13.15.17s.11,0,.14-.07.1-.07.18,0,.3,0,.45,0a.16.16,0,0,0,.18-.13c0-.1.15-.13.25-.12s.49,0,.73.08.23.19.38.24a.48.48,0,0,0,.24,0,1.2,1.2,0,0,1,.38,0c.1,0,.21,0,.23.13s.16.15.29.14.35,0,.52,0,.14.09.21.12.15.07.12-.07,0-.12.12-.12a2.38,2.38,0,0,1,1.37.44.65.65,0,0,0,.34.13.31.31,0,0,1,.16.06c.16.11.16.13,0,.24s-.07,0-.08.07-.18.11-.3.07-.14-.14-.25-.14-.17,0-.23-.17a.35.35,0,0,1-.17.31c-.09,0,0,.13,0,.2S38.16,5,38,5a1.22,1.22,0,0,0-.6.22.57.57,0,0,1-.39.12.68.68,0,0,0-.65.25s0,.06,0,.13a.28.28,0,0,1,0,.33c-.13.21-.37.34-.46.57,0,.06-.1.13-.17.12s0-.1-.07-.16a.74.74,0,0,1,.33-1c.15-.08.24-.23.39-.32s.1-.16.14-.29c-.12.11-.18.22-.3.26s-.11.06-.15,0-.22-.11-.31,0-.31.11-.22.33c0,0-.35.11-.39,0s-.17-.08-.26-.06a.4.4,0,0,1-.15,0,.84.84,0,0,0-.86.3,3.84,3.84,0,0,1-.43.35c.14.06.22.2.38.1s.09,0,.13.06.2.13.23-.09a.45.45,0,0,1,.11.33,2.25,2.25,0,0,0,.06.36.51.51,0,0,1,0,.09c-.24.08-.12.23-.08.37-.21.06-.21.05-.19-.15s0-.31,0-.46,0-.1-.06-.1-.06,0-.06.08c0,.46-.35.72-.63,1-.07.07-.14.14-.26.09s-.14,0-.2.07-.14.25-.28.31,0,.08,0,.12l.1.14c.06.1.11.22.05.31s-.21.1-.32.12-.06-.05-.05-.11.1-.26-.1-.32a.06.06,0,0,1,0-.08c0-.24-.11-.15-.21-.13s-.11.09-.16.05,0-.12,0-.19-.22.19-.4.22c.11.08.15.19.3.13s.16,0,.18.07-.07.06-.12.09-.19.14-.09.31a.55.55,0,0,1,.16.36,1.13,1.13,0,0,1-.78,1c-.15.06-.33.06-.45.2s-.07,0-.1,0-.21-.05-.3,0a.2.2,0,0,0,0,.29c.12.15.29.25.3.48a.28.28,0,0,1-.17.32l-.06,0c-.17.29-.29.09-.41,0a1.07,1.07,0,0,0-.16-.14.62.62,0,0,0-.16-.1c-.15.39-.14.46.19.72.17.13.16.33.2.5s-.11,0-.18,0ZM23,8.13a1.6,1.6,0,0,0,.23,0c.07,0,.1-.06.06-.13a.72.72,0,0,0-.67-.3c-.07,0-.12,0-.19,0-.2-.24-.2-.23-.39,0-.05.07-.08.15-.13.21s0,.14,0,.19a.21.21,0,0,0,.27,0,.61.61,0,0,1,.65,0A.3.3,0,0,0,23,8.13Zm1.64.33a1.3,1.3,0,0,1-.14-.37.33.33,0,0,0-.12-.23c-.16-.14-.14-.22.07-.3.05,0,.1,0,.1-.1s-.07-.08-.13-.08a.67.67,0,0,0-.46.24A.5.5,0,0,0,24,8l.12.18a.22.22,0,0,1,0,.16.26.26,0,0,0,.27.36C24.55,8.68,24.61,8.64,24.6,8.46Z"/><path class="foregroundColor" d="M4.45,5.94c0,.11.12.17.07.27h0c-.08-.16-.26-.2-.35-.35a1,1,0,0,0-.77-.46H3.22a.85.85,0,0,0-.61.07s-.11.1-.17.06,0-.13,0-.27A1.93,1.93,0,0,1,1,6.27l0,0L1.39,6a.83.83,0,0,0,.33-.22c.07-.08.1-.16-.06-.16s-.18-.07-.28,0-.07,0-.09-.09,0-.08-.07-.08S.86,5.27.88,5.15a.31.31,0,0,1,.31-.29.31.31,0,0,0,.12-.05c.06,0,.09,0,.08-.11s-.07,0-.12,0-.39.07-.53-.13-.05-.11,0-.14.28-.18.44,0,.11,0,.13,0,0-.08-.09-.08S1,4.05.86,4s0-.12,0-.13.24-.17.36-.23a1.13,1.13,0,0,1,.84-.21,6.34,6.34,0,0,0,1.22.2,1.24,1.24,0,0,1,.55.16.33.33,0,0,0,.26,0,3.29,3.29,0,0,1,.72-.16c.14,0,.24-.08.33.06.39-.11.73.13,1.11.14,0-.07-.19-.09-.15-.17s.23-.05.34-.11c-.14,0-.31.05-.4,0s-.19-.17-.1-.32c-.15,0-.18.17-.3.23s-.3-.07-.42-.14,0-.16,0-.24.06-.09,0-.12c0-.21.12-.22.24-.19.28.06.61,0,.83.24s.27,0,.37.15c.2-.16.25.05.37.15-.13-.2,0-.3.15-.37a.15.15,0,0,1,.16,0s0,.09,0,.14a.47.47,0,0,0,.23.49c.07,0,.21.07.17.14s-.1.24-.27.22H7.42A.63.63,0,0,0,8.08,4s.07,0,.05-.11,0-.23.13-.2.12,0,.1-.11a.23.23,0,0,1,.06-.25c.06,0,.05-.12,0-.2-.06-.26,0-.38.32-.33.08,0,.22,0,.22.09s-.08.18-.2.23-.2.12-.06.24.12.3.28.39a.25.25,0,0,0,.12,0c.11,0,.18.07.16.19s0,.12.06.13,0-.06,0-.09a0,0,0,0,1,0-.05c.11,0,.14-.09.14-.17s-.07-.12-.16-.15a.49.49,0,0,1-.32-.25A.3.3,0,0,1,9.16,3a.34.34,0,0,1,.42-.13.39.39,0,0,0,.24,0,1.82,1.82,0,0,1,.57,0c.13,0,.16.14.23.22s.44.21.65.34.14.1.22.14.07.12.06.2-.13,0-.09.09.11,0,.15,0,.36.13.5.27.08.1,0,.15a.57.57,0,0,0-.11.11c-.08.12-.16.12-.26,0s-.13-.17-.19-.1,0,.16.12.21l0,0c.07.09.14.19.08.31s-.14,0-.21,0,.12.09,0,.16-.11,0-.17,0A.92.92,0,0,1,11,4.8a.33.33,0,0,0-.38-.11c-.08,0-.2,0-.21-.06s.09-.13.19-.13.24,0,.21-.15c0,0,0,0,.05-.06s.16-.1.1-.2-.15-.22-.3-.21a.17.17,0,0,1-.21-.16c-.07,0-.06.13-.14.14a.25.25,0,0,0-.37-.14c.17.13.09.34.07.47s-.24.19-.44.16c.13.19.35.23.5.36s.09,0,.07.09a.08.08,0,0,1-.12,0c-.12,0-.22-.08-.33,0s-.15,0-.23-.06,0-.07,0-.12,0-.11,0-.18a.65.65,0,0,1-.3.29,1.05,1.05,0,0,0-.52.54c0,.11,0,.22.09.28a.19.19,0,0,1,.09.12.14.14,0,0,0,.16.1A.7.7,0,0,1,9.28,6a1,1,0,0,0,.5.18c.11,0,.13.06.13.16a.4.4,0,0,0,.18.36.11.11,0,0,0,.14,0,.1.1,0,0,0,0-.13l0-.12c-.05-.14-.06-.24.13-.32a.22.22,0,0,0,0-.4c-.09-.06-.12-.1,0-.19s0-.25,0-.37,0-.17.15-.17a1.43,1.43,0,0,1,.73.26s0,.07,0,.11.11.25.23.29.18-.07.22-.18.12-.11.17,0,.26.29.29.5a.26.26,0,0,0,.2.19,1.53,1.53,0,0,1,.25.16c.21.14.17.32.13.49s0,.17.12.18.17.18.22.3,0,.09-.08.08-.37-.09-.56-.11,0-.09,0-.14.13-.23.2-.36a.19.19,0,0,0-.19,0,.49.49,0,0,1-.3.12c-.06,0-.13,0-.05.09s0,.07-.05.06A.59.59,0,0,1,11.88,7c-.14-.11-.26-.09-.39.06a.9.9,0,0,1,.19,0c.1,0,.2,0,.15.17s-.05.15,0,.17.18.16.3.07.09-.08.14,0,0,.09-.05.12-.37.13-.52.25a.05.05,0,0,1-.08,0c0-.06,0-.1,0-.2-.18.17-.5.09-.53.43a.11.11,0,0,1-.08.1c-.25,0-.31.23-.41.4s-.08,0-.13,0c.1.32.07.35-.22.54s-.36.28-.24.58a.57.57,0,0,1,.08.29c0,.05,0,.09-.07.11S10,10,10,10a.53.53,0,0,1-.14-.29c0-.12-.06-.17-.19-.17s-.23-.13-.34,0a.86.86,0,0,1-.61.07.42.42,0,0,0-.4.4.58.58,0,0,1,0,.19.6.6,0,0,0,.17.57A.35.35,0,0,0,9,10.6c.06-.18.21-.17.35-.17s.08.06.05.13l-.09.29c0,.13,0,.19.14.2.34,0,.36,0,.34.39s.13.42.39.33a.18.18,0,0,1,.21.06c.1.1.16.07.23,0a.54.54,0,0,1,.76-.17.6.6,0,0,0,.33.08l.49,0a.82.82,0,0,0,.62.47.61.61,0,0,1,.59.49c0,.09-.05.12-.08.18a1.23,1.23,0,0,1,.6.14.54.54,0,0,0,.27.09,1.51,1.51,0,0,1,.75.31.35.35,0,0,1,.06.44c0,.06-.1.11-.14.16a.82.82,0,0,0-.25.58,1.16,1.16,0,0,1-.27.71c-.11.15-.31.1-.46.19s-.29.15-.29.38a1,1,0,0,1-.28.5,1.86,1.86,0,0,0-.22.27.34.34,0,0,1-.34.18c-.07,0-.1,0-.1.1,0,.26,0,.32-.29.38s-.28,0-.29.23-.1.07-.16.06-.08,0-.1,0,0,.07.06.07.09.07.05.09-.12.27-.27.34-.14.12,0,.2.09.18,0,.26a.71.71,0,0,0-.15.15.45.45,0,0,0,.15.67l.12.06a1.14,1.14,0,0,1-.61,0c0-.09.14-.1.06-.2s-.19.21-.15-.05a.08.08,0,0,0-.05-.08c-.3-.1-.25-.36-.24-.58,0-.07,0-.15,0-.18s-.06-.14,0-.14.11,0,.11-.1.09-.34,0-.53a.67.67,0,0,1,0-.5,6,6,0,0,0,.3-1.55,2,2,0,0,0,.05-.48.35.35,0,0,0-.22-.34c-.35-.12-.46-.42-.61-.71a1.65,1.65,0,0,0-.29-.45.21.21,0,0,1-.07-.19c.09-.27,0-.59.31-.8s.11-.54,0-.66-.07,0-.09,0c-.16.24-.3,0-.44,0a.52.52,0,0,1-.32-.3.23.23,0,0,0-.18-.16,1.18,1.18,0,0,1-.58-.29.19.19,0,0,0-.15,0c-.42.08-.72-.19-1.06-.35a.17.17,0,0,1-.08-.19.32.32,0,0,0-.14-.3c-.21-.16-.33-.41-.53-.58a1,1,0,0,1-.15-.24s-.06-.12-.12-.07,0,.09,0,.13a5.77,5.77,0,0,1,.47.74s0,0,0,.06-.1,0-.14-.07-.21-.34-.41-.42c0-.25-.22-.38-.28-.6A.34.34,0,0,0,6,9a1.23,1.23,0,0,1-.58-.87,1.4,1.4,0,0,1,0-.54A.6.6,0,0,0,5.08,7c-.08,0-.14-.09-.1-.21a.2.2,0,0,0-.13-.27c-.15,0-.14-.12-.17-.21A.47.47,0,0,0,4.45,5.94Zm5.71,1.7a.48.48,0,0,0-.8.07.27.27,0,0,0,0,.26s0,0,.05,0,0-.15,0-.22a.26.26,0,0,1,.22-.24c.12,0,.14.08.17.17s0,.09.09.11S9.91,7.55,10.16,7.64Z"/><path class="foregroundColor" d="M13.4,4.59c-.15.08-.19,0-.25-.09A2.06,2.06,0,0,1,13,4.15a.19.19,0,0,1,.17-.26.15.15,0,0,0,.13-.12s0-.07,0-.1-.07,0-.1,0-.08.06-.13.07-.24-.08-.18-.23.07-.14-.05-.19-.08-.18-.06-.24a.44.44,0,0,0-.08-.34.81.81,0,0,0-.86-.42l-.32,0c-.06,0-.14,0-.16,0s-.16-.13-.24-.19.12-.06.19-.09a.93.93,0,0,0-.26-.1S11,2,11,1.9s0-.09.1-.1l.53-.15c.06,0,.13,0,.14-.09s-.07-.08-.12-.1-.08,0-.08-.06,0-.06.06-.07.19-.11.29-.15.29,0,.32-.18c0,0,.06,0,.09,0l.55-.12h.05c.1.14.26,0,.36.09h.05c0-.23.17-.08.27-.07a.24.24,0,0,0,.21-.15c0-.06,0,0,.08-.06a1,1,0,0,1,.42,0c.17,0,.3-.05.45-.09a3.07,3.07,0,0,1,1.17,0c.17,0,.31.18.5.19s.06.08,0,.11l-.19.07c.19,0,.28,0,.21.16.3,0,.57-.2.88-.12.06,0,.15,0,.16.09s-.07.1-.13.12a2.84,2.84,0,0,0-.77.61c0,.05-.07.13,0,.15l.1,0c.06.05.14.12.13.2s-.12,0-.18.05-.1,0-.14,0,.1,0,.14.07.12.09.08.17-.08.12,0,.18,0,.06-.05.08a.8.8,0,0,0-.32.44.11.11,0,0,0,0,.1.23.23,0,0,1-.06.37,2,2,0,0,1-.82.32.42.42,0,0,0-.36.22c-.1.17-.29.19-.46.22a.35.35,0,0,0-.26.25c-.08.2-.18.4-.27.61,0,0-.07.12-.13.08-.23-.12-.53-.12-.65-.42C13.35,4.84,13.22,4.74,13.4,4.59Z"/><path class="foregroundColor" d="M34.28,14c0,.3.29.42.33.68a.61.61,0,0,0,.25.35,1.83,1.83,0,0,1,.31.36s0,.06.06.07c.23.11.21.33.22.52a.81.81,0,0,1-.17.58,1.23,1.23,0,0,0-.19.44.25.25,0,0,1-.16.17,1.23,1.23,0,0,1-.78.05.24.24,0,0,1-.21-.2c0-.08-.08-.12-.14-.18s-.07-.06-.07,0,0,.08-.06.06,0-.06,0-.1.07-.08,0-.16c-.16.2-.24.07-.32-.07a.4.4,0,0,0-.38-.19,1.28,1.28,0,0,0-.69.19c-.06,0-.11.09-.18.08a.93.93,0,0,0-.46.12.24.24,0,0,1-.3,0s-.08-.06-.06-.11a.72.72,0,0,0-.11-.62,1.26,1.26,0,0,1-.09-.5c0-.17.19-.35.42-.41L31.8,15c.08,0,.17,0,.19-.12s.25-.27.36-.41.2-.19.34-.06.13.07.15,0,.22-.19.29-.33.07,0,.1,0a.68.68,0,0,0,.28.07c.12,0,.16.06.09.15s0,.22.09.29c.36.23.42.2.45-.22A.47.47,0,0,1,34.28,14Z"/><path class="foregroundColor" d="M9.15,2.32A.32.32,0,0,1,9.53,2c-.12-.1-.21-.12-.48-.08a.34.34,0,0,1-.33-.11.65.65,0,0,0-.24-.19c-.08,0-.17-.13-.1-.24s.2-.32.37-.25a2.85,2.85,0,0,1,.67.35c.06-.2.26,0,.39-.11a1,1,0,0,1-.92-.32C9,1,9.06,1,9.15,1S9.56.79,9.8.87c0,0,0,0,.07,0a1.5,1.5,0,0,1,.88-.17,9.17,9.17,0,0,1,.92,0,2.83,2.83,0,0,1,.44.12s.07,0,.06.06-.06.07-.1.09c-.26.13-.53.25-.78.4a1.3,1.3,0,0,1-.42.22.16.16,0,0,0-.13.13c-.05.13-.11.25-.29.26s-.21.07-.09.2,0,.09,0,.11a.38.38,0,0,1-.25.07,5.72,5.72,0,0,0-.92,0Z"/><path class="foregroundColor" d="M35.14,14a.58.58,0,0,1-.48-.2c-.09-.12-.19-.13-.29,0a.2.2,0,0,1-.28,0,.48.48,0,0,0-.27-.09c-.08,0-.1,0,0-.11s0-.17-.08-.22a.29.29,0,0,0-.16-.07A.85.85,0,0,1,33,13s0-.08,0-.12.06,0,.1,0,.32,0,.35.24.1.07.15,0,.28-.12.43,0a2.46,2.46,0,0,1,.76.42l.06,0C34.76,13.78,35,13.84,35.14,14Z"/><path class="foregroundColor" d="M31.45,12.09a.53.53,0,0,1,.17.14c.07.07,0,.1-.06.15s-.08.08,0,.14c.17.3-.15.46-.2.69a.11.11,0,0,1-.1,0c-.14-.1-.33,0-.47-.13s-.14-.23-.17-.37.1-.1.16-.15.45-.22.59-.45A.11.11,0,0,1,31.45,12.09Z"/><path class="foregroundColor" d="M23.47,15.29a1.06,1.06,0,0,0,.1-.57.13.13,0,0,1,.1-.13.6.6,0,0,0,.39-.34c.07-.11.11-.07.14,0a.48.48,0,0,1,0,.39,6.49,6.49,0,0,0-.23.75c0,.16-.11.28-.29.27S23.47,15.5,23.47,15.29Z"/><path class="foregroundColor" d="M25,3.55a.68.68,0,0,1-.6-.14c-.05,0-.09-.07-.05-.13a5.16,5.16,0,0,1,.49-.64.92.92,0,0,1,.63-.27,1.16,1.16,0,0,0,.44-.12l.13,0c.06,0,.14,0,.16.06s0,.11-.1.13a4.14,4.14,0,0,1-.43.13,1.57,1.57,0,0,0-.84.61.17.17,0,0,0,0,.24Z"/><path class="foregroundColor" d="M18.17,6.16c-.06,0,0-.1,0-.15s0-.17-.07-.16-.07,0-.06-.07.08-.12.11-.08.08,0,.13,0,.17-.13.21,0,.06,0,.09,0,.09.06.06.1,0,.25.09.33.12.27.28.32,0,.08,0,.12-.05.09,0,.14h0c-.24.1-.5,0-.75.13.18-.1,0-.27.11-.38s0,0,0,0c.12-.2.12-.21-.11-.25Z"/><path class="foregroundColor" d="M9.16,2.34c-.05.09,0,.13.09.14a1.35,1.35,0,0,0,.4,0,.7.7,0,0,1,.38,0c.07,0,.17,0,.16.14s-.09.15-.19.16H9c-.13,0-.25-.05-.23-.23s-.06-.11-.13-.13a1.6,1.6,0,0,1-.21,0c-.05,0-.13,0-.11-.11s.1-.06.15-.05l.64.15Z"/><path class="foregroundColor" d="M29.8,12.59l.05.05c.15.1.15.36.39.37.05,0,.06.09,0,.15a.92.92,0,0,0,0,.29h0c-.29,0-.4-.21-.53-.38a2.8,2.8,0,0,0-.54-.69s-.09-.08-.06-.13.09,0,.14,0,.23.17.35.25S29.71,12.6,29.8,12.59Z"/><path class="foregroundColor" d="M34,8.1a.19.19,0,0,1,.16.29c-.15.49-.15.5-.64.65a2.09,2.09,0,0,0-.35.13.11.11,0,0,0-.09.06c0,.07-.08.18-.16.1s-.14-.19,0-.29a.8.8,0,0,1,.49-.25.68.68,0,0,0,.54-.55A.46.46,0,0,1,34,8.1Z"/><path class="foregroundColor" d="M31.83,12.2a.47.47,0,0,0,.11-.65c-.06-.1-.14-.15-.17-.28a.68.68,0,0,1,0-.35c0-.07,0-.13.12-.12s.11.08.11.15a.2.2,0,0,1,0,.11c-.1.15,0,.21.12.22s.22.17.24.25.08.25.11.38a.27.27,0,0,1-.12.3c-.08,0-.14-.06-.18-.13s-.09-.06-.13,0S32,12.2,31.83,12.2Z"/><path class="foregroundColor" d="M16.38,4.53s-.13,0-.15,0,.06-.1.1-.14a.1.1,0,0,1,.15,0c.05.11.11.07.2,0s.27,0,.41-.08.21.1.26.2-.06.16-.14.2a.86.86,0,0,1-.7.1c-.12,0-.1-.19-.22-.21S16.38,4.6,16.38,4.53Z"/><path class="foregroundColor" d="M6.28,2.3a.46.46,0,0,0,.14-.07s.08,0,.1,0,.2.13.3.21.1,0,.08-.09,0-.12.09-.14.1,0,.13.09a.14.14,0,0,0,.14.08c.06,0,.14,0,.14.09s0,.16-.13.16a2.2,2.2,0,0,0-.62.12c-.08,0-.16,0-.2-.09s-.14,0-.21-.05-.16-.07-.11-.19Z"/><path class="foregroundColor" d="M8.51,2.74h0c-.11-.21-.29-.06-.42-.1a2,2,0,0,1-.5-.21s-.07-.08,0-.14.08,0,.12,0,.13,0,.21,0a.32.32,0,0,1,.43.14.15.15,0,0,0,.22.06c.06,0,.1-.05.14,0s.09.1.06.17S8.58,2.72,8.51,2.74Z"/><path class="foregroundColor" d="M37.51,17.18a.64.64,0,0,1-.19.29.87.87,0,0,0-.19.23.41.41,0,0,1-.3.19c-.08,0-.08-.08-.15-.11s0-.08,0-.11a1.53,1.53,0,0,1,.18-.18.89.89,0,0,0,.36-.36.08.08,0,0,1,.14,0S37.5,17.09,37.51,17.18Z"/><path class="foregroundColor" d="M8.38,3.13a.21.21,0,0,1-.15.23.15.15,0,0,1-.21,0,.71.71,0,0,0-.14-.1c-.21-.11-.21-.11-.06-.28a.39.39,0,0,1,.4-.11C8.36,2.87,8.33,3.05,8.38,3.13Z"/><path class="foregroundColor" d="M31.82,13.25c0,.06,0,.11,0,.15s0,0-.06,0l0,0a.86.86,0,0,1,0-.57.21.21,0,0,1,.25-.14.56.56,0,0,0,.35,0c0,.14-.09.15-.18.16a.41.41,0,0,0-.11,0c-.08,0-.21-.05-.21.07s.13,0,.19,0,.11,0,.13,0,0,.06-.08.07-.08.06,0,.13.08.11,0,.17S31.9,13.27,31.82,13.25Z"/><path class="foregroundColor" d="M6.28,2.3l-.15.14S6,2.55,6,2.52s-.26,0-.39-.1a.05.05,0,0,1,0-.1c.17-.06.28-.21.48-.24S6.32,2.05,6.28,2.3Z"/><path class="foregroundColor" d="M37.35,16.33c.23,0,.21.23.36.29s.09.09.18.06.1,0,.06.11-.08.12-.11.18-.15.23-.21.18a.29.29,0,0,1-.11-.35.2.2,0,0,0,0-.23Z"/><path class="foregroundColor" d="M18.17,6.16l.09.08a1.3,1.3,0,0,0-.1.25c0,.1-.24.24-.34.22l-.07,0a1,1,0,0,1,.08-.37C17.91,6.15,18,6.16,18.17,6.16Z"/><path class="foregroundColor" d="M30.23,13.45c.25.06.51.06.75.12s.32.18.52.14.1,0,.14,0a.27.27,0,0,1-.21.06l-1.05-.17c-.11,0-.21-.05-.16-.19Z"/><path class="foregroundColor" d="M9.67,10.37a.36.36,0,0,1,.39-.12,2.09,2.09,0,0,1,.63.28s.07,0,.05.09-.05,0-.08,0a.35.35,0,0,1-.29-.09.67.67,0,0,0-.61-.19Z"/><path class="foregroundColor" d="M34,8.1c-.08-.09-.06-.16,0-.26s.21-.33.43-.12c0,0,.11,0,.18,0a.4.4,0,0,1-.45.29C34,8,34,8.07,34,8.1Z"/><path class="foregroundColor" d="M7.56,1.62c.15.09.38,0,.5.24,0,0,.06.08,0,.13S8,2,7.94,2a.71.71,0,0,0-.38-.09c-.14,0-.12-.14-.15-.22S7.48,1.64,7.56,1.62Z"/>
</symbol>
<symbol id="map-servers-tr-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M11,27.24v-2h3v-2h2v2h3v2Zm9-2v2h2a1,1,0,0,0,1-1h0a1,1,0,0,0-1-1Zm-10,0H8a1,1,0,0,0-1,1H7a1,1,0,0,0,1,1h2Z"/><path class="foregroundColor" d="M15,6.05a4.34,4.34,0,0,0-4.35,4.1c0,2.15,1.56,4.63,4.35,9,2.75-4.34,4.35-6.87,4.35-9A4.35,4.35,0,0,0,15,6.05Zm0,6.21a1.86,1.86,0,1,1,1.86-1.86A1.86,1.86,0,0,1,15,12.26Z"/><path class="foregroundColor" d="M15,3.76a8.5,8.5,0,1,1-8.5,8.5A8.51,8.51,0,0,1,15,3.76m0-1a9.5,9.5,0,1,0,9.5,9.5A9.5,9.5,0,0,0,15,2.76Z"/>
</symbol>
<symbol id="markers-drpdwm" viewBox="0 0 18 18">
<polygon class="foregroundColor" points="4.22 4 4.22 7.56 4.22 11.11 4.22 14 5.13 14 5.13 10.74 12.87 7.56 4.22 4"/><polygon class="foregroundColor" points="13.17 14.77 11.17 12.77 15.17 12.77 13.17 14.77"/>
</symbol>
<symbol id="measure-etb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.19,22.17a.5.5,0,0,1-.5-.5v-1a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v1A.5.5,0,0,1,24.19,22.17Z"/><path class="foregroundColor" d="M19.19,22.17a.5.5,0,0,1-.5-.5v-1a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v1A.5.5,0,0,1,19.19,22.17Z"/><path class="foregroundColor" d="M14.19,22.17a.5.5,0,0,1-.5-.5v-1a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v1A.5.5,0,0,1,14.19,22.17Z"/><path class="foregroundColor" d="M9.19,22.17a.5.5,0,0,1-.5-.5v-1a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v1A.5.5,0,0,1,9.19,22.17Z"/><path class="foregroundColor" d="M4.19,23.17a.5.5,0,0,1-.5-.5v-2a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v2A.5.5,0,0,1,4.19,23.17Z"/><path class="foregroundColor" d="M26.51,14.83l-3-3a.61.61,0,0,0-.88,0,.63.63,0,0,0,0,.88l1.93,1.93H4.32a.63.63,0,1,0,0,1.25H24.56l-1.93,1.94a.63.63,0,0,0,0,.88.63.63,0,0,0,.88,0l3-3A.61.61,0,0,0,26.51,14.83Z"/><path class="foregroundColor" d="M7.63,8.08a5,5,0,0,1-.16,1.34,2.86,2.86,0,0,1-.48,1,2,2,0,0,1-.77.62,2.25,2.25,0,0,1-1,.22A2.87,2.87,0,0,1,4,11V10a1.94,1.94,0,0,0,1.07.29,1.22,1.22,0,0,0,1-.44,2,2,0,0,0,.37-1.25h0a1.25,1.25,0,0,1-1.13.55A1.45,1.45,0,0,1,4.65,9a1.4,1.4,0,0,1-.51-.36,1.63,1.63,0,0,1-.33-.54,1.94,1.94,0,0,1-.12-.7,2.07,2.07,0,0,1,.15-.8A1.88,1.88,0,0,1,4.24,6a1.94,1.94,0,0,1,.63-.4,2.28,2.28,0,0,1,.8-.14,1.83,1.83,0,0,1,.82.18,1.68,1.68,0,0,1,.62.51A2.4,2.4,0,0,1,7.5,7,4.23,4.23,0,0,1,7.63,8.08ZM6.39,7.43A1.35,1.35,0,0,0,6.33,7a.86.86,0,0,0-.15-.32.64.64,0,0,0-.24-.22.62.62,0,0,0-.31-.08.59.59,0,0,0-.29.07.71.71,0,0,0-.23.19A.77.77,0,0,0,5,7a1.1,1.1,0,0,0-.06.37A1.23,1.23,0,0,0,5,7.72.63.63,0,0,0,5.11,8a.69.69,0,0,0,.24.18.78.78,0,0,0,.31.06A.73.73,0,0,0,6,8.18.66.66,0,0,0,6.19,8a.87.87,0,0,0,.15-.25A1.13,1.13,0,0,0,6.39,7.43Z"/><path class="foregroundColor" d="M10.26,11.25c-1.33,0-2-.94-2-2.81A3.83,3.83,0,0,1,8.8,6.22a1.79,1.79,0,0,1,1.57-.76q2,0,2,2.85a3.79,3.79,0,0,1-.53,2.18A1.76,1.76,0,0,1,10.26,11.25Zm.05-4.85c-.53,0-.8.67-.8,2s.27,1.9.79,1.9.76-.66.76-2S10.81,6.4,10.31,6.4Z"/><path class="foregroundColor" d="M14,7.53a1.36,1.36,0,0,1-.51-.09,1.09,1.09,0,0,1-.4-.25,1.16,1.16,0,0,1-.26-.39,1.35,1.35,0,0,1-.09-.5,1.51,1.51,0,0,1,.09-.53,1.25,1.25,0,0,1,.26-.41,1.15,1.15,0,0,1,.41-.26,1.35,1.35,0,0,1,.55-.1,1.44,1.44,0,0,1,.51.09,1.33,1.33,0,0,1,.39.26,1.24,1.24,0,0,1,.25.4,1.6,1.6,0,0,1,0,1,1.11,1.11,0,0,1-.26.4,1.25,1.25,0,0,1-.41.26A1.46,1.46,0,0,1,14,7.53Zm0-2.11a.73.73,0,0,0-.3.06.76.76,0,0,0-.23.17,1.15,1.15,0,0,0-.14.27,1.55,1.55,0,0,0,0,.7.72.72,0,0,0,.14.26.55.55,0,0,0,.22.18.65.65,0,0,0,.29.06.73.73,0,0,0,.3-.06.52.52,0,0,0,.22-.17.71.71,0,0,0,.13-.26,1.23,1.23,0,0,0,.05-.35,2,2,0,0,0,0-.37,1.13,1.13,0,0,0-.13-.27.62.62,0,0,0-.22-.17A.84.84,0,0,0,14.06,5.42Z"/>
</symbol>
<symbol id="metrics" viewBox="0 0 18 18">
<rect class="foregroundColor" x="3.13" y="9.49" width="2" height="3"/><rect class="foregroundColor" x="6.38" y="6.49" width="2" height="6"/><rect class="foregroundColor" x="9.62" y="8.49" width="2" height="4"/><rect class="foregroundColor" x="12.87" y="5.49" width="2" height="7"/>
</symbol>
<symbol id="mnvr-manager-emtb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.3,6c-1.11,0-2-1-2.7-1.8H11.4C10.66,5,9.81,6,8.7,6H6V25.8H24V6ZM15,5.1a.9.9,0,1,1-.9.9A.9.9,0,0,1,15,5.1Zm8.1,19.8H6.9V6.9h4.5l1.89,1.8H16.8l1.8-1.8h4.5Z"/><path class="foregroundColor" d="M20.22,12.67l1-2.38a.45.45,0,0,0,0-.28A.38.38,0,0,0,21,9.8l-2.38-1a.37.37,0,1,0-.29.69l1.55.64c-2.08.7-7.24,4.92-8.34,6.05l.67.39c1-1,6.07-5.13,8-5.75l-.64,1.55a.37.37,0,0,0,.2.49.32.32,0,0,0,.14,0A.35.35,0,0,0,20.22,12.67Z"/><path class="foregroundColor" d="M9.46,19.62a1.65,1.65,0,1,1,2.32-.17A1.65,1.65,0,0,1,9.46,19.62Zm1.66-1.94a.91.91,0,1,0-1.18,1.38.91.91,0,1,0,1.18-1.38Z"/><path class="foregroundColor" d="M13.06,12.1l-.93-2.4a.41.41,0,0,0-.2-.21.34.34,0,0,0-.28,0l-2.4.93A.37.37,0,0,0,9,10.9a.37.37,0,0,0,.48.21l1.57-.61a11.53,11.53,0,0,0-1.33,5.4l.75-.19a11,11,0,0,1,1.26-4.91l.61,1.57a.37.37,0,0,0,.48.21A.25.25,0,0,0,13,12.5.35.35,0,0,0,13.06,12.1Z"/><path class="foregroundColor" d="M17.72,18.91a1,1,0,0,0-1.34,0,1,1,0,0,0,0,1.35,1,1,0,0,0,1.34,0A1,1,0,0,0,17.72,18.91Z"/><path class="foregroundColor" d="M19.48,22.6l.59-.59c-.2-.41-.44-.78-.24-1.27s.61-.58,1.07-.74v-.83c-.41-.14-.86-.24-1.07-.74s0-.83.24-1.27l-.59-.59a1.46,1.46,0,0,1-1.28.24,1.48,1.48,0,0,1-.32-.21,2.09,2.09,0,0,1-.41-.86h-.84c-.14.43-.24.86-.73,1.07a1.43,1.43,0,0,1-1.27-.24l-.59.59a1.44,1.44,0,0,1,.23,1.27c-.2.49-.62.59-1.07.74V20c.54.18.88.29,1.07.74s0,.84-.23,1.27l.59.59a1.43,1.43,0,0,1,1.27-.23c.52.22.61.71.73,1.06h.84c.13-.38.23-.85.73-1.06A1.44,1.44,0,0,1,19.48,22.6ZM16,20.66a1.52,1.52,0,1,1,2.14,0A1.51,1.51,0,0,1,16,20.66Z"/>
</symbol>
<symbol id="move-down-layer" viewBox="0 0 16 16">
<path class="foregroundColor" d="M4.05,9.58h0l3.48,3.48a.68.68,0,0,0,.94,0L12,9.58a.67.67,0,0,0,.19-.47.65.65,0,0,0-.2-.47.67.67,0,0,0-.47-.19h0a.66.66,0,0,0-.46.19L8.67,11V3.42a.67.67,0,0,0-1.34,0V11L5,8.64a.67.67,0,0,0-1.13.47A.68.68,0,0,0,4.05,9.58Z"/>
</symbol>
<symbol id="move-up-layer" viewBox="0 0 16 16">
<path class="foregroundColor" d="M12,6.42h0L8.47,2.94a.68.68,0,0,0-.94,0L4.05,6.42a.67.67,0,0,0-.19.47.65.65,0,0,0,.2.47.67.67,0,0,0,.47.19h0A.66.66,0,0,0,5,7.36L7.33,5v7.56a.67.67,0,0,0,1.34,0V5L11,7.36a.67.67,0,0,0,1.13-.47A.68.68,0,0,0,12,6.42Z"/>
</symbol>
<symbol id="mp_mn-tlbr_prmnt_ic_edit-map" viewBox="0 0 30 30">
<path class="foregroundColor" d="M9.23,3.68A5.35,5.35,0,0,0,3.87,8.73c0,2.64,1.92,5.7,5.36,11.11,3.39-5.34,5.35-8.47,5.35-11.11A5.34,5.34,0,0,0,9.23,3.68Zm0,7.65A2.3,2.3,0,1,1,11.52,9,2.3,2.3,0,0,1,9.23,11.33Z"/><path class="foregroundColor" d="M19.13,26.32a7,7,0,1,1,7-7A7,7,0,0,1,19.13,26.32Zm0-13a6,6,0,1,0,6,6A6,6,0,0,0,19.13,13.32Z"/><rect class="foregroundColor" x="16.3" y="18" width="6.79" height="1.5" transform="translate(-7.49 19.42) rotate(-45)"/><polygon class="foregroundColor" points="17.28 22.23 15.63 22.82 16.22 21.17 17.28 22.23"/>
</symbol>
<symbol id="navigate-hand" viewBox="0 0 30 30">
<path class="foregroundColor" d="M16.17,24.63l-.9,0H12.78A1.6,1.6,0,0,1,11.51,24L5.9,17.53a1.41,1.41,0,0,1-.33-1.15,1.35,1.35,0,0,1,.7-.94,2.06,2.06,0,0,1,2.05,0A9.22,9.22,0,0,1,9.81,16.5l.18.16c.28.23.55.47.82.71a.31.31,0,0,0,.22-.06L10.88,17c-.14-.34-.27-.69-.42-1-.58-1.3-1.16-2.61-1.73-3.91l-.87-1.95A2.38,2.38,0,0,1,7.71,8.5,1.65,1.65,0,0,1,8.93,7.34a1.49,1.49,0,0,1,1.55.51,3.38,3.38,0,0,1,.46.7l.81,1.76c.25.54.49,1.07.74,1.6s.41.86.62,1.28l0-5.85A1.9,1.9,0,0,1,13.58,6a1.65,1.65,0,0,1,1.62-.53,1.62,1.62,0,0,1,1.2,1.16,4.17,4.17,0,0,1,.13,1c.07,1.41.13,2.75.19,4.08l.08,1.81,1.11-4.86a1.93,1.93,0,0,1,.71-1.19,1.77,1.77,0,0,1,1.71-.23,1.43,1.43,0,0,1,.84.93,2.69,2.69,0,0,1,.06,1.39c-.29,1.5-.59,3-.88,4.48l1-1.92a2.89,2.89,0,0,1,.52-.7A1.49,1.49,0,0,1,23.53,11h0a1.4,1.4,0,0,1,.84,1,2.22,2.22,0,0,1,0,1.3L20.78,23.8a1.12,1.12,0,0,1-1.15.82Zm-1.8-1.27h.9l1.52,0H19.6l3.55-10.5a.89.89,0,0,0,0-.56c0-.13-.07-.15-.08-.15h0a.26.26,0,0,0-.33,0,1.87,1.87,0,0,0-.3.41c-.5.94-1,1.89-1.46,2.83A1,1,0,0,1,20,16a1.7,1.7,0,0,1-.64-.19.94.94,0,0,1-.48-1.07Q19.46,12,20,9.23a1.39,1.39,0,0,0,0-.74c0-.08-.06-.16-.09-.17a.49.49,0,0,0-.52.06.73.73,0,0,0-.24.47L17.9,14.21a1.12,1.12,0,0,1-.8.88,1.17,1.17,0,0,1-1-.19,1.21,1.21,0,0,1-.51-.93l-.1-2.29c-.06-1.34-.12-2.67-.19-4a2.9,2.9,0,0,0-.08-.75.41.41,0,0,0-.28-.3.46.46,0,0,0-.42.16.68.68,0,0,0-.16.54l0,6.69a1.31,1.31,0,0,1,0,.32,1,1,0,0,1-.77.8,1,1,0,0,1-1.06-.41,2.26,2.26,0,0,1-.16-.26l-.27-.55c-.24-.49-.48-1-.71-1.48s-.5-1.07-.74-1.61L9.81,9.08a2.17,2.17,0,0,0-.29-.43.24.24,0,0,0-.28-.1c-.25.07-.29.18-.32.28A1.16,1.16,0,0,0,9,9.64l.87,1.95c.58,1.31,1.16,2.61,1.73,3.92.16.35.3.71.44,1.07l.14.35a1.12,1.12,0,0,1-.08,1,1.46,1.46,0,0,1-1,.67,1.32,1.32,0,0,1-1.1-.33c-.25-.24-.52-.46-.78-.68L9,17.45a7.13,7.13,0,0,0-1.27-.93.83.83,0,0,0-.86,0s-.07.06-.07.07,0,.06,0,.11l5.61,6.51a.37.37,0,0,0,.32.15h1.59Z"/>
</symbol>
<symbol id="navigate_hand" viewBox="0 0 30 30">
<path class="foregroundColor" d="M17.32,24.52H12.79A1.55,1.55,0,0,1,11.58,24L6,17.46a1.25,1.25,0,0,1-.31-1.06,1.19,1.19,0,0,1,.65-.87,1.94,1.94,0,0,1,1.95,0,9,9,0,0,1,1.47,1.08l.18.15c.28.24.56.47.82.72a.18.18,0,0,0,.15,0,.34.34,0,0,0,.22-.11L11,17c-.14-.35-.28-.7-.43-1C10,14.66,9.39,13.34,8.8,12L8,10.11a2.32,2.32,0,0,1-.14-1.59A1.51,1.51,0,0,1,9,7.44a1.38,1.38,0,0,1,1.44.47,3.08,3.08,0,0,1,.45.68c.27.6.55,1.19.82,1.78s.49,1.06.73,1.58.46,1,.7,1.44l.11.23,0-6.28A1.82,1.82,0,0,1,13.65,6a1.53,1.53,0,0,1,2.66.59,4.57,4.57,0,0,1,.12.94c.06,1.38.12,2.7.18,4l.11,2.35.08.07L18,8.59a1.89,1.89,0,0,1,.67-1.13,1.63,1.63,0,0,1,1.61-.22,1.31,1.31,0,0,1,.78.88,2.5,2.5,0,0,1,.06,1.34l-1,5.18c.44-.86.87-1.71,1.32-2.56a3,3,0,0,1,.5-.68,1.41,1.41,0,0,1,1.56-.32h0a1.31,1.31,0,0,1,.78.92,2,2,0,0,1,0,1.23L20.68,23.77a1,1,0,0,1-1.05.75Zm-.63-1.05h2.94L23.25,12.9a1,1,0,0,0,0-.62c-.05-.18-.11-.2-.15-.22h0a.36.36,0,0,0-.43.07,2.14,2.14,0,0,0-.32.44c-.49.94-1,1.88-1.46,2.83a.84.84,0,0,1-.85.49,1.73,1.73,0,0,1-.6-.17.85.85,0,0,1-.43-1l1.08-5.5a1.43,1.43,0,0,0,0-.8c-.07-.19-.13-.21-.16-.22a.58.58,0,0,0-.61.07.86.86,0,0,0-.28.53l-1.22,5.36a1,1,0,0,1-.73.8,1.08,1.08,0,0,1-.94-.17,1.12,1.12,0,0,1-.47-.86l-.1-2.35c-.06-1.31-.12-2.63-.19-3.94a3.71,3.71,0,0,0-.08-.77.48.48,0,0,0-.36-.36.49.49,0,0,0-.51.19.75.75,0,0,0-.19.6l0,6.69a1.13,1.13,0,0,1,0,.3.94.94,0,0,1-.69.72.9.9,0,0,1-1-.37,1.28,1.28,0,0,1-.15-.25l-.28-.57c-.23-.48-.47-1-.7-1.45s-.49-1.06-.73-1.59S10.17,9.63,9.9,9a2.08,2.08,0,0,0-.3-.45.32.32,0,0,0-.38-.13.46.46,0,0,0-.4.34,1.25,1.25,0,0,0,.09.88l.85,1.92c.59,1.32,1.17,2.63,1.75,4,.16.36.3.72.45,1.09l.13.33a1.06,1.06,0,0,1-.07.93,1.37,1.37,0,0,1-1,.62,1.24,1.24,0,0,1-1-.31l-.77-.68-.19-.15a7,7,0,0,0-1.29-.95.91.91,0,0,0-.94,0,.19.19,0,0,0-.06.33l5.61,6.51a.48.48,0,0,0,.41.19h3.9Zm-.1-9.73"/>
</symbol>
<symbol id="navigate_pointer" viewBox="0 0 30 30">
<path class="foregroundColor" d="M10.75,9.2l7.86,6.55H13.7l-2.95,3.84ZM9.25,6V24l5.19-6.75h8.31Z"/>
</symbol>
<symbol id="nexus-brand" viewBox="18.842 1.158 54.549 55.83">

  <defs>
    <linearGradient id="Gradient" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -25.802112, 73.301141)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-2" x1="41.99" y1="44.42" x2="85.92" y2="0.49" href="#Gradient" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -11.27658, 77.926557)"></linearGradient>
    <linearGradient id="Gradient-3" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#Gradient" gradientTransform="matrix(1, 0, 0, -1, -52.686444, 15.542414)"></linearGradient>
    <linearGradient id="Gradient-4" x1="32.81" y1="35.23" x2="76.74" y2="-8.7" href="#Gradient" gradientTransform="matrix(1, 0, 0, -1, -43.494416, 35.103535)"></linearGradient>
    <linearGradient id="Gradient-5" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#Gradient" gradientTransform="matrix(1, 0, 0, -1, -3.315534, 41.70135)"></linearGradient>
    <bx:export>
      <bx:file format="svg"></bx:file>
    </bx:export>
    <linearGradient id="Gradient-1" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#gradient-1" gradientTransform="matrix(1, 0, 0, -1, 41.473568, 36.834431)"></linearGradient>
    <linearGradient id="gradient-1" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-6" x1="41.99" y1="44.42" x2="85.92" y2="0.49" href="#gradient-2" gradientTransform="matrix(0.999999, 0, 0, -0.999999, 39.897229, 39.281343)"></linearGradient>
    <linearGradient id="gradient-2" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-3" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -3.786105, 62.767331)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-4" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, 42.98086, 37.497405)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-7" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#gradient-5" gradientTransform="matrix(1, 0, 0, -1, 38.339499, 28.119017)"></linearGradient>
    <linearGradient id="gradient-5" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-8" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#gradient-6" gradientTransform="matrix(1, 0, 0, -1, 0.224132, 44.118649)"></linearGradient>
    <linearGradient id="gradient-6" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-9" x1="32.81" y1="35.23" x2="76.74" y2="-8.7" href="#gradient-7" gradientTransform="matrix(1, 0, 0, -1, -4.024496, 30.589434)"></linearGradient>
    <linearGradient id="gradient-7" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-10" x1="32.81" y1="35.23" x2="76.74" y2="-8.7" href="#gradient-8" gradientTransform="matrix(1, 0, 0, -1, 29.699694, 20.316634)"></linearGradient>
    <linearGradient id="gradient-8" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-11" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#gradient-9" gradientTransform="matrix(1, 0, 0, -1, -7.995584, 19.149491)"></linearGradient>
    <linearGradient id="gradient-9" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-9-1" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#gradient-10" gradientTransform="matrix(1, 0, 0, -1, -1.708419, 15.235164)"></linearGradient>
    <linearGradient id="gradient-10" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-9-2" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#gradient-11" gradientTransform="matrix(1, 0, 0, -1, -0.015558, 17.978205)"></linearGradient>
    <linearGradient id="gradient-11" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
  </defs>
  <g transform="matrix(-1.03185, 0, 0, 1.03185, 87.343, -0.631567)" id="object-0" style="">
    <path d="M 26.941 42.405 C 26.941 42.405 25.418 39.191 24.726 39.446 C 24.726 39.446 24.007 39.805 23.508 40.044 L 22.371 36.928 C 26.371 34.498 23.809 35.281 28.469 33.101 L 34.005 40.754 C 32.35 40.619 30.285 40.853 26.941 42.405 Z" style="fill: url(#Gradient); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.938332, -0.345736, 0.345736, -0.938332, 0.000001, 0)"></path>
    <path d="M 27.216 14.945 C 27.216 14.945 26.51 12.278 25.948 12.383 C 25.948 12.383 20.964 12.235 16.205 12.552 L 15.293 7.656 C 17.391 7.374 19.914 7.054 21.774 6.853 C 27.014 6.283 28.518 6.54 31.636 7.535 L 35.591 16.619 C 35.591 16.619 31.72 14.125 27.216 14.945 Z" style="fill: url(#Gradient-3);" stroke="black" stroke-width="0.2" transform="matrix(-0.983083, -0.183162, 0.183162, -0.983083, 48.178831339119, 25.986510289072)"></path>
    <path d="M 26.875 23.443 C 26.875 23.443 25.908 20.102 25.168 20.29 C 25.168 20.29 23.135 20.628 18.368 21.671 L 17.538 17.863 C 19.488 17.351 18.462 17.609 19.13 17.447 C 24.613 16.114 24.907 16.134 30.535 15.715 L 34.677 23.476 C 29.464 22.307 26.875 23.443 26.875 23.443 Z" style="fill: url(#Gradient-4); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.975232, -0.221184, 0.221184, -0.975232, -0.000008, -0.000004)"></path>
    <path d="M 58.395 20.605 C 58.395 20.605 57.529 18.195 56.993 18.35 C 56.993 18.35 54.504 19.734 52.904 20.205 L 51.645 15.946 C 53.211 15.315 54.191 14.274 55.784 13.56 C 57.295 12.883 58.648 12.36 61.103 11.806 L 66.083 19.833 C 63.402 19.24 61.193 19.699 58.395 20.605 Z" style="fill: url(#Gradient-5);" stroke="black" stroke-width="0.2" transform="matrix(-0.960479, -0.278351, 0.278351, -0.960479, 78.97568593435, 60.586503518302)"></path>
    <path d="M 34.915 37.29 L 35.197 38.31 L 32.386 36.291 C 32.386 36.291 36.408 44.486 31.291 53.002 L 28.629 47.211 C 32.125 42.794 26.929 37.498 30.186 33.944 C 30.186 33.944 35.566 29.556 36.113 29.499 L 41.933 34.379 C 41.933 34.379 41.393 33.423 36.678 38.067 C 36.53 38.212 35.051 37.147 34.915 37.29 Z" style="fill: url(#Gradient-2); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.826347, -0.563162, 0.563162, -0.826347, 0.681368, -0.261844)"></path>
    <path d="M 64.617 28.351 L 64.958 29.066 C 57.884 33.298 55.525 37.706 55.525 37.706 C 55.525 37.706 57.458 33.417 64.617 28.351 Z" style="fill: url(#Gradient-1); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.966962, -0.254919, 0.254919, -0.966962, -0.477151, 0.062192)"></path>
    <path d="M 61.361 23.539 L 62.148 24.35 C 59.591 25.969 56.401 34.569 56.401 34.569 C 56.401 34.569 58.456 25.841 61.361 23.539 Z" style="fill: url(#Gradient-6); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.826347, -0.563162, 0.563162, -0.826347, -0.770894, 0.236268)"></path>
    <path d="M 44.955 26.818 L 44.729 30.504 C 39.881 32.977 43.373 37.47 37.514 41.102 L 37.262 38.5 C 43.205 34.457 40.393 29.532 44.955 26.818 Z" style="fill: url(#gradient-3);" stroke="black" stroke-width="0.2" transform="matrix(-0.965926, -0.258819, 0.258819, -0.965926, 72.440698285847, 77.289000184601)"></path>
    <path d="M 61.223 20.65 L 61.965 21.925 C 58.965 23.206 54.511 29.408 54.511 29.408 C 54.511 29.408 56.863 24.125 61.223 20.65 Z" style="fill: url(#gradient-4); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.938332, -0.345736, 0.345736, -0.938332, -0.991152, 0.177115)"></path>
    <path d="M 50.091 20.134 L 49.917 24.215 C 45.929 25.531 48.849 27.563 44.037 30.128 L 43.813 27.994 C 47.554 25.705 45.043 23.043 50.091 20.134 Z" style="fill: url(#Gradient-8);" stroke="black" stroke-width="0.2" transform="matrix(-0.960479, -0.278351, 0.278351, -0.960479, 81.763899875668, 63.198898810368)"></path>
    <path d="M 62.798 18.161 L 63.379 19.386 C 60.463 20.286 55.255 24.303 55.255 24.303 C 55.255 24.303 59.498 19.798 62.798 18.161 Z" style="fill: url(#Gradient-7); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.960479, -0.278351, 0.278351, -0.960479, -2.986787, 0.361965)"></path>
    <path d="M 48.71 16.933 L 48.4 19.612 C 46.08 20.111 45.701 21.267 43.218 22.213 L 43.161 21.058 C 45.879 19.871 46.463 18.065 48.71 16.933 Z" style="fill: url(#Gradient-9); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.975232, -0.221184, 0.221184, -0.975232, -0.586235, 0.064387)"></path>
    <path d="M 60.017 14.94 L 60.425 15.935 C 58.184 16.396 52.609 19.149 52.609 19.149 C 52.609 19.149 56.587 16.361 60.017 14.94 Z" style="fill: url(#Gradient-10);" stroke="black" stroke-width="0.2" transform="matrix(-0.975232, -0.221184, 0.221184, -0.975232, 106.521898022949, 46.322188386108)"></path>
    <path d="M 50.097 13.686 L 49.676 15.452 C 47.917 15.38 47.056 15.416 45.18 15.687 L 45.245 14.687 C 46.854 14.309 47.795 14.053 50.097 13.686 Z" style="fill: url(#Gradient-11); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.995616, -0.093531, 0.093531, -0.995616, -0.98221, 0.047008)"></path>
    <path d="M 56.674 9.655 L 56.374 10.844 C 54.007 11.547 45.439 11.828 45.439 11.828 L 52.192 10.525 L 52.423 11.035 L 52.56 10.478 C 52.56 10.478 55.859 9.817 56.674 9.655 Z" style="fill: url(#gradient-9-1);" stroke="black" stroke-width="0.2" transform="matrix(-0.995616, -0.093531, 0.093531, -0.995616, 101.032000041453, 26.294199519047)"></path>
    <path d="M 52.142 13.779 L 52.368 14.675 C 48.925 15.226 44.804 16.142 44.804 16.142 C 44.804 16.142 47.328 14.983 52.142 13.779 Z" style="fill: url(#gradient-9-2);" stroke="black" stroke-width="0.2" transform="matrix(-0.995616, -0.093531, 0.093531, -0.995616, 101.260121543514, 33.409877205059)"></path>
  </g>

</symbol>
<symbol id="node-protocol-icon" viewBox="0 0 20 20">
<path class="foregroundColor" d="M17.38,5.76a2.09,2.09,0,0,0-1.52.65L12,4.19a2.12,2.12,0,0,0,.11-.62,2.12,2.12,0,1,0-4.24,0A2.12,2.12,0,0,0,8,4.19L4.14,6.41a2.09,2.09,0,0,0-1.52-.65,2.11,2.11,0,0,0-.44,4.18v4.42a2.12,2.12,0,1,0,.95,0V9.92A2.11,2.11,0,0,0,4.7,8.18h.94a.86.86,0,0,1,.86.87v6.37h1V9.05A1.81,1.81,0,0,0,5.64,7.23h-1L8.46,5a2.13,2.13,0,0,0,3.08,0l3.84,2.21a2,2,0,0,0-.11.64,2.12,2.12,0,1,0,2.11-2.11ZM3.78,16.43a1.17,1.17,0,1,1-1.6-1.08,1.23,1.23,0,0,1,.44-.08,1.27,1.27,0,0,1,.51.12A1.18,1.18,0,0,1,3.78,16.43ZM10,4.73a1.1,1.1,0,0,1-.65-.22,1.16,1.16,0,0,1-.49-.82.53.53,0,0,1,0-.12,1.17,1.17,0,0,1,2.34,0,.53.53,0,0,1,0,.12,1.16,1.16,0,0,1-.49.82A1.1,1.1,0,0,1,10,4.73ZM17.38,9a1.17,1.17,0,0,1-1.16-1.17.5.5,0,0,1,0-.13,1.1,1.1,0,0,1,.49-.81,1.12,1.12,0,0,1,.64-.22,1.17,1.17,0,1,1,0,2.33Z"/><path class="foregroundColor" d="M8.62,10.5h4.27a.43.43,0,0,0,0-.85H8.62a.43.43,0,1,0,0,.85Z"/><path class="foregroundColor" d="M12.89,13.4H8.62a.43.43,0,0,0-.43.43.44.44,0,0,0,.43.43h4.27a.43.43,0,0,0,.42-.43A.42.42,0,0,0,12.89,13.4Z"/><path class="foregroundColor" d="M14.59,11.56h-6a.44.44,0,0,0-.43.43.43.43,0,0,0,.43.43h6A.43.43,0,0,0,15,12,.43.43,0,0,0,14.59,11.56Z"/>
</symbol>
<symbol id="node" viewBox="0 0 16 16">
<path class="foregroundColor" d="M12.22,10a1.73,1.73,0,0,0-1.25.53L6.11,6.23A2.2,2.2,0,0,0,6.59,5h3.88a1.78,1.78,0,1,0,0-.47H6.59a2.31,2.31,0,1,0-.8,2l4.9,4.29a1.77,1.77,0,1,0,3.31.88A1.78,1.78,0,0,0,12.22,10Z"/>
</symbol>
<symbol id="open-dialog" viewBox="0 0 12 12">
<polygon class="foregroundColor" points="4.04 9.9 2.69 9.92 9.94 2.7 9.91 4.06 10.79 4.09 10.88 1.14 7.92 1.22 7.96 2.1 9.31 2.08 2.06 9.3 2.09 7.94 1.21 7.91 1.12 10.86 4.08 10.78 4.04 9.9"/><polygon class="foregroundColor" points="6.29 1.88 6.29 1 1 1 1 6.27 1.88 6.27 1.88 1.88 6.29 1.88"/><polygon class="foregroundColor" points="11 5.73 10.12 5.73 10.12 10.12 5.71 10.12 5.71 11 11 11 11 5.73"/>
</symbol>
<symbol id="open-manager" viewBox="0 0 16 12">
<path class="foregroundColor" d="M.5.5v11h15V.5Zm1,6h6v4h-6Zm13,4h-6v-4h6Zm0-5H1.5v-4h13Z"/>
</symbol>
<symbol id="open-sidepanel-map-element" viewBox="0 0 24 24">
<path class="foregroundColor" d="M13.21,10.54H17.5a.56.56,0,0,0,0-1.11H13.21a.56.56,0,0,0,0,1.11Z"/><path class="foregroundColor" d="M13.21,7.73h1.43a.56.56,0,1,0,0-1.11H13.21a.56.56,0,0,0,0,1.11Z"/><path class="foregroundColor" d="M14.64,15.14H13.21a.56.56,0,0,0,0,1.11h1.43a.56.56,0,1,0,0-1.11Z"/><path class="foregroundColor" d="M19.79,12.29H13.21a.55.55,0,0,0-.55.55.55.55,0,0,0,.55.55h6.58a.55.55,0,0,0,.55-.55A.55.55,0,0,0,19.79,12.29Z"/><rect class="foregroundColor" x="9.5" y="4" width="1" height="16"/>
</symbol>
<symbol id="outgoing" viewBox="0 0 22 7">
<rect class="foregroundColor" y="4" width="2" height="3"/><rect class="foregroundColor" x="3.25" y="1" width="2" height="6"/><rect class="foregroundColor" x="6.49" y="3" width="2" height="4"/><rect class="foregroundColor" x="9.74" width="2" height="7"/><path class="foregroundColor" d="M14.91,4.57h-.65V2.43h.65Zm1.29,0V2.43h-.65V4.57Zm2.25-2.14H16.84V4.57h1.61V6.5L22,3.5,18.45.5Z"/>
</symbol>
<symbol id="outline-color-shp-opts" viewBox="0 0 20 20">
<path class="foregroundColor" d="M16.38,16.38H3.63V3.63H16.38Zm-12-.75H15.63V4.38H4.38Z"/><polygon class="foregroundColor" points="14.25 14.25 13 14.25 13 13.75 13.75 13.75 13.75 13 14.25 13 14.25 14.25"/><rect class="foregroundColor" x="9" y="13.75" width="2" height="0.5"/><polygon class="foregroundColor" points="7 14.25 5.75 14.25 5.75 13 6.25 13 6.25 13.75 7 13.75 7 14.25"/><rect class="foregroundColor" x="5.75" y="9" width="0.5" height="2"/><polygon class="foregroundColor" points="6.25 7 5.75 7 5.75 5.75 7 5.75 7 6.25 6.25 6.25 6.25 7"/><rect class="foregroundColor" x="9" y="5.75" width="2" height="0.5"/><polygon class="foregroundColor" points="14.25 7 13.75 7 13.75 6.25 13 6.25 13 5.75 14.25 5.75 14.25 7"/><rect class="foregroundColor" x="13.75" y="9" width="0.5" height="2"/>
</symbol>
<symbol id="outputs" viewBox="0 0 16 16">
<path class="foregroundColor" d="M10.4,6.2V3.8L15.2,8l-4.8,4.2V9.8H5.6V6.2Zm-1.2,6v0a4.8,4.8,0,1,1,0-8.3V2.5a6,6,0,1,0,0,11Z"/>
</symbol>
<symbol id="point" viewBox="0 0 30 30">
<rect class="foregroundColor" x="23.65" y="14.75" width="2" height="0.5"/><rect class="foregroundColor" x="4.35" y="14.75" width="2" height="0.5"/><polygon class="foregroundColor" points="20.3 8.99 15 14.29 9.7 8.99 8.99 9.7 14.29 15 8.99 20.3 9.7 21.01 15 15.71 20.3 21.01 21.01 20.3 15.71 15 21.01 9.7 20.3 8.99"/><rect class="foregroundColor" x="14.75" y="4.35" width="0.5" height="2"/><rect class="foregroundColor" x="14.75" y="23.65" width="0.5" height="2"/>
</symbol>
<symbol id="polygon" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.61,3.9H21.15V5.7H8.85V3.9H4.39V8.37H6.12v5.09H4.39v4.47h2l.44,3.7H5.34V26.1H9.81V24.36H12V26.1h4.46V21.63H14.88l-.39-4h1.26V15.74l4.17-.42v.5h0v1h4.46v-1h0V13.36h0v-1H19.92v1h0v1l-4.17.42V13.21H11.29v4.46h2.19l.39,4H12v1.73H9.81V21.63h-2l-.44-3.7H8.85V13.46H7.12V8.37H8.85V6.7h12.3V8.37h1.41l-.59,4h1l.58-4h2V3.9Zm-3.69,9.46h2.46v2.46H20.92V13.36Zm-7.53,3.31h-1.1V14.21h2.46v2.46H13.39Zm-.39,6h2.46V25.1H13V22.63Zm-5.07,0h.88V25.1H6.34V22.63H7.93Zm-.08-8.17v2.47H5.39V14.46H7.85Zm0-7.09H5.39V4.9H7.85V7.37Zm16.76,0H22.15V4.9h2.46Z"/>
</symbol>
<symbol id="polyline" viewBox="0 0 30 30">
<path class="foregroundColor" d="M24.14,4.06H20.67V5.67H9.33V3.94H4.86V8.4H6.64v5H4.91v4.46h2l.43,3.71H5.86v4.46h4.47V21.6h-2L7.9,17.89H9.37V13.43H7.64v-5H9.33V6.67H20.67V8.52h1.38l-.55,3.81H19.44v2l-2.92.34V13.18H12.06v4.46h4.46v-2l2.92-.34v1.47H23.9V12.33H22.51l.55-3.81h2.08V4.06ZM8.45,22.6h.88v2.46H6.86V22.6H8.45Zm-.08-8.17v2.46H5.91V14.43H8.37Zm0-7H5.86V4.94H8.33V7.4Zm7.19,9.24H13.06V14.18h2.46v2.46Zm7.38-3.31v2.46H20.44V13.33H22.9Zm1.24-5.81H21.67V5.06h2.47Z"/>
</symbol>
<symbol id="print" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23,15.11H21V10.44l-4-4H9v8.67H7a2.5,2.5,0,0,0-2.5,2.5v6.45h21V17.61A2.5,2.5,0,0,0,23,15.11ZM17,7.85h0l2.59,2.59H17Zm-7,8.26V7.44h6v4h4v8H10Zm14.5,7H5.5V17.61A1.5,1.5,0,0,1,7,16.11H9v4.33H21V16.11h2a1.5,1.5,0,0,1,1.5,1.5Z"/>
</symbol>
<symbol id="radar-additional-act" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23.15,11.2,23,10.88l-.1-.22h0A9,9,0,0,0,15,6a8.87,8.87,0,0,0-3.78.84,9,9,0,0,0-4.35,12,9,9,0,0,0,12,4.35A9,9,0,0,0,23.15,11.2ZM18.31,22.09a7.85,7.85,0,0,1-6,.26,7.82,7.82,0,0,1-.64-14.44A7.7,7.7,0,0,1,15,7.18a7.93,7.93,0,0,1,2.55.42,7.78,7.78,0,0,1,4.28,3.57l-2.29,1.07L16.9,13.46l-1.07.5.62-1.71.08-.21a.6.6,0,0,0-.35-.76l-.2,0a.59.59,0,0,0-.56.39l0,.08h-.71a2.76,2.76,0,0,0-.4.06h0l-.11,0A3.24,3.24,0,0,0,12,16.4a3.39,3.39,0,0,0,.3.51.58.58,0,0,0,.1.12,2.15,2.15,0,0,0,.31.35,1.11,1.11,0,0,0,.11.11,3.45,3.45,0,0,0,.45.33.2.2,0,0,0,.08,0,2.81,2.81,0,0,0,.43.21l.18.07a3.06,3.06,0,0,0,.41.1h.05l.1,0a2.74,2.74,0,0,0,.48,0h.06a3.31,3.31,0,0,0,.48-.06h.06l.13,0A3.52,3.52,0,0,0,16.4,18a3.28,3.28,0,0,0,1.71-1.87,3.39,3.39,0,0,0,.16-.59s0-.09,0-.14v-.06c0-.13,0-.27,0-.4s0-.13,0-.19,0-.29-.06-.42,0-.12,0-.17l1.56-.73v0a2.58,2.58,0,0,1,.08.26c0,.07,0,.14,0,.2s0,.18.05.26l0,.21a2.28,2.28,0,0,1,0,.26c0,.07,0,.14,0,.21a2.44,2.44,0,0,1,0,.27v.19l0,.29v.06a.5.5,0,0,1,0,.12,2.84,2.84,0,0,1-.06.31l0,.07s0,.06,0,.08-.09.31-.14.46A5,5,0,0,1,15,20a4.88,4.88,0,0,1-1.72-.31,5,5,0,0,1,3.29-9.47.63.63,0,0,0,.19,0,.59.59,0,0,0,.56-.4.59.59,0,0,0,0-.45.64.64,0,0,0-.35-.3,6.19,6.19,0,0,0-4.06,11.7c.19.07.37.12.56.17l.13,0h.06l.37.08.17,0h.06l.35,0h.51l.41,0,.27,0,.37-.06h.07l.19,0,.4-.11.07,0,.15,0a5.36,5.36,0,0,0,.6-.24,6.17,6.17,0,0,0,3.2-3.49q.11-.29.18-.57l0-.13v-.06c0-.13.06-.26.08-.39s0-.1,0-.15v-.08c0-.13,0-.24,0-.35a1.09,1.09,0,0,0,0-.18v-.4c0-.07,0-.14,0-.21s0-.26,0-.37a2.17,2.17,0,0,1,0-.25c0-.11,0-.22-.07-.33s0-.16-.06-.25l-.09-.31-.06-.16,1.49-.7A7.81,7.81,0,0,1,18.31,22.09ZM15.9,16.93a2,2,0,0,1-.9.2,2.13,2.13,0,0,1-.9-4.07,1.88,1.88,0,0,1,.4-.13h.13l.29,0H15l-.35,1-.11,0-.09,0L14.3,14l-.09.08-.08.07-.07.09a.58.58,0,0,0-.08.1l-.05.08-.06.13a.64.64,0,0,1,0,.07c0,.07,0,.11,0,.16a.28.28,0,0,0,0,.09h0v.17a.54.54,0,0,1,0,.08.9.9,0,0,0,0,.13l0,.1,0,.13,0,.09a1.15,1.15,0,0,0,.07.11l.09.11.06.07.11.09.08.06.12.06.08,0,.14,0,.09,0H15l.18,0h.08l.1,0,.13,0h0l.06,0a1.74,1.74,0,0,0,.22-.15l.05,0a1.55,1.55,0,0,0,.19-.24h0l0,0a1.24,1.24,0,0,0,.11-.28c0-.09,0-.16,0-.22l.89-.41a.88.88,0,0,1,0,.16.66.66,0,0,1,0,.14,2.26,2.26,0,0,1,0,.26h0v0a.15.15,0,0,1,0,.07,1.93,1.93,0,0,1-.09.38A2.14,2.14,0,0,1,15.9,16.93Z"/>
</symbol>
<symbol id="redo-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M6.14,17.31h0a11.21,11.21,0,0,1,5-4.28,10.19,10.19,0,0,1,10.66,1.63l-1-2.88a1,1,0,0,1,.48-1.19.8.8,0,0,1,1.07.52l1.62,4.63a1,1,0,0,1-.47,1.19l-4.16,1.78a.81.81,0,0,1-1.08-.52A1,1,0,0,1,18.71,17l2.05-.89a8.69,8.69,0,0,0-9-1.37A9.55,9.55,0,0,0,7.48,18.4h0a.78.78,0,0,1-1.13.11A.91.91,0,0,1,6.14,17.31Z"/>
</symbol>
<symbol id="reload" viewBox="0 0 20 20">
<path class="foregroundColor" d="M10,3.38A6.59,6.59,0,0,0,4.6,6.16V4.93a.61.61,0,0,0-1.22,0V7.56A.61.61,0,0,0,4,8.18H6.61A.62.62,0,0,0,6.61,7H5.55A5.35,5.35,0,0,1,10,4.6a5.4,5.4,0,1,1-5.38,5.84A.63.63,0,0,0,4,9.87a.58.58,0,0,0-.44.19.62.62,0,0,0-.17.48A6.62,6.62,0,1,0,10,3.38Z"/>
</symbol>
<symbol id="reset-diagnostics-2" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,6a9,9,0,0,0-7.65,4.25l-.21.34V7.89A.57.57,0,0,0,6,7.89v3.69a.57.57,0,0,0,.57.57h3.68a.57.57,0,0,0,.57-.57.58.58,0,0,0-.57-.57h-2l.1-.18A7.88,7.88,0,1,1,7.22,16h3.1l1.85.89,1.08-2.74,2.05,5.33,1.86-6.29,1,3.47h3.66a.46.46,0,1,0,0-.91h-3L17.18,10l-2,6.72-1.93-5-1.58,4-1.06-.51-4,0H6.56l-.18,0a.59.59,0,0,0-.19.13.66.66,0,0,0-.16.45A9,9,0,1,0,15,6Z"/>
</symbol>
<symbol id="reset-diagnostics" viewBox="0 0 30 30">
<path class="foregroundColor" d="M18.77,14.86a4.6,4.6,0,0,0-3.7,1.85V16a.47.47,0,0,0-.93,0V17.8a.46.46,0,0,0,.46.46h1.82a.46.46,0,0,0,.46-.46.47.47,0,0,0-.46-.46h-.66a3.65,3.65,0,0,1,3-1.55,3.7,3.7,0,1,1-3.69,4,.47.47,0,0,0-.46-.43.46.46,0,0,0-.34.14.5.5,0,0,0-.12.36,4.62,4.62,0,1,0,4.61-5Z"/><path class="foregroundColor" d="M8.71,12.16h5.18a.55.55,0,0,0,0-1.1H8.71a.55.55,0,1,0,0,1.1Z"/><path class="foregroundColor" d="M8.71,13.64a.55.55,0,1,0,0,1.1h3a9.43,9.43,0,0,1,.88-1.1Z"/><path class="foregroundColor" d="M8.71,16.29a.55.55,0,0,0,0,1.1h1.83a7.84,7.84,0,0,1,.35-1.1Z"/><path class="foregroundColor" d="M10.29,18.94H8.71a.55.55,0,0,0-.55.55.55.55,0,0,0,.55.54h1.58c0-.18,0-.36,0-.54S10.28,19.12,10.29,18.94Z"/><path class="foregroundColor" d="M6,24.71v-18h4.5l1.89,1.8h3.51l1.8-1.8h4.5v5a9.59,9.59,0,0,1,.9.47V5.81h-2.7c-1.11,0-2-1-2.7-1.8h-7.2c-.74.83-1.6,1.8-2.7,1.8H5.09v19.8h7.8a8.76,8.76,0,0,1-.82-.9Zm8.1-19.8a.9.9,0,1,1,0,1.8.9.9,0,0,1,0-1.8Z"/><path class="foregroundColor" d="M18.76,13a6.5,6.5,0,1,1-6.5,6.5,6.5,6.5,0,0,1,6.5-6.5m0-1a7.5,7.5,0,1,0,7.5,7.5,7.5,7.5,0,0,0-7.5-7.5Z"/>
</symbol>
<symbol id="reset-opacity-tr-tb" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="7.65 13.49 1.87 10.13 7.65 6.77 13.43 10.13 7.65 13.49"/><polygon class="foregroundColor" points="12.6 12.03 8.18 14.6 7.65 14.9 7.13 14.6 2.69 12.02 1.86 12.51 5.45 14.6 7.65 15.88 9.85 14.6 13.44 12.51 12.6 12.03"/><polygon class="foregroundColor" points="12.6 14.5 8.18 17.07 7.65 17.38 7.13 17.07 2.69 14.49 1.86 14.98 5.45 17.07 7.65 18.35 9.85 17.07 13.44 14.98 12.6 14.5"/><path class="foregroundColor" d="M17.75,4.6V8.9h-.92V5.65l-.18.12-.22.11L16.18,6a1,1,0,0,1-.25,0V5.23A3.71,3.71,0,0,0,16.61,5a3.59,3.59,0,0,0,.58-.36Z"/><path class="foregroundColor" d="M20.47,9c-1,0-1.5-.7-1.5-2.11a2.87,2.87,0,0,1,.4-1.66,1.35,1.35,0,0,1,1.18-.57c1,0,1.46.71,1.46,2.14a2.78,2.78,0,0,1-.4,1.63A1.31,1.31,0,0,1,20.47,9Zm0-3.64c-.4,0-.6.5-.6,1.51s.19,1.42.58,1.42.58-.49.58-1.46S20.88,5.33,20.51,5.33Z"/><path class="foregroundColor" d="M23.92,9c-1,0-1.5-.7-1.5-2.11a2.87,2.87,0,0,1,.4-1.66A1.36,1.36,0,0,1,24,4.63c1,0,1.46.71,1.46,2.14a2.87,2.87,0,0,1-.39,1.63A1.34,1.34,0,0,1,23.92,9Zm0-3.64c-.4,0-.6.5-.6,1.51s.19,1.42.59,1.42.57-.49.57-1.46S24.33,5.33,24,5.33Z"/><path class="foregroundColor" d="M20.64,25.4a7.5,7.5,0,1,1,7.5-7.5A7.52,7.52,0,0,1,20.64,25.4Zm0-14a6.5,6.5,0,1,0,6.5,6.5A6.51,6.51,0,0,0,20.64,11.4Z"/><path class="foregroundColor" d="M20.47,14.4c-3.06,0-5,2.67-5.42,3.29.42.66,2.51,3.71,5.42,3.71,3.15,0,5.06-3,5.42-3.71C25.51,17.1,23.59,14.4,20.47,14.4Zm0,5.5a2,2,0,1,1,2-2A2,2,0,0,1,20.46,19.9Z"/>
</symbol>
<symbol id="reset-start-over" viewBox="0 0 18 18">
<path class="foregroundColor" d="M8.85,10.25A.25.25,0,0,1,8.6,10V6.64a.25.25,0,0,1,.25-.25.25.25,0,0,1,.25.25V10A.25.25,0,0,1,8.85,10.25Z"/><rect class="foregroundColor" x="7.85" y="3.25" width="2" height="1"/><rect class="foregroundColor" x="12.93" y="5.16" width="1" height="1" transform="translate(-0.07 11.16) rotate(-45)"/><path class="foregroundColor" d="M8.85,4.88A5.11,5.11,0,0,0,4.72,7V6.1a.5.5,0,0,0-.5-.5.51.51,0,0,0-.5.5v2a.5.5,0,0,0,.5.5h2a.5.5,0,0,0,0-1H5.48A4.08,4.08,0,0,1,8.85,5.88a4.13,4.13,0,0,1,0,8.25,4,4,0,0,1-1.51-.29l-.5.86a5,5,0,0,0,2,.43,5.13,5.13,0,0,0,0-10.25Z"/><path class="foregroundColor" d="M4.74,10.34a.51.51,0,0,0-.5-.46.5.5,0,0,0-.37.15.58.58,0,0,0-.13.39,4.75,4.75,0,0,0,.33,1.36L5,11.32A3.58,3.58,0,0,1,4.74,10.34Z"/><path class="foregroundColor" d="M5.53,12.43l-.89.46a5,5,0,0,0,1.13,1.17l.5-.86A4.15,4.15,0,0,1,5.53,12.43Z"/>
</symbol>
<symbol id="reset" viewBox="0 0 18 18">
<path class="foregroundColor" d="M9,4.09a5,5,0,0,0-4,2V5.29a.49.49,0,0,0-1,0v2a.49.49,0,0,0,.49.49h2a.49.49,0,0,0,0-1H5.72A4,4,0,1,1,5,9.41.49.49,0,0,0,4.51,9a.48.48,0,0,0-.36.15A.53.53,0,0,0,4,9.49a5,5,0,1,0,5-5.4Z"/>
</symbol>
<symbol id="resize-map-element" viewBox="0 0 24 24">
<polygon class="foregroundColor" points="17.38 5.94 11.47 11.85 12.13 12.51 18.04 6.6 17.99 8.03 18.92 8.07 19.04 4.94 15.91 5.05 15.94 5.99 17.38 5.94"/><polygon class="foregroundColor" points="18.31 10.01 18.31 18.32 10.33 18.32 10.33 13.46 5.68 13.46 5.68 5.68 13.97 5.68 13.97 4.75 4.75 4.75 4.75 19.25 19.25 19.25 19.25 10.01 18.31 10.01"/>
</symbol>
<symbol id="route-emtb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21.3,6c-1.11,0-2-1-2.7-1.8H11.4C10.66,5,9.81,6,8.7,6H6V25.8H24V6ZM15,5.1a.9.9,0,1,1-.9.9A.9.9,0,0,1,15,5.1Zm8.1,19.8H6.9V6.9h4.5l1.89,1.8H16.8l1.8-1.8h4.5Z"/><path class="foregroundColor" d="M10.53,17.9a2,2,0,1,1,2-2A2,2,0,0,1,10.53,17.9Zm0-3.1a1.1,1.1,0,1,0,1.1,1.1A1.1,1.1,0,0,0,10.53,14.8Z"/><path class="foregroundColor" d="M20,14.14a2,2,0,1,1,2-2A2,2,0,0,1,20,14.14ZM20,11a1.1,1.1,0,1,0,1.1,1.1A1.1,1.1,0,0,0,20,11Z"/><path class="foregroundColor" d="M15,17.1a11.06,11.06,0,0,1-1.86-.18l.18-1c4.08.72,4.85-1.22,4.88-1.31l.94.33C19.06,15.06,18.33,17.1,15,17.1Z"/><path class="foregroundColor" d="M15.81,23c-2.86-.52-4.75-1.43-5.61-2.7a2.21,2.21,0,0,1-.44-1.63l1,.19S10.49,21,16,22Z"/>
</symbol>
<symbol id="route-spread-both" viewBox="0 0 20 15">
<path class="foregroundColor" d="M2,5.05a2,2,0,0,0,1.06.48,1.89,1.89,0,0,0,.41,0,.86.86,0,0,0,.23,0,1,1,0,0,0,.24,0,1.73,1.73,0,0,0,.39-.18l0,0A2.26,2.26,0,0,0,4.71,5a1.9,1.9,0,0,0,.38-.46,2,2,0,0,0,.2-.5l4.24.54a2,2,0,0,0,.2.91,1.92,1.92,0,0,0,.47.6,1.89,1.89,0,0,0,1.06.48,1.94,1.94,0,0,0,1.15-.19A2.16,2.16,0,0,0,13,5.92a2,2,0,0,0,.42-.82l1.32.17a2,2,0,0,0,.2.9,2.16,2.16,0,0,0,.47.61,2.07,2.07,0,0,0,1.06.48,1.89,1.89,0,0,0,.41,0l.23,0a2,2,0,0,0,.24,0A2.26,2.26,0,0,0,17.76,7l0,0a2.26,2.26,0,0,0,.31-.23,2.12,2.12,0,0,0,.38-.46,1.9,1.9,0,0,0,.25-.75,1.9,1.9,0,0,0-.05-.79,2.42,2.42,0,0,0-.25-.54,1.76,1.76,0,0,0-.24-.3,2,2,0,0,0-.36-.3.91.91,0,0,0-.22-.11,1.39,1.39,0,0,0-.21-.11A2.74,2.74,0,0,0,17,3.29a2,2,0,0,0-1.16.2,1.92,1.92,0,0,0-.6.46,2,2,0,0,0-.43.82L13.51,4.6a2,2,0,0,0-.21-.9,1.22,1.22,0,0,0-.12-.19,2.37,2.37,0,0,0-.23-.3s-.07-.08-.11-.11a2,2,0,0,0-2.22-.29,1.84,1.84,0,0,0-.6.47,1.9,1.9,0,0,0-.43.82L5.35,3.55A1.9,1.9,0,0,0,5.29,3,2.65,2.65,0,0,0,5,2.47a1.76,1.76,0,0,0-.24-.3,2,2,0,0,0-.36-.3,2.12,2.12,0,0,0-.21-.11c-.08,0-.14-.08-.22-.11a2.46,2.46,0,0,0-.39-.09,2,2,0,0,0-1.16.2,1.92,1.92,0,0,0-.6.46,2,2,0,0,0-.48,1.07,1.94,1.94,0,0,0,.19,1.15A2.16,2.16,0,0,0,2,5.05ZM16.75,3.77a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16.75,3.77ZM11.52,3.1A1.5,1.5,0,1,1,10,4.6,1.5,1.5,0,0,1,11.52,3.1ZM3.36,2.05a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,3.36,2.05Z"/><path class="foregroundColor" d="M18.48,8.72a2.12,2.12,0,0,0-.38-.46A2.26,2.26,0,0,0,17.79,8a1.36,1.36,0,0,0-.21-.1h0a2,2,0,0,0-.21-.1,2,2,0,0,0-.24,0l-.19,0h0a1.89,1.89,0,0,0-.41,0,2.07,2.07,0,0,0-1.06.48,2.16,2.16,0,0,0-.47.61,2,2,0,0,0-.2.9l-1.32.17A2,2,0,0,0,13,9.08a1.29,1.29,0,0,0-.17-.16,1.66,1.66,0,0,0-.3-.22l-.14-.09a1.94,1.94,0,0,0-1.15-.19,1.89,1.89,0,0,0-1.06.48,1.82,1.82,0,0,0-.47.6,2,2,0,0,0-.2.9L5.29,11a2.25,2.25,0,0,0-.2-.51A2.06,2.06,0,0,0,4.71,10a1.66,1.66,0,0,0-.31-.23A1.65,1.65,0,0,0,4,9.55L3.74,9.5h0a2,2,0,0,0-.24,0,1.89,1.89,0,0,0-.41,0A1.92,1.92,0,0,0,2,10a2.12,2.12,0,0,0-.47.6,2,2,0,0,0-.19,1.16,1.92,1.92,0,0,0,.48,1.06,2.12,2.12,0,0,0,.6.47,2,2,0,0,0,1.16.19A1.38,1.38,0,0,0,4,13.34a2.14,2.14,0,0,0,.22-.1l.21-.11a1.86,1.86,0,0,0,.31-.26l0,0a3.15,3.15,0,0,0,.24-.3A2.65,2.65,0,0,0,5.29,12a2,2,0,0,0,.06-.54l4.24-.55a2,2,0,0,0,.42.82,2.16,2.16,0,0,0,.61.47,2,2,0,0,0,2.22-.29,1.92,1.92,0,0,0,.46-.6,2,2,0,0,0,.21-.9l1.31-.17a2,2,0,0,0,.43.82,1.92,1.92,0,0,0,.6.46,2,2,0,0,0,1.16.2l.39-.09a1.39,1.39,0,0,0,.21-.11l.22-.1a2.67,2.67,0,0,0,.36-.31,1.76,1.76,0,0,0,.24-.3,2.42,2.42,0,0,0,.25-.54,2.07,2.07,0,0,0-.2-1.54ZM3.36,13a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,3.36,13Zm8.16-1A1.5,1.5,0,1,1,13,10.4,1.5,1.5,0,0,1,11.52,11.9Zm5.23-.67a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16.75,11.23Z"/><circle class="innerColor" cx="16.75" cy="9.73" r="1.5"/><circle class="innerColor" cx="11.52" cy="10.4" r="1.5"/><circle class="innerColor" cx="3.36" cy="11.45" r="1.5"/><circle class="innerColor" cx="16.75" cy="5.27" r="1.5"/><circle class="innerColor" cx="11.52" cy="4.6" r="1.5"/><circle class="innerColor" cx="3.36" cy="3.55" r="1.5"/>
</symbol>
<symbol id="route-spread-bottom" viewBox="0 0 20 15">
<path class="foregroundcolor" d="M18.59,10.67a2,2,0,0,0-.32-.5A1.66,1.66,0,0,0,18,9.91a2,2,0,0,0-.39-.26c-.08,0-.16-.05-.23-.08l-.23-.08a2.58,2.58,0,0,0-.4,0,2.05,2.05,0,0,0-1.12.34,2.31,2.31,0,0,0-.54.54,2.07,2.07,0,0,0-.31.87H13.45a2.08,2.08,0,0,0-.32-.87,2.13,2.13,0,0,0-.54-.54,2,2,0,0,0-1.12-.34,2,2,0,0,0-1.11.34,2.17,2.17,0,0,0-.55.54,2.07,2.07,0,0,0-.31.87H5.22a2,2,0,0,0-.13-.53,2,2,0,0,0-.32-.5,1.66,1.66,0,0,0-.27-.26,2,2,0,0,0-.39-.26C4,9.61,4,9.6,3.88,9.57l-.23-.08a2.58,2.58,0,0,0-.4,0,2.05,2.05,0,0,0-1.12.34,2.31,2.31,0,0,0-.54.54,2.05,2.05,0,0,0-.34,1.12,2,2,0,0,0,.34,1.12,2.13,2.13,0,0,0,.54.54,2,2,0,0,0,1.12.34,1.75,1.75,0,0,0,.4,0,1,1,0,0,0,.23-.08h0l.23-.07A2,2,0,0,0,4.5,13a1.72,1.72,0,0,0,.27-.27,1.86,1.86,0,0,0,.32-.5,1.79,1.79,0,0,0,.13-.53H9.5a2,2,0,0,0,.31.87,2,2,0,0,0,.55.54,1.92,1.92,0,0,0,1.11.34,2,2,0,0,0,1.12-.34,1.27,1.27,0,0,0,.12-.1,1.66,1.66,0,0,0,.27-.26,1.3,1.3,0,0,0,.15-.18,2,2,0,0,0,.32-.87h1.33a2,2,0,0,0,.31.87,2.13,2.13,0,0,0,.54.54,2,2,0,0,0,1.12.34,1.75,1.75,0,0,0,.4,0,1,1,0,0,0,.23-.08l.23-.07A2,2,0,0,0,18,13a1.72,1.72,0,0,0,.27-.27,1.86,1.86,0,0,0,.32-.5,2,2,0,0,0,.16-.78A2.05,2.05,0,0,0,18.59,10.67ZM3.25,13a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,3.25,13Zm8.22,0a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,11.47,13Zm5.28,0a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16.75,13Z"/><path class="foregroundcolor" d="M3.25,5.55A2,2,0,0,0,5.11,4.28L9.5,5.4a2.48,2.48,0,0,0,0,.27,2,2,0,0,0,3.87.72l1.44.36a1.13,1.13,0,0,0,0,.25,2,2,0,1,0,.14-.73L13.45,5.9a1.79,1.79,0,0,0,0-.23,2,2,0,0,0-3.85-.75L5.23,3.79a1.94,1.94,0,0,0,0-.24,2,2,0,1,0-2,2Zm13.5,0A1.5,1.5,0,1,1,15.25,7,1.5,1.5,0,0,1,16.75,5.5ZM11.47,4.17A1.5,1.5,0,1,1,10,5.67,1.5,1.5,0,0,1,11.47,4.17ZM3.25,2.05a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,3.25,2.05Z"/><circle class="innercolor" cx="16.75" cy="7" r="1.5"/><circle class="innercolor" cx="11.47" cy="5.67" r="1.5"/><circle class="innercolor" cx="3.25" cy="3.55" r="1.5"/><circle class="innercolor" cx="16.75" cy="11.45" r="1.5"/><circle class="innercolor" cx="11.47" cy="11.45" r="1.5"/><circle class="innercolor" cx="3.25" cy="11.45" r="1.5"/>
</symbol>
<symbol id="route-spread-gap" viewBox="0 0 20 15">
<circle class="innercolor" cx="11.47" cy="3.55" r="1.5"/><path class="foregroundcolor" d="M18.59,10.67a2,2,0,0,0-.32-.5A2.23,2.23,0,0,0,18,9.91a2,2,0,0,0-.85-.42,2.58,2.58,0,0,0-.4,0,2.05,2.05,0,0,0-1.12.34,2.31,2.31,0,0,0-.54.54,2.07,2.07,0,0,0-.31.87H13.45a2.08,2.08,0,0,0-.32-.87,2.13,2.13,0,0,0-.54-.54,2,2,0,0,0-1.12-.34,2,2,0,0,0-1.11.34,2.17,2.17,0,0,0-.55.54,2.07,2.07,0,0,0-.31.87H5.22a2,2,0,0,0-.13-.53,2,2,0,0,0-.32-.5,2.23,2.23,0,0,0-.27-.26,2,2,0,0,0-.85-.42,2.58,2.58,0,0,0-.4,0,2.05,2.05,0,0,0-1.12.34,2.31,2.31,0,0,0-.54.54,2.05,2.05,0,0,0-.34,1.12,2,2,0,0,0,.34,1.12,2.13,2.13,0,0,0,.54.54,2,2,0,0,0,1.12.34,1.75,1.75,0,0,0,.4,0A1.88,1.88,0,0,0,4.5,13a1.72,1.72,0,0,0,.27-.27,1.86,1.86,0,0,0,.32-.5,1.79,1.79,0,0,0,.13-.53H9.5a2,2,0,0,0,.31.87,2,2,0,0,0,.55.54,1.92,1.92,0,0,0,1.11.34,2,2,0,0,0,1.12-.34,2,2,0,0,0,.86-1.41h1.33a2,2,0,0,0,.31.87,2.13,2.13,0,0,0,.54.54,2,2,0,0,0,1.12.34,1.75,1.75,0,0,0,.4,0A1.88,1.88,0,0,0,18,13a1.72,1.72,0,0,0,.27-.27,1.86,1.86,0,0,0,.32-.5,2,2,0,0,0,.16-.78A2.05,2.05,0,0,0,18.59,10.67ZM3.25,13a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,3.25,13Zm8.22,0a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,11.47,13Zm5.28,0a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16.75,13Z"/><circle class="innercolor" cx="11.47" cy="11.45" r="1.5"/><circle class="innercolor" cx="3.25" cy="11.45" r="1.5"/><circle class="innercolor" cx="16.75" cy="11.45" r="1.5"/><path class="foregroundcolor" d="M18.59,2.77a1.86,1.86,0,0,0-.32-.5A1.72,1.72,0,0,0,18,2a1.88,1.88,0,0,0-.85-.41,1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.31.87H13.45a2,2,0,0,0-.86-1.41,2,2,0,0,0-1.12-.34,1.92,1.92,0,0,0-1.11.34,2,2,0,0,0-.55.54,2,2,0,0,0-.31.87H5.22a1.79,1.79,0,0,0-.13-.53,1.86,1.86,0,0,0-.32-.5A1.72,1.72,0,0,0,4.5,2a1.88,1.88,0,0,0-.85-.41,1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.34,1.12,2.05,2.05,0,0,0,.34,1.12,2.31,2.31,0,0,0,.54.54,2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0,2,2,0,0,0,.85-.42,2.23,2.23,0,0,0,.27-.26,2,2,0,0,0,.32-.5,2,2,0,0,0,.13-.53H9.5a2.07,2.07,0,0,0,.31.87,2.17,2.17,0,0,0,.55.54,2,2,0,0,0,1.11.34,2,2,0,0,0,1.12-.34,2.13,2.13,0,0,0,.54-.54,2.08,2.08,0,0,0,.32-.87h1.33a2.07,2.07,0,0,0,.31.87,2.31,2.31,0,0,0,.54.54,2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0A2,2,0,0,0,18,5.09a2.23,2.23,0,0,0,.27-.26,2,2,0,0,0,.32-.5,2.05,2.05,0,0,0,.16-.78A2,2,0,0,0,18.59,2.77ZM3.25,5.05a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,3.25,5.05Zm8.22,0A1.5,1.5,0,1,1,13,3.55,1.51,1.51,0,0,1,11.47,5.05Zm5.28,0a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16.75,5.05Z"/><circle class="innercolor" cx="3.25" cy="3.55" r="1.5"/><circle class="innercolor" cx="16.75" cy="3.55" r="1.5"/>
</symbol>
<symbol id="route-spread-normal" viewBox="0 0 20 15">
<circle class="innerColor" cx="11.47" cy="8" r="1.5"/><circle class="innerColor" cx="11.47" cy="3.55" r="1.5"/><path class="foregroundColor" d="M18.27,6.72A1.72,1.72,0,0,0,18,6.45,1.88,1.88,0,0,0,17.15,6a1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.31.87H13.45a2,2,0,0,0-.86-1.41A2,2,0,0,0,11.47,6a1.92,1.92,0,0,0-1.11.34,2,2,0,0,0-.55.54,2,2,0,0,0-.31.87H5.22a1.74,1.74,0,0,0-.45-1,1.72,1.72,0,0,0-.27-.27A1.88,1.88,0,0,0,3.65,6a1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54A2,2,0,0,0,1.25,8a2.05,2.05,0,0,0,.34,1.12,2.31,2.31,0,0,0,.54.54A2.05,2.05,0,0,0,3.25,10a2.58,2.58,0,0,0,.4,0,2.1,2.1,0,0,0,.85-.41,1.72,1.72,0,0,0,.27-.27,2,2,0,0,0,.32-.5,2,2,0,0,0,.13-.53H9.5a2,2,0,0,0,.31.87,2.17,2.17,0,0,0,.55.54,2,2,0,0,0,1.11.34,2,2,0,0,0,1.12-.34,2.13,2.13,0,0,0,.54-.54,2,2,0,0,0,.32-.87h1.33a2,2,0,0,0,.31.87,2.31,2.31,0,0,0,.54.54,2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0A2.1,2.1,0,0,0,18,9.55a1.72,1.72,0,0,0,.27-.27,2,2,0,0,0,.32-.5A2.05,2.05,0,0,0,18.75,8a2,2,0,0,0-.16-.78A1.86,1.86,0,0,0,18.27,6.72ZM3.25,9.5A1.5,1.5,0,1,1,4.75,8,1.5,1.5,0,0,1,3.25,9.5Zm8.22,0A1.5,1.5,0,1,1,13,8,1.5,1.5,0,0,1,11.47,9.5Zm5.28,0A1.5,1.5,0,1,1,18.25,8,1.5,1.5,0,0,1,16.75,9.5Z"/><circle class="innerColor" cx="3.25" cy="8" r="1.5"/><path class="foregroundColor" d="M2.13,5.21a2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0,2,2,0,0,0,.85-.42,2.23,2.23,0,0,0,.27-.26,2,2,0,0,0,.32-.5,2,2,0,0,0,.13-.53H9.5a2.07,2.07,0,0,0,.31.87,2.17,2.17,0,0,0,.55.54,2,2,0,0,0,1.11.34,2,2,0,0,0,1.12-.34,2.13,2.13,0,0,0,.54-.54,2.08,2.08,0,0,0,.32-.87h1.33a2.07,2.07,0,0,0,.31.87,2.31,2.31,0,0,0,.54.54,2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0A2,2,0,0,0,18,5.09a2.23,2.23,0,0,0,.27-.26,2,2,0,0,0,.32-.5,2.05,2.05,0,0,0,.16-.78,2,2,0,0,0-.16-.78,1.86,1.86,0,0,0-.32-.5A1.72,1.72,0,0,0,18,2a1.88,1.88,0,0,0-.85-.41,1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.31.87H13.45a2,2,0,0,0-.86-1.41,2,2,0,0,0-1.12-.34,1.92,1.92,0,0,0-1.11.34,2,2,0,0,0-.55.54,2,2,0,0,0-.31.87H5.22a1.79,1.79,0,0,0-.13-.53,1.86,1.86,0,0,0-.32-.5A1.72,1.72,0,0,0,4.5,2a1.88,1.88,0,0,0-.85-.41,1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.34,1.12,2.05,2.05,0,0,0,.34,1.12A2.31,2.31,0,0,0,2.13,5.21ZM16.75,2.05a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16.75,2.05Zm-5.28,0A1.5,1.5,0,1,1,10,3.55,1.5,1.5,0,0,1,11.47,2.05Zm-8.22,0a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,3.25,2.05Z"/><circle class="innerColor" cx="3.25" cy="3.55" r="1.5"/><circle class="innerColor" cx="16.75" cy="3.55" r="1.5"/><circle class="innerColor" cx="16.75" cy="8" r="1.5"/>
</symbol>
<symbol id="route-spread-top" viewBox="0 0 20 15">
<path class="foregroundcolor" d="M2.13,5.21a2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0l.23-.08c.07,0,.15,0,.23-.08a2,2,0,0,0,.39-.26,1.66,1.66,0,0,0,.27-.26,2,2,0,0,0,.32-.5,2,2,0,0,0,.13-.53H9.5a2.07,2.07,0,0,0,.31.87,2.17,2.17,0,0,0,.55.54,2,2,0,0,0,1.11.34,2,2,0,0,0,1.12-.34,2.13,2.13,0,0,0,.54-.54,2.08,2.08,0,0,0,.32-.87h1.33a2.07,2.07,0,0,0,.31.87,2.31,2.31,0,0,0,.54.54,2.05,2.05,0,0,0,1.12.34,2.58,2.58,0,0,0,.4,0l.23-.08c.07,0,.15,0,.23-.08A2,2,0,0,0,18,5.09a1.66,1.66,0,0,0,.27-.26,2,2,0,0,0,.32-.5,2.05,2.05,0,0,0,.16-.78,2,2,0,0,0-.16-.78,1.86,1.86,0,0,0-.32-.5A1.72,1.72,0,0,0,18,2a2,2,0,0,0-.39-.26l-.23-.07a1,1,0,0,0-.23-.08,1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.31.87H13.45a2,2,0,0,0-.32-.87A1.3,1.3,0,0,0,13,2.25,1.66,1.66,0,0,0,12.71,2a1.27,1.27,0,0,0-.12-.1,2,2,0,0,0-1.12-.34,1.92,1.92,0,0,0-1.11.34,2,2,0,0,0-.55.54,2,2,0,0,0-.31.87H5.22a1.79,1.79,0,0,0-.13-.53,1.86,1.86,0,0,0-.32-.5A1.72,1.72,0,0,0,4.5,2a2,2,0,0,0-.39-.26l-.23-.07a1,1,0,0,0-.23-.08,1.75,1.75,0,0,0-.4,0,2,2,0,0,0-1.12.34,2.13,2.13,0,0,0-.54.54,2,2,0,0,0-.34,1.12,2.05,2.05,0,0,0,.34,1.12A2.31,2.31,0,0,0,2.13,5.21ZM16.75,2.05a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16.75,2.05Zm-5.28,0A1.5,1.5,0,1,1,10,3.55,1.5,1.5,0,0,1,11.47,2.05Zm-8.22,0a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,3.25,2.05Z"/><path class="foregroundcolor" d="M16.75,6a2,2,0,0,0-2,2,1.13,1.13,0,0,0,0,.25l-1.44.36a2,2,0,0,0-3.87.72,2.48,2.48,0,0,0,0,.27L5.11,10.72a2,2,0,1,0,.14.73,1.94,1.94,0,0,0,0-.24l4.39-1.13a2,2,0,0,0,3.85-.75,1.79,1.79,0,0,0,0-.23l1.44-.37A2,2,0,1,0,16.75,6ZM3.25,13a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,3.25,13Zm8.22-2.12A1.5,1.5,0,1,1,13,9.33,1.5,1.5,0,0,1,11.47,10.83ZM16.75,9.5A1.5,1.5,0,1,1,18.25,8,1.5,1.5,0,0,1,16.75,9.5Z"/><circle class="innercolor" cx="16.75" cy="3.55" r="1.5"/><circle class="innercolor" cx="11.47" cy="3.55" r="1.5"/><circle class="innercolor" cx="3.25" cy="3.55" r="1.5"/><circle class="innercolor" cx="16.75" cy="8" r="1.5"/><circle class="innercolor" cx="11.47" cy="9.33" r="1.5"/><circle class="innercolor" cx="3.25" cy="11.45" r="1.5"/>
</symbol>
<symbol id="router" viewBox="0 0 30 30">
<path class="foregroundColor" d="M10.41,13.66a17.77,17.77,0,0,0-2.85-1.17,15.84,15.84,0,0,0,7.31-6.9l-3.1-1.71L18.63,1,20,8.39l-3-1.68A18.85,18.85,0,0,1,10.41,13.66Zm4.46,10.75-3.1,1.71L18.63,29,20,21.61l-3,1.68c-3.52-6.23-9-9.46-15.91-9.46v2.34A15.23,15.23,0,0,1,14.87,24.41ZM29,15.05l-5.83-4.72v3.5H14.11c-.48.41-1,.8-1.48,1.16a16.9,16.9,0,0,1,1.5,1.18h9v3.5Z"/>
</symbol>
<symbol id="run-all" viewBox="0 0 30 30">
<path class="foregroundColor" d="M12.17,23.5l2.12-6.37H7.92L17.83,6.5l-2.12,6.38h6.37Z"/>
</symbol>
<symbol id="running-full" viewBox="0 0 18 18">
<path class="foregroundColor" d="M7,15l1.5-4.5H4L11,3,9.5,7.5H14Z"/>
</symbol>
<symbol id="running-not-long" viewBox="0 0 18 18">
<rect class="foregroundColor" x="8.5" width="1" height="18" transform="translate(-3.68 7.89) rotate(-40)"/>
</symbol>
<symbol id="running-not" viewBox="0 0 18 12">
<rect class="foregroundColor" x="8.5" y="0.78" width="1" height="10.44" transform="matrix(0.77, -0.64, 0.64, 0.77, -1.75, 7.19)"/>
</symbol>
<symbol id="running-partial" viewBox="0 0 18 18">
<path class="foregroundColor" d="M9.2,6,8.79,7.26l-.33,1h3.81L8.8,12l.41-1.23.33-1H5.73L9.2,6M11,3,4,10.5H8.5L7,15l7-7.5H9.5L11,3Z"/>
</symbol>
<symbol id="running-partial_error" viewBox="0 0 18 18">
<path class="errorColor" d="M9.2,6,8.79,7.26l-.33,1h3.81L8.8,12l.41-1.23.33-1H5.73L9.2,6M11,3,4,10.5H8.5L7,15l7-7.5H9.5L11,3Z"/>
</symbol>
<symbol id="running-partial_warning" viewBox="0 0 18 18">
<path class="warningColor" d="M9.2,6,8.79,7.26l-.33,1h3.81L8.8,12l.41-1.23.33-1H5.73L9.2,6M11,3,4,10.5H8.5L7,15l7-7.5H9.5L11,3Z"/>
</symbol>
<symbol id="running" viewBox="0 0 18 12">
<path class="foregroundColor" d="M7,12,8.5,7.5H4L11,0,9.5,4.5H14Z"/>
</symbol>
<symbol id="schedule" viewBox="0 0 18 18">
<path class="foregroundColor" d="M3.93,13.19V6.57h10V7.8a5,5,0,0,1,.66.32V4.24H12.33v.53a.61.61,0,0,1,0,.13.75.75,0,0,1-.74.63.74.74,0,0,1-.73-.63.61.61,0,0,1,0-.13V4.24H7.06v.53A.61.61,0,0,1,7,4.9a.75.75,0,0,1-.74.63.74.74,0,0,1-.73-.63.61.61,0,0,1,0-.13V4.24H3.26v9.61H7.92a5,5,0,0,1-.2-.66Z"/><path class="foregroundColor" d="M11.16,4.9a.44.44,0,0,0,.3.28l.1,0A.44.44,0,0,0,12,4.9a.61.61,0,0,0,0-.13V3.61a.42.42,0,0,0-.2-.35.41.41,0,0,0-.24-.08.41.41,0,0,0-.16,0,.41.41,0,0,0-.25.33.19.19,0,0,0,0,.07V4.77a.31.31,0,0,0,0,.08A.07.07,0,0,0,11.16,4.9Z"/><path class="foregroundColor" d="M6.29,5.2a.44.44,0,0,0,.31-.13.48.48,0,0,0,.1-.17.61.61,0,0,0,0-.13V3.61a.44.44,0,0,0-.44-.43h0a.51.51,0,0,0-.17,0,.44.44,0,0,0-.26.4V4.77a.31.31,0,0,0,0,.13A.43.43,0,0,0,6.29,5.2Z"/><path class="foregroundColor" d="M12.27,8.2a4,4,0,1,0,4,4A4,4,0,0,0,12.27,8.2Zm0,7.49a3.5,3.5,0,1,1,3.49-3.49A3.5,3.5,0,0,1,12.27,15.69Z"/><path class="foregroundColor" d="M14,11.9H12.6v-2a.34.34,0,0,0-.67,0v2.69h2a.34.34,0,0,0,0-.67Z"/>
</symbol>
<symbol id="scrll-arrw-left" viewBox="0 0 20 20">
<polygon class="foregroundColor" points="8 10 12 14 12 6 8 10"/>
</symbol>
<symbol id="scrll-arrw-right" viewBox="0 0 20 20">
<polygon class="foregroundColor" points="12 10 8 14 8 6 12 10"/>
</symbol>
<symbol id="sector" viewBox="0 0 30 30">
<path class="foregroundColor" d="M21,4.55V6a14.43,14.43,0,0,0-10.17,4.47A14.16,14.16,0,0,0,6.32,21H4.58v4.46H9V22.81l.32.33h.7L9,22.09V21H8l-.66-.69a10.56,10.56,0,0,1,.42-2.8l5.44,5.64h.69L7.9,17a14.48,14.48,0,0,1,1-2.23L17,23.14h.69L9.13,14.27a16.7,16.7,0,0,1,1.31-1.88L20.8,23.14H21v2.31h4.46V21H23.69V9h1.73V4.55ZM8,22v2.46H5.58V22ZM20.56,7.06l.4.41V9h1.48l.22.23v3.24l-4.84-5A10.42,10.42,0,0,1,20.56,7.06Zm-3.28.55,5.39,5.59v3.25l-7.6-7.89A14.47,14.47,0,0,1,17.28,7.61ZM14.63,8.82l8,8.35v3.24L12.75,10.1A14.19,14.19,0,0,1,14.63,8.82ZM21,21v1.6L10.77,12c.23-.27.47-.54.73-.79s.58-.53.88-.79L22.55,21Zm3.46,1v2.46H22V22Zm0-14H22V5.55h2.46Z"/>
</symbol>
<symbol id="selected-rendering-shp-opts-tb" viewBox="0 0 20 20">
<path class="foregroundColor" d="M18,4.48V2h-2.5v.75h-11V2H2v2.5h.75v11H2V18h2.5v-.75h11V18H18v-2.5h-.75v-11Zm-1.75,11h-.75v.75H4.46v-.73H3.73V4.48h.75V3.73h11v.75h.75Z"/><path class="foregroundColor" d="M14.88,14.88H5.13V5.13h9.75Zm-9-.75h8.25V5.88H5.88Z"/><polygon class="foregroundColor" points="13 13 7 7 13 7 13 13"/>
</symbol>
<symbol id="selection-full" viewBox="0 0 18 12">
<path class="foregroundColor" d="M4,1H14L9.91,7.29V11H8.09V7.29Z" transform="translate(0 0)"/>
</symbol>
<symbol id="selection-none" viewBox="0 0 18 12">
<path class="errorColor" d="M12.28,1.93,9.13,6.78,9,7l-.13-.2L5.72,1.93h6.56M14,1H4L8.09,7.29V11H9.91V7.29L14,1Z"/>
</symbol>
<symbol id="selection-partial" viewBox="0 0 18 12">
<path class="warningColor" d="M12.28,1.93,9.13,6.78,9,7l-.13-.2L5.72,1.93h6.56M14,1H4L8.09,7.29V11H9.91V7.29L14,1Z"/>
</symbol>
<symbol id="shape-options" viewBox="0 0 30 30">
<path class="foregroundColor" d="M16.42,21.3a1.49,1.49,0,0,1-1.22-.51c-.87-1.09.41-3.41.68-3.86a21.49,21.49,0,0,0,1.77-3.61,19.74,19.74,0,0,0-2.73,2.45c-1.93,1.95-3.06,2.58-3.79,2.08-1.35-.93.64-5.93,1.95-8.84a.5.5,0,1,1,.91.41C12.4,13,11.33,16.53,11.71,17c0,0,.54,0,2.5-2,2.84-2.88,3.67-3.19,4.22-2.77.28.21.94.72-1.69,5.13-.58,1-1,2.38-.75,2.74.11.14.54.21,1.39-.06a.51.51,0,0,1,.63.33.5.5,0,0,1-.33.62A4.18,4.18,0,0,1,16.42,21.3ZM11.71,17h0Z"/><path class="foregroundColor" d="M15.19,6.54a.5.5,0,0,0-.5-.5h-2a.5.5,0,0,0-.5.5.51.51,0,0,0,.5.5h2A.5.5,0,0,0,15.19,6.54Z"/><path class="foregroundColor" d="M11.19,6.54a.5.5,0,0,0-.5-.5h-2a.5.5,0,0,0-.5.5.51.51,0,0,0,.5.5h2A.5.5,0,0,0,11.19,6.54Z"/><path class="foregroundColor" d="M11.46,23h-2a.51.51,0,0,0-.5.5.5.5,0,0,0,.5.5h2a.5.5,0,0,0,.5-.5A.5.5,0,0,0,11.46,23Z"/><path class="foregroundColor" d="M19.46,23h-2a.51.51,0,0,0-.5.5.5.5,0,0,0,.5.5h2a.5.5,0,0,0,.5-.5A.5.5,0,0,0,19.46,23Z"/><path class="foregroundColor" d="M15.46,23h-2a.51.51,0,0,0-.5.5.5.5,0,0,0,.5.5h2a.5.5,0,0,0,.5-.5A.5.5,0,0,0,15.46,23Z"/><path class="foregroundColor" d="M19.19,6.54a.5.5,0,0,0-.5-.5h-2a.5.5,0,0,0-.5.5.51.51,0,0,0,.5.5h2A.5.5,0,0,0,19.19,6.54Z"/><path class="foregroundColor" d="M6.54,21.88a.53.53,0,0,0-.25.08H5v3H8V22H6.79A.57.57,0,0,0,6.54,21.88Z"/><path class="foregroundColor" d="M6,16.38a.5.5,0,0,0,1,0v-2a.5.5,0,1,0-1,0Z"/><path class="foregroundColor" d="M6,20.38a.5.5,0,0,0,1,0v-2a.5.5,0,0,0-1,0Z"/><path class="foregroundColor" d="M6.54,12.88a.5.5,0,0,0,.5-.5v-2a.5.5,0,0,0-1,0v2A.5.5,0,0,0,6.54,12.88Z"/><path class="foregroundColor" d="M23.46,14.27a.5.5,0,0,0,.5-.5v-2a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5v2A.5.5,0,0,0,23.46,14.27Z"/><path class="foregroundColor" d="M23,17.77a.5.5,0,0,0,.5.5.5.5,0,0,0,.5-.5v-2a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5Z"/><path class="foregroundColor" d="M24,21.77v-2a.5.5,0,0,0-.5-.5.5.5,0,0,0-.5.5v2A.58.58,0,0,0,23,22H22v1h-.5a.51.51,0,0,0-.5.5.5.5,0,0,0,.5.5H22v1h3V22h-1A.41.41,0,0,0,24,21.77Z"/><path class="foregroundColor" d="M22,5V6H20.69a.5.5,0,0,0-.5.5.51.51,0,0,0,.5.5H22V8h1V9.77a.5.5,0,0,0,.5.5.5.5,0,0,0,.5-.5V8h1V5Z"/><path class="foregroundColor" d="M5,8H6v.34a.5.5,0,0,0,1,0V8H8V5H5Z"/>
</symbol>
<symbol id="show-eye-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,9c-5.25,0-8.6,4.59-9.3,5.64C6.43,15.78,10,21,15,21c5.4,0,8.66-5.23,9.3-6.35C23.64,13.63,20.35,9,15,9Zm0,9.43A3.43,3.43,0,1,1,18.42,15,3.44,3.44,0,0,1,15,18.43Z"/>
</symbol>
<symbol id="show-hide-layer-manager" viewBox="0 0 30 30">
<polygon class="foregroundColor" points="15 16.61 5.02 10.8 15 5 24.98 10.8 15 16.61"/><polygon class="foregroundColor" points="23.55 14.08 15.91 18.52 15.01 19.05 14.1 18.52 6.45 14.07 5 14.91 11.21 18.52 15.01 20.73 18.8 18.52 25 14.92 23.55 14.08"/><polygon class="foregroundColor" points="23.55 18.34 15.91 22.79 15.01 23.32 14.1 22.79 6.45 18.34 5 19.18 11.21 22.79 15.01 25 18.8 22.79 25 19.19 23.55 18.34"/>
</symbol>
<symbol id="show-labels" viewBox="0 0 16 16">
<path class="foregroundColor" d="M11.14,6.33a3.75,3.75,0,0,0-.51-1.18c-.22-.25-.32-.35-1-.35H8.84v5.64c0,1,.12,1.08,1.19,1.16v.45h-4V11.6c1-.08,1.12-.15,1.12-1.16V4.8H6.44c-.67,0-.86.11-1.09.4a4.33,4.33,0,0,0-.49,1.14H4.4c.09-.88.17-1.81.19-2.39H5c.18.28.33.3.69.3h4.79a.78.78,0,0,0,.7-.3h.35c0,.51.06,1.56.12,2.34Z"/>
</symbol>
<symbol id="similar-map-element" viewBox="0 0 24 24">
<path class="cls-2" d="M20,6.46V4H17.54V5H6.46V4H4V6.46H5V17.54H4V20H6.46V19H17.54v1H20V17.54H19V6.46ZM18.52,17.54h-1v1H6.46v-1h-1V6.46h1v-1H17.54v1h1Z"/><rect class="cls-2" x="9.62" y="12.57" width="4.76" height="0.99"/><rect class="cls-2" x="9.62" y="10.44" width="4.76" height="1"/>
</symbol>
<symbol id="sita-icon" viewBox="0 0 32 32">

  <path d="M 5.895 16.37 Q 5.546 16.37 5.235 16.233 Q 4.924 16.096 4.691 15.868 Q 4.459 15.64 4.326 15.329 Q 4.193 15.017 4.193 14.669 L 4.193 11.265 Q 4.193 10.917 4.326 10.605 Q 4.459 10.294 4.691 10.066 Q 4.924 9.838 5.235 9.701 Q 5.546 9.564 5.895 9.564 L 11.672 9.564 Q 12.021 9.564 12.332 9.701 Q 12.644 9.838 12.876 10.066 Q 13.108 10.294 13.241 10.605 Q 13.374 10.917 13.374 11.265 L 13.374 12.801 L 11.672 12.801 L 11.672 11.265 L 5.895 11.265 L 5.895 14.669 L 11.672 14.669 Q 12.021 14.669 12.332 14.801 Q 12.644 14.934 12.876 15.167 Q 13.108 15.399 13.241 15.71 Q 13.374 16.022 13.374 16.37 L 13.374 19.765 Q 13.374 20.114 13.241 20.425 Q 13.108 20.737 12.876 20.969 Q 12.644 21.201 12.332 21.334 Q 12.021 21.467 11.672 21.467 L 5.729 21.467 Q 5.38 21.467 5.069 21.334 Q 4.758 21.201 4.525 20.969 Q 4.293 20.737 4.16 20.425 Q 4.027 20.114 4.027 19.765 L 4.027 18.238 L 5.729 18.238 L 5.729 19.765 L 11.672 19.765 L 11.672 16.37 Z M 16.013 21.467 L 12.61 9.564 L 14.312 9.564 L 16.951 19.035 L 19.583 9.564 L 21.284 9.564 L 23.916 19.035 L 26.555 9.564 L 28.257 9.564 L 24.854 21.467 L 22.986 21.467 L 20.437 12.544 L 17.881 21.467 Z"/>

</symbol>
<symbol id="start-stop" viewBox="0 0 18 18">
<path class="foregroundColor" d="M9,6.46V9.83h3.36A3.37,3.37,0,0,0,9,6.46Z"/><path class="foregroundColor" d="M9,15.2a5.38,5.38,0,1,1,5.38-5.37A5.38,5.38,0,0,1,9,15.2ZM9,5.45a4.38,4.38,0,1,0,4.38,4.38A4.38,4.38,0,0,0,9,5.45Z"/><rect class="foregroundColor" x="8" y="2.8" width="2" height="1"/><rect class="foregroundColor" x="13.06" y="4.72" width="1.06" height="1.06" transform="translate(0.26 11.14) rotate(-45)"/>
</symbol>
<symbol id="stop-all" viewBox="0 0 30 30">
<path class="foregroundColor" d="M15,6.25A8.75,8.75,0,1,0,23.75,15,8.77,8.77,0,0,0,15,6.25ZM7.75,15A7.21,7.21,0,0,1,9.8,10l9.26,11A7.24,7.24,0,0,1,7.75,15Zm3.19-6A7.24,7.24,0,0,1,20.2,20Z"/>
</symbol>
<symbol id="system-view" viewBox="0 0 30 30">
<path class="foregroundColor" d="M14.57,4.29V17.84H9.05c0-.44,0-.9,0-1.35s-.12-.62-.62-.62H2.87c-.47,0-.61.12-.61.59v3.62a.59.59,0,0,0,.58.58H8.48c.45,0,.57-.12.57-.57s0-.9,0-1.38h5.52v7.53c0,.73,0,.79.79.79H21c0,.49,0,.94,0,1.39a.6.6,0,0,0,.6.59h5.59c.48,0,.6-.11.6-.6V24.82c0-.47-.13-.6-.6-.6H21.56a.61.61,0,0,0-.61.6c0,.46,0,.92,0,1.36H15.4V18.66H21c0,.48,0,.94,0,1.4s.2.62.92.56a26.75,26.75,0,0,1,4.65,0c1,.08,1.21.06,1.22-.57V16.43a.58.58,0,0,0-.58-.58H21.52c-.45,0-.57.13-.57.58s0,.92,0,1.4H15.41V10.7H21c0,.48,0,.93,0,1.38s.12.58.57.58h5.64a.58.58,0,0,0,.58-.58V8.46c0-.46-.14-.59-.61-.59H21.57c-.5,0-.62.12-.62.62s0,.91,0,1.35H15.41V4.29H21.1a.7.7,0,0,0,.7-.7V1.7a.7.7,0,0,0-.7-.7H8.85a.7.7,0,0,0-.7.7V3.58a.7.7,0,0,0,.7.7Zm7.21,12.37H26.9V19.8H21.78Zm0-7.95H26.9v3.13H21.78ZM8.22,19.84H3.1V16.71H8.22ZM26.9,28.19H21.78V25.05H26.9Z"/>
</symbol>
<symbol id="tak-icon" viewBox="0 0 32 32">

  <path d="M 1.495 9.851 L 11.134 9.851 L 11.134 11.772 L 7.462 11.772 L 7.462 21.751 L 5.167 21.751 L 5.167 11.772 L 1.495 11.772 Z M 15.267 11.772 L 14.655 11.772 L 11.153 21.751 L 8.79 21.751 L 13.057 9.851 L 16.848 9.851 L 21.115 21.751 L 18.752 21.751 Z M 11.374 17.093 L 18.31 17.093 L 18.31 19.014 L 11.374 19.014 Z M 20.572 9.851 L 22.867 9.851 L 22.867 14.764 L 23.564 14.764 L 27.746 9.851 L 30.551 9.851 L 25.621 15.699 L 30.687 21.751 L 27.746 21.751 L 23.564 16.685 L 22.867 16.685 L 22.867 21.751 L 20.572 21.751 Z" style="text-transform: capitalize;"/>

</symbol>
<symbol id="text" viewBox="0 0 30 30">
<path class="foregroundColor" d="M19.58,12.23a5.3,5.3,0,0,0-.75-1.71c-.31-.36-.45-.5-1.48-.5H16.23v8.2c0,1.46.18,1.57,1.73,1.69v.64H12.17v-.64c1.48-.12,1.64-.23,1.64-1.69V10H12.74c-1,0-1.25.16-1.58.57a6.66,6.66,0,0,0-.72,1.66H9.78c.12-1.27.24-2.63.28-3.47h.52c.26.4.47.44,1,.44h7a1.15,1.15,0,0,0,1-.44h.51c0,.73.09,2.27.17,3.4Z"/><polygon class="foregroundColor" points="25.7 25.7 23.2 25.7 23.2 24.7 24.7 24.7 24.7 23.2 25.7 23.2 25.7 25.7"/><path class="foregroundColor" d="M19.92,25.7H16.64v-1h3.28Zm-6.56,0H10.08v-1h3.28Z"/><polygon class="foregroundColor" points="6.8 25.7 4.3 25.7 4.3 23.2 5.3 23.2 5.3 24.7 6.8 24.7 6.8 25.7"/><path class="foregroundColor" d="M5.3,19.92h-1V16.64h1Zm0-6.56h-1V10.08h1Z"/><polygon class="foregroundColor" points="5.3 6.8 4.3 6.8 4.3 4.3 6.8 4.3 6.8 5.3 5.3 5.3 5.3 6.8"/><path class="foregroundColor" d="M19.92,5.3H16.64v-1h3.28Zm-6.56,0H10.08v-1h3.28Z"/><polygon class="foregroundColor" points="25.7 6.8 24.7 6.8 24.7 5.3 23.2 5.3 23.2 4.3 25.7 4.3 25.7 6.8"/><path class="foregroundColor" d="M25.7,19.92h-1V16.64h1Zm0-6.56h-1V10.08h1Z"/>
</symbol>
<symbol id="undo-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M23.86,17.31h0a11.21,11.21,0,0,0-5-4.28A10.19,10.19,0,0,0,8.22,14.66l1-2.88a1,1,0,0,0-.48-1.19.8.8,0,0,0-1.07.52L6.06,15.74a1,1,0,0,0,.47,1.19l4.16,1.78a.81.81,0,0,0,1.08-.52A1,1,0,0,0,11.29,17l-2-.89a8.69,8.69,0,0,1,9-1.37,9.55,9.55,0,0,1,4.24,3.66h0a.78.78,0,0,0,1.13.11A.91.91,0,0,0,23.86,17.31Z"/>
</symbol>
<symbol id="unlinked" viewBox="0 0 18 18">
<path class="foregroundColor" d="M10.67,5.08a1.37,1.37,0,0,1,1-.43,1.41,1.41,0,0,1,1,.4,1.43,1.43,0,0,1,.43,1,1.45,1.45,0,0,1-.4,1L10.92,8.86l.66.64,1.78-1.81A2.31,2.31,0,0,0,14,6a2.32,2.32,0,0,0-2.39-2.3A2.26,2.26,0,0,0,10,4.44L8.23,6.24l.66.64Z"/><path class="foregroundColor" d="M7.38,12.64a1.45,1.45,0,0,1-1,.41h0A1.42,1.42,0,0,1,5,11.61a1.44,1.44,0,0,1,.42-1l1.78-1.8-.65-.65L4.73,10a2.35,2.35,0,0,0,0,3.31,2.33,2.33,0,0,0,3.3,0l1.78-1.8-.65-.65Z"/><path class="foregroundColor" d="M5.42,7.72a.48.48,0,0,0,.47-.36h0a.49.49,0,0,0-.35-.6L4.26,6.42a.52.52,0,0,0-.37,0,.46.46,0,0,0-.23.3v0a.48.48,0,0,0,.06.33.5.5,0,0,0,.3.23l1.28.34Z"/><path class="foregroundColor" d="M5.87,6.64a.48.48,0,0,0,.34.14.54.54,0,0,0,.35-.14.52.52,0,0,0,.14-.35A.48.48,0,0,0,6.56,6L5.61,5a.49.49,0,0,0-.69.69Z"/><path class="foregroundColor" d="M6.85,5.63A.48.48,0,0,0,7.32,6l.13,0h0a.5.5,0,0,0,.34-.6L7.45,4.09a.48.48,0,0,0-.23-.29.44.44,0,0,0-.37,0,.46.46,0,0,0-.3.23.47.47,0,0,0,0,.37Z"/><path class="foregroundColor" d="M14.29,10.86a.53.53,0,0,0-.3-.23l-1.28-.34a.5.5,0,0,0-.6.35.49.49,0,0,0,.35.6l1.28.34h.13a.5.5,0,0,0,.24-.06.46.46,0,0,0,.23-.3v0A.48.48,0,0,0,14.29,10.86Z"/><path class="foregroundColor" d="M12.13,11.36a.48.48,0,0,0-.34-.14h0a.48.48,0,0,0-.49.49.48.48,0,0,0,.14.34l1,.94a.49.49,0,0,0,.34.14.52.52,0,0,0,.35-.14.49.49,0,0,0,0-.69Z"/><path class="foregroundColor" d="M11.15,12.37a.51.51,0,0,0-.23-.29.45.45,0,0,0-.37-.05h0a.5.5,0,0,0-.34.6l.35,1.28a.5.5,0,0,0,.23.3.5.5,0,0,0,.24.06h.13a.49.49,0,0,0,.34-.6Z"/>
</symbol>
<symbol id="unlock-layer" viewBox="0 0 12 12">
      
        <path class="foregroundColor"
              d="M9,5.17H8.29V3.85a2.33,2.33,0,0,0-2-2.34A2.29,2.29,0,0,0,3.71,3.78V5.17H3.05a.46.46,0,0,0-.45.45V10a.45.45,0,0,0,.45.45H9A.45.45,0,0,0,9.4,10V5.62A.46.46,0,0,0,9,5.17ZM4.24,3.78a1.76,1.76,0,1,1,3.52,0V5.17H4.24ZM8.87,9.67a.29.29,0,0,1-.29.29H3.42a.29.29,0,0,1-.29-.29V6a.29.29,0,0,1,.29-.29H8.58A.29.29,0,0,1,8.87,6Z"/>
      
</symbol>
<symbol id="unlock-mp-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M22.12,13.18h-6v-2.3a4.58,4.58,0,0,0-4.57-4.57h-.3A4.65,4.65,0,0,0,7,11v.89a.79.79,0,0,0,1.57,0v-1a3,3,0,0,1,3-3h.21A3.07,3.07,0,0,1,14.56,11v2.18H10.63a.88.88,0,0,0-.88.89v8.74a.88.88,0,0,0,.88.88H22.12a.89.89,0,0,0,.88-.88V14.07A.89.89,0,0,0,22.12,13.18Zm-.61,9.08H11.34V14.74H21.51Z"/>
</symbol>
<symbol id="up-down-arrow-layer" viewBox="0 0 16 16">
<path class="foregroundColor" d="M11.54,7.12a.63.63,0,0,1-.45-.19L8,3.84,4.91,6.93A.63.63,0,1,1,4,6.05L7.56,2.51A.65.65,0,0,1,8,2.33H8a.63.63,0,0,1,.44.18L12,6.05a.63.63,0,0,1,0,.88A.59.59,0,0,1,11.54,7.12Z"/><path class="foregroundColor" d="M8,13.67a.62.62,0,0,1-.44-.18L4,10a.63.63,0,0,1,.89-.88L8,12.16l3.09-3.09A.63.63,0,1,1,12,10L8.44,13.49A.62.62,0,0,1,8,13.67Z"/>
</symbol>
<symbol id="upload-additional-act" viewBox="0 0 30 30">
<path class="foregroundColor" d="M18,20.91v1.18h-5.9V20.91Zm0-2.37h-5.9v1.19H18Zm-5.9-4.13v2.95H18V14.41H21.5L15,7.91l-6.5,6.5Z"/>
</symbol>
<symbol id="vcci-brand" viewBox="0 0 18 12">
<path class="foregroundColor" d="M9.44,6.44c-.35-.14-.35-.14-.58.15a.15.15,0,0,1-.21,0L8.2,6.41A31.2,31.2,0,0,1,4.12,4a1.44,1.44,0,0,0-.4-.28,1.76,1.76,0,0,0,.4.37c.1.1.2.2.31.29A25.27,25.27,0,0,0,6.89,6.26a21.81,21.81,0,0,0,3.4,2,.16.16,0,0,0,.23-.06l.74-.93c.09-.1.08-.15-.06-.19A18.5,18.5,0,0,1,9.44,6.44Z"/><path class="foregroundColor" d="M8.31,7.92C8,7.66,8,7.65,7.76,8c-.1.13-.17.12-.28,0-.71-.59-1.39-1.19-2-1.84,0,0-.07-.11-.17-.08.06.08.1.16.15.23,1,1.4,2.09,2.73,3.2,4,.08.09.12.08.19,0,.3-.39.6-.77.92-1.15.08-.1.07-.15,0-.22C9.22,8.64,8.76,8.29,8.31,7.92Z"/><path class="foregroundColor" d="M15.46,1.72H12.77c-.07,0-.14,0-.17.07a.55.55,0,0,1-.66.3s-.07,0-.11,0c-.24,0-.48,0-.73,0a.21.21,0,0,0-.19,0,2.07,2.07,0,0,0-.43,0,.08.08,0,0,0-.1,0,1.22,1.22,0,0,0-.33,0A.08.08,0,0,0,10,2a1,1,0,0,0-.3,0s0,0-.06,0c-.74-.07-1.47-.18-2.24-.29l.06.06.14.06a17.73,17.73,0,0,0,3.06.74,22.92,22.92,0,0,0,2.6.27l0,0c.45,0,.91,0,1.36,0a.16.16,0,0,0,.14-.07l.75-1s.07-.05.05-.09S15.49,1.72,15.46,1.72Z"/><path class="foregroundColor" d="M14.09,3.44c-.84-.06-1.67-.11-2.51-.22a.13.13,0,0,0-.14,0,2.62,2.62,0,0,0-.24.34.22.22,0,0,1-.25.09c-.21,0-.43-.06-.64-.09,0,0,0,0-.06,0-.87-.18-1.72-.38-2.57-.64A22.1,22.1,0,0,1,5.12,2a.45.45,0,0,0-.28-.12A.54.54,0,0,0,5.07,2a12.2,12.2,0,0,0,1.56.91A18.05,18.05,0,0,0,10.74,4.3a15.43,15.43,0,0,0,1.91.28,4.07,4.07,0,0,0,.49,0,.24.24,0,0,0,.28-.12c.24-.31.49-.62.74-.92,0,0,.07-.07.06-.11S14.13,3.44,14.09,3.44Z"/><path class="foregroundColor" d="M12.66,5.26a19.09,19.09,0,0,1-2.25-.46c-.07,0-.15,0-.19,0a.42.42,0,0,1-.57.2s-.05,0-.06,0L9.52,5s0,0-.06,0L9.39,5s-.05,0-.06,0l-.66-.19c-.43-.16-.87-.31-1.3-.49A23.41,23.41,0,0,1,2.94,2a2.94,2.94,0,0,0-.5-.37,2.14,2.14,0,0,0,.49.46,7.25,7.25,0,0,0,.75.62,21.46,21.46,0,0,0,5,2.83,14.86,14.86,0,0,0,1.7.59,11.21,11.21,0,0,0,1.09.32c.38.1.38.1.6-.24s.43-.54.64-.81C12.82,5.26,12.71,5.26,12.66,5.26Z"/>
</symbol>
<symbol id="vcci-connection" viewBox="0 0 18 12">
<path class="foregroundColor" d="M17,7H13V5h4ZM11,7H7V5h4ZM5,7H1V5H5Z"/>
</symbol>
<symbol id="vertical-tool-display-r-tb" viewBox="0 0 30 30">
<path class="foregroundColor" d="M18.44,5.46v4h-4v-4h4m1-1h-6v6h6v-6Z"/><rect class="foregroundColor" x="20.44" y="4.46" width="6" height="6" transform="translate(46.88 14.93) rotate(-180)"/><rect class="foregroundColor" x="20.44" y="11.46" width="6" height="6" transform="translate(46.88 28.93) rotate(-180)"/><path class="foregroundColor" d="M11.44,5.46v4h-4v-4h4m1-1h-6v6h6v-6Z"/><rect class="foregroundColor" x="20.44" y="18.46" width="6" height="6" transform="translate(46.88 42.93) rotate(-180)"/><path class="foregroundColor" d="M18.35,24.41,15.92,22a.61.61,0,0,0-.86.86l1.39,1.4h-1a9,9,0,0,1-9-9v-1l1.39,1.4a.63.63,0,0,0,.86,0,.6.6,0,0,0,0-.86L6.32,12.39a.6.6,0,0,0-.86,0L3,14.82a.6.6,0,0,0,0,.86.59.59,0,0,0,.43.17.6.6,0,0,0,.43-.17l1.39-1.4v1a10.22,10.22,0,0,0,10.21,10.2h1l-1.39,1.4a.6.6,0,0,0,0,.86.63.63,0,0,0,.43.17.65.65,0,0,0,.43-.17l2.43-2.44A.6.6,0,0,0,18.35,24.41Z"/>
</symbol>
<symbol id="view-on-map" viewBox="0 0 18 18">
<path class="foregroundColor" d="M9,3A4,4,0,0,0,5,6.75C5,8.71,6.45,11,9,15c2.52-4,4-6.29,4-8.25A4,4,0,0,0,9,3ZM9,8.68A1.71,1.71,0,1,1,10.7,7,1.7,1.7,0,0,1,9,8.68Z"/>
</symbol>
<symbol id="view-type_listview" viewBox="0 0 20 15">
<path class="foregroundColor" d="M20,1.76H0V0H20Zm0,2.65H0V6.18H20Zm0,4.41H0v1.77H20Zm0,4.42H0V15H20Z"/>
</symbol>
<symbol id="view-type_rows" viewBox="0 0 20 15">
<rect class="foregroundColor" y="12.24" width="9.17" height="2.76"/><rect class="foregroundColor" x="10.83" y="12.24" width="9.17" height="2.76"/><rect class="foregroundColor" width="9.17" height="2.76"/><rect class="foregroundColor" x="10.83" width="9.17" height="2.76"/><rect class="foregroundColor" y="6.12" width="9.17" height="2.76"/><rect class="foregroundColor" x="10.83" y="6.12" width="9.17" height="2.76"/>
</symbol>
<symbol id="view-type_tiles-small" viewBox="0 0 20 15">
<rect class="foregroundColor" width="4" height="4"/><rect class="foregroundColor" x="16" width="4" height="4"/><rect class="foregroundColor" x="10.6" width="4" height="4"/><rect class="foregroundColor" y="11" width="4" height="4"/><rect class="foregroundColor" x="16" y="11" width="4" height="4"/><rect class="foregroundColor" x="5.3" width="4" height="4"/><rect class="foregroundColor" y="5.5" width="4" height="4"/><rect class="foregroundColor" x="5.3" y="5.5" width="4" height="4"/><rect class="foregroundColor" x="16" y="5.5" width="4" height="4"/><rect class="foregroundColor" x="10.6" y="5.5" width="4" height="4"/><rect class="foregroundColor" x="5.3" y="11" width="4" height="4"/><rect class="foregroundColor" x="10.6" y="11" width="4" height="4"/>
</symbol>
<symbol id="view-type_tiles" viewBox="0 0 20 15">
<path class="foregroundColor" d="M9.17,6.76H0V0H9.17ZM0,15H9.17V8.24H0Z"/><path class="foregroundColor" d="M20,6.76H10.83V0H20ZM10.83,15H20V8.24H10.83Z"/>
</symbol>
<symbol id="world-map" viewBox="0 0 60 30">
<path class="foregroundColor" d="M45,19c-.23-.19-.24-.5-.38-.73a.94.94,0,0,1-.19-.72,2.48,2.48,0,0,0-.17-.86c0-.08-.08-.13-.18-.06s-.31.07-.34-.18-.32-.48-.45-.74c0-.07-.62.07-.71.16a7.22,7.22,0,0,1-.94.8.41.41,0,0,0-.19.39,1.25,1.25,0,0,1-.27.82c-.18.2-.31.19-.4-.06a4.25,4.25,0,0,1-.59-2.05c-.31.17-.51.17-.59-.19,0-.2-.61-.49-.82-.45a1.55,1.55,0,0,1-.93-.08,2.31,2.31,0,0,0-.77-.2.58.58,0,0,1-.49-.38.29.29,0,0,0-.21-.19c-.15,0-.23,0-.18.17a1,1,0,0,0,.46.54l.07.05c.21.29.3.31.55.06s.23-.21.29.07c0,.12.17.14.27.2.25.15.25.25.09.49a2.68,2.68,0,0,1-1,.8,8.22,8.22,0,0,1-1.36.61.63.63,0,0,1-.24,0A4.12,4.12,0,0,0,35,16.18c-.11-.18-.3-.27-.36-.5a3.44,3.44,0,0,0-.57-1,.13.13,0,0,0-.18,0c-.08,0-.06.1,0,.17.18.46.45.88.6,1.36a2.26,2.26,0,0,0,.92,1.07c0,.28.15.38.43.31s.41-.08.61-.13.32,0,.24.23a2.77,2.77,0,0,1-1,1.42,3.63,3.63,0,0,0-.81.85.91.91,0,0,0,0,1,2.22,2.22,0,0,1,.11.92.4.4,0,0,1-.26.33l-.26.14a.59.59,0,0,0-.33.73c0,.24,0,.46-.25.54a.22.22,0,0,0-.16.22A1.82,1.82,0,0,1,32,25.25a2.61,2.61,0,0,0-.46.08.32.32,0,0,1-.43-.31,1.11,1.11,0,0,0-.26-.73,1.61,1.61,0,0,1-.31-.78,1.48,1.48,0,0,0-.31-.87.9.9,0,0,1,0-1.06c.24-.36.09-.66,0-1a1.53,1.53,0,0,0-.45-.81.45.45,0,0,1-.13-.53c.15-.45.06-.57-.41-.63-.08,0-.17,0-.21-.09-.15-.29-.4-.25-.64-.17s-.59.28-.93.15a.17.17,0,0,0-.14,0c-.66.29-1-.18-1.34-.58-.15-.17-.28-.36-.44-.51a.63.63,0,0,1-.18-.71.89.89,0,0,0,0-.84c-.06-.09,0-.17,0-.25a5.24,5.24,0,0,1,.54-.87.57.57,0,0,1,.22-.19.62.62,0,0,0,.39-.51.57.57,0,0,1,.36-.51s.06,0,.07,0c.13-.38.43-.26.69-.24a.39.39,0,0,0,.26,0A2.71,2.71,0,0,1,29.47,13h.17c.32-.06.5.12.38.43s0,.28.12.28c.41,0,.68.35,1.05.44.16,0,.24.08.29-.13a.29.29,0,0,1,.44-.2c.28.13.57.16.85.26a.51.51,0,0,0,.4,0,.36.36,0,0,1,.23,0c.14,0,.3.14.4,0a1.32,1.32,0,0,0,.26-.8c0-.14-.13-.13-.23-.08s-.51,0-.78,0a.41.41,0,0,1-.45-.46c0-.1,0-.31-.21-.35s-.23.14-.4.11c.07.17.3.3.08.52s-.28.17-.34,0-.44-.48-.42-.85c0,0-.08-.12-.13-.14-.29-.1-.45-.37-.71-.51-.06,0-.12-.12-.19,0s0,.14,0,.21a2.56,2.56,0,0,0,.76.63,1.65,1.65,0,0,1,.17.15c-.16,0-.23,0-.26.19a.81.81,0,0,1-.38.44c-.11.06-.25-.09-.36-.2.13-.11.25,0,.36-.06s.25-.18.09-.33a1.71,1.71,0,0,0-.42-.28A.89.89,0,0,1,30,11.8c-.16-.27-.34-.17-.57-.15a1.84,1.84,0,0,0-1,.52c-.12.09-.24.18-.21.33a.31.31,0,0,1-.17.34.9.9,0,0,1-1,.14c-.42-.23-.43-.2-.34-.72a1.15,1.15,0,0,0,0-.32c-.06-.25.07-.32.28-.32s.43,0,.65,0,.44-.21.28-.49a.61.61,0,0,0-.34-.31c-.07,0-.16-.06-.15-.16s.13-.1.21-.11.4-.18.62-.2a.27.27,0,0,0,.2-.27h0a.76.76,0,0,0,.43-.26.72.72,0,0,1,.6-.37c.19,0,.09-.13.08-.23s-.18-.4.07-.53c.1,0,.21-.2.3-.1a.32.32,0,0,1,0,.38c-.13.17-.07.35.09.33s.59.17.88,0a.47.47,0,0,1,.37,0c.2.06.34,0,.37-.27s0-.52.42-.37c.11,0,.12-.08.08-.15-.19-.3,0-.35.23-.38a1.61,1.61,0,0,1,.38,0c.06,0,.15,0,.14-.12s-.09,0-.15-.06a.7.7,0,0,0-.4.08.43.43,0,0,1-.59-.17.58.58,0,0,1,.13-.72,2.25,2.25,0,0,0,.3-.24c0-.06.17-.12.11-.22a.29.29,0,0,0-.3-.08c-.09,0-.21.09-.21.19s-.19.32-.36.42c-.37.23-.41.42-.15.76a.23.23,0,0,1-.08.36.41.41,0,0,0-.2.39c0,.16-.43.47-.58.43A.18.18,0,0,1,30.27,9a.79.79,0,0,0,0-.2c-.09-.15-.13-.36-.25-.45s-.28.13-.44.17-.36,0-.43-.22S29.06,8,29,7.89a.44.44,0,0,1,.21-.53A4.21,4.21,0,0,0,29.8,7a3,3,0,0,0,.91-1.26c.09-.22.35-.17.52-.26.34-.17.73-.2,1.09-.37a1.13,1.13,0,0,1,.87,0c.18.07,0,.19,0,.27A3.59,3.59,0,0,1,34.86,6c.22.15.16.37-.1.46a1.52,1.52,0,0,1-1-.1c.26.22.1.65.53.67,0-.08-.13-.16,0-.25s.16,0,.24,0,.23.11.28-.13a.44.44,0,0,1,.47-.29c.16,0,.21,0,.17-.17s0-.22,0-.32,0-.12.08-.12a.35.35,0,0,1,.32.11c.05,0,.05.1,0,.14s-.15.06-.06.15a.17.17,0,0,0,.26,0,1.35,1.35,0,0,1,.45-.28c.18-.09.5-.24.56-.18s.34,0,.51,0,.32-.15.5,0,.14-.07.08-.16-.1-.09-.15-.14-.13-.08-.08-.16.12,0,.19,0a9.38,9.38,0,0,1,1.41.48c-.11-.21-.32-.35-.28-.6,0,0,0,0,0-.06,0-.17.54-.68.71-.65s.34.08.28.39a2.19,2.19,0,0,0,0,.8c.07.35,0,.5-.31.65l-.09.06a.48.48,0,0,0,.56-.23.39.39,0,0,0,0-.51.31.31,0,0,1,0-.28s0-.09,0-.1c-.28-.35.1-.49.21-.72.2.09-.05.34.15.43.12-.39.23-.42.54-.31a1,1,0,0,0,.45.15c-.06-.12-.18-.12-.22-.22s-.06-.3.18-.33l.44,0c.18,0,.34,0,.36-.24a.23.23,0,0,1,.16-.17A2.35,2.35,0,0,1,44,3.44a1,1,0,0,0,.71-.27c.5-.36.5-.39,1-.09a1,1,0,0,0,.54.09.67.67,0,0,1,.49.17c.21.21.2.39-.06.5a8.28,8.28,0,0,0-.83.45l.05,0c.43-.36.92-.06,1.38-.14.27-.05.48.19.75.2s.37.1.47-.16a.17.17,0,0,1,.21-.05.49.49,0,0,0,.2.05c.47,0,.47,0,.52.54a.35.35,0,0,0,.22.26c.1,0,.17,0,.22-.1s.14-.11.27-.07.46,0,.69,0a.23.23,0,0,0,.27-.2c0-.15.24-.19.39-.18q.56,0,1.11.12c.24,0,.35.29.58.36a.65.65,0,0,0,.36,0,2,2,0,0,1,.59,0c.14,0,.32,0,.35.18.06.29.24.24.44.23s.53,0,.8,0,.2.14.32.19.23.1.18-.11,0-.18.18-.18A3.54,3.54,0,0,1,58.51,6a1,1,0,0,0,.53.2.42.42,0,0,1,.24.09c.24.17.24.2,0,.36-.05,0-.12.06-.14.11-.1.27-.27.18-.45.12s-.22-.23-.38-.22-.27,0-.36-.27c0,.25-.08.39-.25.47s-.07.21,0,.31,0,.28-.19.28a1.62,1.62,0,0,0-.92.34A.94.94,0,0,1,56,8a1.06,1.06,0,0,0-1,.38c0,.07-.06.1,0,.19A.44.44,0,0,1,55,9c-.2.33-.55.51-.7.87a.29.29,0,0,1-.25.19c-.12,0-.09-.16-.12-.25a1.13,1.13,0,0,1,.52-1.53c.21-.12.36-.35.59-.49s.15-.24.21-.43c-.18.15-.28.33-.46.4s-.17.08-.23,0-.33-.18-.46-.07-.48.16-.35.51c0,.06-.54.16-.6.07s-.24-.12-.39-.09a.45.45,0,0,1-.23,0,1.32,1.32,0,0,0-1.32.46,5,5,0,0,1-.64.54c.2.08.33.3.58.15.07,0,.14,0,.2.09s.3.2.35-.13a.66.66,0,0,1,.15.5,5.17,5.17,0,0,0,.11.54.45.45,0,0,1,0,.14c-.36.12-.18.36-.11.57-.33.08-.33.08-.3-.23s0-.47,0-.71c0-.07,0-.15-.1-.15s-.09.07-.09.13c0,.7-.54,1.1-1,1.55-.1.11-.21.21-.38.14s-.23,0-.32.11-.2.37-.42.47,0,.12,0,.18l.16.22a.43.43,0,0,1,.06.47c-.09.14-.31.15-.48.18s-.09-.07-.07-.16.14-.41-.16-.49a.11.11,0,0,1,0-.13c.06-.35-.16-.22-.32-.19s-.16.13-.24.07,0-.18,0-.29c-.24,0-.34.29-.61.33.16.12.23.3.46.21s.25,0,.28.1-.11.1-.18.15-.3.21-.14.46.26.31.24.55a1.74,1.74,0,0,1-1.19,1.53c-.23.09-.51.09-.69.3-.06.06-.11,0-.15,0s-.32-.08-.46.05a.3.3,0,0,0,0,.45c.17.23.44.39.46.73a.46.46,0,0,1-.28.5s-.07,0-.08,0c-.27.43-.44.14-.63-.08A1.26,1.26,0,0,0,45,17.3a1.94,1.94,0,0,0-.24-.15c-.23.6-.22.7.28,1.1.26.21.25.51.32.77s-.18,0-.29,0ZM34.52,12.15c.12,0,.24,0,.36-.05a.13.13,0,0,0,.09-.2,1.12,1.12,0,0,0-1-.46c-.1,0-.18.08-.28,0-.31-.36-.31-.35-.59,0-.08.11-.13.22-.21.32s0,.21.06.29a.34.34,0,0,0,.41.05.93.93,0,0,1,1,0A.71.71,0,0,0,34.52,12.15Zm2.5.49a1.92,1.92,0,0,1-.21-.55.47.47,0,0,0-.18-.35c-.25-.22-.22-.34.1-.47.08,0,.16,0,.16-.14s-.11-.12-.2-.14a1.14,1.14,0,0,0-.7.37.63.63,0,0,0,.14.54c0,.09.12.18.17.27a.28.28,0,0,1,0,.25.42.42,0,0,0,.42.56C36.94,13,37,12.92,37,12.64Z"/><path class="foregroundColor" d="M6.27,8.8c0,.17.18.26.1.42H6.31c-.12-.23-.4-.3-.53-.53A1.46,1.46,0,0,0,4.59,8c-.06,0-.13,0-.2,0a1.28,1.28,0,0,0-.93.11c-.08,0-.17.14-.27.09s0-.2,0-.41A2.92,2.92,0,0,1,1,9.31l0-.06.59-.32a1.63,1.63,0,0,0,.51-.34c.1-.13.15-.24-.09-.24s-.27-.11-.43,0-.11-.06-.13-.13S1.43,8,1.33,8C1.06,8,.78,7.78.82,7.6s.17-.45.47-.44c.06,0,.12,0,.19-.08s.13-.07.11-.17-.11-.06-.18-.06c-.28,0-.59.11-.81-.2-.09-.12-.07-.16.05-.21s.42-.28.68-.07c0,0,.17.07.19,0s-.06-.12-.13-.12c-.26,0-.37-.28-.6-.36s-.07-.19,0-.21.36-.26.54-.35A1.71,1.71,0,0,1,2.67,5a10.45,10.45,0,0,0,1.86.29,2.31,2.31,0,0,1,.84.24.44.44,0,0,0,.39,0,5,5,0,0,1,1.11-.25c.2,0,.35-.13.49.1.6-.17,1.13.19,1.71.2-.07-.11-.3-.13-.23-.26s.34-.08.52-.16c-.22-.06-.49.08-.61-.08s-.3-.26-.17-.48c-.23,0-.26.26-.45.34s-.46-.11-.65-.22,0-.24.08-.36.1-.13.09-.19c-.06-.32.17-.32.35-.28.43.1.94,0,1.27.36.17.19.41.07.56.23.31-.23.39.08.57.24-.19-.32,0-.46.22-.57a.24.24,0,0,1,.26,0c.07.07,0,.14,0,.22a.72.72,0,0,0,.35.74c.1.06.32.11.26.22s-.15.37-.41.33l-.29,0a1,1,0,0,0,1,.21c.06,0,.11-.07.08-.16s0-.36.21-.32.18,0,.15-.17a.32.32,0,0,1,.09-.37c.09-.08.07-.19.05-.31-.09-.4.07-.58.49-.5.12,0,.33-.05.35.13s-.13.29-.31.35-.31.18-.1.38.19.45.43.59a.39.39,0,0,0,.18.06c.17,0,.28.1.24.29s0,.18.1.19,0-.09,0-.14a.09.09,0,0,1,.08-.07.21.21,0,0,0,.21-.25.29.29,0,0,0-.23-.23A.74.74,0,0,1,13.37,5a.48.48,0,0,1,.09-.61.52.52,0,0,1,.63-.19.59.59,0,0,0,.37,0,2.32,2.32,0,0,1,.87,0c.2,0,.25.22.36.34.27.28.67.32,1,.51a3.78,3.78,0,0,0,.33.22c.1,0,.11.19.09.31s-.19,0-.13.13.17,0,.22,0c.24.17.55.2.77.41s.11.15,0,.23a1.29,1.29,0,0,0-.17.16c-.12.19-.24.19-.39,0s-.2-.26-.3-.15,0,.25.18.32,0,0,0,0,.22.29.13.48-.22-.08-.33,0,.18.14.08.24-.17,0-.25,0a1.39,1.39,0,0,1-.76-.41.51.51,0,0,0-.58-.17c-.12,0-.3.08-.32-.09s.14-.2.3-.19.36,0,.32-.23c0,0,0-.06.06-.09s.26-.16.16-.31a.53.53,0,0,0-.46-.32c-.19,0-.28-.07-.31-.25-.11,0-.1.21-.23.22a.38.38,0,0,0-.56-.22c.26.2.15.52.12.72s-.38.3-.68.24c.2.29.53.35.76.55.05,0,.14.06.11.15a.14.14,0,0,1-.18,0c-.18-.08-.33-.14-.51,0s-.23-.06-.35-.09,0-.12,0-.19.07-.17,0-.28a1,1,0,0,1-.46.45,1.68,1.68,0,0,0-.79.83c0,.16-.08.33.14.42s.11.1.14.18.1.16.24.15a1.13,1.13,0,0,1,.52.19,1.75,1.75,0,0,0,.77.28c.15,0,.19.09.19.23a.63.63,0,0,0,.27.56c.07,0,.14.1.22,0a.17.17,0,0,0,.06-.21.84.84,0,0,0-.05-.17c-.07-.22-.09-.38.2-.49a.34.34,0,0,0,0-.62c-.13-.09-.17-.15-.06-.29s0-.38,0-.57,0-.26.23-.26a2.15,2.15,0,0,1,1.11.41c.06,0,.05.1,0,.16,0,.26.17.38.36.44s.27-.1.33-.27.18-.17.26,0,.39.45.43.76a.43.43,0,0,0,.31.3,1.6,1.6,0,0,1,.38.23c.33.21.27.5.2.75s.06.27.18.28c.3,0,.27.28.34.45a.1.1,0,0,1-.11.13c-.29-.05-.57-.14-.87-.17-.14,0-.07-.14,0-.21l.32-.56a.29.29,0,0,0-.3.07.94.94,0,0,1-.46.19c-.09,0-.2,0-.07.12s0,.11-.09.09a.72.72,0,0,1-.24-.1c-.21-.17-.39-.14-.6.1a1.12,1.12,0,0,1,.3-.08c.16,0,.31,0,.22.26s-.07.22.05.25.27.24.46.11.14-.12.21,0,0,.14-.07.19c-.24.17-.56.19-.79.38a.09.09,0,0,1-.13,0c-.05-.1.07-.16.07-.32-.28.26-.77.15-.81.66,0,.08-.07.15-.13.15-.38,0-.47.35-.62.61s-.12.07-.2,0c.15.49.11.54-.33.83s-.56.42-.37.87a.9.9,0,0,1,.12.46.19.19,0,0,1-.1.17c-.08,0-.14,0-.18-.08a.81.81,0,0,1-.2-.45.26.26,0,0,0-.29-.26c-.18,0-.36-.2-.53-.07a1.31,1.31,0,0,1-.93.1.63.63,0,0,0-.6.62,2.93,2.93,0,0,1-.05.29.89.89,0,0,0,.26.86.52.52,0,0,0,.83-.14c.1-.28.32-.25.54-.25s.12.09.08.19-.1.3-.15.45,0,.29.22.3c.52,0,.55.07.51.6s.21.64.61.5a.29.29,0,0,1,.32.09c.15.16.24.11.34,0a.84.84,0,0,1,1.18-.26.76.76,0,0,0,.5.12c.24,0,.48,0,.73,0a1.31,1.31,0,0,0,.95.72,1,1,0,0,1,.91.75c0,.13-.08.18-.12.27a1.8,1.8,0,0,1,.91.22.86.86,0,0,0,.41.13,2.28,2.28,0,0,1,1.14.48.5.5,0,0,1,.09.66,1.27,1.27,0,0,1-.21.25,1.26,1.26,0,0,0-.38.89,1.64,1.64,0,0,1-.41,1.08c-.17.22-.47.15-.7.28s-.45.23-.45.59-.24.53-.43.76-.23.26-.33.41a.52.52,0,0,1-.51.28c-.11,0-.16,0-.15.15,0,.4-.05.49-.44.58-.22.05-.43.05-.46.35,0,.12-.14.12-.24.1s-.12-.05-.15,0,0,.1.1.11.13.1.07.14c-.21.12-.19.41-.42.51s-.21.19,0,.31.14.27,0,.39a1.2,1.2,0,0,0-.22.23.68.68,0,0,0,.22,1s.09,0,.2.1a1.74,1.74,0,0,1-.94,0c0-.13.22-.15.09-.3s-.29.33-.24-.08a.11.11,0,0,0-.07-.12c-.46-.16-.38-.54-.36-.89,0-.1,0-.22-.06-.27s-.08-.21.06-.21a.15.15,0,0,0,.17-.15c0-.27.13-.52,0-.82a1,1,0,0,1,0-.76,9.22,9.22,0,0,0,.46-2.36,3.64,3.64,0,0,0,.07-.74.52.52,0,0,0-.33-.52c-.54-.17-.71-.64-.93-1.07a2.22,2.22,0,0,0-.45-.69.32.32,0,0,1-.1-.29c.13-.42,0-.91.48-1.22.24-.18.16-.83-.07-1,0,0-.11-.05-.13,0-.26.37-.47,0-.67-.06a.72.72,0,0,1-.49-.46.41.41,0,0,0-.28-.25,1.73,1.73,0,0,1-.89-.43.22.22,0,0,0-.22-.06c-.64.12-1.11-.29-1.63-.54a.25.25,0,0,1-.11-.29.5.5,0,0,0-.22-.46c-.32-.25-.51-.62-.81-.88a1.45,1.45,0,0,1-.24-.38c0-.06-.08-.17-.16-.1s0,.15.05.2a7.07,7.07,0,0,1,.72,1.14s0,.05,0,.08-.16-.06-.22-.11c-.23-.19-.32-.52-.62-.63,0-.39-.34-.59-.43-.92a.47.47,0,0,0-.28-.29,1.85,1.85,0,0,1-.88-1.33,2.06,2.06,0,0,1,0-.82.92.92,0,0,0-.47-1c-.13-.07-.22-.13-.15-.32a.32.32,0,0,0-.2-.41c-.23,0-.22-.18-.26-.32A.78.78,0,0,0,6.27,8.8ZM15,11.39a.73.73,0,0,0-1.22.12.45.45,0,0,0,0,.39s.06,0,.07,0,.07-.22.09-.34a.4.4,0,0,1,.33-.37c.18,0,.21.13.26.26s0,.15.13.18S14.6,11.25,15,11.39Z"/><path class="foregroundColor" d="M19.92,6.75c-.22.12-.28.07-.38-.14a3.31,3.31,0,0,1-.18-.53.3.3,0,0,1,.26-.41.23.23,0,0,0,.2-.17s.06-.12,0-.16-.1,0-.14.06a.26.26,0,0,1-.2.11c-.24,0-.37-.13-.27-.35s.1-.22-.08-.3S19,4.59,19,4.49A.64.64,0,0,0,18.9,4a1.21,1.21,0,0,0-1.31-.64c-.16,0-.33,0-.49.06s-.21,0-.25-.07-.23-.19-.35-.28c.05-.13.17-.09.28-.14a1.32,1.32,0,0,0-.39-.14c-.07,0-.14,0-.15-.13s.08-.13.15-.15l.83-.24c.08,0,.19,0,.2-.14S17.32,2,17.25,2s-.14,0-.13-.09.05-.08.09-.11a3.74,3.74,0,0,1,.45-.22c.18-.07.44,0,.49-.28,0,0,.08,0,.13-.05L19.11,1s.08,0,.09,0c.14.21.38,0,.54.13a.16.16,0,0,0,.07,0c.06-.36.27-.13.42-.11a.37.37,0,0,0,.32-.23c0-.1.07-.08.12-.09a1.39,1.39,0,0,1,.64,0c.26.07.46-.07.69-.13a4.69,4.69,0,0,1,1.79,0c.26,0,.47.26.76.29.11,0,.09.11,0,.15a2.24,2.24,0,0,1-.3.12c.29,0,.43,0,.32.24.47,0,.87-.3,1.35-.18.1,0,.23,0,.24.13S26,1.5,26,1.54a4.41,4.41,0,0,0-1.18.93c-.08.08-.1.19.07.23a.34.34,0,0,1,.14,0c.1.07.22.17.2.3s-.17.07-.27.08a.26.26,0,0,0-.22.07c0,.12.15.06.22.11s.18.13.13.26-.13.18,0,.27S25,3.93,25,4a1.11,1.11,0,0,0-.49.67.16.16,0,0,0,0,.15.35.35,0,0,1-.09.56,3,3,0,0,1-1.25.49.68.68,0,0,0-.56.33c-.15.26-.43.29-.69.35a.49.49,0,0,0-.4.37c-.12.32-.28.62-.41.94,0,.08-.11.18-.21.12-.34-.19-.81-.18-1-.64C19.84,7.12,19.64,7,19.92,6.75Z"/><path class="foregroundColor" d="M51.79,21.12c0,.45.45.64.51,1a.89.89,0,0,0,.38.54,2.79,2.79,0,0,1,.47.55s.05.09.09.11c.35.16.32.5.34.79a1.3,1.3,0,0,1-.26.89,1.82,1.82,0,0,0-.29.67.35.35,0,0,1-.24.26A1.87,1.87,0,0,1,51.6,26a.37.37,0,0,1-.32-.3c0-.13-.13-.19-.22-.27s-.1-.09-.1,0,0,.12-.1.1,0-.1,0-.15.11-.12.06-.24c-.24.3-.36.09-.48-.12a.64.64,0,0,0-.58-.28,1.81,1.81,0,0,0-1.06.29c-.09.05-.16.13-.26.12a1.37,1.37,0,0,0-.71.18.41.41,0,0,1-.47-.06c0-.05-.12-.1-.08-.17a1.13,1.13,0,0,0-.17-.95,1.87,1.87,0,0,1-.13-.77c0-.26.28-.53.63-.62L48,22.7c.12,0,.26-.08.29-.19.08-.3.38-.41.54-.63a.34.34,0,0,1,.54-.09c.07.06.19.1.21-.06s.35-.29.45-.5c0-.06.11,0,.16,0a1.05,1.05,0,0,0,.43.11c.18,0,.24.09.13.24s-.06.32.13.44c.56.34.65.3.69-.35A.75.75,0,0,1,51.79,21.12Z"/><path class="foregroundColor" d="M13.43,3.28A.5.5,0,0,1,14,2.8c-.19-.15-.32-.18-.73-.12a.54.54,0,0,1-.51-.16,1.07,1.07,0,0,0-.36-.3.24.24,0,0,1-.15-.36c.13-.2.31-.49.56-.38a4.72,4.72,0,0,1,1,.53c.09-.3.39-.06.59-.17A1.6,1.6,0,0,1,13,1.35c.1-.13.26-.11.4-.12.33,0,.63-.28,1-.16,0,0,.08,0,.11,0A2.32,2.32,0,0,1,15.89.78a10.93,10.93,0,0,1,1.4.06A4.37,4.37,0,0,1,18,1c.05,0,.1,0,.09.1s-.09.1-.15.13c-.4.2-.81.4-1.2.62a2.11,2.11,0,0,1-.63.33.24.24,0,0,0-.2.2.5.5,0,0,1-.44.41c-.06,0-.33.09-.14.29s0,.14,0,.17a.64.64,0,0,1-.4.11,10.85,10.85,0,0,0-1.4-.07Z"/><path class="foregroundColor" d="M53.11,21.14a.89.89,0,0,1-.74-.31c-.13-.18-.28-.2-.44,0a.32.32,0,0,1-.42.07.75.75,0,0,0-.41-.14c-.12,0-.16-.07-.08-.17s0-.26-.12-.34a.56.56,0,0,0-.24-.1,1.31,1.31,0,0,1-.86-.58c0-.06-.08-.13,0-.19s.1,0,.16,0c.23,0,.49,0,.53.37,0,.13.16.11.23,0,.21-.25.43-.18.67-.07a3.54,3.54,0,0,1,1.14.63s.1.06.09.07C52.54,20.78,52.91,20.85,53.11,21.14Z"/><path class="foregroundColor" d="M47.47,18.18a1.31,1.31,0,0,1,.27.22c.1.11-.05.16-.1.23s-.11.13-.07.21c.26.46-.23.7-.31,1.06,0,0-.12.1-.14.08-.22-.16-.51-.07-.72-.2s-.23-.36-.27-.56.15-.16.24-.23c.29-.25.7-.34.9-.7A.21.21,0,0,1,47.47,18.18Z"/><path class="foregroundColor" d="M35.3,23.07a1.72,1.72,0,0,0,.15-.86.2.2,0,0,1,.15-.2.93.93,0,0,0,.6-.53c.1-.16.17-.1.21,0a.69.69,0,0,1,0,.58,9.1,9.1,0,0,0-.34,1.16c-.07.24-.18.42-.46.41S35.3,23.4,35.3,23.07Z"/><path class="foregroundColor" d="M37.66,5.15a1.08,1.08,0,0,1-.92-.21c-.07,0-.13-.1-.07-.19a9.11,9.11,0,0,1,.74-1,1.47,1.47,0,0,1,1-.4,2.2,2.2,0,0,0,.68-.18.68.68,0,0,1,.2-.07c.09,0,.21,0,.25.09s-.07.17-.17.2-.43.15-.65.2a2.37,2.37,0,0,0-1.27.93.26.26,0,0,0,0,.37Z"/><path class="foregroundColor" d="M27.21,9.14c-.09-.07-.05-.15,0-.24s0-.26-.11-.24S27,8.61,27,8.55s.12-.18.16-.11.13.05.21,0,.25-.19.31.06c0,.08.1,0,.15.07s.13.08.08.15c-.17.23,0,.39.14.5s.19.41.43.5c.08,0,.06.12,0,.18a.15.15,0,0,0,0,.21h0c-.36.17-.76.07-1.14.21.27-.15,0-.42.16-.58s0-.06,0-.08c.18-.3.18-.31-.17-.37Z"/><path class="foregroundColor" d="M13.45,3.3c-.07.15,0,.21.14.22a1.57,1.57,0,0,0,.61,0,1,1,0,0,1,.58,0c.11,0,.26,0,.25.2a.27.27,0,0,1-.29.24c-.37,0-.75,0-1.12,0l-.35,0c-.19,0-.37-.08-.35-.34s-.09-.18-.2-.21L12.4,3.3c-.08,0-.2-.06-.16-.17s.14-.09.23-.07l1,.22Z"/><path class="foregroundColor" d="M45,19,45,19c.23.15.23.54.59.57.08,0,.1.14.06.23a1.59,1.59,0,0,0-.06.43h0c-.45.06-.62-.3-.82-.57A4.24,4.24,0,0,0,44,18.64c-.06,0-.14-.11-.09-.19s.14,0,.21,0c.27,0,.35.26.54.38S44.81,19,45,19Z"/><path class="foregroundColor" d="M51.29,12.11a.29.29,0,0,1,.25.43c-.24.75-.24.76-1,1a3.46,3.46,0,0,0-.54.19.21.21,0,0,0-.14.1c-.05.1-.12.27-.23.14s-.22-.28,0-.44a1.29,1.29,0,0,1,.75-.38,1,1,0,0,0,.83-.83.46.46,0,0,1,.1-.21Z"/><path class="foregroundColor" d="M48.06,18.36a.72.72,0,0,0,.16-1c-.08-.14-.21-.23-.26-.43a1.18,1.18,0,0,1,0-.53c0-.1,0-.2.18-.18a.19.19,0,0,1,.17.23.31.31,0,0,1,0,.17c-.16.23-.07.31.17.33a.46.46,0,0,1,.37.39c0,.2.13.38.18.58a.41.41,0,0,1-.19.45c-.12.07-.21-.08-.27-.2s-.15-.08-.21,0A.27.27,0,0,1,48.06,18.36Z"/><path class="foregroundColor" d="M24.47,6.66c-.06-.07-.2.07-.22-.06s.08-.16.14-.22a.14.14,0,0,1,.23,0c.09.16.18.11.31.06s.42-.06.63-.12.32.16.39.3-.09.25-.21.3a1.33,1.33,0,0,1-1.07.16c-.18-.07-.15-.28-.34-.32C24.3,6.79,24.47,6.75,24.47,6.66Z"/><path class="foregroundColor" d="M9.06,3.24a.56.56,0,0,0,.21-.09c.05,0,.12-.08.16,0s.31.2.46.31.14,0,.12-.13.05-.18.14-.22.14.06.18.14a.25.25,0,0,0,.22.12c.1,0,.22,0,.22.13s-.07.26-.2.25a3.47,3.47,0,0,0-1,.18.23.23,0,0,1-.31-.13c-.05-.12-.2-.06-.31-.08s-.24-.11-.16-.28Z"/><path class="foregroundColor" d="M12.46,3.91h-.07c-.15-.31-.43-.09-.62-.15A2.47,2.47,0,0,1,11,3.44a.15.15,0,0,1-.07-.2.11.11,0,0,1,.18,0c.12.09.21,0,.32,0a.48.48,0,0,1,.67.21.24.24,0,0,0,.33.09c.09,0,.15-.07.21,0s.14.16.1.26S12.57,3.88,12.46,3.91Z"/><path class="foregroundColor" d="M56.72,26a1,1,0,0,1-.28.44,1.09,1.09,0,0,0-.29.35.72.72,0,0,1-.46.3c-.12,0-.13-.13-.22-.18s0-.12,0-.17a1.64,1.64,0,0,1,.27-.26,1.41,1.41,0,0,0,.55-.56c0-.1.12-.13.22-.06S56.72,25.83,56.72,26Z"/><path class="foregroundColor" d="M12.26,4.52c0,.22-.11.28-.23.35a.21.21,0,0,1-.31,0,1.21,1.21,0,0,0-.22-.15c-.31-.17-.32-.17-.09-.43A.59.59,0,0,1,12,4.07C12.23,4.12,12.18,4.4,12.26,4.52Z"/><path class="foregroundColor" d="M48,20c0,.09,0,.16-.07.23s-.06,0-.09.06a.1.1,0,0,1-.05-.06,1.33,1.33,0,0,1,0-.88c.09-.14.2-.26.38-.21a.75.75,0,0,0,.54-.07c0,.21-.14.23-.27.24s-.12,0-.18,0-.32-.07-.31.11.19,0,.29,0,.17,0,.2.07-.07.09-.13.1-.12.1-.08.2.14.17,0,.26S48.16,20,48,20Z"/><path class="foregroundColor" d="M9.06,3.24l-.22.23c-.09,0-.18.16-.26.11s-.41-.07-.6-.14a.1.1,0,0,1,0-.17c.27-.08.45-.32.74-.35S9.12,2.86,9.06,3.24Z"/><path class="foregroundColor" d="M56.48,24.66c.35,0,.33.35.54.45s.15.13.28.09.16.06.1.17-.12.18-.18.27-.22.35-.32.27a.44.44,0,0,1-.16-.53.34.34,0,0,0,0-.36C56.63,24.92,56.57,24.81,56.48,24.66Z"/><path class="foregroundColor" d="M27.21,9.14l.13.13a1.57,1.57,0,0,0-.14.38c0,.15-.37.36-.53.33a.23.23,0,0,1-.1-.05c-.05-.06.09-.5.12-.57C26.81,9.12,27,9.13,27.21,9.14Z"/><path class="foregroundColor" d="M45.62,20.26c.37.1.77.1,1.14.19s.49.27.79.21a.16.16,0,0,1,.21.08.42.42,0,0,1-.32.09l-1.6-.25c-.16,0-.32-.08-.23-.31Z"/><path class="foregroundColor" d="M14.23,15.56a.53.53,0,0,1,.6-.17,3.16,3.16,0,0,1,1,.42c.05,0,.11.06.08.14s-.08.06-.13.06a.58.58,0,0,1-.44-.14,1,1,0,0,0-.93-.29S14.3,15.57,14.23,15.56Z"/><path class="foregroundColor" d="M51.29,12.11c-.12-.15-.08-.25,0-.41s.31-.5.65-.18c.07.07.17,0,.27,0a.58.58,0,0,1-.67.44c-.16,0-.17.15-.27.2Z"/><path class="foregroundColor" d="M11,2.21c.23.13.58.07.76.37,0,.06.09.12,0,.19a.18.18,0,0,1-.22,0A1,1,0,0,0,11,2.65c-.21,0-.18-.22-.23-.33S10.9,2.24,11,2.21Z"/>
</symbol>
<symbol id="zoom-in" viewBox="0 0 30 30">
<path class="foregroundColor" d="M19,20A8.52,8.52,0,1,1,20,19l4.75,4.75a.73.73,0,0,1,.22.53.75.75,0,0,1-.75.75.74.74,0,0,1-.53-.22ZM13.51,6.5a7,7,0,1,0,7,7A7,7,0,0,0,13.51,6.5Z"/><path class="foregroundColor" d="M17.05,12.82H14.19V10a.68.68,0,0,0-.68-.68.69.69,0,0,0-.69.68v2.86H10a.69.69,0,0,0-.69.69.68.68,0,0,0,.69.68h2.85v2.86a.69.69,0,0,0,.69.69.68.68,0,0,0,.68-.69V14.19h2.86a.68.68,0,0,0,.69-.68A.69.69,0,0,0,17.05,12.82Z"/>
</symbol>
<symbol id="zoom-out" viewBox="0 0 30 30">
<path class="foregroundColor" d="M19,20A8.52,8.52,0,1,1,20,19l4.75,4.75a.73.73,0,0,1,.22.53.75.75,0,0,1-.75.75.74.74,0,0,1-.53-.22ZM13.51,6.5a7,7,0,1,0,7,7A7,7,0,0,0,13.51,6.5Z"/><path class="foregroundColor" d="M17.05,12.82H10a.69.69,0,0,0-.69.69.68.68,0,0,0,.69.68h7.08a.68.68,0,0,0,.69-.68A.69.69,0,0,0,17.05,12.82Z"/>
</symbol>
</defs>
</svg>