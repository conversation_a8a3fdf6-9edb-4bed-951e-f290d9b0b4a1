import { Coordinate } from 'src/app/cursit/models/cursit.model';
import { GISElement } from './GISElement';
import { ShapeStyleInfo } from './models/gis.model';
import { ShapeType } from './models/map-element.model';

interface BaseMilitaryElement {
  sidc?: string;
  id?: string;
  label?: string;
  catCode?: string;
  coordinates: Coordinate | Coordinate[];
  reference?: string;
}

export interface FanAreaOptions {
  maximumRange?: number;
  minimumRange?: number;
  orientationAngle?: number;
  sectorAngle?: number;
}

export interface MilitaryElementFanArea extends BaseMilitaryElement {
  type: ShapeType.ARC_BAND;
  fanAreaOptions: FanAreaOptions;
}

export interface MilitaryElementEllipse extends BaseMilitaryElement {
  type: ShapeType.ELLIPSE;
}

export interface MilitaryElementPoint extends BaseMilitaryElement {
  type: ShapeType.POINT;
}

export interface MilitaryElementPolygon extends BaseMilitaryElement {
  type: ShapeType.POLYGON | ShapeType.POLYLINE;
}

export interface MilitaryElementCorridor extends BaseMilitaryElement {
  type: ShapeType.GEO_BUFFER;
  width: number;
}

export type MilitaryElement =
  | MilitaryElementFanArea
  | MilitaryElementEllipse
  | MilitaryElementPoint
  | MilitaryElementPolygon
  | MilitaryElementCorridor;

export interface ElementFactory<T> {
  shapeCreationStyleInfo: ShapeStyleInfo | undefined;

  createMilitaryElement(params: MilitaryElement): GISElement<T> | null;
}

export abstract class MapElementFactory<T> implements ElementFactory<T> {
  _shapeCreationStyleInfo: ShapeStyleInfo | undefined;

  set shapeCreationStyleInfo(shapeCreationStyleInfo: ShapeStyleInfo) {
    this._shapeCreationStyleInfo = shapeCreationStyleInfo;
  }

  get shapeCreationStyleInfo(): ShapeStyleInfo {
    return this._shapeCreationStyleInfo;
  }

  abstract createMilitaryElement(params: MilitaryElement): GISElement<T> | null;
}
