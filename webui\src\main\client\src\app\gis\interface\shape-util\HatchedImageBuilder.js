/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/9/2024, 11:53 AM
 */


const HTML5_CANVAS_SUPPORTED = function () {
  if ("object" !== typeof document) return false;
  let e = document.createElement("canvas");
  const t = !!(e.getContext && e.getContext("2d"));
  e = null;
  return t
}();

function createHTML5Canvas(e, t) {
  if (!HTML5_CANVAS_SUPPORTED) throw "HTML5 Canvas is not supported";
  const n = document.createElement("canvas");
  n.width = e;
  n.height = t;
  n.style.webkitUserSelect = "none";
  n.style.msUserSelect = "none";
  n.style.userSelect = "none";
  n.style.khtmlUserSelect = "none";
  n.style.mozUserSelect = "none";
  n.style.oUserSelect = "none";
  return n
}

const mimeType = {
  css: "text/css",
  js: "application/javascript",
  json: "application/json",
  geojson: "application/geo+json",
  png: "image/png",
  jpg: "image/jpg",
  jpeg: "image/jpeg",
  jp2: "image/jp2",
  gif: "image/gif",
  xml: "application/xml",
  gml: "application/gml+xml",
  kml: "application/vnd.google-earth.kml+xml",
  kmz: "application/vnd.google-earth.kmz",
  gltf: "model/gltf+json",
  svg: "image/svg+xml",
  wms: "application/vnd.ogc.se_xml",
  none: "text/plain;charset=UTF-8",
  form: "application/x-www-form-urlencoded;charset=UTF-8"
};


class HatchedImageBuilder {
  constructor() {
    this._patterns = void 0;
    this._width = void 0;
    this._height = void 0;
    this._lineWidth = void 0;
    this._lineColor = void 0;
    this._backgroundColor = void 0;
    this._patterns = [HatchedImageBuilder.Pattern.BACK_SLASH];
    this._width = 10;
    this._height = 10;
    this._lineWidth = 1;
    this._lineColor = "rgb(64,64,64)";
    this._backgroundColor = "rgb(192,192,192)"
  }

  build() {
    const t = Math.max(this._width, this._lineWidth + 2);
    const e = Math.max(this._height, this._lineWidth + 2);
    const i = createHTML5Canvas(t, e);
    const o = i.getContext("2d", {willReadFrequently: true});
    if (o) {
      if (-1 != this._patterns.indexOf(HatchedImageBuilder.Pattern.BACKGROUND)) {
        o.fillStyle = this._backgroundColor;
        o.fillRect(0, 0, t, e)
      }
      o.strokeStyle = this._lineColor;
      o.lineWidth = this._lineWidth;
      if (-1 != this._patterns.indexOf(HatchedImageBuilder.Pattern.BACK_SLASH)) {
        o.beginPath();
        o.moveTo(-t, -e);
        o.lineTo(2 * t - 1, 2 * e - 1);
        o.moveTo(-t, 0);
        o.lineTo(t - 1, 2 * e - 1);
        o.moveTo(0, -e);
        o.lineTo(2 * t - 1, e - 1);
        o.closePath();
        o.stroke()
      }
      if (-1 != this._patterns.indexOf(HatchedImageBuilder.Pattern.SLASH)) {
        o.beginPath();
        o.moveTo(-t, e - 1);
        o.lineTo(t - 1, -e);
        o.moveTo(-t, 2 * e - 1);
        o.lineTo(2 * t - 1, -e);
        o.moveTo(0, 2 * e - 1);
        o.lineTo(2 * t - 1, 0);
        o.closePath();
        o.stroke()
      }
      if (-1 != this._patterns.indexOf(HatchedImageBuilder.Pattern.HORIZONTAL)) {
        const i = e / 2;
        o.beginPath();
        o.moveTo(0, i);
        o.lineTo(t, i);
        o.closePath();
        o.stroke()
      }
      if (-1 != this._patterns.indexOf(HatchedImageBuilder.Pattern.VERTICAL)) {
        const i = t / 2;
        o.beginPath();
        o.moveTo(i, 0);
        o.lineTo(i, e);
        o.closePath();
        o.stroke()
      }
      const h = document.createElement("img");
      h.src = i.toDataURL(mimeType.png);
      return h
    } else return new HTMLImageElement
  }

  patterns(t) {
    this._patterns = t || this._patterns;
    return this
  }

  patternSize(t, e) {
    this._width = t || this._width;
    this._height = e || this._height;
    return this
  }

  lineWidth(t) {
    this._lineWidth = t || this._lineWidth;
    return this
  }

  lineColor(t) {
    this._lineColor = t || this._lineColor;
    return this
  }

  backgroundColor(t) {
    this._backgroundColor = t || this._backgroundColor;
    return this
  }
}

(function (t) {
  let e = function (t) {
    t["HORIZONTAL"] = "horizontal";
    t["VERTICAL"] = "vertical";
    t["SLASH"] = "slash";
    t["BACK_SLASH"] = "back_slash";
    t["BACKGROUND"] = "background";
    return t
  }({});
  t.Pattern = e
})(HatchedImageBuilder || (HatchedImageBuilder = {}));
export {HatchedImageBuilder};
