<nexus-page pageTitle="SETTINGS" icon="settings">
  <ng-template #headerSlot>
    <button mat-stroked-button (click)="resetAllSettings()" matTooltip="Reset All settings to default">Reset
      All</button>
  </ng-template>

  <vcci-conn-panel-settings [settings]="(settings$ |async).connPanel" (onSettingsChanged)="saveChange($event)" />
  <hr style="margin: 0 10px; opacity: 0.2;" />
  <vcci-cursit-settings [settings]="(settings$ |async).cursit" (onSettingsChanged)="saveChange($event)" />
  <hr style="margin: 0 10px; opacity: 0.2;" />
  <vcci-config-dialog-settings [settings]="(settings$ |async).configDialog" (onSettingsChanged)="saveChange($event)" />
  <hr style="margin: 0 10px; opacity: 0.2;" />
  <vcci-layout-settings [settings]="(settings$ |async).layout" (onSettingsChanged)="saveChange($event)" />
</nexus-page>
