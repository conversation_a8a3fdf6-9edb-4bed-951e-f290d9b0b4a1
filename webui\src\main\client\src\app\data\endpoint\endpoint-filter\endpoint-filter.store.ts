import { HttpErrorResponse } from '@angular/common/http';
import { Inject, Injectable, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

import { ComponentStore } from '@ngrx/component-store';
import { tapResponse } from '@ngrx/operators';

import { SFMap } from '@simfront/common-libs';

import {
  catchError,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  filter,
  first,
  map,
  of,
  pipe,
  switchMap,
  tap,
  withLatestFrom
} from 'rxjs';

import { HttpErrorComponent } from '../../../shared/http-error/http-error.component';

import { DataPath, isValidDataPath } from '../../data-path/data-path.model';
import { DataService } from '../../data.service';
import { Entity, extractSymbolCode } from '../../entity/entity.model';

import { OutboundObject } from '../association/association.model';
import {
  ConfigureInstruction,
  Endpoint,
  getEndpointId
} from '../endpoint.model';
import {
  checkFilterLocation,
  createEmptyFilter,
  Filter,
  FilterGroup,
  FilterInfo,
  GeoFilter
} from '../filter.model';
import { removeKey } from '../../../shared/util/util.model';
import {
  linkToGlobalState,
  unlinkFromGlobalState
} from '../../../reducers/component-store.reducer';
import { Store } from '@ngrx/store';
import { cursitActions } from '../../../cursit/actions/cursit.actions';
import {
  GeoFilterPoints,
  selectIsGeoDraw,
  selectNewGeoAreaDrawn
} from '../../../cursit/reducers/cursit.reducer';
import { getEntryPointName, getServiceId } from '../../entity/id.model';

type MyEntity = Entity & { assoc: OutboundObject | undefined };
type TreeEntity = Entity & {
  type: 'Entity';
  assoc: OutboundObject | undefined;
  symbol: string;
  fullRoute: boolean;
  children: [];
};

interface EndpointNode {
  type: 'Endpoint';
  name: string;
  fullRoute: boolean;
  children: TreeEntity[];
}

interface ServiceNode {
  type: 'Service';
  name: string;
  children: EndpointNode[];
}

export interface EndpointFilterState {
  endpoint: Endpoint | undefined;
  filters: FilterGroup[];
  filterInfos: FilterInfo[];
  selected:
    | {
        key: number;
        info: string;
      }
    | undefined;
  entities: MyEntity[];
  routes: DataPath[];
  busy: boolean;
}

@Injectable()
export class EndpointFilterStore
  extends ComponentStore<EndpointFilterState>
  implements OnDestroy
{
  constructor(
    @Inject(MAT_DIALOG_DATA) private initialState: EndpointFilterState,
    private data: DataService,
    private _snackBar: MatSnackBar,
    private store: Store
  ) {
    super({
      ...initialState,
      filters:
        initialState.filters.length === 0
          ? [{}]
          : JSON.parse(JSON.stringify(initialState.filters)),
      routes: [],
      entities: [],
      busy: false
    });
    linkToGlobalState(this.state$, 'EndpointFilterStore', this.store);
  }

  readonly busy$ = this.select((state) => state.busy);
  readonly endpoint$ = this.select((state) => state.endpoint);
  readonly filterInfos$ = this.select((state) =>
    state.filterInfos.filter((f) =>
      checkFilterLocation(f, state.endpoint.capability)
    )
  );

  readonly filters$ = this.select((state) => state.filters);
  readonly nonEmptyFilters$ = this.filters$.pipe(
    map((groups) => groups.filter((group) => Object.values(group).length > 0))
  );

  readonly selectedInfo$ = this.select((state) => {
    const info: FilterInfo | undefined =
      this._filterInfoMap(state)[state.selected?.info || ''];
    return info;
  });

  readonly selectedFilterValue$ = this.select((state) => {
    if (state.selected === undefined) {
      return undefined;
    }
    const group = state.filters[state.selected.key];
    if (group === undefined) {
      return undefined;
    }
    const filter = group[state.selected?.info];
    if (filter === undefined) {
      const info = state.filterInfos.find(
        (f) => f.name === state.selected.info
      );
      return createEmptyFilter(info, state.selected.key);
    }
    return filter;
  });

  readonly candidateEntities$ = this.select((state) => state.entities);
  readonly routes$ = this.select((state) => state.routes).pipe(
    distinctUntilChanged(),
    withLatestFrom(this.endpoint$),
    map(([routes, endpoint]) =>
      routes.filter(
        (r) =>
          isValidDataPath(r) &&
          (r.pathEnd.endpoint === endpoint.name ||
            r.pathStart.endpoint === endpoint.name)
      )
    )
  );
  readonly treeData$ = combineLatest([
    this.candidateEntities$,
    this.routes$
  ]).pipe(
    map(([entities, routes]) => {
      const nodesMap: SFMap<ServiceNode> = {};
      const endpointsMap: SFMap<EndpointNode> = {};
      entities.forEach((entity) => {
        const serviceName = getServiceId(entity.uniqueId);
        const endpointName = getEntryPointName(entity.uniqueId);
        const addService = !nodesMap[serviceName];
        const service: ServiceNode = nodesMap[serviceName] ?? {
          type: 'Service',
          name: serviceName,
          children: []
        };
        if (addService) {
          nodesMap[serviceName] = service;
        }
        const add = !endpointsMap[endpointName];
        const fullRoute =
          routes.find(
            (r) =>
              r.pathEnd.endpoint === endpointName ||
              r.pathStart.endpoint === endpointName
          )?.isTransferAllDataRoute ?? false;

        const endpoint: EndpointNode = endpointsMap[endpointName] ?? {
          type: 'Endpoint',
          name: endpointName,
          fullRoute,
          children: []
        };
        endpoint.children.push({
          ...entity,
          type: 'Entity',
          symbol: extractSymbolCode(entity),
          children: [],
          fullRoute
        });
        if (add) {
          endpointsMap[endpointName] = endpoint;
          service.children.push(endpoint);
        }
      });
      return Object.values(nodesMap);
    })
  );

  readonly newGeoAreaDrawn$ = this.store.select(selectNewGeoAreaDrawn);
  readonly isGeoDrawn$ = this.store.select(selectIsGeoDraw);

  private _filterInfoMap = (state: EndpointFilterState): SFMap<FilterInfo> =>
    state.filterInfos.reduce<SFMap<FilterInfo>>((acc, info) => {
      acc[info.name] = info;
      return acc;
    }, {});

  readonly removeGroup = this.updater((state, index: number) => ({
    ...state,
    filters: [...state.filters.filter((_, i) => i !== index)]
  }));

  readonly addGroup = this.updater((state) => ({
    ...state,
    filters: [...state.filters, {}]
  }));

  readonly resetAll = this.updater((state) => ({
    ...state,
    filters:
      this.initialState.filters.length === 0
        ? [{}]
        : JSON.parse(JSON.stringify(this.initialState.filters))
  }));

  readonly selectFilter = this.updater(
    (state, selected: { key: number; info: string }) => ({
      ...state,
      selected
    })
  );

  readonly updateFilter = this.updater((state, filter: Filter) => ({
    ...state,
    filters: [
      ...state.filters.map((g, i) => {
        if (i === state.selected.key) {
          return { ...g, [state.selected.info]: filter };
        }
        return g;
      })
    ]
  }));

  readonly removeFilter = this.updater((state, _: Filter) => ({
    ...state,
    filters: [
      ...state.filters.map((g, i) => {
        if (i === state.selected.key) {
          return removeKey(g, state.selected.info);
        }
        return g;
      })
    ]
  }));

  private readonly updateCandidateEntities = this.updater(
    (state, entities: MyEntity[]) => ({
      ...state,
      busy: false,
      entities
    })
  );

  /**
   * For each filter group, find the filters that are geo filters
   * @private
   */
  readonly getActiveGeoFilter$ = this.selectedFilterValue$
    .pipe(
      filter((filter) => filter && filter.type === 'GEO_FILTER'),
      map((filter) => {
        const geoFilter = filter as GeoFilter;
        const points: GeoFilterPoints = {
          p1: {
            latitude: geoFilter.firstPointLatitude,
            longitude: geoFilter.firstPointLongitude
          },
          p2: {
            latitude: geoFilter.secondPointLatitude,
            longitude: geoFilter.secondPointLongitude
          }
        };
        this.store.dispatch(cursitActions.activeGeoFilterChanged(points));
      }),
      catchError((err) => {
        console.log(err);
        return [];
      })
    )
    .subscribe();

  readonly getCandidateEntities = this.effect<void>(
    pipe(
      tap(() => this.patchState({ busy: true })),
      combineLatestWith(this.endpoint$),
      map(([_, endpoint]) => endpoint),
      filter((endpoint) => endpoint !== undefined),
      switchMap((endpoint) =>
        of(endpoint).pipe(
          combineLatestWith(
            this.data.endpoint.getExternalObjects(
              getEndpointId(endpoint),
              endpoint.capability === 'DATA_RECEIVER'
                ? 'ALL_LISTENERS'
                : 'ALL_SENDERS'
            ),
            this.data.endpoint.readAssociationsAPI(getEndpointId(endpoint)),
            this.data.entity.entities$.pipe(first())
          )
        )
      ),
      map(([_, exObjs, associations, entities]) => {
        const filteredExObjs = exObjs
          .filter((o) => entities[o.uniqueId] === undefined)
          .map((o) => ({
            ...o,
            assoc: associations.find((a) => a.srcId === o.uniqueId)
          }));
        const associatedEntities = Object.values(entities).map((e) => ({
          ...e,
          assoc: associations.find((a) => a.srcId === e.uniqueId)
        }));
        return [...filteredExObjs, ...associatedEntities];
      }),
      tapResponse(
        (entities: MyEntity[]) => this.updateCandidateEntities(entities),
        (_) => this.patchState({ entities: [], busy: false })
      )
    )
  );

  readonly getRoutes = this.effect<void>(
    pipe(
      combineLatestWith(this.endpoint$, this.data.node.entities$.pipe(first())),
      filter(([endpoint]) => endpoint === undefined),
      map(([_, endpoint, nodes]) => nodes[endpoint.node]?.dataPaths ?? []),
      tapResponse(
        (routes: DataPath[]) => this.patchState({ routes }),
        (_) =>
          this._errorHandler({
            name: 'Local Error',
            message: 'Failed to retrieve routes.'
          })
      )
    )
  );

  readonly submit = this.effect<void>(
    pipe(
      tap(() => this.patchState({ busy: true })),
      withLatestFrom(this.endpoint$, this.nonEmptyFilters$),
      map(([_, endpoint, filters]) => ({
        id: getEndpointId(endpoint),
        instruction: ConfigureInstruction.UpdateFilters,
        endpoint: { name: endpoint.name, node: endpoint.node, filters }
      })),
      switchMap((result) =>
        of(result).pipe(
          tapResponse(
            (payload) => {
              // this.matDialogRef.close();
              this.patchState({ busy: false });
              this.data.endpoint.configureEndpoint(payload);
            },
            (_) =>
              this._errorHandler({
                name: 'Local Error',
                message: 'Failed to save endpoint filters.'
              })
          )
        )
      )
    )
  );

  private _errorHandler(her: HttpErrorResponse | Error): void {
    this.patchState({ busy: false });
    this._snackBar.openFromComponent(HttpErrorComponent, {
      data: her,
      panelClass: ['mat-toolbar', 'error']
    });
  }

  override ngOnDestroy() {
    super.ngOnDestroy();
    unlinkFromGlobalState('EndpointFilterStore', this.store);
  }
}
