import {
  FunctionIdMappings,
  FunctionIdMappingsByValue
} from './milstd-2525b.model';

export const EntityTypes = ['Unit', 'Materiel'] as const;

export const SymbolSets = [
  'Warfighting',
  'Signals Intelligence',
  'Stability Operations',
  'Emergency Management',
  'Tactical Graphic',
  'Unknown'
] as const;

export type SymbolSet = (typeof SymbolSets)[number];

export const SymbolSetValues = [
  'S', // Warfighting
  'I', // Signals Intelligence
  'O', // Stability Operations
  'E' // Emergency Management
] as const;

export const Affiliations = [
  'Pending',
  'Unknown',
  'Friend',
  'Neutral',
  'Hostile',
  'Assumed Friend',
  'Suspect',
  'Exercise Pending',
  'Exercise Unknown',
  'Exercise Friend',
  'Exercise Neutral',
  'Exercise Assumed Friend',
  'Joker',
  'Faker'
] as const;

export type Affiliation = (typeof Affiliations)[number];

export const AffiliationValues = [
  'P', // Pending
  'U', // Unknown
  'F', // Friend
  'N', // Neutral
  'H', // Hostile
  'A', // Assumed Friend
  'S', // Suspect
  'G', // Exercise Pending
  'W', // Exercise Unknown
  'D', // Exercise Friend
  'L', // Exercise Neutral
  'M', // Exercise Assumed Friend
  'J', // Joker
  'K' // Faker
] as const;

export const BattleDimensions = [
  'Space',
  'Air',
  'Ground',
  'Sea Surface',
  'Subsurface',
  'SOF (Special Operations Forces)',
  'Other'
] as const;

export type BattleDimension = (typeof BattleDimensions)[number];

export const BattleDimensionValues = [
  'P', // Space
  'A', // Air
  'G', // Ground
  'S', // Sea Surface
  'U', // Subsurface
  'F', // SOF (Special Operations Forces)
  'X' // Other
] as const;

export const Statuses = [
  'Present',
  'Planned/Anticipated',
  'Unspecified'
] as const;

export type Status = (typeof Statuses)[number];

export const StatusValues = [
  'P', // Present
  'A', // Planned/Anticipated
  '-' // Unspecified
] as const;

export const Echelons = [
  'Team/Crew',
  'Squad',
  'Section',
  'Platoon/Detachment',
  'Company/Battery/Troop',
  'Battalion/Squadron',
  'Regiment/Group',
  'Brigade',
  'Division',
  'Corps',
  'Army',
  'Army Group/Front',
  'Region',
  'Command',
  'Unspecified'
] as const;

export type Echelon = (typeof Echelons)[number];

export const EchelonValues = [
  'A', // Team/Crew
  'B', // Squad
  'C', // Section
  'D', // Platoon/Detachment
  'E', // Company/Battery/Troop
  'F', // Battalion/Squadron
  'G', // Regiment/Group
  'H', // Brigade
  'I', // Division
  'J', // Corps
  'K', // Army
  'L', // Army Group/Front
  'M', // Region
  'N', // Command
  '*' // Unspecified
] as const;

export const Mobilities = [
  'Wheeled (Limited Cross-Country)',
  'Wheeled (Cross-Country)',
  'Tracked',
  'Wheeled and Tracked Combination',
  'Towed',
  'Railway',
  'Over-Snow (Prime Mover)',
  'Sled',
  'Pack Animals',
  'Barge',
  'Amphibious',
  'Other',
  'Unspecified'
] as const;

export type Mobility = (typeof Mobilities)[number];

export const MobilityValues = [
  'O', // Wheeled (Limited Cross-Country)
  'P', // Wheeled (Cross-Country)
  'Q', // Tracked
  'R', // Wheeled and Tracked Combination
  'S', // Towed
  'T', // Railway
  'U', // Over-Snow (Prime Mover)
  'V', // Sled
  'W', // Pack Animals
  'X', // Barge
  'Y', // Amphibious
  'Z', // Other
  '*' // Unspecified
] as const;

export const CountryNames = [
  'Unspecified',
  'Aruba', // AA
  'Antigua and Barbuda', // AC
  'United Arab Emirates', // AE
  'Afghanistan', // AF
  'Algeria', // AG
  'Azerbaijan', // AJ
  'Albania', // AL
  'Armenia', // AM
  'Andorra', // AN
  'Angola', // AO
  'American Samoa', // AQ
  'Argentina', // AR
  'Australia', // AS
  'Ashmore and Cartier Islands', // AT
  'Austria', // AU
  'Anguilla', // AV
  'Antarctica', // AY
  'Bahrain', // BA
  'Barbados', // BB
  'Botswana', // BC
  'Bermuda', // BD
  'Belgium', // BE
  'Bahamas, The', // BF
  'Bangladesh', // BG
  'Belize', // BH
  'Bosnia and Herzegovina', // BK
  'Bolivia', // BL
  'Burma', // BM
  'Benin', // BN
  'Belarus', // BO
  'Solomon Islands', // BP
  'Navassa Island', // BQ
  'Brazil', // BR
  'Bassas da India', // BS
  'Bhutan', // BT
  'Bulgaria', // BU
  'Bouvet Island', // BV
  'Brunei', // BX
  'Burundi', // BY
  'Canada', // CA
  'Cambodia', // CB
  'Chad', // CD
  'Sri Lanka', // CE
  'Congo', // CF
  'Congo, Democratic Republic of the', // CG
  'China', // CH
  'Chile', // CI
  'Cayman Islands', // CJ
  'Cocos (Keeling) Islands', // CK
  'Cameroon', // CM
  'Comoros', // CN
  'Colombia', // CO
  'Northern Mariana Islands', // CQ
  'Coral Sea Islands', // CR
  'Costa Rica', // CS
  'Central African Republic', // CT
  'Cuba', // CU
  'Cape Verde', // CV
  'Cook Islands', // CW
  'Cyprus', // CY
  'Denmark', // DA
  'Djibouti', // DJ
  'Dominica', // DO
  'Jarvis Island', // DQ
  'Dominican Republic', // DR
  'Dhekelia', // DX
  'Ecuador', // EC
  'Egypt', // EG
  'Ireland', // EI
  'Equatorial Guinea', // EK
  'Estonia', // EN
  'Eritrea', // ER
  'El Salvador', // ES
  'Ethiopia', // ET
  'Europa Island', // EZ
  'Czechia', // EZ
  'French Guiana', // FG
  'Finland', // FI
  'Fiji', // FJ
  'Falkland Islands', // FK
  'Micronesia', // FM
  'Faroe Islands', // FO
  'French Polynesia', // FP
  'Baker Island', // FQ
  'France', // FR
  'French Southern and Antarctic Lands', // FS
  'Gambia', // GA
  'Gabon', // GB
  'Georgia', // GG
  'Ghana', // GH
  'Gibraltar', // GI
  'Grenada', // GJ
  'Guernsey', // GK
  'Greenland', // GL
  'Germany', // GM
  'Glorioso Islands', // GO
  'Guadeloupe', // GP
  'Guam', // GQ
  'Greece', // GR
  'Guatemala', // GT
  'Guinea', // GV
  'Guyana', // GY
  'Gaza Strip', // GZ
  'Haiti', // HA
  'Hong Kong', // HK
  'Heard Island and McDonald Islands', // HM
  'Honduras', // HO
  'Howland Island', // HQ
  'Croatia', // HR
  'Hungary', // HU
  'Iceland', // IC
  'Indonesia', // ID
  'Isle of Man', // IM
  'India', // IN
  'British Indian Ocean Territory', // IO
  'Clipperton Island', // IP
  'Iran', // IR
  'Israel', // IS
  'Italy', // IT
  'Ivory Coast', // IV
  'Iraq', // IZ
  'Japan', // JA
  'Jersey', // JE
  'Jamaica', // JM
  'Jan Mayen', // JN
  'Jordan', // JO
  'Johnston Atoll', // JQ
  'Juan de Nova Island', // JU
  'Kenya', // KE
  'Kyrgyzstan', // KG
  'Korea, North', // KN
  'Kingman Reef', // KQ
  'Kiribati', // KR
  'Korea, South', // KS
  'Christmas Island', // KT
  'Kuwait', // KU
  'Kosovo', // KV
  'Kazakhstan', // KZ
  'Laos', // LA
  'Lebanon', // LE
  'Latvia', // LG
  'Lithuania', // LH
  'Liberia', // LI
  'Slovakia', // LS
  'Palmyra Atoll', // LQ
  'Liechtenstein', // LS
  'Lesotho', // LT
  'Luxembourg', // LU
  'Libya', // LY
  'Madagascar', // MA
  'Martinique', // MB
  'Macau', // MC
  'Moldova', // MD
  'Mayotte', // MF
  'Mongolia', // MG
  'Montserrat', // MH
  'Malawi', // MI
  'Montenegro', // MJ
  'North Macedonia', // MK
  'Mali', // ML
  'Monaco', // MN
  'Morocco', // MO
  'Mauritius', // MP
  'Midway Islands', // MQ
  'Mauritania', // MR
  'Malta', // MT
  'Oman', // MU
  'Maldives', // MV
  'Mexico', // MX
  'Malaysia', // MY
  'Mozambique', // MZ
  'New Caledonia', // NC
  'Niue', // NE
  'Northfolk Islands', // NF
  'Niger', // NG
  'Vanuatu', // NH
  'Nigeria', // NI
  'Netherlands', // NL
  'Sint Maarten', // NM
  'Norway', // NO
  'Nepal', // NP
  'Nauru', // NR
  'Suriname', // NS
  'Netherlands Antilles', // NT
  'Nicaragua', // NU
  'New Zealand', // NZ
  'South Sudan', // OD
  'Paraguay', // PA
  'Pitcairn Islands', // PC
  'Peru', // PE
  'Paracel Islands', // PF
  'Spratly Islands', // PG
  'Etorofu, Habomai, Kunashiri, and Shikotan Islands', // PJ
  'Pakistan', // PK
  'Poland', // PL
  'Panama', // PM
  'Portugal', // PO
  'Papua New Guinea', // PP
  'Palau', // PS
  'Guinea-Bissau', // PU
  'Qatar', // QA
  'Reunion', // RE
  'Serbia', // RI
  'Marshall Islands', // RM
  'Saint Martin', // RN
  'Romania', // RO
  'Philippines', // RP
  'Puerto Rico', // RQ
  'Russia', // RS
  'Rwanda', // RQ
  'Saudi Arabia', // SA
  'Saint Pierre and Miquelon', // SB
  'Saint Kitts and Nevis', // SC
  'Seychelles', // SE
  'South Africa', // SF
  'Senegal', // SG
  'Saint Helena, Ascension, and Tristan da Cunha', // SH
  'Slovenia', // SI
  'Sierra Leone', // SL
  'San Marino', // SM
  'Singapore', // SN
  'Somalia', // SO
  'Spain', // SP
  'Suriname', // SR
  'Saint Lucia', // ST
  'Sudan', // SU
  'Svalbard', // SV
  'Sweden', // SW
  'South Georgia and South Sandwich Islands', // SX
  'Syria', // SY
  'Switzerland', // SZ
  'Saint Barthelemy', // TB
  'Trinidad and Tobago', // TD
  'Tromelin Island', // TE
  'Thailand', // TH
  'Tajikistan', // TI
  'Turks and Caicos Islands', // TK
  'Tokelau', // TL
  'Tonga', // TN
  'Togo', // TO
  'Sao Tome and Principe', // TP
  'Tunisia', // TS
  'Timor-Leste', // TT
  'Türkiye', // TU
  'Tuvalu', // TV
  'Taiwan', // TW
  'Turkmenistan', // TX
  'Tanzania', // TZ
  'Curaçao', // UC
  'Uganda', // UG
  'United Kingdom', // UK
  'Ukraine', // UP
  'United States', // US
  'Burkina Faso', // UV
  'Uruguay', // UY
  'Uzbekistan', // UZ
  'Saint Vincent and the Grenadines', // VC
  'Venezuela', // VE
  'British Virgin Islands', // VI
  'Vietnam', // VM
  'United States Virgin Islands', // VQ
  'Vatican City', // VT
  'Namibia', // WA
  'West Bank', // WE
  'Wallis and Futuna', // WF
  'Western Sahara', // WI
  'Wake Island', // WQ
  'Samoa', // WS
  'Eswatini', // WZ
  'Yemen', // YM
  'Zambia', // ZA
  'Zimbabwe' // ZI
];

export const CountryCodes = [
  '**',
  'AA',
  'AC',
  'AE',
  'AF',
  'AG',
  'AJ',
  'AL',
  'AM',
  'AN',
  'AO',
  'AQ',
  'AR',
  'AS',
  'AT',
  'AU',
  'AV',
  'AX',
  'AY',
  'BA',
  'BB',
  'BC',
  'BD',
  'BE',
  'BF',
  'BG',
  'BH',
  'BK',
  'BL',
  'BM',
  'BN',
  'BO',
  'BP',
  'BR',
  'BS',
  'BT',
  'BU',
  'BV',
  'BX',
  'BY',
  'CA',
  'CB',
  'CD',
  'CE',
  'CF',
  'CG',
  'CH',
  'CI',
  'CJ',
  'CK',
  'CM',
  'CN',
  'CO',
  'CQ',
  'CR',
  'CS',
  'CT',
  'CU',
  'CV',
  'CW',
  'CY',
  'DA',
  'DJ',
  'DO',
  'DQ',
  'DR',
  'DX',
  'EC',
  'EG',
  'EI',
  'EK',
  'EN',
  'ER',
  'ES',
  'ET',
  'EU',
  'EZ',
  'FG',
  'FI',
  'FJ',
  'FK',
  'FM',
  'FO',
  'FP',
  'FQ',
  'FR',
  'FS',
  'GA',
  'GB',
  'GG',
  'GH',
  'GI',
  'GJ',
  'GK',
  'GL',
  'GM',
  'GO',
  'GP',
  'GQ',
  'GR',
  'GT',
  'GV',
  'GY',
  'GZ',
  'HA',
  'HK',
  'HM',
  'HO',
  'HQ',
  'HR',
  'HU',
  'IC',
  'ID',
  'IM',
  'IN',
  'IO',
  'IP',
  'IR',
  'IS',
  'IT',
  'IV',
  'IZ',
  'JA',
  'JE',
  'JM',
  'JN',
  'JO',
  'JQ',
  'JU',
  'KE',
  'KG',
  'KN',
  'KQ',
  'KR',
  'KS',
  'KT',
  'KU',
  'KZ',
  'LA',
  'LE',
  'LG',
  'LH',
  'LI',
  'LO',
  'LQ',
  'LS',
  'LT',
  'LU',
  'LV',
  'LY',
  'MA',
  'MB',
  'MC',
  'MD',
  'MF',
  'MG',
  'MH',
  'MI',
  'MJ',
  'MK',
  'ML',
  'MN',
  'MO',
  'MP',
  'MQ',
  'MR',
  'MT',
  'MU',
  'MV',
  'MX',
  'MY',
  'MZ',
  'NC',
  'NE',
  'NF',
  'NG',
  'NH',
  'NI',
  'NL',
  'NM',
  'NO',
  'NP',
  'NR',
  'NS',
  'NT',
  'NU',
  'NZ',
  'OD',
  'PA',
  'PC',
  'PE',
  'PF',
  'PG',
  'PJ',
  'PK',
  'PL',
  'PM',
  'PO',
  'PP',
  'PS',
  'PU',
  'QA',
  'RE',
  'RI',
  'RM',
  'RN',
  'RO',
  'RP',
  'RQ',
  'RS',
  'RW',
  'SA',
  'SB',
  'SC',
  'SE',
  'SF',
  'SG',
  'SH',
  'SI',
  'SL',
  'SM',
  'SN',
  'SO',
  'SP',
  'SR',
  'ST',
  'SU',
  'SV',
  'SW',
  'SX',
  'SY',
  'SZ',
  'TB',
  'TD',
  'TE',
  'TH',
  'TI',
  'TK',
  'TL',
  'TN',
  'TO',
  'TP',
  'TS',
  'TT',
  'TU',
  'TV',
  'TW',
  'TX',
  'TZ',
  'UC',
  'UG',
  'UK',
  'UP',
  'US',
  'UV',
  'UY',
  'UZ',
  'VC',
  'VE',
  'VI',
  'VM',
  'VQ',
  'VT',
  'WA',
  'WE',
  'WF',
  'WI',
  'WQ',
  'WS',
  'WZ',
  'YM',
  'ZA',
  'ZI'
];

export const OperationalConditions = [
  'Fully Capable',
  'Damaged',
  'Destroyed',
  'Full to Capacity',
  'Unspecified'
] as const;

export type OperationalCondition = (typeof OperationalConditions)[number];

export const OperationalConditionValues = [
  'F', // Fully Capable
  'D', // Damaged
  'X', // Destroyed
  'C', // Full to Capacity
  '*' // Unspecified
] as const;

export const milsymCodeFormFields: EntityFormField<string>[] = [
  {
    label: 'Milsym Code',
    name: 'milsymCode',
    type: 'text'
  }
];

export const milsymFormFields: EntityFormField<string>[] = [
  {
    label: 'Symbols',
    options: SymbolSets,
    name: 'symbolSet',
    type: 'select'
  },
  {
    label: 'Affiliations',
    options: Affiliations,
    name: 'affiliation',
    type: 'select'
  },
  {
    label: 'Battle Dimensions',
    options: BattleDimensions,
    name: 'dimension',
    type: 'select'
  },
  { label: 'Status', options: Statuses, name: 'status', type: 'select' },
  {
    label: 'Function ID',
    options: ['Unknown'],
    name: 'functionID',
    type: 'select'
  },
  {
    label: 'Mobility Indicators',
    options: Mobilities,
    name: 'mobility',
    type: 'select'
  },
  { label: 'Country', options: CountryNames, name: 'country', type: 'select' },
  {
    label: 'Echelons',
    options: ['Unspecified'],
    name: 'echelon',
    type: 'select'
  },
  {
    label: 'Operational Conditions',
    options: OperationalConditions,
    name: 'operationalCondition',
    type: 'select'
  }
];

/**
 * 1	S	Symbol Set (e.g., S = Warfighting)
 * 2	F	Standard Identity/Affiliation (F = Friend)
 * 3	A	Battle Dimension (A = Air)
 * 4	M	Status (M = Present)
 * 5-10	XXXXXX	Function ID (e.g., MF---- = Fixed-Wing Aircraft)
 * 11	R	Mobility Indicator (R = Tracked)
 * 12	F	Echelon Indicator (F = Battalion/Squadron)
 * 13-14	**	Country Code (2 characters)
 * 15	*	Operational Condition
 */
export interface MILSTD2525 {
  symbolSet: SymbolSet;
  affiliation: Affiliation;
  dimension: BattleDimension;
  status: Status;
  functionId: string;
  echelon: Echelon;
  mobility: Mobility;
  countryCode: string;
  operationalCondition: OperationalCondition;
}

export interface EntityFormField<T> {
  label: string;
  options?: readonly T[];
  name: string;
  type: 'text' | 'select';
}

export const isValidCode = (code: string) => code && code.length >= 15;

export const generateCode = (
  symbolSet: SymbolSet,
  affiliation: Affiliation,
  dimension: BattleDimension,
  status: Status,
  echelon: Echelon,
  mobility: Mobility,
  countryCode: string,
  operationalConditional: OperationalCondition,
  functionID: string
) => {
  const symbolSetValue = SymbolSetValues[SymbolSets.indexOf(symbolSet)] ?? null;
  const affiliationValue =
    AffiliationValues[Affiliations.indexOf(affiliation)] ?? null;
  const dimensionValue =
    BattleDimensionValues[BattleDimensions.indexOf(dimension)] ?? null;
  const statusValue = StatusValues[Statuses.indexOf(status)] ?? null;
  const echelonValue = EchelonValues[Echelons.indexOf(echelon)] ?? null;
  const mobilityValue = MobilityValues[Mobilities.indexOf(mobility)] ?? null;
  const countryCodeValue =
    CountryCodes[CountryNames.indexOf(countryCode)] ?? null;
  const operationalConditionValue =
    OperationalConditionValues[
      OperationalConditions.indexOf(operationalConditional)
    ] ?? null;

  const code =
    symbolSetValue +
    affiliationValue +
    dimensionValue +
    statusValue +
    functionID +
    mobilityValue +
    echelonValue +
    countryCodeValue +
    operationalConditionValue;

  if (!isValidCode(code)) {
    throw new Error('Invalid MIL-STD-2525 code.');
  }
  return code;
};

export const getSymbolSet = (code: string): SymbolSet => {
  if (isTacticalGraphic(code)) {
    return 'Tactical Graphic';
  }
  const scheme = code[0]; // The first character of the code
  const index = SymbolSetValues.indexOf(
    scheme as (typeof SymbolSetValues)[number]
  );
  return index !== -1 ? SymbolSets[index] : 'Unknown';
};

export const getAffiliation = (code: string): Affiliation => {
  const affiliation = code[1];
  const index = AffiliationValues.indexOf(
    affiliation as (typeof AffiliationValues)[number]
  );
  return index !== -1 ? Affiliations[index] : 'Unknown';
};

export const getBattleDimension = (code: string): BattleDimension => {
  const dimensionChar = code[2]; // The third character of the code
  const index = BattleDimensionValues.indexOf(
    dimensionChar as (typeof BattleDimensionValues)[number]
  );
  return index !== -1 ? BattleDimensions[index] : 'Other';
};

export const getStatus = (code: string): Status => {
  const statusChar = code[3]; // The fourth character of the code
  const index = StatusValues.indexOf(
    statusChar as (typeof StatusValues)[number]
  );
  return index !== -1 ? Statuses[index] : 'Unspecified';
};

export const getFunctionId = (code: string): string => {
  const functionIdChar = code.substring(4, 10);
  const mapping = FunctionIdMappingsByValue(getBattleDimension(code));

  const value = mapping[functionIdChar];

  return value ?? 'Unknown';
};

export const getMobility = (code: string): Mobility => {
  const mobilityChar = code[10];
  const index = MobilityValues.indexOf(
    mobilityChar as (typeof MobilityValues)[number]
  );
  return index !== -1 ? Mobilities[index] : 'Unspecified';
};

export const getEchelon = (code: string): Echelon => {
  const echelonChar = code[11];
  const index = EchelonValues.indexOf(
    echelonChar as (typeof EchelonValues)[number]
  );
  return index !== -1 ? Echelons[index] : 'Unspecified';
};

export const getCountryCode = (code: string): string => {
  const countryCodeChar = code.substring(12, 14);
  const index = CountryCodes.indexOf(
    countryCodeChar as (typeof CountryCodes)[number]
  );
  return index !== -1 ? CountryNames[index] : 'Unspecified';
};

export const getOperationalCondition = (code: string): OperationalCondition => {
  const conditionChar = code[14];
  const index = OperationalConditionValues.indexOf(
    conditionChar as (typeof OperationalConditionValues)[number]
  );
  return index !== -1 ? OperationalConditions[index] : 'Unspecified';
};

export const isTacticalGraphic = (code: string): boolean => {
  const codingScheme = code[0]; // First character
  const battleDimension = code[2]; // Third character
  return codingScheme === 'W' && battleDimension === 'T';
};

export const getSymbolInfo = (code: string): MILSTD2525 => {
  if (!isValidCode(code)) {
    throw new Error(
      'Invalid MIL-STD-2525 code. It must be a 15-character string.'
    );
  }

  return {
    symbolSet: getSymbolSet(code),
    affiliation: getAffiliation(code),
    status: getStatus(code),
    dimension: getBattleDimension(code),
    functionId: getFunctionId(code),
    echelon: getEchelon(code),
    mobility: getMobility(code),
    countryCode: getCountryCode(code),
    operationalCondition: getOperationalCondition(code)
  };
};
