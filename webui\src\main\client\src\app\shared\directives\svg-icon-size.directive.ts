import { Directive, HostBinding, Input } from '@angular/core';

@Directive({
  selector: 'mat-icon',
  standalone: true
})
export class SvgIconSizeDirective {
  @Input() size: number | null = null;

  @HostBinding('attr.inline') get inlineAttribute(): boolean {
    return !!this.size;
  }

  @HostBinding('style') get svgIconStyle(): string {
    return this.size ? `
      height: ${this.size}px;
      width: ${this.size}px;
      font-size: ${this.size}px;
      line-height: ${this.size}px;
    ` : null;
  }
}
