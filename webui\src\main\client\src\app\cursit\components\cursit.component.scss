/*#mapContainer {*/
/*  overflow: auto;*/
/*  position: relative;*/
/*  width: 100%;*/
/*  height: 100%;*/
/*  max-width: 100%;*/
/*  max-height: 100%;*/
/*  min-height: 128px;*/
/*}*/

@import 'toolbar-mixins';

:host ::ng-deep .leaflet-ruler.leaflet-control {
  display: none !important;
}

#map {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--map-background);
}

.cursit-label-div {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 5px;
  left: 10px;
  z-index: 500;
  pointer-events: none;
}

.cursit-label {
  font-weight: bold;
  font-size: 20px;
  color: $icon-color;
}

::ng-deep .cursit-icon .foregroundColor {
  fill: $icon-color;
}

.cursit-icon {
  margin-right: 10px;
  width: 60px;
  height: 60px;
}
