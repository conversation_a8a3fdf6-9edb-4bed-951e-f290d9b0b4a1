<div class="toolbar-wrapper">
  <div class="button-wrapper">
    <button class="shape-toolbar-button" [ngClass]="{'expanded': expanded}"
            matTooltip="{{expanded ? 'Collapse Entity Movement Toolbar' : 'Expand Entity Movement Toolbar'}}"
            (click)="toggleEntityMovementToolbar()">
      <mat-icon class="button-icon" svgIcon="entity-movement-tb"></mat-icon>
    </button>
  </div>
  <div *ngIf="expanded" class="toolbar">
    <div *ngFor="let buttonInfo of buttonInfoList" class="button-wrapper">
      <button
        matTooltip="{{buttonInfo.label}}"
        class="shape-toolbar-button" (click)="buttonAction(buttonInfo.action)">
        <mat-icon class="button-icon" svgIcon="{{ buttonInfo.svgIcon }}"></mat-icon>
      </button>
    </div>
  </div>
</div>
