import {
  Component,
  Input,
  inject,
  EventEmitter,
  Output,
  effect,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormControl,
  Validators
} from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { debounceTime } from 'rxjs';
import { FilterType, GeoFilter } from '../../../data/endpoint/filter.model';
import { GeoFilterPoints } from '../../../cursit/reducers/cursit.reducer';
import { cursitActions } from '../../../cursit/actions/cursit.actions';
import { toolbarExpanded } from '../../../gis/interface/components/top-toolbar/top-toolbar.component';
@Component({
  selector: 'vcci-geo-filter',
  templateUrl: './geo-filter.component.html',
  styleUrls: ['./geo-filter.component.css'],
  standalone: false
})
export class GeoFilterComponent implements OnChanges {
  private store = inject(Store);
  private fb = inject(FormBuilder);
  private toolbarExpanded = toolbarExpanded;

  @Input() filter: GeoFilter | undefined;
  /* TODO: Find a way to exclude newGeoAreaDrawn from the input
   * but will still fill in the form inputs when a geo filter is drawn
   */
  @Input() newGeoAreaDrawn: GeoFilterPoints | undefined;
  @Input() isGeoDrawn: boolean = false;

  @Output() filterChanged = new EventEmitter<GeoFilter>();
  @Output() geoFilterChanged = new EventEmitter<GeoFilterPoints>();
  @Output() geoFilterDrawn = new EventEmitter<null>();
  @Output() geoDrawToggled = new EventEmitter<boolean>();

  geoForm: FormGroup = this.fb.group({
    p1: this.fb.group({
      latitude: new FormControl<number | null>(null, Validators.required),
      longitude: new FormControl<number | null>(null, Validators.required)
    }),
    p2: this.fb.group({
      latitude: new FormControl<number | null>(null, Validators.required),
      longitude: new FormControl<number | null>(null, Validators.required)
    })
  });
  geoFormChanges = toSignal<GeoFilterPoints>(
    this.geoForm.valueChanges.pipe(debounceTime(500)),
    {
      initialValue: this.geoForm.value
    }
  );
  filterIdTest = 0;

  private previousValues: GeoFilterPoints | null = null;

  constructor() {
    // Effect to update the filter when the form values change
    effect(() => {
      const values = this.geoFormChanges();
      // Check if all required values are present
      if (
        values.p1.latitude !== null &&
        values.p1.longitude !== null &&
        values.p2.latitude !== null &&
        values.p2.longitude !== null
      ) {
        // Check if values have changed
        if (this.havePointsChanged(values)) {
          this.previousValues = {
            p1: { ...values.p1 },
            p2: { ...values.p2 }
          };

          // Emit the updated filter to the parent component
          this.emitFilterChange(values);
        }
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle filter changes
    if (changes['filter']?.currentValue) {
      const filter = changes['filter'].currentValue;

      // Convert filter to GeoFilterPoints format
      const points: GeoFilterPoints = {
        p1: {
          latitude: filter.firstPointLatitude,
          longitude: filter.firstPointLongitude
        },
        p2: {
          latitude: filter.secondPointLatitude,
          longitude: filter.secondPointLongitude
        }
      };

      // Only update the form if the values have changed
      if (this.havePointsChanged(points)) {
        this.updateFormWithPoints(points);
      }
    }

    // Handle newGeoAreaDrawn changes
    if (changes['newGeoAreaDrawn']?.currentValue) {
      const points = changes['newGeoAreaDrawn'].currentValue;

      // Only update the form if the values have changed
      if (this.havePointsChanged(points)) {
        this.updateFormWithPoints(points);
        // Emit the updated filter to the parent component
        this.emitFilterChange(points);
      }
    }
  }

  /**
   * Checks if the points have changed compared to the previous values
   * @param points The points to check
   * @returns True if the points have changed or if there are no previous values
   */
  private havePointsChanged(points: GeoFilterPoints): boolean {
    return (
      !this.previousValues ||
      this.previousValues.p1.latitude !== points.p1.latitude ||
      this.previousValues.p1.longitude !== points.p1.longitude ||
      this.previousValues.p2.latitude !== points.p2.latitude ||
      this.previousValues.p2.longitude !== points.p2.longitude
    );
  }

  private updateFormWithPoints(points: GeoFilterPoints): void {
    this.geoForm.patchValue(
      {
        p1: {
          latitude: points.p1.latitude,
          longitude: points.p1.longitude
        },
        p2: {
          latitude: points.p2.latitude,
          longitude: points.p2.longitude
        }
      },
      { emitEvent: false }
    );
  }

  emitFilterChange(values: GeoFilterPoints): void {
    this.filterIdTest++;

    const currentGeo: GeoFilter = {
      areaLabel: this.filter?.areaLabel || `Geo Filter ${this.filterIdTest}`,
      firstPointLatitude: values.p1.latitude,
      firstPointLongitude: values.p1.longitude,
      secondPointLatitude: values.p2.latitude,
      secondPointLongitude: values.p2.longitude,
      radius: this.filter?.radius || 0,
      categoryCode: this.filter?.categoryCode || '',
      type: FilterType.Geo,
      name: this.filter?.name || 'GEO',
      databaseId: this.filter?.databaseId || -1,
      description: this.filter?.description || 'GEO Filter',
      groupId: this.filter?.groupId || 0
    };

    // Emit the updated filter to the parent component
    this.filterChanged.emit(currentGeo);
    this.geoFilterChanged.emit(values);
    this.toolbarExpanded.update((v) => !v);
  }

  onGeoClick(): void {
    // Prevents the user from toggling the geo draw if it is already active
    if (!this.isGeoDrawn) {
      this.toolbarExpanded.update((v) => !v);
      this.store.dispatch(cursitActions.toggleGeoDraw());
      this.geoDrawToggled.emit(true);
    }
  }

  updateGeoArea(
    point: 'p1' | 'p2',
    key: 'longitude' | 'latitude',
    value: number
  ): void {
    this.geoForm.patchValue({
      [point]: { ...this.geoForm.get(point)?.value, [key]: value }
    });
  }

  resetGeoArea(): void {
    this.geoForm.setValue({
      p1: { latitude: null, longitude: null },
      p2: { latitude: null, longitude: null }
    });

    this.emitFilterChange(this.geoForm.value);
  }
}
