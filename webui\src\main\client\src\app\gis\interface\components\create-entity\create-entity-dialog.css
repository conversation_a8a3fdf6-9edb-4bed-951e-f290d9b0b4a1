/* Dialog styles */
:host {
  display: block;
}

mat-dialog-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
  max-height: 75vh;
}

form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Top section layout */
.top-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.symbol-container {
  flex: 0 0 30%;
  min-height: 150px;
  max-width: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  width: 100%;
  background-color: rgba(249, 249, 249, 0.25);
}

.symbol-container:hover {
  background-color: rgba(249, 249, 249, 0.6);
}

.entity-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.entity-name-suffix {
  display: flex;
  gap: 16px;
}

.name-field {
  flex: 1;
}

.suffix-field {
  flex: 0 0 35%;
}

/* Tabs section */
.tabs-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Location section */

.location-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.location-fields-mgrs {
  display: flex;
  align-items: center;
  gap: 16px;
}

.longitude-field,
.latitude-field {
  flex: 1;
}

/* Entity type radio buttons */
.entity-type-select {
  margin-right: 16px;
}

/* Unscrollable class for map selection */
.unscrollable {
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .top-section {
    flex-direction: column;
  }

  .location-fields {
    flex-direction: column;
    gap: 0;
  }
}
