<div class="toolbar-wrapper">
  <div class="button-wrapper">
    <button
      matTooltip="{{expanded ? 'Collapse Drawing Toolbar' : 'Expand Drawing Toolbar'}}"
      class="shape-toolbar-button" [ngClass]="{'expanded': expanded}"
      (click)="toggleShapeToolbar()">
      <mat-icon class="button-icon" svgIcon="draw-shape"></mat-icon>
    </button>
  </div>
  <div *ngIf="expanded" class="toolbar">
    <div class="shape-group">
      <div *ngFor="let buttonInfo of buttonInfoList1" class="button-wrapper">
        <button
          matTooltip="{{buttonInfo.label}}"
          class="shape-toolbar-button" (click)="createTheShape(buttonInfo.shapeType)">
          <mat-icon class="button-icon" svgIcon="{{ buttonInfo.svgIcon }}"></mat-icon>
        </button>
      </div>
    </div>
    <div class="shape-group">
      <div *ngFor="let buttonInfo of buttonInfoList2" class="button-wrapper">
        <button
          matTooltip="{{buttonInfo.label}}"
          class="shape-toolbar-button" (click)="createTheShape(buttonInfo.shapeType)">
          <mat-icon class="button-icon" svgIcon="{{ buttonInfo.svgIcon }}"></mat-icon>
        </button>
      </div>
    </div>
    <div class="shape-group">
      <div *ngFor="let buttonInfo of buttonInfoList3" class="button-wrapper">
        <button
          matTooltip="{{buttonInfo.label}}"
          class="shape-toolbar-button" (click)="createTheShape(buttonInfo.shapeType)">
          <mat-icon class="button-icon" svgIcon="{{ buttonInfo.svgIcon }}"></mat-icon>
        </button>
      </div>
    </div>
  </div>
</div>
