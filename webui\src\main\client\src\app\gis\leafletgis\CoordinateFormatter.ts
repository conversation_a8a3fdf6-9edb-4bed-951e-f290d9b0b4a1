
function computeFor(value: number, latOrLon: boolean): Values {
  const degrees = Math.abs(value);
  const degreesInt = Math.floor(degrees);
  const degreesFrac = degrees - degreesInt;
  const secondsTotal = 3600 * degreesFrac;
  const minutes = secondsTotal / 60;
  const minutesInt = Math.floor(minutes);
  const seconds = secondsTotal - minutesInt * 60;

  let nsEWValue: string;
  if (latOrLon) {
    if (value < 0) {
      nsEWValue = 'S';
    } else {
      nsEWValue = 'N';
    }
  } else if (value < 0) {
    nsEWValue = 'W';
  } else {
    nsEWValue = 'E';
  }

  const values: Values = {
    NSEWValue: nsEWValue,
    initValue: value,
    degrees,
    degreesInt,
    degreesFrac,
    secondsTotal,
    minutes,
    minutesInt,
    seconds
  };

  return values;
}

function format(values: Values, latOrLon: boolean): string {
  const units = {
    degrees: '°',
    minutes: '′',
    seconds: '″'
  };

  let degreesString = values.degreesInt.toString();
  if (latOrLon) {
    if (values.degreesInt < 10) {
      degreesString = `0${degreesString}`;
    } else {
      degreesString = values.degreesInt.toString();
    }
  } else if (values.degreesInt < 10) {
    degreesString = `00${degreesString}`;
  } else if (values.degreesInt < 100) {
    degreesString = `0${degreesString}`;
  }

  let minutesString = values.minutesInt.toString();
  if (values.minutesInt < 10) {
    minutesString = `0${minutesString}`;
  }

  let secondsString = Math.floor(values.seconds).toString();
  if (values.seconds < 10) {
    secondsString = `0${secondsString}`;
  }

  return `${degreesString}${units.degrees}${minutesString}${units.minutes}${secondsString}${units.seconds}${values.NSEWValue}`;
}

export function latLonFormatter(latLon: [number, number]): string {
  const latValues: Values = computeFor(latLon[0], true);
  const lonValues: Values = computeFor(latLon[1], false);

  const latString = `${format(latValues, true)}`;
  const lonString = `${format(lonValues, false)}`;

  return `${latString},${lonString}`;
}

interface Values {
  NSEWValue: string;
  initValue: number;
  degrees: number;
  degreesInt: number;
  degreesFrac: number;
  secondsTotal: number;
  minutes: number;
  minutesInt: number;
  seconds: number;

}

