<div class="container">

  <ng-container *ngIf="isInput" [ngTemplateOutlet]="actionMenu"/>

  <mat-card
    #containerElement
    class="main-content"
    [vcciDraggable]="endpoint"
    [isAccepted]="isAccepted"
    [ngClass]="selected ? 'selectable' : ''"
    (click)="endpointClicked()"
    (onDropped)="handleOnDrop($event)">
    <div mat-card-title class="text-container">
      <div>ENDPOINT</div>
      <div>{{endpoint.name}}</div>
      <div>CONNECTION</div>
      <div>{{endpoint.connection}}</div>
      <div>IP/ID/HOST</div>
      <div>{{host}}</div>
      <div>PORT</div>
      <div>{{port}}</div>
      <div>{{isInput ? 'INCOMING' : 'OUTGOING'}}</div>
      <div>{{endpoint?.metrics.incomingPacketCount}}/{{endpoint?.metrics.outgoingPacketCount}}</div>
    </div>
    <div class="node">{{endpoint.node}}</div>
  </mat-card>

  <ng-container *ngIf="!isInput" [ngTemplateOutlet]="actionMenu"/>

</div>

<ng-template #actionMenu>
  <div
    class="actions-container"
    [ngClass]="isInput ? 'input' : 'output'"
    [@visible]="selected">
    <button [buttonSize]="32" [iconSize]="28" mat-icon-button (click)="openUpdateEndpointDialog()" matTooltip="Edit Endpoint Parameters" matTooltipPosition="left">
      <mat-icon style="transform: scale(1.2)" svgIcon="edit"/>
    </button>
    <button [buttonSize]="32" [iconSize]="28" mat-icon-button (click)="data.node.openFiltersDialog(endpoint)" matTooltip="Filter Data" matTooltipPosition="left">
      <mat-icon svgIcon="filtered-full"/>
    </button>
    <button [buttonSize]="32" [iconSize]="28" mat-icon-button (click)="restartEndpoint()" matTooltip="Restart Data Communication" matTooltipPosition="left">
      <mat-icon svgIcon="reload"/>
    </button>
    <button [buttonSize]="32" [iconSize]="28" mat-icon-button (click)="data.endpoint.openAssocDialog(getEndpointId(endpoint))" matTooltip="Link Endpoint" matTooltipPosition="left">
      <mat-icon svgIcon="entity-association"/>
    </button>
    <button [buttonSize]="32" [iconSize]="28" mat-icon-button (click)="data.endpoint.openMetricsDialog(getEndpointId(endpoint))" matTooltip="Show Endpoint Metrics" matTooltipPosition="left">
      <mat-icon svgIcon="metrics"/>
    </button>
    <button [buttonSize]="32" [iconSize]="28" mat-icon-button (click)="data.endpoint.openDeleteDialog(getEndpointId(endpoint))" matTooltip="Delete Endpoint" matTooltipPosition="left">
      <mat-icon svgIcon="delete"/>
    </button>
  </div>
</ng-template>


