/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/31/2024, 1:06 PM
 */
import { Feature } from '@luciad/ria/model/feature/Feature';
import { CoordinateReference } from '@luciad/ria/reference/CoordinateReference';
import { Arc } from '@luciad/ria/shape/Arc';
import { ArcBand } from '@luciad/ria/shape/ArcBand';
import { Bounds } from '@luciad/ria/shape/Bounds';
import { Circle } from '@luciad/ria/shape/Circle';
import { CircularArc } from '@luciad/ria/shape/CircularArc';
import { Ellipse } from '@luciad/ria/shape/Ellipse';
import { GeoBuffer } from '@luciad/ria/shape/GeoBuffer';
import { Point } from '@luciad/ria/shape/Point';
import { PointCoordinates } from '@luciad/ria/shape/PointCoordinate';
import { Polygon } from '@luciad/ria/shape/Polygon';
import { Polyline } from '@luciad/ria/shape/Polyline';
import { Sector } from '@luciad/ria/shape/Sector';
import { createPoint, createShape } from '@luciad/ria/shape/ShapeFactory';
import { IconStyle } from '@luciad/ria/view/style/IconStyle';
import { ShapeStyle } from '@luciad/ria/view/style/ShapeStyle';
import { Coordinate } from '../../cursit/models/cursit.model';
import { ShapeStyle as GenericShapeStyle } from '../interface/models/gis.model';
import { ShapeType } from '../interface/models/map-element.model';
import { LuciadMapDisplay } from './LuciadMapDisplay';

export class LuciadShape extends Feature {
  coordinateReference: CoordinateReference;
  coordinates: PointCoordinates | PointCoordinates[];
  luciadMapDisplay: LuciadMapDisplay;
  selected: boolean;
  private _selectedStyle: ShapeStyle | IconStyle;
  private _normalStyle: ShapeStyle | IconStyle;
  private _genericNormalIconStyle: GenericShapeStyle;
  private _genericSelectedIconStyle: GenericShapeStyle;
  label: string;

  constructor(
    luciadMapDisplay: LuciadMapDisplay,
    coordinateReference: CoordinateReference,
    shapeType: ShapeType,
    coordinates?: PointCoordinates | PointCoordinates[],
    label?: string
  ) {
    super(createShape(shapeType, coordinateReference));
    this.luciadMapDisplay = luciadMapDisplay;
    if (label) {
      this.label = label;
    }
    this.selected = false;
    this.coordinates = coordinates;
    this.coordinateReference = coordinateReference;
  }

  set selectedStyle(shapeStyle: ShapeStyle | IconStyle) {
    this._selectedStyle = shapeStyle;
  }

  get selectedStyle(): ShapeStyle | IconStyle {
    return this._selectedStyle;
  }

  set normalStyle(shapeStyle: ShapeStyle | IconStyle) {
    this._normalStyle = shapeStyle;
  }

  get normalStyle(): ShapeStyle | IconStyle {
    return this._normalStyle;
  }

  set genericNormalIconStyle(genericIconStyle: GenericShapeStyle) {
    this._genericNormalIconStyle = genericIconStyle;
  }

  get genericNormalIconStyle(): GenericShapeStyle {
    return this._genericNormalIconStyle;
  }

  set genericSelectedIconStyle(genericIconStyle: GenericShapeStyle) {
    this._genericSelectedIconStyle = genericIconStyle;
  }

  get genericSelectedIconStyle(): GenericShapeStyle {
    return this._genericSelectedIconStyle;
  }

  addLocation(coordinate: Coordinate): void {
    const { shape } = this;
    if (shape instanceof Polygon || shape instanceof  Polyline) {
      shape.insertPoint(shape.pointCount, createPoint(this.coordinateReference, coordinate));
    }
  }

  removeLocation(index: number): void {
    const { shape } = this;
    if (shape instanceof Polygon || shape instanceof  Polyline) {
      shape.removePoint(index);
    }
  }

  getLocations(): Coordinate[] | undefined {
    const { shape } = this;
    if (shape instanceof Polygon || shape instanceof  Polyline) {
      const { pointCount } = shape;
      const locationArray: Coordinate[] = [];
      this.coordinates = [];
      for (let i = 0; i < pointCount; i += 1) {
        const { x, y } = shape.getPoint(i);
        this.coordinates.push([x, y]);
        locationArray.push([x, y]);
      }

      return locationArray;
    }

    if (shape instanceof GeoBuffer) {
      const { baseShape } = shape;
      if (baseShape instanceof Polyline) {
        const { pointCount } = baseShape;
        const locationArray: Coordinate[] = [];
        this.coordinates = [];
        for (let i = 0; i < pointCount; i += 1) {
          const { x, y } = baseShape.getPoint(i);
          this.coordinates.push([x, y]);
          locationArray.push([x, y]);
        }

        return locationArray;
      }
    }

    return undefined;
  }

  getLocation(): Coordinate | undefined {
    const { shape } = this;
    if (shape instanceof Point || shape instanceof Bounds) {
      this.coordinates = [shape.x, shape.y];
      return [shape.x, shape.y];
    }

    if (shape instanceof Ellipse
      || shape instanceof ArcBand
      || shape instanceof Arc
      || shape instanceof Sector
      || shape instanceof Circle
      || shape instanceof CircularArc) {
      this.coordinates = [shape.center.x, shape.center.y];
      return [shape.center.x, shape.center.y];
    }

    return undefined;
  }
}
