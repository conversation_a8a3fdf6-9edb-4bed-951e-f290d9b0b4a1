import { SFMap } from '@simfront/common-libs';

import { GISElement } from './GISElement';
import { LayerSource, LayerType } from '../../data/layer-manager/layer-manager.model';

export interface MapLayer<L> {
  set parentId(parentId: string);

  get parentId(): string;

  setVisible?(visible: boolean): void;

  isVisible?(): boolean;

  setSelectable?(selectable: boolean): void;

  setEditable?(editable: boolean): void;

  getGISLayer?(): L;

  setGISLayer?(layer: L): void;

  addElement?(element: GISElement<L>): void;

  removeElement?(element?: GISElement<L>, id?: string): boolean | Promise<boolean>;

  // getElementCount: ()=> number;
  getElement?(id: string): GISElement<L> | null | undefined;

  getElements?(): SFMap<GISElement<L>>;

  get layerId(): string;

  set layerId(id: string);

  redraw?(): null | Promise<void>;

  set layerName(layerName: string);

  get layerName(): string;

  setRedrawLayer?(redraw: boolean): void;

  isRedrawLayer?(): boolean;

  layerType: LayerType;

  layerSource: LayerSource;

  isFeatureLayer?: () => boolean;
}

export abstract class AbstractMapLayer<L> implements MapLayer<L> {
  protected gisLayer: L;

  _layerId: string;

  _layerName: string;

  _parentId: string;

  elementCache: SFMap<GISElement<L>> = {};

  _redrawLayer = true;

  _layerType: LayerType;

  _layerSource: LayerSource;

  _visible: boolean;

  constructor(layer?: L, id?: string, layername?: string) {
    if (layer) {
      this.setGISLayer(layer);
    }
    if (id) {
      this.layerId = id;
    }
    if (layername) {
      this.layerName = layername;
    }
  }

  set layerType(type: LayerType) {
    this._layerType = type;
  }

  get layerType(): LayerType {
    return this._layerType;
  }

  set layerSource(source: LayerSource) {
    this._layerSource = source;
  }

  get layerSource(): LayerSource {
    return this._layerSource;
  }

  set parentId(parentId: string) {
    this._parentId = parentId;
  }

  get parentId(): string {
    return this._parentId;
  }

  setVisible(visible: boolean): void {
    this._visible = visible;
    this.setGISLayerVisibility(visible);
  }

  abstract isVisible(): boolean;

  get layerId(): string {
    return this._layerId;
  }

  set layerId(id: string) {
    this._layerId = id;
  }

  setRedrawLayer(redraw: boolean): void {
    this._redrawLayer = redraw;
  }

  isRedrawLayer(): boolean {
    return this._redrawLayer;
  }

  abstract isFeatureLayer(): boolean;

  abstract setEditable(editable: boolean): void;

  abstract setSelectable(selectable: boolean): void;

  abstract setGISLayerVisibility(visible: boolean): void;

  addElement(element: GISElement<L>): void {
    if (!(element.id in this.elementCache)) {
      element.mapLayer = this;
      this.elementCache[element.id] = element;
      this.gisAddElement(element);
    }
  }

  getElements(): SFMap<GISElement<L>> {
    return this.elementCache;
  }

  abstract gisAddElement(element: GISElement<L>): void;

  removeElement(element?: GISElement<L>, id?: string): boolean | Promise<boolean> {
    let elementID: string;
    if (element) {
      elementID = element.id;
    } else if (id) {
      elementID = id;
    } else {
      return false;
    }

    if (elementID in this.elementCache) {
      const gisElement = this.elementCache[elementID];
      delete this.elementCache[elementID];
      return this.gisRemoveElement(gisElement);
    }

    return false;
  }

  abstract gisRemoveElement(element?: GISElement<L>): boolean | Promise<boolean>;

  abstract getElement(id: string): GISElement<L> | null | undefined;

  getGISLayer(): L {
    return this.gisLayer;
  }

  setGISLayer(layer: L): void {
    this.gisLayer = layer;
  }

  set layerName(lyrName: string) {
    this._layerName = lyrName;
  }

  get layerName(): string {
    return this._layerName;
  }

  abstract redraw(): Promise<void>;
}

