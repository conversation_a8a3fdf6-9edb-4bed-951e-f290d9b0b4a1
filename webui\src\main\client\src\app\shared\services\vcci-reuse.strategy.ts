import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, DetachedRouteHandle, RouteReuseStrategy } from '@angular/router';

import { SFMap } from '@simfront/common-libs';

const getPath = (ars: ActivatedRouteSnapshot): string => ars.parent?.routeConfig?.path || '';

@Injectable()
export class VcciReuseStrategy implements RouteReuseStrategy {
  routesToCache: string[] = ['', 'cursit'];
  storedRouteHandles: SFMap<DetachedRouteHandle> = {};

  retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {
    return this.storedRouteHandles[getPath(route)] || null;
  }

  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    return this.storedRouteHandles[getPath(route)] !== undefined;
  }

  shouldDetach(route: ActivatedRouteSnapshot): boolean {
    return this.routesToCache.includes(getPath(route));
  }

  shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    return future.routeConfig === curr.routeConfig;
  }

  store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle | null): void {
    this.storedRouteHandles[getPath(route)] = handle;
  }
}
