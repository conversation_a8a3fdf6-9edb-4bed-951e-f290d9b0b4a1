import { HttpErrorResponse, HttpEvent, HttpH<PERSON>ler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { catchError, Observable, throwError } from 'rxjs';
import { HttpErrorComponent } from './http-error.component';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {
  constructor(private _snackBar: MatSnackBar) {
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((her: HttpErrorResponse) => {
        this._snackBar.openFromComponent(HttpErrorComponent, {
          data: { her, method: req.method}, panelClass: ['mat-toolbar', 'error'], duration: 5000
        });
        return throwError(() => her);
      })
    );
  }
}
