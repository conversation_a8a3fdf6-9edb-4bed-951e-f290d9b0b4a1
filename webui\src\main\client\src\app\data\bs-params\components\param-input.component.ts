import { Component } from '@angular/core';
import { BaseParamComponent } from './base-param.component';
import { ReactiveFormsModule } from '@angular/forms';
import { IconButtonSizeDirective } from '../../../shared/directives/icon-button-size.directive';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MatFormField,
  MatLabel,
  MatSuffix
} from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { SvgIconSizeDirective } from '../../../shared/directives/svg-icon-size.directive';
import { MatCheckbox } from '@angular/material/checkbox';

@Component({
  selector: 'vcci-param-input',
  template: `
    <mat-form-field>
      <mat-label>{{ label }}</mat-label>
      <input
        matInput
        [type]="parameter.type === 'PASSWORD' ? 'password' : 'text'"
        [formControl]="formControl"
        [placeholder]="defaultValue"
        (input)="onInput($event)"
        (keydown.tab)="onUseDefault()"
      />
      @if (hasNext) {
        <button matSuffix mat-icon-button (click)="onGetNext()">
          <mat-icon>east</mat-icon>
        </button>
      }
      @if (invalid) {
        <mat-error>{{ errorMessage() }}</mat-error>
      }
    </mat-form-field>
    @if (!!this.parameter.group) {
      <mat-checkbox
        [checked]="isSelectedInGroup"
        [disabled]="!isPreviousEnabled"
        (change)="setGroupValue($event.checked)"
      />
    }
  `,
  styles: ``,
  imports: [
    IconButtonSizeDirective,
    MatFormField,
    MatIcon,
    MatIconButton,
    MatInput,
    MatLabel,
    MatSuffix,
    ReactiveFormsModule,
    SvgIconSizeDirective,
    MatError,
    MatCheckbox
  ]
})
export class ParamInputComponent extends BaseParamComponent {}
