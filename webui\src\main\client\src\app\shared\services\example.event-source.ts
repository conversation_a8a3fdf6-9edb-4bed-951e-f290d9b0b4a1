import { Injectable } from '@angular/core';

import { environment } from '../../../environments/environment';
import { EventSourceStoreService } from './event-source-store.service';

export interface Example {
  id:   number;
  name: string;
}

@Injectable({
  providedIn: 'root'
})
export class ExampleEventSource extends EventSourceStoreService<string> {
  constructor() {
    super(`${environment.origin}/events`, 'Example');
  }
}
