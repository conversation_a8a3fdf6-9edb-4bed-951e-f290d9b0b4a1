import { NgIf } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { RouterLink } from '@angular/router';

import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';

import { environment } from '../../../environments/environment';

import { LayoutActions } from '../actions/layout.actions';
import { selectSidePanel, SidePanelState } from '../reducers/layout.reducer';
import { NavigationComponent } from './navigation.component';
import { MatDialog } from '@angular/material/dialog';
import { GenericDialog } from 'src/app/shared/generic-dialog/generic-dialog.dialog';

@Component({
  selector: 'vcci-toolbar',
  templateUrl: 'toolbar.component.html',
  host: {
    class: 'custom-mat-container'
  },
  styles: [
    `
      :host {
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        height: 55px;
        align-items: center;
        padding-left: 15px;
      }

      .toolbar {
        display: flex;
        align-items: center;
        gap: 8px;

        button {
          font-size: 17px;
        }
      }

      .menu-group-title:first-of-type {
        margin-top: 5px;
      }

      .menu-group-title {
        margin: 10px;
      }

       .cursit-icon ::ng-deep .foregroundColor {
        fill: currentColor;
      }
    `
  ],
  imports: [
    MatIconModule,
    MatButtonModule,
    MatDividerModule,
    MatMenuModule,
    NgIf,
    RouterLink,
    LetDirective,
    NavigationComponent
  ]
})
export class ToolbarComponent {
  private readonly store: Store = inject(Store);
  private readonly http: HttpClient = inject(HttpClient);
  private readonly _dialogService = inject(MatDialog);

  sidePanelState$ = this.store.select(selectSidePanel);
  dbAutoBackUpEnabled = false;

  constructor() {
    this.http
      .get(`${environment.origin}/database/auto-backup-state`, {
        responseType: 'text'
      })
      .subscribe((val) => {
        this.dbAutoBackUpEnabled = val === 'true';
      });
  }

  changeSidePanel(type: SidePanelState): void {
    this.store.dispatch(LayoutActions.changeSidePanel(type));
  }

  toggleAutoBackup(): void {
    this.http
      .get(
        `${environment.origin}/database/${this.dbAutoBackUpEnabled ? 'disable' : 'enable'}-auto-backup`,
        { responseType: 'text' }
      )
      .subscribe((val) => {
        this.dbAutoBackUpEnabled = val === 'Database auto backup enabled!';
      });
  }

  backupDatabase(): void {
    let dialog = this._dialogService.open(GenericDialog, {
      data: {
        title: 'Database Backup',
        message: 'Please wait while we are backing up the database.'
      }
    });
    this.http
      .get(`${environment.origin}/database/backup`, { responseType: 'text' })
      .subscribe((val) => {
        dialog.close();
        if (val !== 'Database Backup message sent')
          this._dialogService.open(GenericDialog, {
            data: {
              title: 'Backup Failure',
              message: 'Database Backup failed. Please try again later.',
            }
          });
      });
  }

  protected readonly environment = environment;
}
