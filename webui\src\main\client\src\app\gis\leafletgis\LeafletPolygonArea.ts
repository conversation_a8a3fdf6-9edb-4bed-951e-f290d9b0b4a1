import { Coordinate } from '../../cursit/models/cursit.model';
import { ShapeType } from '../interface/models/map-element.model';
import { LeafletMap } from './LeafletMap';
import { LeafletGraphics } from './LeafletGraphics';
import { LeafletMapDisplay } from './LeafletMapDisplay';

/**
 * LeafletPolygon class for rendering polygon areas on a Leaflet map
 * Extends LeafletShape to handle polygon rendering based on BattleSpacePolygonArea data
 */

export class LeafletPolygonArea extends LeafletGraphics {
  // Default stroke width

  /**
   * Creates a new LeafletPolygon instance
   *
   * @param id Unique identifier for the polygon
   * @param map LeafletMap instance to render on
   * @param shape ShapeType of the polygon
   * @param coordinates Optional array of coordinates forming the polygon
   * @param catCode Optional styling options for the polygon
   */
  constructor(
    id: string,
    map: LeafletMapDisplay,
    type: ShapeType,
    catCode: string,
    coordinates: Coordinate[],
    label: string
  ) {
    super(id, map, type, catCode, coordinates, label);

    // Need at least 3 points to form a polygon area
    if (this._locations.length < 3 && type === ShapeType.POLYGON) {
      console.error('Not enough points to form a polygon');
      return;
    }

    this.drawPolygon();
  }

  private drawPolygon(): void {
    // Clear any existing graphics
    this._graphicsElement.clear();

    // Get projected points for drawing
    const points = this._locations.map((location) =>
      this._map.map.projectFromLatLonToPoint([location[0], location[1]])
    );

    // Calculate the current zoom scale
    const scale = this.getZoomScale(this._map.map.getZoom());
    const adjustedStrokeWidth = this._strokeWidth / scale;

    this._graphicsElement.poly(points, this._type === ShapeType.POLYGON);

    // Need fill to handle element selection
    this._graphicsElement.fill({
      color: this.getFillColorFromCatCode(this._catCode),
      alpha: 0.01
    });

    this._graphicsElement.stroke({
      width: adjustedStrokeWidth,
      color: this.selected
        ? 'red'
        : this.getFillColorFromCatCode(this._catCode),
      alignment: 0.5
    });
  }

  /**
   * Redraws the polygon based on current locations and styling
   */
  redraw(): void {
    this.drawPolygon();
    this.addLabel();
  }

  /**
   * Sets the speed and bearing of the polygon
   * @param speed Speed value
   * @param bearing Bearing in degrees
   */
  setSpeedAndBearing(speed: number, bearing: number): void {
    // TODO: Implement any logic to set the speed and bearing of the polygon
  }

  /**
   * Sets the symbol code of the polygon
   * @param symbolCode Symbol code
   */
  setSymbolCode(symbolCode: string): void {
    // TODO: Implement any logic to set the symbol code of the polygon
  }

  /**
   * Updates the stroke width based on the zoom level
   * @param zoom The current zoom level
   */
  updateStrokeWidth(zoom: number): void {
    const scale = this.getZoomScale(zoom);
    const adjustedStrokeWidth = this._strokeWidth / scale;

    this._graphicsElement.setStrokeStyle({
      width: adjustedStrokeWidth,
      color: this._selected
        ? 0xff0000
        : this.getFillColorFromCatCode(this._catCode),
      alignment: 0.5
    });

    this.redraw();
  }
}
