import { Bounds } from '@luciad/ria/shape/Bounds';
import { EVENT_IGNORED, HandleEventResult } from '@luciad/ria/view/controller/HandleEventResult';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { GestureEventType } from '@luciad/ria/view/input/GestureEventType';
import { PickInfo } from '@luciad/ria/view/PickInfo';
import { LuciadMapDisplay } from '../LuciadMapDisplay';
import { RectangleController } from './rectangle-controller';

export class BulkSelectController extends RectangleController {
  constructor(private mapDisplay: LuciadMapDisplay) {
    super();
  }

  override onGestureEvent = (ge: GestureEvent): HandleEventResult => {
    if (ge.type === GestureEventType.MOVE) {
      return this.moveOccurred(ge);
    }
    if (ge.type === GestureEventType.SINGLE_CLICK_CONFIRMED) {

      // return this.singleClickConfirmedOccurred(ge);
    }
    return super.onGestureEvent(ge);
  };

  moveOccurred(ge: GestureEvent): HandleEventResult {
    // this.mapDisplay.mousePosition = ge.viewPoint;
    return super.onGestureEvent(ge);
  }

  singleClickConfirmedOccurred(ge: GestureEvent): HandleEventResult {
    // this.mapDisplay.closeModifications();
    this.mapDisplay.map.clearSelection();
    // const selection: PickInfo[] = [];
    // this.mapDisplay.getMap().selectObjects(selection, {editSelection: SelectionType.NEW});
    // this.mapDisplay.getMap().layerTree.children.forEach(lyr=>{
    //   if(lyr instanceof FeatureLayer){
    //     lyr.painter.invalidateAll();
    //   }
    // });
    // console.log(this.mapDisplay.getMap().selectedObjects.length);
    return  EVENT_IGNORED;
  }

  override rectangleDragged(modelBounds: Bounds | null, viewBounds: Bounds | null): void  {
    if (this.mapDisplay.map) {
      let pickAt: PickInfo[] = [];

      if (modelBounds && viewBounds) {
        const centerView = viewBounds.focusPoint;
        // this.map.clearSelection();
        // console.log(this.map.selectedObjects);
        pickAt = this.mapDisplay.map.pickAtRectangle(
          centerView.x,
          centerView.y,
          viewBounds.width,
          viewBounds.height
        );
      }

      // this.mapDisplay.closeModifications();

      this.mapDisplay.selectPickAt(pickAt, true);
    }
    this.reset();
  }
}
