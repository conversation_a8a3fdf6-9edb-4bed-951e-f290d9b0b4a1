import { createFeature, createReducer, on } from '@ngrx/store';
import { mapActions } from '../actions/map.actions';
import { MapElementSelectionInfo } from '../models/gis.model';

export interface MapElementSelectionState {
  mapElementInformation: MapElementSelectionInfo;
  isMenuVisible: boolean;
  isMenuOpen: boolean;
}

const initialState: MapElementSelectionState = {
  mapElementInformation: {
    elementId: '',
    screenCoordinates: { x: 0, y: 0 },
    coordinates: [[0, 0]]
  },
  isMenuVisible: true,
  isMenuOpen: false
};

export const elementSelectionFeature = createFeature({
  name: 'selectMapElement',
  reducer: createReducer(
    initialState,
    on(mapActions.mapElementSelection, (state, action) => ({
      ...state,
      mapElementInformation: action.payload
    })),
    on(mapActions.hideEntitySelectionMenu, (state) => ({
      ...state,
      isMenuVisible: false
    })),
    on(mapActions.showEntitySelectionMenu, (state) => ({
      ...state,
      isMenuVisible: true
    })),
    on(mapActions.openEntitySelectionDialog, (state) => ({
      ...state,
      isMenuOpen: true
    })),
    on(mapActions.closeEntitySelectionDialog, (state) => ({
      ...state,
      isMenuOpen: false
    }))
  )
});

export const {
  name,
  reducer,
  // this is coming from the name of the feature above (name: selectMapElement)
  selectSelectMapElementState,
  selectMapElementInformation,
  selectIsMenuVisible
} = elementSelectionFeature;
