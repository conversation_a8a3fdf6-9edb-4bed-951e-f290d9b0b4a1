import {
  Component,
  inject,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

import { TableColumn, TableSelectionMode } from '@simfront/common-libs';

import { DataService } from '../../data.service';
import { Entity, extractSymbolCode } from '../../entity/entity.model';

import { Candidate, OutboundObject } from './association.model';
import { getServiceId, getEntryPointName } from '../../entity/id.model';

@Component({
  templateUrl: 'association.dialog.html',
  styleUrls: ['association.dialog.css'],
  standalone: false
})
export class AssociationDialog implements OnInit {
  private readonly ds = inject(DataService);
  public readonly data = inject<string>(MAT_DIALOG_DATA);

  assocEntities$ = this.ds.entitiesWithAssocs$;
  src$ = this.ds.entitiesWithoutAssocs$;
  dst$ = this.ds.endpoint.unassociatedCandidates$;
  busy$ = this.ds.endpoint.assocBusy$;

  @ViewChild('serviceTemplate') serviceTemplate!: TemplateRef<any>;

  assocCols: TableColumn<Entity & { assoc: OutboundObject | undefined }>[] = [
    {
      header: 'Service',
      prop: (e) => getServiceId(e.uniqueId)
    },
    { header: 'Endpoint', prop: (e) => getEntryPointName(e.uniqueId) },
    {
      header: 'Name',
      prop: (e) => e.name,
      tooltip: (e) =>
        `Service: ${getServiceId(e.uniqueId)} \nEndpoint: ${getEntryPointName(e.uniqueId)}`
    },
    { header: 'Symbol', prop: (e) => extractSymbolCode(e), type: 'milsym' },
    {
      header: 'Associated Entity',
      prop: (e) => e.assoc?.dstName,
      tooltip: (e) =>
        `Service: ${getServiceId(e.assoc?.dstId)} \nEndpoint: ${getEntryPointName(e.assoc?.dstId)}`
    }
  ];

  srcCols: TableColumn<Entity & { assoc: OutboundObject | undefined }>[] = [
    {
      header: 'Service',
      prop: (e) => getServiceId(e.uniqueId)
    },
    { header: 'Endpoint', prop: (e) => getEntryPointName(e.uniqueId) },
    { header: 'Name', prop: (e) => e.name },
    { header: 'Symbol', prop: (e) => extractSymbolCode(e), type: 'milsym' }
  ];

  dstCols: TableColumn<Candidate>[] = [
    {
      header: 'Service',
      prop: (c) => getServiceId(c.objectId)
    },
    { header: 'Endpoint', prop: (c) => getEntryPointName(c.objectId) },
    { header: 'Name', prop: (c) => c.objectName },
    {
      header: 'Symbol',
      prop: (c) => c.iconCodeTypeMap.MILSTD_2525B ?? 'SFGPUCMS--*****',
      type: 'milsym'
    }
  ];

  ngOnInit(): void {
    this.assocEntities$.subscribe((e) => console.log(e));
  }

  assocTrackBy = (e: Entity) => e.uniqueId;
  srcTrackBy = (e: Entity) => e.uniqueId;
  dstTrackBy = (c: Candidate) => c.objectId;

  selectionMode = TableSelectionMode.Single;
  selectedSrc: Entity | undefined = undefined;
  selectedDst: Candidate | undefined = undefined;
  selectedAssoc: (Entity & { assoc: OutboundObject | undefined }) | undefined =
    undefined;

  onAssocSelected(e: Entity[]): void {
    this.selectedAssoc = e[0] as Entity & { assoc: OutboundObject | undefined };
  }

  onSrcSelected(e: Entity[]): void {
    this.selectedSrc = e[0];
  }

  onDstSelected(c: Candidate[]): void {
    this.selectedDst = c[0];
  }

  onAssoc(e: Entity, c: Candidate): void {
    this.ds.endpoint.associate(this.getOO(e, c));
  }

  onDissoc(e: Entity): void {
    this.ds.endpoint.dissociate(e.uniqueId);
  }

  onApply(): void {
    this.ds.endpoint.applyAssociations(this.data);
  }

  private getOO(e: Entity, c: Candidate): OutboundObject {
    return {
      srcId: e.uniqueId,
      srcName: e.name,
      dstId: c.objectId,
      dstName: c.objectName,
      idx: -1,
      dstParams: {
        listParameters: {},
        singleParameters: {}
      }
    };
  }

  get assDisabled() {
    return this.selectedSrc === undefined || this.selectedDst === undefined;
  }

  get disDisabled() {
    return this.selectedAssoc === undefined;
  }

  protected readonly undefined = undefined;
}
