import {
  After<PERSON>iewInit,
  Component,
  DestroyRef,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

import {
  TableColumn,
  TableComponent,
  TableSelectionMode
} from '@simfront/common-libs';

import { Subscription } from 'rxjs';

import { EndpointFilterStore } from '../../../data/endpoint/endpoint-filter/endpoint-filter.store';
import { FollowEntityFilter } from '../../../data/endpoint/filter.model';
import { Entity, extractSymbolCode } from '../../../data/entity/entity.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'vcci-follow-entity-filter',
  templateUrl: 'follow-entity-filter.component.html',
  styleUrls: ['follow-entity-filter.component.css'],
  standalone: false
})
export class FollowEntityFilterComponent
  implements OnChanges, AfterViewInit, OnD<PERSON>roy
{
  @ViewChild(TableComponent) table: TableComponent<Entity>;

  @Input({ required: true }) filter: FollowEntityFilter | undefined = undefined;
  @Input({ required: true }) candidateEntities: Entity[] = [];

  private _destroyRef = inject(DestroyRef);
  private _tableSelectionSub: Subscription | undefined = undefined;

  form:
    | FormGroup<{
        uniqueId: FormControl<string>;
        northSouthDistanceMetres: FormControl<number | null>;
        eastWestDistanceMetres: FormControl<number | null>;
      }>
    | undefined;

  trackBy = (e: Entity) => e.uniqueId;
  selectionMode = TableSelectionMode.Single;
  columns: TableColumn<Entity>[] = [
    { header: 'Id', prop: (e) => e.uniqueId },
    { header: 'Name', prop: (e) => e.name },
    { header: 'Symbol', prop: (e) => extractSymbolCode(e), type: 'milsym' }
  ];

  constructor(private store: EndpointFilterStore) {
    this.form = new FormGroup({
      uniqueId: new FormControl(
        { value: this.filter?.uniqueId, disabled: false },
        { nonNullable: false, updateOn: 'change' }
      ),
      northSouthDistanceMetres: new FormControl(
        { value: this.filter?.northSouthDistanceMetres ?? 10, disabled: false },
        { nonNullable: false, updateOn: 'change' }
      ),
      eastWestDistanceMetres: new FormControl(
        { value: this.filter?.eastWestDistanceMetres ?? 10, disabled: false },
        { nonNullable: false, updateOn: 'change' }
      )
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['filter']) {
      this.form.patchValue({ ...this.filter }, { emitEvent: false });
    }
    if (changes['candidateEntities']) {
      this.updateTableSelection();
    }
  }

  onSelectionChanged(selections: Entity[]): void {
    const selection = selections.length === 0 ? undefined : selections[0];
    const uniqueId = selection?.uniqueId;
    if (uniqueId !== this.filter.uniqueId) {
      this.form.patchValue({ uniqueId });
    }
  }

  ngAfterViewInit(): void {
    this.updateTableSelection();
    this.form.valueChanges
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((f) => this.store.updateFilter({ ...this.filter, ...f }));
  }

  ngOnDestroy(): void {
    if (this._tableSelectionSub) {
      this._tableSelectionSub.unsubscribe();
    }
  }

  updateTableSelection(): void {
    if (this.table) {
      const selection = this.candidateEntities.find(
        (c) => c.uniqueId === this.filter.uniqueId
      );
      if (selection) {
        this.table.selectionModel.select(selection);
      }
    }
  }
}
