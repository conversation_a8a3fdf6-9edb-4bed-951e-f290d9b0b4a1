:host {
  position: relative;
  display: grid;
  grid-template-rows: min-content auto;
  height: 100%;
}

.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-weight: 600;
  font-size: 21px;
  height: 28px;
  padding: 20px;
  border-bottom: 2px solid rgba(var(--divider-light-rgb), var(--opacity));

  mat-icon {
    font-size: 25px;
    height: 28px;
    width: 28px;
    margin-right: 14px;
  }

  label {
    font-weight: bold;
    font-size: 20px;
  }
}

.page-content {
  overflow-y: auto;
  max-height: 100%;
}
