import { ComponentType } from '@angular/cdk/overlay';
import { Component, ElementRef, EventEmitter, Input, Output, Renderer2, ViewChild } from '@angular/core';
import { IPopoutWindowModal, PopoutWindow, PopoutWindowConfig, PopoutWindowRect } from './popout-window.model';
import { PopoutWindowService } from './popout-window.service';

@Component({
    selector: 'vcci-popout-window',
    template: `
    <div class="file-container" #innerWrapper style="width: 100%; height: 100%;">
      <ng-content></ng-content>
    </div>`,
    styles: `
    .file-container {
      background: var(--background-gradient-light);
    }
    `,
    standalone: false
})
export class PopoutWindowComponent extends PopoutWindow {
  @ViewChild('innerWrapper') private innerWrapper!: ElementRef;

  @Input() windowTitle: string | undefined;

  @Input() positionRect: PopoutWindowRect | undefined;

  @Input() windowStyle: string | undefined;
  @Input() windowStyleUrl: string | undefined;
  @Input() suppressCloneStyles = false;

  @Output() opened: EventEmitter<undefined> = new EventEmitter();
  @Output() closed: EventEmitter<undefined> = new EventEmitter();

  constructor(private renderer2: Renderer2, private elementRef: ElementRef) {
    super();
    super.config = {
      title: this.windowTitle,
      suppressClonedStyles: this.suppressCloneStyles,
      windowStyleUrl: this.windowStyleUrl,
      windowStyle: this.windowStyle,
      rect: this.positionRect || this.elementRef.nativeElement.getBoundingClientRect()
    };
  }

  override afterClosed() {
    this.closed.emit();
    this.renderer2.appendChild(this.elementRef.nativeElement, this.innerWrapper.nativeElement);
  }

  override open<T, C extends IPopoutWindowModal>(component?: ComponentType<C>, config?: PopoutWindowConfig<T>): void {
    super.open(component, config);
    if (!this.window) {
      this.window = PopoutWindowService.createWindow(config);
      this.renderer2.appendChild(this.document!.body, this.innerWrapper.nativeElement);
      PopoutWindowService.assignStyle(this, config);
    } else {
      this.window.focus();
    }
    this.opened.emit();
  }
}
