import {
  Component,
  inject,
  Input,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { SFMap } from '@simfront/common-libs';
import { DataService, EndpointWithConn } from '../../../data/data.service';
import { Point } from '../../../shared/util/svg/line-svg.model';

interface RoutePart {
  id: string;
  point: Point;
  endpoint: EndpointWithConn;
}

export interface RouteItem {
  name: string;
  input: RoutePart;
  output: RoutePart;
  isFullRoute: boolean;
}

export interface OtherBlockItem {
  points: Point[];
  connectedTo: string[];
  isInput: boolean;
  endpoint: EndpointWithConn;
}

type Line = RouteItem & { points: Point[] };

@Component({
  selector: 'vcci-proto-router-svg',
  templateUrl: 'proto-router-svg.component.html',
  styleUrls: ['proto-router-svg.component.css'],
  standalone: false
})
export class ProtoRouterSvgComponent implements OnChanges {
  ds: DataService = inject(DataService);
  @Input() routeItems: RouteItem[] = [];
  @Input() selected: string[] = [];
  @Input() trigger = false;

  connectionBlocks: Line[] = [];
  connectionBlocksTrackBy = (_: number, i: Line) => `${i.name}`;
  otherBlocksTrackBy = (_: number, i: { key: string; value: OtherBlockItem }) =>
    `${i.value.connectedTo.join('-')}`;
  otherBlocks: SFMap<OtherBlockItem> = {};

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['routeItems'] || changes['trigger']) {
      this.connectionBlocks = [];
      this.otherBlocks = {};
      this.routeItems.forEach((r) => {
        const points = [
          { x: r.input.point.x + 74, y: r.input.point.y },
          this.getMiddlePoint(r),
          { x: r.output.point.x - 74, y: r.output.point.y }
        ];
        this.connectionBlocks.push({ ...r, points });
        this.addToOtherBlock(r.input, r.output, true);
        this.addToOtherBlock(r.output, r.input, false);
      });
    }
  }

  addToOtherBlock(a: RoutePart, b: RoutePart, isInput = true): void {
    const inputBlock = this.otherBlocks[a.id];
    if (inputBlock) {
      this.otherBlocks[a.id] = {
        ...inputBlock,
        connectedTo: [...inputBlock.connectedTo, b.id]
      };
    } else {
      this.otherBlocks[a.id] = {
        points: this.getPointOffset(a, isInput),
        connectedTo: [a.id, b.id],
        isInput,
        endpoint: a.endpoint
      };
    }
  }

  getPointOffset(route: RoutePart, isInput = true): Point[] {
    return [
      route.point,
      { x: isInput ? route.point.x + 74 : route.point.x - 74, y: route.point.y }
    ];
  }

  getMiddlePoint(routeItem: RouteItem): Point {
    const distanceX = routeItem.output.point.x - routeItem.input.point.x;
    const distanceY = routeItem.output.point.y - routeItem.input.point.y;
    const x = routeItem.input.point.x + distanceX / 2;
    const y = routeItem.input.point.y + distanceY / 2;
    return { x, y };
  }

  deleteRoute(id: string): void {
    this.ds.dataPath.openDialog({ crud: 'Delete', quantity: 'One', id });
  }
}
