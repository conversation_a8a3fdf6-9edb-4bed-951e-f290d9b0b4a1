<mat-form-field subscriptSizing="dynamic" appearance="outline" (click)="$event.stopPropagation()" style="width: 100%">
  <mat-label>Search Entities...</mat-label>
  <input
          matInput placeholder="Placeholder" [(ngModel)]="searchQuery" (ngModelChange)="onSearch()">
</mat-form-field>
<mat-selection-list (click)="$event.stopPropagation()" style="max-height: 300px" *ngIf="(entityList$ | async)?.length; else noEntities"
                    (selectionChange)="onSelectionChange($event)" [multiple]="false">
  <mat-list-option *ngFor="let entity of entityList$ | async" [value]="entity">
    {{ entity.name }}
  </mat-list-option>
</mat-selection-list>
<ng-template #noEntities>
  <span class="no_entities_found">No entities found.</span>
</ng-template>


