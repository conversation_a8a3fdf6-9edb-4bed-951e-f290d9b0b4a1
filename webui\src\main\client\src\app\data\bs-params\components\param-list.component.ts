import { Component } from '@angular/core';
import { BaseParamComponent } from './base-param.component';
import { <PERSON><PERSON><PERSON>r, <PERSON><PERSON>orm<PERSON>ield, Mat<PERSON>abel } from '@angular/material/form-field';
import { MatOption } from '@angular/material/core';
import { MatSelect } from '@angular/material/select';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckbox } from '@angular/material/checkbox';

@Component({
  selector: 'vcci-param-list',
  template: `
    <mat-form-field appearance="fill">
      <mat-label>{{ label }}</mat-label>
      <mat-select
        [formControl]="formControl"
        (selectionChange)="onOptionChange($event.value)"
        [multiple]="multiple"
      >
        @for (option of options; track option) {
          <mat-option
            [value]="option"
            [class.bold-text]="defaultValue === option"
          >
            {{ option }}
          </mat-option>
        }
      </mat-select>
      @if (invalid) {
        <mat-error>{{ errorMessage() }}</mat-error>
      }
    </mat-form-field>
    @if (this.parameter.group) {
      <mat-checkbox
        [checked]="isSelectedInGroup"
        [disabled]="!isPreviousEnabled"
        (change)="setGroupValue($event.checked)"
      />
    }
  `,
  styles: `
    .bold-text {
      font-weight: bold;
    }
  `,
  imports: [
    MatFormField,
    MatOption,
    MatSelect,
    MatLabel,
    ReactiveFormsModule,
    MatError,
    MatCheckbox
  ]
})
export class ParamListComponent extends BaseParamComponent {
  get options() {
    return this.parameter.list ? this.parameter.listOptions : [];
  }

  get multiple() {
    return this.parameter.valueList;
  }

  onOptionChange(value: string[] | string | boolean) {
    this.onValueChange(value);
    if (this.hasNext) {
      this.onGetNext();
    }
  }
}
