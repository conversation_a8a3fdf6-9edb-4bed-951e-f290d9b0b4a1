import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DataService } from '../../data/data.service';
import { RouterLegendsComponent } from '../components/router-legends.component';
import { ViewStyle } from '../models/router-page.model';

@Component({
  selector: 'vcci-proto-router-container',
  templateUrl: './proto-router-container.component.html',
  styleUrls: ['./proto-router-container.component.css'],
  standalone: false
})
export class ProtoRouterContainerComponent {
  routes$ = this.data.dataPath.routes$;
  inputs$ = this.data.inputEndpointWithConn$;
  outputs$ = this.data.outputEndpointWithConn$;
  selectedEndpointIds$ = this.data.endpoint.selectedIds$;
  selectedConnectionId$ = this.data.connection.selectedId$;
  selectedRoutes$ = this.data.dataPath.selectedIds$;

  viewStyle: ViewStyle = 'Medium';

  constructor(
    private data: DataService,
    private matDialog: MatDialog
  ) {}

  // TODO: Move this to action, reducer for the page.
  openLegendsDialog(): void {
    this.matDialog.open(RouterLegendsComponent);
  }
}
