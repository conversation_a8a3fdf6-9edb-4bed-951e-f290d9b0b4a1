import { Component, inject, Input, OnInit } from '@angular/core';
import { nullishOrEmpty } from '../../../../shared/util/util.model';
import {
  AttributeFilter,
  Filter,
  FilterInfo,
  FilterType,
  GeoFilter,
  isAttribute<PERSON>ilter,
  isEntityFilter,
  isFollowEntityFilter,
  isGeoFilter,
  isOptionFilter,
  OptionFilter
} from '../../filter.model';
import { EndpointFilterStore } from '../endpoint-filter.store';

@Component({
  selector: 'vcci-filter-editor',
  templateUrl: 'endpoint-filter-editor.component.html',
  styleUrls: ['endpoint-filter-editor.component.css'],
  standalone: false
})
export class EndpointFilterEditorComponent implements OnInit {
  store = inject(EndpointFilterStore);
  protected readonly isAttributeFilter = isAttributeFilter;
  protected readonly isOptionFilter = isOptionFilter;
  protected readonly isEntityFilter = isEntityFilter;
  protected readonly isFollowEntityFilter = isFollowEntityFilter;
  protected readonly isGeoFilter = isGeoFilter;
  protected readonly FilterType = FilterType;

  protected readonly entities$ = this.store.candidateEntities$;
  protected readonly routes$ = this.store.routes$;
  protected readonly newGeoAreaDrawn$ = this.store.newGeoAreaDrawn$;
  protected readonly isGeoDrawn$ = this.store.isGeoDrawn$;
  protected readonly treeData$ = this.store.treeData$;

  @Input({ required: true }) filter: Filter | undefined;
  @Input({ required: true }) info: FilterInfo | undefined;

  constructor() {}

  ngOnInit() {
    this.store.getCandidateEntities();
    this.store.getRoutes();
  }

  onOptionFilterChange(
    filter: OptionFilter,
    option: string,
    checked: boolean
  ): void {
    if (!filter.selectedOptions.includes(option) && checked) {
      filter.selectedOptions.push(option);
    } else if (filter.selectedOptions.includes(option) && !checked) {
      filter.selectedOptions.splice(filter.selectedOptions.indexOf(option), 1);
    }
    this.store.updateFilter(filter);
  }

  onAttributeFilterChange(
    filter: AttributeFilter<string | number>,
    value: string
  ): void {
    if (nullishOrEmpty(value)) {
      this.store.removeFilter(filter);
    } else {
      this.store.updateFilter({
        ...filter,
        value: this.info.type === FilterType.String ? value : (+value as number)
      });
    }
  }

  onGeoFilterChange(filter: GeoFilter): void {
    this.store.updateFilter(filter);
  }
}
