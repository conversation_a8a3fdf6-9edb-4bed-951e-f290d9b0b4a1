import { Injectable } from '@angular/core';

import { filter, Observable, Subject } from 'rxjs';

import { Entity } from './entity.model';
import { bufferTimeById } from '../../shared/util/util.model';
import { EventSourceService } from '../../shared/services/event-source.service';
import { environment } from '../../../environments/environment';

@Injectable({ providedIn: 'root' })
export class EntityService extends EventSourceService<Entity> {

  override onOpen = () => {};
  override onMessage = (me: MessageEvent<Entity>) => this._entities$.next(me.data);
  override onError = (e: Event) => console.error(e);

  private _entities$: Subject<Entity> = new Subject();
  public entities$: Observable<Entity[]> = this._entities$.pipe(
    bufferTimeById(1000, e => e.uniqueId),
    filter(entities => entities.length > 0)
  );

  constructor() {
    super(`${environment.origin}/events/entities`);
  }

  override stop() {
    super.stop();
    this._entities$.complete();
  }

}
