import { createEverything } from '@simfront/common-libs';

import { RootState } from '..';
import {
  ConnectionPanelDisplay,
  SidePanelState
} from 'src/app/core/reducers/layout.reducer';
import { validateObject } from 'src/app/shared/util/util.model';

export type Engine = 'Leaflet' | 'Luciad 2D' | 'Luciad 3D';
export type LocationFormat = 'Lat Lon' | 'Mgrs';

export interface Settings {
  connPanel: ConnectionPanelSettings;
  cursit: CursitSettings;
  configDialog: ConfigDialogSettings;
  layout: LayoutSettings;
}

export interface ConnectionPanelSettings {
  mode: SidePanelState;
  position: 'left' | 'right';
  expanded: boolean;
  connectionsExpanded: boolean;
  connectionDisplay: ConnectionPanelDisplay;
}

export interface CursitSettings {
  engine: Engine;
  locationFormat: LocationFormat;
  gridColour: string;
}

export interface ConfigDialogSettings {
  useConDefaults: boolean;
  useEndDefaults: boolean;
  networkInterface: string;
  rememberNetworkInterface: boolean;
}

export interface LayoutSettings {
  hideDataman: boolean;
  hideArcs: boolean;
}


export function isValidSettings(s: any): s is Settings {
  // Define the schema for validation
  const settingsSchema = {
    connPanel: {
      mode: ['Consolidated', 'Routing', 'Hidden'],
      position: ['left', 'right'],
      expanded: 'boolean',
      connectionsExpanded: 'boolean',
      connectionDisplay: ['ALL', 'ENDPOINTS']
    },
    cursit: {
      engine: ['Leaflet', 'Luciad 2D', 'Luciad 3D'],
      locationFormat: ['Lat Lon', 'Mgrs'],
      gridColour: 'string'
    },
    configDialog: {
      useConDefaults: 'boolean',
      useEndDefaults: 'boolean',
      networkInterface: 'string',
      rememberNetworkInterface: 'boolean'
    },
    layout: {
      hideDataman: 'boolean',
      hideArcs: 'boolean'
    }
  };

  return validateObject(s, settingsSchema);
}

export const DEFAULT_SETTINGS: Settings = {
  connPanel: {
    mode: 'Consolidated',
    position: 'left',
    expanded: true,
    connectionsExpanded: true,
    connectionDisplay: 'ALL'
  },
  cursit: {
    engine: 'Leaflet',
    locationFormat: 'Lat Lon',
    gridColour: '#e6daa6'
  },
  configDialog: {
    useConDefaults: true,
    useEndDefaults: true,
    rememberNetworkInterface: false,
    networkInterface: ''
  },
  layout: {
    hideDataman: false,
    hideArcs: false
  }
};

export const { facade: SettingsFacadeBase, reducer: settingsReducer } =
  createEverything<RootState, Settings, 'settings', never, string>(
    'settings',
    () => 'user',
    '/settings',
    (state) => state.settings,
    false,
    'local'
  );
