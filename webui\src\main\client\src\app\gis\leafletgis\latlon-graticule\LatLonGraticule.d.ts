/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/2/2024, 4:05 PM
 */

import { Layer, LayerOptions, Map } from 'leaflet';

declare interface GridOptions extends LayerOptions {
  showLabel?: boolean;
  opacity?: number;
  weight?: number;
  color?: string;
  font?: string;
  lngLineCurved?: number;
  latLineCurved?: number;
  zoomInterval?: [];
}

declare class LatLonGraticuleLayer extends Layer {
  constructor(options?: GridOptions);

  override onAdd(map: Map): this;

  override onRemove(map: Map): this;
}

