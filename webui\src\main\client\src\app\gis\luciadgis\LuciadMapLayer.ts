import { Feature } from '@luciad/ria/model/feature/Feature';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { Layer } from '@luciad/ria/view/Layer';
import { GISElement } from '../interface/GISElement';
import { AbstractMapLayer } from '../interface/MapLayer';
import { LuciadMilitaryMapElement } from './LuciadMilitaryMapElement';

export class LuciadMapLayer extends AbstractMapLayer<Layer> {
  gisAddElement(element: LuciadMilitaryMapElement): void {
    if (element.gisObject) {
      const gisLayer = this.getGISLayer();
      if (gisLayer && gisLayer instanceof FeatureLayer) {
        gisLayer.model.add(element.gisObject as Feature);
      }
    }
  }

  gisRemoveElement(element?: LuciadMilitaryMapElement): boolean | Promise<boolean> {
    const gisLayer = this.getGISLayer();
    if (gisLayer && gisLayer instanceof FeatureLayer) {
      if (element) {
        return gisLayer.model.remove(element.id);
      }
    }

    return false;
  }

  setGISLayerVisibility(visible: boolean): void {
    const lyr = this.getGISLayer();
    lyr.visible = visible;
    if (!lyr.visibleInTree) {
      lyr.visibleInTree = visible;
    }
  }

  isVisible(): boolean {
    const layer = this.getGISLayer();
    return layer.visible;
  }

  setSelectable(selectable: boolean): void {
    const lyr: FeatureLayer = this.getGISLayer() as FeatureLayer;
    lyr.selectable = selectable;
  }

  setEditable(editable: boolean): void {
    const lyr: FeatureLayer = this.getGISLayer() as FeatureLayer;
    lyr.editable = editable;
  }

  getElement(id: string): GISElement<Layer> | null | undefined {
    if (id in this.elementCache) {
      return this.elementCache[id];
    }

    return null;
  }

  override redraw(): Promise<void> {
    if (this.isRedrawLayer()) {
      if (this.getGISLayer() instanceof FeatureLayer) {
        (this.getGISLayer() as FeatureLayer).painter.invalidateAll();
      }
    }
    return null;
  }

  isFeatureLayer(): boolean {
    const layer = this.getGISLayer();
    return layer.type !== 'BASE' && layer instanceof  FeatureLayer;
  }
}
