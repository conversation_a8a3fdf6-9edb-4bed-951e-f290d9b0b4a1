import {
  AfterViewInit,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import { SFMap } from '@simfront/common-libs';
import { LOG_SEVERITIES, LogEntry } from '../../data/log/log.model';
import { LogFilters } from '../containers/log-container.component';
import { FormControl, FormGroup } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { MatSelect } from '@angular/material/select';

type CheckboxState = 'unchecked' | 'checked' | 'indeterminate';

@Component({
  selector: 'vcci-log',
  template: `
    <form [formGroup]="formGroup">
      <mat-form-field>
        <mat-label>Service</mat-label>
        <mat-select #nodeMatSelect formControlName="nodes" multiple>
          <div
            matRipple
            role="option"
            class="mat-mdc-option mdc-list-item mat-mdc-option-multiple"
            [class.mdc-list-item--selected]="isServiceSelected"
            (click)="onMasterServiceCheckboxChange()"
          >
            <mat-pseudo-checkbox
              class="mat-mdc-option-pseudo-checkbox"
              [state]="serviceCheckboxState"
            />
            <span class="mdc-list-item__primary-text">All</span>
          </div>
          <mat-option *ngFor="let node of nodes" [value]="node">{{
            node
          }}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field>
        <mat-label>Severity Level</mat-label>
        <mat-select formControlName="severities" multiple>
          <div
            matRipple
            role="option"
            class="mat-mdc-option mdc-list-item mat-mdc-option-multiple"
            [class.mdc-list-item--selected]="isSeveritySelected"
            (click)="onMasterSeverityCheckboxChange()"
          >
            <mat-pseudo-checkbox
              class="mat-mdc-option-pseudo-checkbox"
              [state]="severityCheckboxState"
            />
            <span class="mdc-list-item__primary-text">All</span>
          </div>
          <mat-option *ngFor="let severity of severities" [value]="severity">{{
            severity
          }}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field>
        <input matInput formControlName="filter" placeholder="Search..." />
      </mat-form-field>

      <mat-slide-toggle formControlName="grouped" class="group-slider">
        Group
      </mat-slide-toggle>

      <flex-block />

      <button
        mat-icon-button
        style="position: relative; top: -10px;"
        (click)="onRefresh.emit()"
        matTooltip="Refresh Logs"
      >
        <mat-icon>refresh</mat-icon>
      </button>
    </form>

    <div class="content">
      <vcci-ms-log
        *ngFor="let item of logEntries | keyvalue"
        [nodeName]="item.key"
        [logEntries]="item.value"
      />
    </div>
  `,
  styles: [
    `
      form {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: fit-content;
        padding: 10px;
        gap: 10px;
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
      }

      .group-slider {
        position: relative;
        bottom: 7px;
        left: 5px;
      }
    `
  ],
  standalone: false
})
export class LogComponent implements AfterViewInit {
  @ViewChild('nodeMatSelect', { read: MatSelect, static: true })
  nodeMatSelect!: MatSelect;

  @Input({ required: true }) nodes: string[] = [];
  @Input({ required: true }) logEntries: SFMap<LogEntry[]> = {};

  @Output() onRefresh = new EventEmitter();
  @Output() filterChanges = new EventEmitter<LogFilters>();

  formGroup = new FormGroup({
    nodes: new FormControl(['VCCI']),
    severities: new FormControl([...LOG_SEVERITIES]),
    filter: new FormControl(''),
    grouped: new FormControl(false)
  });

  serviceCheckboxState: CheckboxState = 'indeterminate';
  severityCheckboxState: CheckboxState = 'checked';

  protected readonly severities = LOG_SEVERITIES;

  constructor(private destroyRef: DestroyRef) {
    this.formGroup.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe(() => this.updateFilters());
    this.formGroup
      .get('nodes')
      .valueChanges.pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe(() => this.updateServiceCheckboxState());
    this.formGroup
      .get('severities')
      .valueChanges.pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe(() => this.updateSeverityCheckboxState());
  }

  ngAfterViewInit() {
    this.nodeMatSelect.options.changes
      .pipe(takeUntilDestroyed(this.destroyRef), distinctUntilChanged())
      .subscribe(() => this.updateServiceCheckboxState());
  }

  onMasterServiceCheckboxChange(): void {
    if (
      this.serviceCheckboxState === 'unchecked' ||
      this.serviceCheckboxState === 'indeterminate'
    ) {
      this.formGroup.get('nodes').setValue([...this.nodes]);
    } else {
      this.formGroup.get('nodes').setValue([]);
    }
  }

  updateServiceCheckboxState(): void {
    const length = this.formGroup.get('nodes').getRawValue().length;
    this.serviceCheckboxState =
      length === 0
        ? 'unchecked'
        : length === this.nodes.length
          ? 'checked'
          : 'indeterminate';
  }

  onMasterSeverityCheckboxChange(): void {
    if (
      this.severityCheckboxState === 'unchecked' ||
      this.severityCheckboxState === 'indeterminate'
    ) {
      this.formGroup.get('severities').setValue([...LOG_SEVERITIES]);
    } else {
      this.formGroup.get('severities').setValue([]);
    }
  }

  updateSeverityCheckboxState(): void {
    const length = this.formGroup.get('severities').getRawValue().length;
    this.severityCheckboxState =
      length === 0
        ? 'unchecked'
        : length === LOG_SEVERITIES.length
          ? 'checked'
          : 'indeterminate';
  }

  get isServiceSelected(): boolean {
    return this.serviceCheckboxState !== 'unchecked';
  }

  get isSeveritySelected(): boolean {
    return this.severityCheckboxState !== 'unchecked';
  }

  updateFilters(): void {
    this.filterChanges.emit(this.formGroup.getRawValue());
  }
}
