import { Pipe, PipeTransform } from '@angular/core';
import { EndpointWithConn } from '../../data/data.service';
import { hasFilterValue } from '../../data/endpoint/filter.model';

@Pipe({
  name: 'hasFilters',
  pure: true,
  standalone: true
})
export class HasFilterPipe implements PipeTransform {
  transform(endpoint: EndpointWithConn): boolean {
    return endpoint?.filters?.some((g) =>
      Object.values(g)?.some((f) => hasFilterValue(f))
    );
  }
}
