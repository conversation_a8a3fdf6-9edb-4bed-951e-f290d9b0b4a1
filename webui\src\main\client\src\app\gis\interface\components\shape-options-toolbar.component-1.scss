@import 'toolbar-mixins';

@mixin toolbar-wrapper-styling {
  @include toolbar-standards;
}

.toolbar-wrapper {
  @include toolbar-wrapper-styling;
}

.options-bar-wrapper {
  @include toolbar-wrapper-styling;
  right: 50px;
}

.toolbar {
  @include toolbar-styling;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: start;
  gap: 10px;
}

.button-wrapper {
  @include button-wrapper-styling;
}

.button-group {
  display: flex;
  gap: 4px;
  margin-right: 10px;
}

button {
  @include toolbar-button-styling;

  &.option-button {
    width: 35px;
    height: 35px;
    margin-right: 5px;
  }
}

.expanded {
  @include expanded-button-styling;
}

:host ::ng-deep .expanded .button-icon .foregroundColor {
  @include expanded-button-styling;
}

button:not(.color-picker-button):hover {
  @include button-hover-styling;
}

.color-picker-button:hover {
  cursor: pointer;
  background-color: $active-button-background-color;
}

button:active {
  @include button-active-styling;
}

button.selected {
  @include button-hover-styling;
}

label {
  @include label-styling;

  &.fill-label,
  &.stroke-label {
    margin-top: unset;
    margin-right: 10px;
    font-size: 13px;
  }
}

::ng-deep .button-icon {
  @include toolbar-button-icon-styling;
}

::ng-deep .line-stroke-select {
  background-color: $button-background-color !important;
}

::ng-deep .line-stroke-select .mat-mdc-select-value {
  width: 100px !important;
}

.stroke-select-wrapper {
  width: 100px;
  margin-right: 10px;
}

::ng-deep .line-stroke-option,
::ng-deep .stroke-pattern-option {
  background-color: $button-background-color !important;
  min-height: 30px !important;

  & mat-pseudo-checkbox {
    display: none;
  }
}

.stroke-pattern-option[aria-selected='true'],
.line-stroke-option[aria-selected='true'],
.stroke-pattern-option:hover,
.line-stroke-option:hover {
  background-color: $active-button-background-color !important;
  min-height: 30px !important;
}

::ng-deep .line-stroke-select path {
  fill: $icon-color;
}

::ng-deep .line-stroke-select svg {
  margin-left: -2px;
}

::ng-deep .line-stroke-select .mat-mdc-select-trigger {
  width: 100px;
  border-radius: 2px;
  border: solid 1px $icon-color;
}

.custom-color-input {
  width: 0;
  height: 0;
  margin-top: 20px;
  background-color: transparent;
  border: none;
}

:host ::ng-deep .color-picker-icon .foregroundColor {
  fill: inherit;
}

//mat-mdc-select-value is the class that keeps the main combobox area

//line stroke patterns
::ng-deep .solid .mat-mdc-select-value::before {
  @include stroke-option-styling(3px, solid, $icon-color);
}

::ng-deep .dashed .mat-mdc-select-value::before {
  @include stroke-option-styling(3px, dashed, $icon-color);
}

::ng-deep .dot .mat-mdc-select-value::before {
  @include stroke-option-styling(3px, dotted, $icon-color);
}
