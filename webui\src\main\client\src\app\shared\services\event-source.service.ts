import { OnBeforeUnload } from '@simfront/common-libs';

export abstract class EventSourceService<T> {

  private es: EventSource | null = null;

  abstract onOpen: () => void;
  abstract onMessage: (me: MessageEvent<T>) => void;
  abstract onError: (e: Event) => void;

  protected constructor(private url: string) {
  }

  open(url?: string) {
    if (url) {
      this.url = url;
    }
    this._connect();
  }

  protected _connect(): void {
      this.es = new EventSource(this.url, { withCredentials: true });
      this.es.onopen = this.onOpen;
      this.es.onmessage = (me: MessageEvent) => {
        // Ignore the open and ping message...
        if (me.data === '__OPEN__' || me.data === '__PING__' || me.data === '__HEARTBEAT__') {
          return;
        }
        try {
          this.onMessage({ ...me, data: JSON.parse(me.data) });
        } catch (ignored) {
          this.onMessage(me);
        }
      };
      this.es.onerror = this.onError;
  }

  stop(): void {
    console.warn(`Stopped event source service: ${this.url}`);
    this.teardown();
  }

  @OnBeforeUnload
  private _stop(): void {
    this.stop();
  }

  protected teardown(): void {
    this.es.close();
    this.es = null;
  }

}
