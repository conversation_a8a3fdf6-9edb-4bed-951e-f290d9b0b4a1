import { PortalModule } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LetDirective } from '@ngrx/component';
import { CoreModule } from '@simfront/common-libs';
import { BsnModule } from '../battlespace/bsn.module';
import { CursitModule } from '../cursit/cursit.module';
import { FiltersModule } from '../filters/filters.module';
import { MaterialModule } from '../material/material.module';
import { FilterHasValuePipe } from '../shared/pipes/filter-has-value.pipe';
import { ObjectPipe } from '../shared/pipes/object.pipe';
import { StringPipe } from '../shared/pipes/string.pipe';
import { PopoutDialogComponent } from '../shared/popout-window/popout-dialog.component';
import { ConnectionForm } from './connection/connection-form.component';
import { AssociationDialog } from './endpoint/association/association.dialog';
import { EndpointFilterDetailsComponent } from './endpoint/endpoint-filter/endpoint-filter-details/endpoint-filter-details.component';
import { EndpointFilterEditorComponent } from './endpoint/endpoint-filter/endpoint-filter-editor/endpoint-filter-editor.component';
import { EndpointFilterGroupComponent } from './endpoint/endpoint-filter/endpoint-filter-group/endpoint-filter-group.component';
import { EndpointFilterDialog } from './endpoint/endpoint-filter/endpoint-filter.dialog';
import { EndpointForm } from './endpoint/endpoint-form.component';
import { MetricsDialog } from './endpoint/metrics/metrics.dialog';
import { DetailedMetricsComponent } from './endpoint/metrics/detailed-metrics.component';

@NgModule({
  declarations: [
    MetricsDialog,
    DetailedMetricsComponent,
    AssociationDialog,
    EndpointFilterDialog,
    EndpointFilterGroupComponent,
    EndpointFilterDetailsComponent,
    EndpointFilterEditorComponent,
    ConnectionForm,
    EndpointForm
  ],
  imports: [
    MaterialModule,
    CommonModule,
    CursitModule,
    FiltersModule,
    CoreModule,
    StringPipe,
    LetDirective,
    ObjectPipe,
    PortalModule,
    PopoutDialogComponent,
    FilterHasValuePipe,
    BsnModule
  ]
})
export class DataModule {}
