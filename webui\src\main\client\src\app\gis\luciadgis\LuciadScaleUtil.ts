import { OutOfBoundsError } from '@luciad/ria/error/OutOfBoundsError';
import { createEllipsoidalGeodesy } from '@luciad/ria/geodesy/GeodesyFactory';
import { LineType } from '@luciad/ria/geodesy/LineType';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { createPoint } from '@luciad/ria/shape/ShapeFactory';
import { createTransformation } from '@luciad/ria/transformation/TransformationFactory';
import { Map } from '@luciad/ria/view/Map';

/**
 * Used to calculate and access map scale
 * Modification of LuciadRIA_2020.0.03\samples\common\ScaleUtil.js
 */
export class ScaleUtil {
  private readonly WGS84 = getReference('CRS:84');
  private readonly geodesy = createEllipsoidalGeodesy(this.WGS84);
  private readonly INCH_TO_CM = 2.54;
  private readonly CM_TO_METER = 100;

  private static truncate(aNumber: number): number {
    const value = 100000000;
    return Math.round(aNumber * value) / value;
  }

  private _calculateMapUnitPerMeterRatio(map: Map): number {
    const { viewSize } = map;
    const viewPoint = [viewSize[0] / 2, viewSize[1] / 2];
    const worldReference = map.reference;
    const mapToModelTransformation = createTransformation(worldReference, this.WGS84);

    try {
      // The points on the world reference
      const mapLeftPoint = map.viewToMapTransformation.transform(
        createPoint(null, [viewPoint[0] - 50, viewPoint[1]])
      );
      const mapRightPoint = map.viewToMapTransformation.transform(
        createPoint(null, [viewPoint[0] + 50, viewPoint[1]])
      );

      // The points on the model reference
      const modelLeftPoint = mapToModelTransformation.transform(mapLeftPoint);
      const modelRightPoint = mapToModelTransformation.transform(mapRightPoint);

      // The distance between the points
      const distanceInMeters = this.geodesy.distance(
        modelLeftPoint,
        modelRightPoint,
        LineType.SHORTEST_DISTANCE
      );

      if (distanceInMeters === 0.0) {
        // This happens when we are zoomed in a lot
        return 1;
      }
      const mapDistance = Math.sqrt((mapLeftPoint.x - mapRightPoint.x) ** 2
        + (mapLeftPoint.y - mapRightPoint.y) ** 2);
      const mapUnitPerMeterRatio = mapDistance / distanceInMeters;

      // Now we discretize the results of the calculations.  This makes sure getting the map scale
      // after it was just set yields the same result.
      return ScaleUtil.truncate(mapUnitPerMeterRatio);
    } catch (e) {
      if (e instanceof OutOfBoundsError) {
        return 1;
      }
      throw e;
    }
  }

  getScaleAtMapCenter(map: Map, dpi?: number): number {
    const mapUnitPerMeter = this._calculateMapUnitPerMeterRatio(map);
    // scale is mapscale -> how many real world cm are displayed in 1cm.
    // recalculate to pixels per meter, assume a 96dpi screen
    let DPI = 96;
    if (dpi) {
      DPI = dpi;
    }

    return map.mapScale[0] * (DPI / this.INCH_TO_CM) * this.CM_TO_METER * mapUnitPerMeter;
  }
}
