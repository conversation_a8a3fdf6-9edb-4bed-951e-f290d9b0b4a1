import { LatLngTuple } from 'leaflet';
import { Graphics, Container, Text } from 'pixi.js';
import { Point } from 'leaflet';
import { Coordinate } from '../../cursit/models/cursit.model';
import {
  LocationSettable,
  MilSymbolSettable
} from '../interface/LocationSettable';
import { ShapeType } from '../interface/models/map-element.model';
import { LeafletMap } from './LeafletMap';
import { LeafletMapDisplay } from './LeafletMapDisplay';

/**
 * Base class for all shapes in Leaflet GIS.
 * Provides common functionality for different shape types.
 */
export abstract class LeafletGraphics
  extends Container
  implements LocationSettable, MilSymbolSettable
{
  _id: string;
  _type: ShapeType;
  _locations: LatLngTuple[] = [];
  _projectedLocations: LatLngTuple[] = [];
  _graphicsElement: Graphics;
  _selectionBox: Graphics;
  _labelText: Text;
  _selected: boolean = false;
  _map: LeafletMapDisplay;
  _catCode: string;
  _options: {
    width?: number;
    maximumRange?: number;
    minimumRange?: number;
    orientationAngle?: number;
    sectorAngle?: number;
  };
  _label: string;
  _labelAdded = false;
  _originalIndexInParent: number;
  _broughtToFront = false;
  _strokeWidth: number = 3000;

  constructor(
    id: string,
    map: LeafletMapDisplay,
    type: ShapeType,
    catCode: string,
    coordinates: Coordinate[],
    label: string,
    options?: {
      width?: number;
      maximumRange?: number;
      minimumRange?: number;
      orientationAngle?: number;
      sectorAngle?: number;
    }
  ) {
    super();
    this._id = id;
    this._map = map;
    this._type = type;
    this._catCode = catCode;
    this._label = label;
    this._options = options;
    this._graphicsElement = new Graphics();
    this._selectionBox = new Graphics();
    this._labelText = new Text();
    this.addChild(this._graphicsElement);

    // Initialize coordinates if provided
    if (coordinates) {
      this.setLocation(coordinates);
    }
  }

  select(): void {
    this._selected = true;
    this.alpha = 1.0;
    this._selectionBox.visible = true;
    this._map.selectionMenuElement.visible = true;
    this.bringToFront();
  }

  deselect(): void {
    this._selected = false;
  }

  bringToFront() {
    if (this.parent) {
      const { parent } = this;
      this._originalIndexInParent = parent.getChildIndex(this);
      parent.removeChild(this);
      parent.addChild(this);
    }
  }

  addSelectionMenu(): void {
    this._map.selectionMenuElement.scale.set(
      1 / this._map.map.scaleForProjection
    );
  }

  changeColor(): void {
    const scale = this._map.leafletMap.scaleForProjection;
    const adjustedStrokeWidth = this._strokeWidth / scale;

    this._graphicsElement.stroke({
      width: adjustedStrokeWidth,
      color: this.selected
        ? 'red'
        : this.getFillColorFromCatCode(this._catCode),
      alignment: 0.5
    });
  }

  set broughtToFront(value: boolean) {
    this._broughtToFront = value;
  }

  get broughtToFront(): boolean {
    return this._broughtToFront;
  }

  get originalParentIndex(): number {
    return this._originalIndexInParent;
  }

  /**
   * Gets the shape ID
   */
  get id(): string {
    return this._id;
  }

  /**
   * Sets the shape ID
   */
  set id(id: string) {
    this._id = id;
  }

  /**
   * Gets the shape type
   */
  get type(): ShapeType {
    return this._type;
  }

  /**
   * Gets the locations as LatLngTuple array
   */
  get locations(): LatLngTuple[] {
    return this._locations;
  }

  /**
   * Sets the locations from LatLngTuple array
   */
  set locations(locations: LatLngTuple[]) {
    this._locations = locations;
    this.projectLocations(locations);
  }

  /**
   * Gets the locations as Coordinate array
   */
  get locationsAsCoordinate(): Coordinate[] {
    return this._locations.map((loc) => [loc[0], loc[1]]);
  }

  /**
   * Gets the projected locations
   */
  get projectedLocations(): LatLngTuple[] {
    return this._projectedLocations;
  }

  /**
   * Sets the projected locations
   */
  set projectedLocations(locations: LatLngTuple[]) {
    this._projectedLocations = locations;
  }

  /**
   * Gets whether the shape is selected
   */
  get selected(): boolean {
    return this._selected;
  }

  /**
   * Sets whether the shape is selected
   */
  set selected(selected: boolean) {
    this._selected = selected;
    this._map.selected = selected;
  }

  /**
   * Projects locations from geographic coordinates to map coordinates
   * @param locations Array of locations to project
   * @returns Projected locations
   */
  projectLocations(locations: LatLngTuple[]): LatLngTuple[] {
    this._projectedLocations = locations.map((loc) =>
      this._map.map.projectFromLatLonToLatLngExpression([loc[0], loc[1]])
    );
    return this._projectedLocations;
  }

  /**
   * Gets a location at a specific index
   * @param index Index of the location to retrieve
   * @returns The location at the specified index
   */
  getLocation(index: number): LatLngTuple {
    return this._locations[index];
  }

  /**
   * Sets the location of the shape
   * @param locations Coordinates for the shape
   */
  setLocation(locations: Coordinate[] | Coordinate): void {
    if (Array.isArray(locations) && !Array.isArray(locations[0])) {
      // Single coordinate
      this._locations = [[locations[0] as number, locations[1] as number]];
    } else if (Array.isArray(locations) && Array.isArray(locations[0])) {
      // Array of coordinates
      this._locations = locations.map((loc) => [
        loc[0],
        loc[1]
      ]) as LatLngTuple[];
    }

    this.projectLocations(this._locations);
    this.redraw();
  }

  /**
   * Adds a location to the shape
   * @param location Location to add
   * @param index Optional index to insert at
   */
  addLocation(location: LatLngTuple | Coordinate, index?: number): void {
    let latLng: LatLngTuple;

    if (Array.isArray(location) && location.length >= 2) {
      latLng = [location[0] as number, location[1] as number];
    } else {
      throw new Error('Invalid location format');
    }

    if (index !== undefined) {
      this._locations.splice(index, 0, latLng);
    } else {
      this._locations.push(latLng);
    }

    this.projectLocations(this._locations);
    this.redraw();
  }

  /**
   * Adds a label to the shape
   */
  addLabel(): void {
    if (this._label) {
      const coordinates = this._locations[0];
      let projected: Point;

      projected = this._map.map.projectFromLatLonToPoint([
        coordinates[0],
        coordinates[1]
      ]);

      // Configure label properties
      this._labelText.text = this._label;
      this._labelText.style.fontSize = 14;
      this._labelText.style.fontWeight = 'bold';
      this._labelText.scale.set(1 / this._map.map.scaleForProjection);
      this._labelText.x = projected.x;
      this._labelText.y = projected.y;

      this.addChild(this._labelText);
      this._labelAdded = true;
    }
  }

  /**
   * Sets the visibility of the label
   * @param visible Whether the label should be visible
   */
  setLabelVisibility(visible: boolean): void {
    this._labelText.visible = visible;
  }

  /**
   * Removes a location at the specified index
   * @param index Index of the location to remove
   */
  removeLocation(index: number): void {
    if (index >= 0 && index < this._locations.length) {
      this._locations.splice(index, 1);
      this._projectedLocations.splice(index, 1);
      this.redraw();
    }
  }

  /**
   * Updates the shape's speed and bearing
   * @param speed Speed value
   * @param bearing Bearing in degrees
   */
  abstract setSpeedAndBearing(speed: number, bearing: number): void;

  /**
   * Sets the symbol code for the shape
   * @param symbolCode Symbol code
   */
  abstract setSymbolCode(symbolCode: string): void;

  /**
   * Redraws the shape based on current properties
   * Subclasses must implement this method
   */
  abstract redraw(): void;

  /**
   * Clears all graphics
   */
  clear(): this {
    this._graphicsElement.clear();
    return this;
  }

  /**
   * Helper method to determine fill color based on category code
   * @param catCode Category code
   * @returns Fill color as number
   */
  public getFillColorFromCatCode(catCode: string): number {
    switch (catCode) {
      case 'FE': // Friendly
        return 0x3388ff; // Blue
      case 'HO': // Hostile
        return 0xff3333; // Red
      case 'UN': // Unknown
        return 0xffff33; // Yellow
      case 'NE': // Neutral
        return 0x33cc33; // Green
      default:
        return 0x3388ff; // Default blue
    }
  }

  /**
   * Helper method to determine the zoom scale
   * @param zoom Zoom level
   * @returns Zoom scale
   */
  public getZoomScale(zoom: number): number {
    return this._map.map.getZoomScale(zoom, 1);
  }

  /**
   * TODO: If not needed in redraw, we could just update the position of the graphics element
   * Compute the destination point from a starting point, distance, and bearing
   * @param latDeg Latitude in degrees
   * @param lonDeg Longitude in degrees
   * @param distanceMeters Distance in meters
   * @param bearingDeg Bearing in degrees
   * @returns The destination point
   */
  public computeDestinationPoint(
    latDeg: number,
    lonDeg: number,
    distanceMeters: number,
    bearingDeg: number
  ): { lat: number; lon: number } {
    // Earth's radius in meters (WGS84)
    const R = 6378137;
    // Convert to radians
    const toRad = (val: number) => (val * Math.PI) / 180;
    // Convert to degrees
    const toDeg = (val: number) => (val * 180) / Math.PI;

    const lat1 = toRad(latDeg);
    const lon1 = toRad(lonDeg);
    const bearing = toRad(bearingDeg);

    // Distance over radius
    const distOverR = distanceMeters / R;

    // Calculate new latitude
    const lat2 = Math.asin(
      Math.sin(lat1) * Math.cos(distOverR) +
        Math.cos(lat1) * Math.sin(distOverR) * Math.cos(bearing)
    );

    // Calculate new longitude
    const lon2 =
      lon1 +
      Math.atan2(
        Math.sin(bearing) * Math.sin(distOverR) * Math.cos(lat1),
        Math.cos(distOverR) - Math.sin(lat1) * Math.sin(lat2)
      );
    return { lat: toDeg(lat2), lon: toDeg(lon2) };
  }
}
