/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/5/2024, 8:03 AM
 */
import { Component, EventEmitter, input, Input, Output } from '@angular/core';
import { ExpandedToolbars } from './toolbar-container.component';
import { GisService } from '../service/gis.service';

@Component({
    selector: 'vcci-right-toolbar-options',
    styleUrls: ['./right-toolbar-options.component.scss'],
    templateUrl: './right-toolbar-options.component.html',
    standalone: false
})

export class RightToolbarOptionsComponent {
  @Output() rightToolbarsExpandedEmitter: EventEmitter<ExpandedToolbars> = new EventEmitter<ExpandedToolbars>();
  @Input() expanded: boolean = false;
  showLabels: boolean = this.gisService._showEntityLabels;

  constructor(private gisService: GisService) {}

  toggleToolDisplayDimension(): void {

  }

  toggleToolbars(): void {
    this.expanded = !this.expanded;
    this.rightToolbarsExpandedEmitter.emit({
      drawing: this.expanded,
      styling:  this.expanded,
      entities: this.expanded,
      movement: this.expanded,
      all: this.expanded
    });
  }

  toggleLabels(): void {
    this.showLabels = this.gisService.toggleEntityLabelVisibility();
  }
}
