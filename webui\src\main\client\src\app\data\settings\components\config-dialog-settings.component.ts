import { Component, effect, input } from '@angular/core';
import { ConfigDialogSettings, DEFAULT_SETTINGS } from '../settings.model';
import { FormControl, FormGroup } from '@angular/forms';
import { outputFromObservable } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';

type SettingsChanged = Partial<{ configDialog: Partial<ConfigDialogSettings> }>;

@Component({
  selector: 'vcci-config-dialog-settings',
  template: `
    <div
      style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;"
    >
      <h2>Connection and Endpoint Dialog Settings</h2>
      <button
        mat-stroked-button
        matTooltip="Reset default Connection and Endpoint Dialog Settings"
        (click)="resetSettings()"
        style="margin: 2px;"
      >
        Reset
      </button>
    </div>
    <form [formGroup]="form">
      <div class="field">
        <h3>Remember Network Interface</h3>
        <mat-checkbox formControlName="rememberNetworkInterface" />
        <mat-icon
          matTooltip="Remember the last used network interface value and use it in the future"
          >info</mat-icon
        >
      </div>
      <div class="field">
        <h3>Suggested Connection Parameters</h3>
        <mat-checkbox formControlName="useConDefaults" />
        <mat-icon matTooltip="Use suggested Connection Parameter values when creating a new connection"
          >info</mat-icon
        >
      </div>
      <div class="field">
        <h3>Suggested Endpoint Parameters</h3>
        <mat-checkbox formControlName="useEndDefaults" />
        <mat-icon matTooltip="Use suggested Endpoint Parameter values when creating a new endpoint">
          info</mat-icon
        >
      </div>
    </form>
  `,
  styleUrls: ['settings.component.css'],
  standalone: false
})
export class ConfigDialogSettingsComponent {
  settings = input.required<ConfigDialogSettings>();
  form = new FormGroup({
    useConDefaults: new FormControl(true),
    useEndDefaults: new FormControl(true),
    rememberNetworkInterface: new FormControl(true)
  });
  onSettingsChanged = outputFromObservable<SettingsChanged>(
    this.form.valueChanges.pipe(map((configDialog) => ({ configDialog })))
  );

  constructor() {
    effect(() => this.form.patchValue(this.settings(), { emitEvent: false }));
  }

  resetSettings(): void {
    this.form.patchValue(DEFAULT_SETTINGS.configDialog);
  }
}
