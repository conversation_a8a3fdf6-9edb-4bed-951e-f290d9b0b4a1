.container {
    display: table-row;
}

.main-content {
    position: relative;
    background: rgba(var(--secondary-background-dark-rgb), var(--opacity));
    color: var(--text);
    min-width: 220px;
    max-width: 220px;
    word-break: break-all;
    display: table-cell;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.main-content:hover {
  cursor: pointer;
}



:host ::ng-deep .actions-container > button .mat-mdc-button-persistent-ripple {
  border-radius: 10% !important;
}

.actions-container {
  display: table-cell;
  width: 36px;
  position: relative;

  button:last-child {
    position: absolute;
    bottom: 0;
    left: 0;
  }

  button {
    background-color: rgba(var(--secondary-background-light-rgb), var(--opacity));
    border-radius: 10% !important;
  }
}

.actions-container.input > button {
    margin-right: 5px;
}

.actions-container.output > button {
    margin-left: 5px;
}

:host ::ng-deep .actions-container > button .foregroundColor {
    fill: var(--text-secondary);
}

.actions-container > button mat-icon {
    width: 24px;
    height: 24px;
    font-size: 24px;
}

:host ::ng-deep .small-icon-button svg {
    width: 24px;
    height: 24px;
}

:host ::ng-deep .small-icon-button .mat-mdc-button-touch-target {
    width: 32px !important;
    height: 32px !important;
}

.node {
    position: absolute;
    bottom: 3px;
    right: 7px;
    font-size: 12px;
}

.text-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: 14px;
    margin: 10px;
    line-height: 16px;
}

.text-container > div:nth-child(odd) {
    letter-spacing: 2px;
    font-weight: 400;
    font-size: 13px;
    color: var(--text-description);
}

.text-container > div:nth-child(even) {
    font-weight: 580;
    margin-bottom: 10px;
}

.text-container > div:last-child {
    margin-bottom: 0;
}
