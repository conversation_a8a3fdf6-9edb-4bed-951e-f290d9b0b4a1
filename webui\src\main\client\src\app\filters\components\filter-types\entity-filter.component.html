<button mat-icon-button class="filter-button" [matMenuTriggerFor]="appMenu">
  <mat-icon>filter_alt</mat-icon>
</button>

<sf-virtual-table-tree
  [data]="filteredData()"
  [metaData]="metaData"
  [columns]="columns"
>
  <sf-table-tree-cell
    *sfTableTreeCellDef="let item; column: 'Name'; metaData: metaData"
  >
    @let obj = item.obj;
    @if (isEntity(obj)) {
      <mat-checkbox
        [checked]="entityFilter | tree: isSelected : obj"
        (change)="updateFilters($event.checked, obj)"
      />
      <sf-symbology [milCode]="obj.symbol" [label]="item.obj.name" />
    } @else {
      <span>{{ item.label }}</span>
    }
  </sf-table-tree-cell>

  <sf-table-tree-cell
    *sfTableTreeCellDef="let item; column: 'Type'; metaData: metaData"
  >
    @if (isEndpoint(item.obj)) {
      @if (!item.obj.fullRoute) {
        <mat-icon
          class="route-desc"
          matTooltip="Partial route, all new entities will be automatically excluded."
        >
          stroke_partial
        </mat-icon>
      } @else {
        <mat-icon
          class="route-desc"
          matTooltip="Full route, all new entities will be automatically included."
        >
          stroke_full
        </mat-icon>
      }
    }
    <span>{{ item.obj.type }}</span>
  </sf-table-tree-cell>

  <sf-table-tree-cell
    *sfTableTreeCellDef="let item; column: ' '; metaData: metaData"
  >
    @let obj = item.obj;
    @if (isEndpoint(obj)) {
      <button
        mat-icon-button
        [matMenuTriggerFor]="endpointMenu"
        [matMenuTriggerData]="{ $implicit: item }"
      >
        <mat-icon>more_vert</mat-icon>
      </button>
    }
    @if (isEntity(obj)) {
      <button mat-icon-button (click)="openDetails(obj.uniqueId)">
        <mat-icon>info</mat-icon>
      </button>
    }
  </sf-table-tree-cell>

  <tr
    mat-row
    *matRowDef="let row; columns: columns"
    [options]="this.options()"
    [sfStickyElement]="row.id"
    [parentId]="expanded()[row.id] ? row.id : row.parentId"
  ></tr>
</sf-virtual-table-tree>

<mat-menu #appMenu="matMenu" style="max-width: 200px" [class]="'filter-menu'">
  <mat-form-field class="filter-menu-item" (click)="$event.stopPropagation()">
    <input
      matInput
      placeholder="Search"
      (click)="$event.stopPropagation()"
      [(ngModel)]="search"
    />
  </mat-form-field>
  <mat-checkbox
    [(ngModel)]="partial"
    class="filter-menu-item"
    [checked]="true"
    (click)="$event.stopPropagation()"
  >
    Partial Routes
  </mat-checkbox>
  <br />
  <mat-checkbox
    [(ngModel)]="full"
    class="filter-menu-item"
    [checked]="true"
    (click)="$event.stopPropagation()"
  >
    Full Routes
  </mat-checkbox>
  <br />
  <mat-checkbox
    [(ngModel)]="included"
    class="filter-menu-item"
    [checked]="true"
    (click)="$event.stopPropagation()"
  >
    Included Entities
  </mat-checkbox>
  <br />
  <mat-checkbox
    [(ngModel)]="excluded"
    class="filter-menu-item"
    [checked]="true"
    (click)="$event.stopPropagation()"
  >
    Excluded Entities
  </mat-checkbox>
</mat-menu>

<mat-menu #endpointMenu="matMenu">
  <ng-template matMenuContent let-item>
    <button mat-menu-item (click)="toggleAll(true, item)">Select All</button>
    <button mat-menu-item (click)="toggleAll(false, item)">Deselect All</button>
  </ng-template>
</mat-menu>
