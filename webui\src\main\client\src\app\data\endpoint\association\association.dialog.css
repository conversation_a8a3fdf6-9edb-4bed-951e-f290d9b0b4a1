mat-card-content {
  width: 100%;
  overflow: auto;
}

.assoc-table-group {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 15px;
  padding-top: 15px;
}

.assoc-table-group > * {
  flex: 1 1 0;
}

:host .assoc-table-group ::ng-deep .mat-mdc-header-cell {
  word-break: unset !important;
}

::ng-deep .mdc-tooltip {
  white-space: pre;
}

::ng-deep .mat-mdc-table > tbody {
  max-height: 200px;
  overflow: auto;
  display: block;
}

::ng-deep .mat-mdc-table thead,
::ng-deep .mat-mdc-table tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

::ng-deep .mat-mdc-table td,
::ng-deep .mat-mdc-table th {
  display: table-cell;
}
