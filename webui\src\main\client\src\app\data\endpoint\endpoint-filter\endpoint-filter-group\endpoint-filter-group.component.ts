import {
  Component,
  inject,
  Input,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import {
  FilterGroup,
  FilterInfo,
  FilterType,
  isGeoFilter
} from '../../filter.model';
import { EndpointFilterStore } from '../endpoint-filter.store';
import { cursitActions } from 'src/app/cursit/actions/cursit.actions';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';

@Component({
  selector: 'vcci-filter-group',
  templateUrl: 'endpoint-filter-group.component.html',
  styleUrls: ['endpoint-filter-group.component.css'],
  standalone: false
})
export class EndpointFilterGroupComponent implements OnChanges {
  @Input() filterInfos: FilterInfo[] = [];
  @Input() filters: FilterGroup[] = [];

  allowedMultipleGroups: FilterInfo[] = [];

  private store = inject(EndpointFilterStore);
  private ngrxStore = inject(Store);
  private router = inject(Router);

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['filterInfos']) {
      this.allowedMultipleGroups = this.filterInfos.filter(
        (info) => info.allowMultiple
      );
    }
  }

  removeGroup(key: number): void {
    this.store.removeGroup(key);
  }

  addGroup(): void {
    this.store.addGroup();
  }

  selectFilter(event: { key: number; info: string }): void {
    this.store.selectFilter(event);

    // Check if the selected filter is not geo filter
    const selectedFilter = this.filters[event.key][event.info];

    // Check the current filter
    const currentFilter = this.filterInfos.find(
      (info) => info.name === event.info
    );

    if (!isGeoFilter(selectedFilter) && currentFilter?.allowMultiple) {
      this.ngrxStore.dispatch(cursitActions.removeGeoFilters());
    }

    /* Check if the selected filter is a geo filter, and if so, navigate to /cursit
     * TEMPORARY FOR THE MVP DEMO
     * TODO: Need to find a better way to improve the UX
     */
    if (
      currentFilter?.type === FilterType.Geo &&
      !this.router.url.includes('/cursit')
    ) {
      this.ngrxStore.dispatch(cursitActions.navigateToCursit());
    }
  }

  resetAll(): void {
    this.store.resetAll();
  }
}
