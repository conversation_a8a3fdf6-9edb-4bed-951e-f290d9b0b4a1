import { Action, createActionGroup, createReducer, emptyProps, on } from '@ngrx/store';

import { createEverything, optionalPayload, payload } from '@simfront/common-libs';

import { environment } from '../../../environments/environment';

import { CreateBSC, Parameters } from '../bs-params/bs-params.model';
import { BSParamDialogPayload } from '../bs-params/bs-params.store';
import { Endpoint, isInputEndpoint } from '../endpoint/endpoint.model';
import { RootState } from '../index';
import { NodeSupport } from '../node/node.model';

export type Direction = 'IN' | 'OUT' | 'BOTH';

export interface Connection {
  id?: number;
  name: string;
  node: string;
  status: 'CONNECTED' | 'DISCONNECTED' | 'ERROR' | 'INVALID' | 'PROCESSING';
  direction: 'IN' | 'OUT' | 'BOTH';
  exchangeConnection: boolean;
  parameters?: Parameters;
}

export const getConnectionId = (conn: Connection): string => `${conn.node}:${conn.name}`;
export const getConnectionHost = (conn: Connection | undefined): string => conn?.parameters?.singleParameters['PARAMETER_IPADDRESS']?.value
  ?? conn?.parameters?.singleParameters['PARAMETER_BIND_IPADDRESS']?.value
  ?? 'UNKNOWN';
export const getConnectionPort = (conn: Connection | undefined): string => conn?.parameters?.singleParameters['PARAMETER_PORT']?.value ?? 'UNKNOWN';
export const canCreateOutput = (support: NodeSupport, endpoints: Endpoint[]): boolean => support.externalOutputSupported
  && (support.multiOutputBSEEnabled || endpoints.filter(e => !isInputEndpoint(e)).length === 0);
export const canCreateInput = (support: NodeSupport, endpoints: Endpoint[]): boolean => support.externalInputSupported
  && (support.multiInputBSEEnabled || endpoints.filter(e => isInputEndpoint(e)).length === 0);
export const isConnection = (val: any): val is Connection => val.name !== undefined && val.node !== undefined && val.status !== undefined && val.direction !== undefined
  && val.exchangeConnection !== undefined;

export const {
  initialState,
  reducer,
  facade: ConnectionFacadeBase
} = createEverything<RootState, Connection, 'connection', never, string>(
  'connection',
  getConnectionId,
  `${environment.origin}/connections`,
  state => state.connection
);

export const ConnectionActions = createActionGroup({
  source: 'connection',
  events: {
    'Create Special': payload<CreateBSC>(),
    'Create Special Success': payload<CreateBSC>(),
    'Open Create Dialog': optionalPayload<BSParamDialogPayload>(),
    'Create Dialog Dismissed': emptyProps(),
    'Open Assoc Dialog': emptyProps(),
    'Assoc Dialog Dismissed': emptyProps()
  }
});

export const mergeReducers = <T>(
  initialState: T,
  ...reducers: ((state: T, action: Action) => T)[]
): (state: T | undefined, action: Action) => T => (state: T | undefined = initialState, action: Action): T => reducers.reduce((currentState, reducer) => reducer(currentState, action), state);

export const connectionReducer = mergeReducers(
  initialState,
  reducer,
  createReducer(
    initialState,
    on(ConnectionActions.createSpecial, state => ({ ...state, creating: state.creating + 1 }))
  )
);
