import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Point } from '../../../shared/util/svg/line-svg.model';
import { RouteColorPipe } from '../../../shared/pipes/route-color.pipe';

@Component({
  selector: '[routeSvgFilter]',
  template: `
    <svg:rect
      [class.svg-normal]="true"
      [attr.x]="iconX"
      [attr.y]="iconY"
      [attr.height]="height"
      [attr.width]="width"
      [attr.fill]="'transparent'"
    />
    <svg:rect
      [attr.x]="iconX"
      [attr.y]="iconY"
      [attr.height]="height"
      [attr.width]="'3'"
      [attr.fill]="selected | routeColor: connectedTo"
    />
    <svg:rect
      [attr.x]="iconX + 8"
      [attr.y]="iconY"
      [attr.height]="height"
      [attr.width]="'3'"
      [attr.fill]="selected | routeColor: connectedTo"
    />
  `,
  imports: [RouteColorPipe],
  styles: `
    .svg-normal {
      pointer-events: auto;
      cursor: auto;
    }
  `
})
export class RouteSvgFilterComponent implements OnChanges {
  private _height = 24;
  private _width = 24;

  @Input({ required: true }) iconPoint: Point | undefined = undefined;
  @Input() connectedTo: string[] = [];
  @Input() isInput: boolean = true;
  @Input() initialOffset = 30;
  @Input() selected: string[] = [];
  @Input() fillColor = 'grey';

  @Input() set height(height: number) {
    this._height = height;
  }

  @Input() set width(width: number) {
    this._width = width;
  }

  iconX = 0;
  iconY = 0;

  get height(): number {
    return this._height;
  }

  get width(): number {
    return this._width;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes['iconPoint'] ||
      changes['initialOffset'] ||
      changes['height'] ||
      changes['width'] ||
      changes['isInput']
    ) {
      this.calculatePositions();
    }
  }

  private calculatePositions(): void {
    if (this.iconPoint === undefined) {
      return;
    }
    // Calculate the position of the image for the button.
    const halfWidth = this.width / 2;
    const halfHeight = this.height / 2;
    this.iconX =
      this.iconPoint.x +
      (this.isInput
        ? this.initialOffset - halfWidth
        : -(this.initialOffset + halfWidth));
    this.iconY = this.iconPoint.y - halfHeight;
  }
}
