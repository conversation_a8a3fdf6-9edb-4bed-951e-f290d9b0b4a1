import { Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BaseParamComponent } from './base-param.component';
import { FilePayloadDirective } from '../../../shared/directives/file-payload.directive';
import { IconButtonSizeDirective } from '../../../shared/directives/icon-button-size.directive';
import {
  <PERSON><PERSON>orm<PERSON>ield,
  MatLabel,
  MatSuffix
} from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { MatInput } from '@angular/material/input';
import { SvgIconSizeDirective } from '../../../shared/directives/svg-icon-size.directive';
import { MatCheckbox } from '@angular/material/checkbox';

@Component({
  selector: 'vcci-param-file',
  template: `
    <mat-form-field (click)="fileInput.click()">
      <mat-label (click)="fileInput.click()">{{ label }}</mat-label>
      <input
        [formControl]="formControl"
        #fileInput
        matInput
        vcciFilePayload
        inert
      />
      @if (hasNext) {
        <button matSuffix mat-icon-button (click)="onGetNext()">
          <mat-icon>east</mat-icon>
        </button>
      }
      <button mat-icon-button matSuffix (click)="cancel($event)">
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>
    @if (!!this.parameter.group) {
      <mat-checkbox
        [checked]="isSelectedInGroup"
        [disabled]="!isPreviousEnabled"
        (change)="setGroupValue($event.checked)"
      />
    }
  `,
  styles: ``,
  imports: [
    FilePayloadDirective,
    IconButtonSizeDirective,
    MatFormField,
    MatIcon,
    MatIconButton,
    MatInput,
    MatLabel,
    MatSuffix,
    ReactiveFormsModule,
    SvgIconSizeDirective,
    MatCheckbox
  ]
})
export class ParamFileComponent extends BaseParamComponent {
  cancel(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.onValueChange(null);
  }
}
