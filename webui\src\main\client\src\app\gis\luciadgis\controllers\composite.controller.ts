import { Feature } from '@luciad/ria/model/feature/Feature';
import { CompositeController } from '@luciad/ria/view/controller/CompositeController';
import { Controller } from '@luciad/ria/view/controller/Controller';
import { EditControllerConstructorOptions } from '@luciad/ria/view/controller/EditController';

import { PanController } from '@luciad/ria/view/controller/PanController';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { LuciadMapDisplay } from '../LuciadMapDisplay';

import { BulkSelectController } from './bulk-select-controller';
import { ShapeEditController } from './editor-controller';

export class ChainedEditController extends CompositeController {
  constructor(
    private luciadMapDisplay: LuciadMapDisplay,
    layer: FeatureLayer,
    feature: Feature,
    options?: EditControllerConstructorOptions
  ) {
    super();

    this.addController(new ShapeEditController(
      luciadMapDisplay,
      layer,
      feature,
      { finishOnSingleClick: true }
    ));
    this.addController(new BulkSelectController(this.luciadMapDisplay));
    this.addController(new PanController());
  }

  addController(controller: Controller): void {
    this.appendController(controller);
  }
}
