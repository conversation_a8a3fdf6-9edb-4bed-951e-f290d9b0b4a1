import { SFMap } from '@simfront/common-libs';

import { Container } from 'pixi.js';

import { GISElement } from '../../interface/GISElement';

export interface MilSymLayer<T> {

  setVisible(visible: boolean): void;

  setSelectable(selectable: boolean): void;

  setEditable(editable: boolean): void;

  addElement(element: GISElement<T>): void;

  removeElement(element?: GISElement<T>, id?: string): boolean | Promise<boolean>;

  // getElementCount: ()=> number;
  getElement(id: string): GISElement<T> | null | undefined;

  name: string;
}

export class MilitarySymbologyLayer implements MilSymLayer<Container> {
  symbologyHash: SFMap<GISElement<Container>>;
  startRenderingIndex: number;
  endRenderingIndex: number;
  _container: Container;

  constructor(private layerName: string) {
  }

  addElement(gisElement: GISElement<Container>): void {
    if (!(gisElement.id in this.symbologyHash)) {
      gisElement.mapLayer = this;
      this.symbologyHash[gisElement.id] = gisElement;
      this.container.addChild(this.symbologyHash[gisElement.id].gisObject);
    }
  }

  removeElement(gisElement: GISElement<Container>): boolean {
    if (gisElement && gisElement.gisObject) {
      this.container.removeChild(this.symbologyHash[gisElement.id].gisObject);
      delete this.symbologyHash[gisElement.id];
    }
    return false;
  }

  updateRenderingIndices(startRenderingIndex: number): void {
    this.startRenderingIndex = startRenderingIndex;
    let sri = startRenderingIndex;
    const keys = Object.keys(this.symbologyHash);
    keys.forEach(key => {
      const element = this.symbologyHash[key].gisObject;
      this.container.setChildIndex(element, startRenderingIndex);
      sri += 1;
    });

    this.endRenderingIndex = sri - 1;
  }

  get container(): Container {
    return this._container;
  }

  set container(container: Container) {
    this._container = container;
  }

  set name(layerName: string) {
    this.layerName = layerName;
  }

  get name(): string {
    return this.layerName;
  }

  getElement(id: string): GISElement<Container> | null | undefined {
    return this.symbologyHash[id];
  }

  setEditable(editable: boolean): void {
    this.container.interactiveChildren = editable;
  }

  setSelectable(selectable: boolean): void {
    this.container.interactiveChildren = selectable;
  }

  setVisible(visible: boolean): void {
    const keys = Object.keys(this.symbologyHash);
    keys.forEach(key => {
      const element = this.symbologyHash[key].gisObject;
      if (element instanceof Container) {
        element.visible = visible;
      }
    });
  }
}
