import { Component, effect, input } from '@angular/core';
import { DEFAULT_SETTINGS, LayoutSettings } from '../settings.model';
import { FormControl, FormGroup } from '@angular/forms';
import { outputFromObservable } from '@angular/core/rxjs-interop';
import { map, tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { LayoutActions } from 'src/app/core/actions/layout.actions';

type SettingsChanged = Partial<{ layout: Partial<LayoutSettings> }>;

@Component({
  selector: 'vcci-layout-settings',
  template: `
    <div
      style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;"
    >
      <h2>Layout Settings</h2>
      <button
        mat-stroked-button
        matTooltip="Reset default Layout Settings"
        (click)="resetSettings()"
        style="margin: 2px;"
      >
        Reset
      </button>
    </div>
    <form [formGroup]="form">
      <div class="field">
        <h3>Hide DataManager Status</h3>
        <mat-checkbox formControlName="hideDataman" />
      </div>
      <div class="field">
        <h3>Hide Background Arc Effect</h3>
        <mat-checkbox formControlName="hideArcs" />
      </div>
    </form>
  `,
  styleUrls: ['settings.component.css'],
  standalone: false
})
export class LayoutSettingsComponent {
  settings = input.required<LayoutSettings>();
  form = new FormGroup({
    hideDataman: new FormControl(false),
    hideArcs: new FormControl(false)
  });
  onSettingsChanged = outputFromObservable<SettingsChanged>(
    this.form.valueChanges.pipe(
      tap((layout) => {
        this.store.dispatch(LayoutActions.setInitialLayoutState(layout));
      }),
      map((layout) => ({ layout }))
    )
  );

  constructor(private store: Store) {
    effect(() => this.form.patchValue(this.settings(), { emitEvent: false }));
  }

  resetSettings(): void {
    this.form.patchValue(DEFAULT_SETTINGS.layout);
  }
}
