import { AfterViewInit, Directive, ElementRef, HostBinding, Input, Renderer2 } from '@angular/core';

@Directive({
  selector: 'button[mat-icon-button]',
  standalone: true
})
export class IconButtonSizeDirective implements AfterViewInit {
  @Input() buttonSize: number | undefined = undefined;

  @Input() set iconSize(value: number) {
    this._iconSize = value;
  }

  get iconSize() {
    return this._iconSize ?? this.buttonSize;
  }

  private _iconSize: number | undefined = undefined;

  @HostBinding('style') get style() {
    return `
      width: ${this.buttonSize}px !important;
      height: ${this.buttonSize}px !important;
      padding: 0 !important;
      display: inline-flex !important;
      align-items: center;
      justify-content: center;
    `;
  }

  get touchTargetStyles() {
    return {
      width: `${this.buttonSize}px`,
      height: `${this.buttonSize}px`
    };
  }

  get imageStyles() {
    return {
      'width': `${this.iconSize}px`,
      'height': `${this.iconSize}px`,
      'font-size': `${this.iconSize}px`
    };
  }

  get svgStyles() {
    return {
      width: `${this.iconSize}px`,
      height: `${this.iconSize}px`
    };
  }

  constructor(private elementRef: ElementRef, private renderer: Renderer2) {
  }

  ngAfterViewInit() {
    if (this.buttonSize === undefined) {
      return;
    }
    const imageRef: HTMLElement = this.elementRef.nativeElement.querySelector('& > *[role=img]');
    this._applyStyles(imageRef, this.imageStyles);
    const svgRef: SVGElement = imageRef.querySelector('svg');
    this._applyStyles(svgRef, this.svgStyles);
    const touchTargetRef: HTMLElement = this.elementRef.nativeElement.querySelector('.mat-mdc-button-touch-target');
    this._applyStyles(touchTargetRef, this.touchTargetStyles);
  }

  private _applyStyles(elementRef: HTMLElement | SVGElement, styles: { [key: string]: string }): void {
    Object.entries(styles).forEach(([key, value]) => {
      if (elementRef) {
        this.renderer.setStyle(elementRef, `${key}`, `${value}`);
      }
    });
  }
}
