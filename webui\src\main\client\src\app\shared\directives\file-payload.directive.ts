import {
  AfterViewInit,
  DestroyRef,
  Directive,
  ElementRef,
  Optional,
  Self
} from '@angular/core';
import { NgControl } from '@angular/forms';
import { FileUploadService } from '../services/file-upload.service';
import { fromEvent, map, switchMap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

export interface FilePayload {
  fileName: string | null;
  fileData: string | null;
}

@Directive({
  selector: '[vcciFilePayload]',
  standalone: true
})
export class FilePayloadDirective implements AfterViewInit {
  constructor(
    @Optional() @Self() private formControl: NgControl,
    @Self() private elementRef: ElementRef,
    private fileUpload: FileUploadService,
    private destroyRef: DestroyRef
  ) {
    this._click$.subscribe();
    this.elementRef.nativeElement.setAttribute('inert', '');
  }

  setFileValue = (files: File[] | null) => {
    if (files !== null && files.length > 0) {
      const file = files[0];
      const reader = new FileReader();
      reader.onload = () => {
        const filePayload: FilePayload = {
          fileName: file.name,
          fileData: reader.result as string
        };
        this.formControl.control.setValue(JSON.stringify(filePayload));
        this.formControl.control.markAsTouched();
      };
      reader.readAsDataURL(file);
    }
  };

  private _click$ = fromEvent<{ target: HTMLInputElement } & Event>(
    this.elementRef.nativeElement,
    'click'
  ).pipe(
    takeUntilDestroyed(this.destroyRef),
    switchMap(() =>
      this.fileUpload
        .selectFiles()
        .pipe(takeUntilDestroyed(this.destroyRef), map(this.setFileValue))
    )
  );

  updateFromControl = (value: string | null) => {
    if (value === null || !value) {
      this.formControl.valueAccessor.writeValue(null);
    } else {
      const filePayload: FilePayload = JSON.parse(value);
      this.formControl.valueAccessor.writeValue(filePayload.fileName);
    }
  };

  ngAfterViewInit() {
    this.formControl.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(this.updateFromControl.bind(this));
  }
}
