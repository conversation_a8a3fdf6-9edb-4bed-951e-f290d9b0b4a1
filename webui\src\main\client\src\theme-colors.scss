// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: #0574B9, secondary: #002733, tertiary: #C34600, neutral: #67696C, neutral variant: #415568, error: #F44336
$_palettes: (
  primary: (0: #000000,
    10: #001d34,
    20: #003355,
    25: #003e67,
    30: #004a79,
    35: #00568b,
    40: #00629e,
    50: #1b7cc1,
    60: #4296dd,
    70: #61b1fa,
    80: #9acbff,
    90: #cfe5ff,
    95: #e9f1ff,
    98: #f7f9ff,
    99: #fcfcff,
    100: #ffffff,
  ),
  secondary: (0: #000000,
    10: #001f29,
    20: #113441,
    25: #1e404c,
    30: #2a4b58,
    35: #365764,
    40: #426370,
    50: #5b7c8a,
    60: #7596a4,
    70: #8fb0bf,
    80: #aaccdb,
    90: #c5e8f8,
    95: #def4ff,
    98: #f3faff,
    99: #fafdff,
    100: #ffffff,
  ),
  tertiary: (0: #000000,
    10: #370e00,
    20: #5a1c00,
    25: #6c2300,
    30: #7f2b00,
    35: #933300,
    40: #a73b00,
    50: #cd4d0a,
    60: #f06627,
    70: #ff8c5d,
    80: #ffb599,
    90: #ffdbce,
    95: #ffede7,
    98: #fff8f6,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (0: #000000,
    10: #191c1e,
    20: #2e3133,
    25: #393c3e,
    30: #45474a,
    35: #505355,
    40: #5c5e61,
    50: #75777a,
    60: #8f9194,
    70: #aaabae,
    80: #c5c6ca,
    90: #e1e2e6,
    95: #f0f0f4,
    98: #f9f9fc,
    99: #fcfcff,
    100: #ffffff,
    4: #0c0e11,
    6: #111416,
    12: #1d2022,
    17: #282a2d,
    22: #333538,
    24: #37393c,
    87: #d9dadd,
    92: #e7e8eb,
    94: #edeef1,
    96: #f3f3f7,
  ),
  neutral-variant: (0: #000000,
    10: #071d2e,
    20: #1e3244,
    25: #293d4f,
    30: #35495b,
    35: #405467,
    40: #4c6074,
    50: #65798d,
    60: #7e93a8,
    70: #99adc3,
    80: #b4c9df,
    90: #d0e5fc,
    95: #e7f2ff,
    98: #f7f9ff,
    99: #fcfcff,
    100: #ffffff,
  ),
  error: (0: #000000,
    10: #410001,
    20: #690002,
    25: #7e0004,
    30: #930005,
    35: #a90107,
    40: #bb1614,
    50: #e0342a,
    60: #ff5545,
    70: #ff8a7b,
    80: #ffb4a9,
    90: #ffdad5,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);

$dark-theme: mat.define-theme((color: (theme-type: dark,
        primary: $primary-palette,
        tertiary: $tertiary-palette,
      ),
      typography: (brand-family: 'Roboto, "Helvetica Neue", sans-serif',
        bold-weight: 800),
      density: (scale: -1)));
