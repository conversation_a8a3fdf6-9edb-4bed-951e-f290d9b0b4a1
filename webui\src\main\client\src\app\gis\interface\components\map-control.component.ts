/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/16/2024, 9:40 AM
 */

import { Component } from '@angular/core';
import { cursitActions } from '../../../cursit/actions/cursit.actions';
import { CursitControllerType } from '../../../cursit/models/cursit.model';
import { GisService } from '../service/gis.service';
import { EntityFacade } from 'src/app/data/entity/entity.facade';

@Component({
  selector: 'vcci-map-control-toolbar',
  styleUrls: ['./map-control.component.scss'],
  templateUrl: './map-control.component.html',
  standalone: false
})
export class MapControlComponent {
  showDetails: boolean = true;
  locked: boolean = false;
  mapContainer: HTMLElement;
  mapMode: CursitControllerType = CursitControllerType.Pan;

  constructor(
    private gisService: GisService,
    private entityFacade: EntityFacade
  ) {}

  battleField(): void {
    this.entityFacade.getEntitiesBoundingBox$().subscribe((boundingBox) => {
      this.gisService.mapFitBounds(boundingBox);
    }).unsubscribe();
  }

  private setPanClasses(panModeActive: boolean): void {
    const mapContainer = document.querySelector('.map');
    const classList = ['pan-active', 'hand-pan'];
    if (panModeActive) {
      mapContainer.classList.add(...classList);
    } else {
      mapContainer.classList.remove(...classList);
    }
  }

  activateMeasuringMode(): void {
    this.mapMode = CursitControllerType.Measuring;
    this.setPanClasses(false);
    this.gisService.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.Measuring
      })
    );
  }

  activateBulkSelectMode(): void {
    this.mapMode = CursitControllerType.BulkSelect;
    this.setPanClasses(false);
    this.gisService.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.BulkSelect
      })
    );
  }

  activatePanMode(): void {
    this.mapMode = CursitControllerType.Pan;
    this.setPanClasses(true);
    this.gisService.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.Pan
      })
    );
  }

  zoomIn(): void {
    this.gisService.mapZoomIn();
  }

  zoomOut(): void {
    this.gisService.mapZoomOut();
  }

  showHideDetails(): void {
    this.showDetails = !this.showDetails;
  }

  toggleLock(): void {
    this.locked = !this.locked;
  }

  get panActive(): boolean {
    return this.mapMode === CursitControllerType.Pan;
  }

  get selectActive(): boolean {
    return this.mapMode === CursitControllerType.BulkSelect;
  }

  get measuring(): boolean {
    return this.mapMode === CursitControllerType.Measuring;
  }

  printMap(): void {
    this.gisService.printCursitMap();
  }
}
