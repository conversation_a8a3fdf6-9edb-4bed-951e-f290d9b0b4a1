import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSelectChange } from '@angular/material/select';

import { ConnectionPanelSettings, DEFAULT_SETTINGS } from '../settings.model';
import { Store } from '@ngrx/store';
import { LayoutActions } from 'src/app/core/actions/layout.actions';

const stateToProp = {
  expanded: ['showLeftSideNav', 'showRightSideNav'],
  mode: 'sidePanel',
  connectionDisplay: 'connectionPanelDisplay',
  connectionsExpanded: 'connectionsExpanded',
  position: 'panelPosition'
};

@Component({
  selector: 'vcci-conn-panel-settings',
  template: `
    <div
      style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;"
    >
      <h2>Default Connection Panel Settings</h2>
      <div>
        <button
          mat-stroked-button
          matTooltip="Reset default Connection Panel Settings"
          (click)="resetSettings()"
          style="margin: 2px;"
        >
          Reset
        </button>
      </div>
    </div>

    <div class="field">
      <h3>Panel Dock Position</h3>
      <mat-form-field>
        <mat-select
          [value]="settings.position"
          (selectionChange)="onSelectChange('position', $event)"
        >
          <mat-option value="left">Left</mat-option>
          <mat-option value="right">Right</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="field">
      <h3>Panel Mode</h3>
      <mat-form-field>
        <mat-select
          [value]="settings.mode"
          (selectionChange)="onSelectChange('mode', $event)"
        >
          <mat-option value="Consolidated">Consolidated</mat-option>
          <mat-option value="Routing">Routing</mat-option>
          <mat-option value="Hidden">Hidden</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="field">
      <h3>Connection Display</h3>
      <mat-form-field>
        <mat-select
          [value]="settings.connectionDisplay"
          (selectionChange)="onSelectChange('connectionDisplay', $event)"
        >
          <mat-option value="ALL">All</mat-option>
          <mat-option value="ENDPOINTS">Endpoints Only</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="field">
      <h3>Panel Appearance</h3>
      <mat-checkbox
        [checked]="settings.expanded"
        (change)="onCheckboxChange('expanded', $event)"
      >
        Expanded Panel
        <mat-icon
          matTooltip="Expanded panel will show all the connection details."
        >
          info
        </mat-icon>
      </mat-checkbox>
      <mat-checkbox
        [checked]="settings.connectionsExpanded"
        (change)="onCheckboxChange('connectionsExpanded', $event)"
      >
        Expanded Connections
        <mat-icon
        matTooltip="Expanded connections will show all the connection endpoint details in the panel."
        >info</mat-icon
        >
      </mat-checkbox>
    </div>
  `,
  styleUrls: ['settings.component.css'],
  styles: `
  ::ng-deep .field mat-checkbox .mdc-label {
    display: flex;
    align-items: flex-end;
    gap: 8px;
  }
  `,
  standalone: false
})
export class ConnectionPanelSettingsComponent {
  @Input() settings: ConnectionPanelSettings;
  @Output() onSettingsChanged = new EventEmitter<{
    connPanel: Partial<ConnectionPanelSettings>;
  }>();

  constructor(private store: Store) {}

  onSelectChange(prop: string, event: MatSelectChange) {
    this.saveChange(prop, event.value);
  }

  onCheckboxChange(prop: string, event: MatCheckboxChange) {
    this.saveChange(prop, event.checked);
  }

  saveChange(prop: string, value: any) {
    this.onSettingsChanged.emit({ connPanel: { [prop]: value } });
    // If the property maps to an array of state properties, set them all
    if (Array.isArray(stateToProp[prop])) {
      const stateUpdates = stateToProp[prop].reduce((acc, stateProp) => {
        acc[stateProp] = value;
        return acc;
      }, {});
      this.store.dispatch(LayoutActions.setInitialLayoutState(stateUpdates));
    } else {
      // Single state properties
      this.store.dispatch(
        LayoutActions.setInitialLayoutState({
          [stateToProp[prop]]: value
        })
      );
    }
  }

  resetSettings() {
    this.onSettingsChanged.emit({ connPanel: DEFAULT_SETTINGS.connPanel });
  }
}
