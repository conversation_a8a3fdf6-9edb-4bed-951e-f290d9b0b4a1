import { Component, EventEmitter, Input } from '@angular/core';
import { TableColumn, TableSelectionMode } from '@simfront/common-libs';
import { IPopoutWindowModal } from '../../../shared/popout-window/popout-window.model';
import { EntityDetails } from '../../../data/entity/entity.model';
import { DataService } from '../../../data/data.service';
import { mapActions } from '../actions/map.actions';
import { Store } from '@ngrx/store';

@Component({
  selector: 'vcci-entity-details',
  templateUrl: './entity-details.component.html',
  styleUrls: ['./entity-details.component.css'],
  standalone: false
})
export class EntityDetailsComponent implements IPopoutWindowModal {
  isPopoutWindow: boolean;
  onClose: EventEmitter<any> = new EventEmitter();
  selectionMode: TableSelectionMode.None;
  entityDetails$ = this.ds.entity.getSelectedEntityDetails();

  columns: TableColumn<EntityDetails>[] = [
    { header: 'Item', prop: (e) => e.key },
    { header: 'Value', prop: (e) => e.val }
  ];

  constructor(
    private ds: DataService,
    private store: Store
  ) {}

  trackBy = (v: EntityDetails) => v.key;

  close(): void {
    this.store.dispatch(mapActions.closeEntitySelectionDialog());
  }
}
