import { Component, inject, Input, WritableSignal } from '@angular/core';
import {
  DataParameter,
  PARAMS_FORM
} from '../../../../data/bs-params/bs-params.model';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DataParameterComponent } from '../../../../data/bs-params/components/data-param.component';
import { BsDataParamBase } from './bs-data-param.base';

@Component({
  selector: 'vcci-bs-data-param',
  templateUrl: './bs-data-param.component.html',
  imports: [DataParameterComponent, ReactiveFormsModule],
  styleUrl: './bs-data-param.component.css'
})
export class BsDataParamComponent extends BsDataParamBase {
  formGroup$: WritableSignal<FormGroup> = inject(PARAMS_FORM, {
    skipSelf: true
  });
  @Input({ required: true }) parameter!: DataParameter;
}
