import {Component, Input, OnDestroy} from '@angular/core';
import {Entity} from '../../../../data/entity/entity.model';
import {Store} from '@ngrx/store';
import {GisService} from '../../service/gis.service';
import {MatSelectionListChange} from '@angular/material/list';

import {cursitActions} from '../../../../cursit/actions/cursit.actions';
import {DataService} from "../../../../data/data.service";


@Component({
    selector: 'vcci-entity-search',
    templateUrl: './entity-search.component.html',
    styleUrls: ['./entity-search.component.css'],
    standalone: false
})
export class EntitySearchComponent implements OnDestroy {
    searchQuery: string = '';
    entityList$ = this.dataService.entity.getFilteredEntities$();

    constructor(private store: Store, private gisService: GisService, private dataService: DataService) {

    }


    /**
     * When component is destroyed clear the store of searched entity ids so when its brought back, the
     * list is all the entities
     */
    ngOnDestroy(): void {
        this.store.dispatch(cursitActions.searchQuery(''));
    }


    /**
     * Called when the user selects an entity in the list
     * @param event
     */
    onSelectionChange(event: MatSelectionListChange) {
        this.setSelectedEntity(event.options[0].value);
    }

    setSelectedEntity(entity: Entity) {
        this.gisService.selectAndCenter(entity.uniqueId);
    }

    /**
     * Called when a user searches for an entity -> update the cursit to filter entities
     */
    onSearch(): void {
        this.store.dispatch(cursitActions.searchQuery(this.searchQuery));
    }
}
