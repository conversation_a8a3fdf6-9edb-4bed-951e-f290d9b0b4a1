import { HierarchicalSymbology } from '@luciad/ria-milsym/symbology/HierarchicalSymbology';
import { MIL_STD_2525b } from '@luciad/ria-milsym/symbology/military/MIL_STD_2525b';
import { MilitarySymbol } from '@luciad/ria-milsym/symbology/military/MilitarySymbol';
import { Feature } from '@luciad/ria/model/feature/Feature';
import { CoordinateReference } from '@luciad/ria/reference/CoordinateReference';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { Arc } from '@luciad/ria/shape/Arc';
import { ArcBand } from '@luciad/ria/shape/ArcBand';
import { Ellipse } from '@luciad/ria/shape/Ellipse';
import { Point } from '@luciad/ria/shape/Point';
import { PointCoordinates } from '@luciad/ria/shape/PointCoordinate';
import { Polygon } from '@luciad/ria/shape/Polygon';
import { Polyline } from '@luciad/ria/shape/Polyline';
import { Shape } from '@luciad/ria/shape/Shape';
import {
  createArcBand,
  createEllipse,
  createPoint,
  createPolygon,
  createPolyline
} from '@luciad/ria/shape/ShapeFactory';
import { Coordinate } from '../../cursit/models/cursit.model';
import { reverseCoordinates } from '../../shared/helpers/helpers';
import { mapActions } from '../interface/actions/map.actions';
import { LocationSettable, MilSymbolSettable } from '../interface/LocationSettable';
import { ShapeType } from '../interface/models/map-element.model';
import { getScreenXYFromMapPoint } from './luciad.utils';
import { LuciadMapDisplay } from './LuciadMapDisplay';

export class MS2525Symbol extends Feature implements LocationSettable, MilSymbolSettable {
  coordinateReference: CoordinateReference;
  coordinates: PointCoordinates | PointCoordinates[];
  luciadMapDisplay: LuciadMapDisplay;
  selected: boolean;

  constructor(
    luciadMapDisplay: LuciadMapDisplay,
    sidc: string,
    label: string,
    coordinateReference: CoordinateReference,
    shapeType: ShapeType,
    coordinates?: PointCoordinates | PointCoordinates[]
  ) {
    switch (shapeType) {
      case ShapeType.POLYLINE: {
        super(createPolyline(
          coordinateReference,
          coordinates as PointCoordinates[]
        ), { code: sidc, label });
        break;
      }
      case ShapeType.POLYGON: {
        super(createPolygon(
          coordinateReference,
          coordinates as PointCoordinates[]
        ), { code: sidc, label });
        break;
      }
      case ShapeType.ELLIPSE: {
        const coords = coordinates as PointCoordinates[];
        super(createEllipse(
          coordinateReference,
          createPoint(coordinateReference, coords[0]),
          2,
          3,
          5
        ));
        break;
      }
      case ShapeType.ARC_BAND: {
        const coords = coordinates as PointCoordinates[];
        super(createArcBand(
          coordinateReference,
          createPoint(coordinateReference, coords[0]),
          2,
          3,
          5,
          6
        ));
        break;
      }
      default: {
        super(
          createPoint(coordinateReference, coordinates as PointCoordinates),
          { code: sidc, label /* modifiers: { movementDirection: '200' }*/ }
        );
        break;
      }
    }
    this.luciadMapDisplay = luciadMapDisplay;
    this.selected = false;
    this.coordinates = coordinates;
    this.coordinateReference = coordinateReference;
  }

  getLocations(): Coordinate[] | undefined {
    const { shape } = this;
    if (shape instanceof Polygon || shape instanceof  Polyline) {
      const { pointCount } = shape;
      const locationArray: Coordinate[] = [];
      this.coordinates = [];
      for (let i = 0; i < pointCount; i += 1) {
        const { x, y } = shape.getPoint(i);
        this.coordinates.push([x, y]);
        locationArray.push([x, y]);
      }
      return locationArray;
    }
    // if (Array.isArray(this.coordinates[0])) {
    //   return this.coordinates as Coordinate[];
    // }
    return undefined;
  }

  getLocation(): Coordinate | undefined {
    const { shape } = this;
    if (shape instanceof Point) {
      this.coordinates = [shape.x, shape.y];
      return [shape.x, shape.y];
    }

    if (shape instanceof Ellipse
      || shape instanceof ArcBand
      || shape instanceof Arc) {
      this.coordinates = [shape.center.x, shape.center.y];
      return [shape.center.x, shape.center.y];
    }

    return undefined;
  }

  static isDirectionOfAttack(sidc: string): boolean {
    return MIL_STD_2525b.getSymbologyNode(sidc).id === '2.X.*******.2.1';
  }

  setLocation(coordinate: Coordinate | Coordinate[]): void {
    this.coordinates = reverseCoordinates(coordinate);
    const { shape } = this;
    if (shape instanceof Polygon || shape instanceof Polyline) {
      const coord: Coordinate[] =  this.coordinates as Coordinate[];
      // if (MS2525Symbol.isDirectionOfAttack(this.sidc)) {
      //   coord = coord.reverse();
      // }
      for (let i = 0; i < coordinate.length; i += 1) {
        shape.removePoint(i);
        shape.insertPoint(i, createPoint(this.coordinateReference, coord[i]));
      }
    } else if (shape instanceof Point
      || shape instanceof Ellipse
      || shape instanceof Arc
      || shape instanceof ArcBand) {
      const coord: Coordinate = this.coordinates as Coordinate;
      shape.move2DToCoordinates(coord[0], coord[1]);
    }

    if (this.selected) {
      const coordinates = shape instanceof Polyline
        || shape instanceof Polygon
        ? this.getLocations()
        : [this.getLocation()];

      const screenXYCoordinate = getScreenXYFromMapPoint(
        this.luciadMapDisplay.map,
        createPoint(
          getReference('CRS:84'),
          coordinates[0]
        )
      );

      this.luciadMapDisplay.store.dispatch(mapActions.mapElementSelection({
        elementId: this.id,
        screenCoordinates: {
          x: screenXYCoordinate[0],
          y: screenXYCoordinate[1]
        },
        coordinates
      }));
    }
  }

  setSpeedAndBearing(speed: number, bearing: number): void {
    this.properties['modifiers'] = { speedLabel: speed, movementDirection: bearing };
  }

  setSymbolCode(symbolCode: string): void {
    this.properties['code'] = symbolCode;
  }

  createFeature(
    shape: Shape,
    symbology: HierarchicalSymbology,
    code: string,
    modifiers: unknown,
    id: number
  ): Feature {
    const properties = {
      code,
      symbology,
      modifiers: {}
    };

    const symbol = new MilitarySymbol(symbology, code);
    Object.keys(modifiers).forEach(prop => {
      if (Object.prototype.hasOwnProperty.call(modifiers, prop)
        && Object.prototype.hasOwnProperty.call(symbol, prop)) {
        (properties.modifiers as unknown)[prop] = modifiers[prop];
      }
    });

    return new Feature(shape, properties, id);
  }
}
