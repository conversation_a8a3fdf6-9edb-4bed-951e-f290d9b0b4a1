<ng-container>
  @let crud = crud$ | async;
  @let model = model$ | async;
  @if (crud === 'Create' || crud === 'Update') {
    <h2 mat-dialog-title>{{ title$ | async }}</h2>
  }

  <mat-dialog-content class="mat-typography">
    <vcci-bs-form
      #form
      [nodes]="nodes$ | async"
      [crud]="crud$ | async"
      [model]="model"
      [settings]="ds.settings.get$('user') | async"
      (networkInterfaceChanges)="onNetworkInterfaceChanges($event)"
    />
  </mat-dialog-content>

  <ng-container *ngrxLet="busy$; let busy">
    <mat-progress-bar [mode]="busy ? 'indeterminate' : 'determinate'" />
  </ng-container>

  @if (error$ | async; as error) {
    <p class="error-msg">* {{ error }}</p>
  }

  @if (crud === 'Create' || crud === 'Update') {
    <mat-dialog-actions align="end">
      @let showConnectionButton = showConnectionButton$ | async;
      @if (showConnectionButton) {
        <button
          mat-button
          color="primary"
          [disabled]="invalid$ | async"
          (click)="form.onCreateConnection()"
        >
          Next
        </button>
      } @else {
        <button
          mat-button
          color="primary"
          [disabled]="invalid$ | async"
          (click)="form.onSubmit()"
        >
          {{ crud }}
        </button>
      }
      <button mat-button mat-dialog-close>Cancel</button>
    </mat-dialog-actions>
  }
</ng-container>
