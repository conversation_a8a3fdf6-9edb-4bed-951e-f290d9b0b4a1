import { LeafletMouseEvent } from 'leaflet';
import { MouseListener } from './EventListener';
import { MapDisplay } from './MapDisplay';

// export type MouseCoordinateType = 'MGRS' | 'LatLon';
export type MousePos = [number, number];

export const MouseCoordinateStd = {
  MGRS: 'MGRS',
  LATLON: 'LatLon',
  GARS: 'GARS'
} as const;

type ObjectValues<T> = T[keyof T];

export type MouseCoordinateType = ObjectValues<typeof MouseCoordinateStd>;

export abstract class MouseCoordinateLocator implements MouseListener {
  mapNodePosition: DOMRect;
  constructor(
    protected mapDisplay: MapDisplay<object, object>,
    private coordinateType: MouseCoordinateType = MouseCoordinateStd.LATLON
  ) {
    // this.mapDisplay = mapDisplay;
    this.mapNodePosition = this.mapDisplay
      .getContainerDOM()
      .getBoundingClientRect();
    this.bindListener();
  }

  setCoordinateType(coordinateType: MouseCoordinateType): void {
    this.coordinateType = coordinateType;
  }

  private bindListener(): void {
    this.mouseMove = this.mouseMove.bind(this);
  }

  mouseMove(event: MouseEvent): void {
    const mousePos: MousePos = [
      event.clientX - this.mapNodePosition.left,
      event.clientY - this.mapNodePosition.top
    ];
    this.gisLocateMouseCoordinate(this.coordinateType, mousePos);
  }

  protected abstract gisLocateMouseCoordinate(
    coordinateType: MouseCoordinateType,
    mousePos: MousePos
  ): void;

  startMouseCoordinateLocator(): void {
    this.mapDisplay.inputHandler.addMouseCoordinatesListeners(this);
  }

  endMouseCoordinateLocator(): void {
    this.mapDisplay.inputHandler.removeMouseCoordinatesListeners(this);
  }
}
