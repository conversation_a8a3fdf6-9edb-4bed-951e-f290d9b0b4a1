import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import {
  TableColumn,
  TableComponent,
  TableSelectionMode
} from '@simfront/common-libs';
import { LogEntry } from '../../data/log/log.model';

@Component({
  selector: 'vcci-ms-log',
  template: `
    <mat-card-header class="title">
      <mat-card-title>{{ nodeName }}</mat-card-title>
    </mat-card-header>
    <mat-card-content class="content">
      <sf-core-table
        class="log-tbl"
        identifier="logEntry"
        includePaginator
        includeSort
        [data]="logEntries"
        [columns]="columns"
        [trackBy]="trackBy"
        [selectionMode]="selectionMode"
        [paginatorPosition]="'top'"
      />
    </mat-card-content>
  `,
  styles: [
    `
      .log-tbl,
      .log-tbl ::ng-deep mat-card {
        background-color: transparent;
        border-width: 0;
      }

      .log-tbl ::ng-deep tr:has(td) {
        height: 30px;
      }

      .log-tbl ::ng-deep td > span > div {
        width: fit-content;
      }

      .log-tbl ::ng-deep th.mat-column-Time {
        width: 1%;
        min-width: 195px;
      }

      .log-tbl ::ng-deep th.mat-column-Severity {
        width: 1%;
        min-width: 120px;
      }

      .log-tbl ::ng-deep td.mat-column-Time {
        color: mediumpurple;
      }

      .log-tbl ::ng-deep .WARN {
        color: lightgoldenrodyellow;
      }

      .log-tbl ::ng-deep .INFO {
        color: lightskyblue;
      }

      .log-tbl ::ng-deep .ERROR {
        color: red;
      }

      .title {
        padding-bottom: 10px;
      }

      .content {
        display: flex;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.025);
        padding: 0;
      }
    `
  ],
  host: {
    style: 'background-color: rgba(0, 0, 0, 0.2)',
    class: 'mat-mdc-card mdc-card'
  },
  standalone: false
})
export class MsLogComponent implements AfterViewInit {
  @ViewChild(TableComponent) table: TableComponent<LogEntry>;

  @Input({ required: true }) nodeName: string = '';
  @Input({ required: true }) logEntries: LogEntry[] = [];

  selectionMode = TableSelectionMode.None;
  trackBy = (log: LogEntry) => `${log.time}`;

  get columns(): TableColumn<LogEntry>[] {
    const columns = [
      {
        header: 'Time',
        prop: (log: LogEntry) => new Date(log.time).toLocaleString()
      },
      {
        header: 'Severity',
        prop: (log: LogEntry) => log.severity,
        classes: (log: LogEntry) => log.severity
      },
      { header: 'Message', prop: (log: LogEntry) => log.message }
    ];
    return this.nodeName === 'Group'
      ? [
          {
            header: 'Originator',
            prop: (log: LogEntry) => log.originator
          },
          ...columns
        ]
      : columns;
  }

  ngAfterViewInit() {
    this.table.dataSource.sortingDataAccessor = (row, sortHeaderId) => {
      const tableColumn: TableColumn<LogEntry> | undefined = this.columns.find(
        (c) => c.header === sortHeaderId
      );
      if (tableColumn?.header === 'Time') {
        return row.time;
      }
      return tableColumn ? String(tableColumn.prop(row)) : '';
    };
    this.table
      .matSort()
      .sort({ id: 'Time', start: 'desc', disableClear: true });
  }
}
