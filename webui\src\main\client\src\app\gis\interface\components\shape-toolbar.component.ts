/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/30/2024, 1:16 PM
 */
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { cursitActions } from '../../../cursit/actions/cursit.actions';
import { CursitControllerType } from '../../../cursit/models/cursit.model';
import { ShapeType } from '../models/map-element.model';
import { GisService } from '../service/gis.service';
import { ExpandedToolbars } from './toolbar-container.component';

interface ButtonInfo {
  svgIcon: string;
  label: string;
  shapeType: ShapeType;
}

@Component({
    selector: 'vcci-shape-toolbar',
    templateUrl: 'shape-toolbar.component.html',
    styleUrls: ['shape-toolbar.component.scss'],
    standalone: false
})

export class ShapeToolbarComponent {
  buttonInfoList1: ButtonInfo[] = [];
  buttonInfoList2: ButtonInfo[] = [];
  buttonInfoList3: ButtonInfo[] = [];
  @Input() expanded: boolean = false;
  @Input() all: boolean = false;

  @Output() shapeToolbarExpanded: EventEmitter<ExpandedToolbars> = new EventEmitter<ExpandedToolbars>();

  constructor(private gisService: GisService) {
    // this.buttonInfoList1.push({
    //   svgIcon: 'text',
    //   label: 'TEXT OBJECT',
    //   shapeType: undefined
    // });
    this.buttonInfoList1.push({
      svgIcon: 'point',
      label: 'Point',
      shapeType: ShapeType.POINT
    });
    this.buttonInfoList1.push({
      svgIcon: 'bounds',
      label: 'Bounds Object',
      shapeType: ShapeType.BOUNDS
    });
    this.buttonInfoList1.push({
      svgIcon: 'polyline',
      label: 'Polyline',
      shapeType: ShapeType.POLYLINE
    });
    this.buttonInfoList1.push({
      svgIcon: 'polygon',
      label: 'Polygon',
      shapeType: ShapeType.POLYGON
    });
    this.buttonInfoList1.push({
      svgIcon: 'circle',
      label: 'Circle',
      shapeType: ShapeType.CIRCLE_BY_CENTER_POINT
    });
    this.buttonInfoList1.push({
      svgIcon: 'circle-by-3-pts',
      label: '3-Point Circle',
      shapeType: ShapeType.CIRCLE_BY_3_POINTS
    });
    this.buttonInfoList1.push({
      svgIcon: 'ellipse',
      label: 'Ellipse',
      shapeType: ShapeType.ELLIPSE
    });
    this.buttonInfoList2.push({
      svgIcon: 'elliptic-arc',
      label: 'Elliptic Arc',
      shapeType: ShapeType.ARC
    });
    this.buttonInfoList2.push({
      svgIcon: 'arc-by-3-pts',
      label: '3-Point Arc',
      shapeType: ShapeType.CIRCULAR_ARC_BY_3_POINTS
    });
    this.buttonInfoList2.push({
      svgIcon: 'arc-by-bulge',
      label: 'Arc by Bulge',
      shapeType: ShapeType.CIRCULAR_ARC_BY_BULGE
    });
    this.buttonInfoList2.push({
      svgIcon: 'arc-by-center',
      label: 'Arc by Centre',
      shapeType: ShapeType.CIRCULAR_ARC_BY_CENTER_POINT
    });
    this.buttonInfoList2.push({
      svgIcon: 'arcband',
      label: 'Arc Band',
      shapeType: ShapeType.ARC_BAND
    });
    this.buttonInfoList2.push({
      svgIcon: 'sector',
      label: 'Sector',
      shapeType: ShapeType.SECTOR
    });
    this.buttonInfoList3.push({
      svgIcon: 'geo-buffer',
      label: 'Geo Buffer',
      shapeType: ShapeType.GEO_BUFFER
    });

    // window.addEventListener('click', (event: MouseEvent) => {
    //   const targetObject = event.target;
    //   if (targetObject) {
    //     const element = targetObject as HTMLElement;
    //     console.log(element.className);
    //     if (element.className !== 'shape-toolbar-button' && !element.closest('.toolbar-wrapper')) {
    //       this.expanded = false;
    //       cdr.detectChanges();
    //     } else {
    //       this.expanded = true;
    //     }
    //   }
    // });

    // this.buttonInfoList3.push({
    //   svgIcon: '',
    //   label: 'ARC BAND',
    //   shapeType: ShapeType.BUF
    // });
    // this.buttonInfoList3.push({
    //   svgIcon: '',
    //   label: 'ARC BAND',
    //   shapeType: ShapeType.ARC_BAND
    // });
  }

  // ngOnChanges(changes: SimpleChanges): void {
  // const thisToolbar = this.toolbarStatus.find(toolbar => toolbar.toolbarName === 'shape_drawing');
  // if (thisToolbar) {
  //   this.expanded = thisToolbar.expanded;
  // }
  // }

  createTheShape(shapeType: ShapeType): void {
    // if (this.expanded) {
    //   this.expanded = false;
    // }
    this.gisService.store.dispatch(cursitActions.updateCursitController(
      {
        controllerType: CursitControllerType.Create,
        controllerPayload: { shapeType }
      }
    ));
  }

  toggleShapeToolbar(): void {
    if (this.all) {
      this.expanded = true;
    } else {
      this.expanded = !this.expanded;
    }
    this.shapeToolbarExpanded.emit({
      entities: false,
      drawing: this.expanded,
      movement: false,
      styling: false,
      all: false
    });

    // this.gisService.store.dispatch(mapActions.toggleSingleToolbar({
    //   toolbarName: 'shape_drawing',
    //   expanded: this.expanded
    // }));
  }

  protected readonly ShapeType = ShapeType;
}
