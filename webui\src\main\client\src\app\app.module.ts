import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi
} from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DEFAULT_OPTIONS } from '@angular/material/dialog';
import { MatIconRegistry } from '@angular/material/icon';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouteReuseStrategy } from '@angular/router';

import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';

import { CommonLibsModule, CoreModule } from '@simfront/common-libs';
import { LeafletModule } from '@bluehalo/ngx-leaflet';
import { LeafletMarkerClusterModule } from '@bluehalo/ngx-leaflet-markercluster';

import { environment } from '../environments/environment';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BsnModule } from './battlespace/bsn.module';
import { UserEffects } from './core/effects/user.effects';
import { VcciCoreModule } from './core/vcci-core.module';
import { CursitModule } from './cursit/cursit.module';
import { DataModule } from './data/data.module';
import { FileChooserComponent } from './file-chooser-component/file-chooser-component.component';
import { FiltersModule } from './filters/filters.module';
import { MaterialModule } from './material/material.module';
import { metaReducers, reducers } from './reducers';
import { SelectGisengineDialogComponent } from './select-gisengine-dialog.component';
import { HttpErrorInterceptor } from './shared/http-error/http-error.interceptor';
import { IS_POPOUT_WINDOW } from './shared/popout-window/popout-window.model';
import { PopoutWindowModule } from './shared/popout-window/popout-window.module';
import { VcciReuseStrategy } from './shared/services/vcci-reuse.strategy';
import { WithCredentialsInterceptor } from './core/interceptors/with-credentials.interceptor';
import { EntitySearchComponent } from './gis/interface/components/entity-search.component/entity-search.component';

@NgModule({
  bootstrap: [AppComponent],
  declarations: [
    AppComponent,
    SelectGisengineDialogComponent,
    FileChooserComponent,
    EntitySearchComponent
  ],
  imports: [
    LeafletModule,
    LeafletMarkerClusterModule,
    BrowserModule,
    AppRoutingModule,
    CursitModule,
    BrowserAnimationsModule,
    MaterialModule,
    PopoutWindowModule,
    StoreModule.forRoot(reducers, {
      runtimeChecks: {
        strictActionImmutability: true,
        strictActionSerializability: true,
        strictActionTypeUniqueness: true,
        strictActionWithinNgZone: false,
        strictStateImmutability: true,
        strictStateSerializability: true
      },
      metaReducers
    }),
    EffectsModule.forRoot([UserEffects]),
    StoreDevtoolsModule.instrument({
      maxAge: 100,
      logOnly: environment.production,
      actionsBlocklist: [
        '[endpoint] Update One Success',
        '[endpoint] Update One Received',
        '[Map] Scale Indicator',
        '[Map] Mouse Move',
        '[entity] Notification',
        '[entity] Create One Success',
        '[entity] Update One Success',
        '[entity] Read Many Received',
        '[entity] Update Many Received'
      ],
      connectInZone: true
    }),
    FormsModule,
    ReactiveFormsModule,
    VcciCoreModule,
    BsnModule,
    CoreModule,
    CommonLibsModule,
    FiltersModule,
    DataModule
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: WithCredentialsInterceptor,
      multi: true
    },
    { provide: HTTP_INTERCEPTORS, useClass: HttpErrorInterceptor, multi: true },
    { provide: RouteReuseStrategy, useClass: VcciReuseStrategy },
    { provide: IS_POPOUT_WINDOW, useValue: false },
    { provide: MAT_DIALOG_DEFAULT_OPTIONS, useValue: { autoFocus: 'dialog' } },
    provideHttpClient(withInterceptorsFromDi())
  ]
})
export class AppModule {
  constructor(iconRegistry: MatIconRegistry) {
    iconRegistry.setDefaultFontSetClass('material-symbols-outlined');
  }
}
