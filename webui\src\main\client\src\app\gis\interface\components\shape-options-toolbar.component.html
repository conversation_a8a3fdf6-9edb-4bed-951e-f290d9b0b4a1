<div class="toolbar-wrapper">
  <!-- <div class="button-wrapper">
    <button
      matTooltip="{{
        expanded
          ? 'Collapse Shape Styling Toolbar'
          : 'Expand Shape Styling Toolbar'
      }}"
      class="shape-toolbar-button"
      [ngClass]="{ expanded: expanded }"
      (click)="toggleShapeOptionsToolbar()"
    >
      <mat-icon class="button-icon" svgIcon="shape-options"></mat-icon>
    </button>
  </div> -->
  @if (expanded) {
    <div class="toolbar">
      <div class="button-group">
        <label class="fill-label">FILLING</label>
        <button
          matTooltip="Plain Fill"
          class="option-button"
          [ngClass]="{
            selected: selected ? selectedPlainFillActive : normalPlainFillActive
          }"
          (click)="plainFill()"
        >
          <mat-icon
            class="button-icon"
            svgIcon="fill-plain-shp-opts-tb"
          ></mat-icon>
        </button>
        <button
          matTooltip="Hatched Fill"
          class="option-button"
          [ngClass]="{
            selected: selected
              ? !selectedPlainFillActive
              : !normalPlainFillActive
          }"
          (click)="hatchedFill()"
        >
          <mat-icon
            class="button-icon"
            svgIcon="fill-hatched-shp-opts-tb"
          ></mat-icon>
        </button>
        <input
          id="custom-fill-color-input"
          class="custom-color-input"
          type="color"
          [(ngModel)]="selected"
          (ngModelChange)="setFillColor()"
        />
        <button
          matTooltip="Fill Color"
          class="option-button color-picker-button"
          (click)="fillColor()"
        >
          <mat-icon
            class="button-icon color-picker-icon"
            svgIcon="fill-plain-shp-opts-tb"
            [style.color]="selected ? selectedFillColor : normalFillColor"
          ></mat-icon>
        </button>
      </div>

      <div class="button-group">
        <label class="fill-label">ON SELECTED</label>
        <button
          [ngClass]="{ selected: selected }"
          matTooltip="Selection"
          class="option-button"
          (click)="activateSelection()"
        >
          <mat-icon
            class="button-icon"
            svgIcon="selected-rendering-shp-opts-tb"
          ></mat-icon>
        </button>
        <button
          [ngClass]="{ selected: !selected }"
          matTooltip="Normal"
          class="option-button"
          (click)="activateNormal()"
        >
          <mat-icon
            class="button-icon"
            svgIcon="fill-plain-shp-opts-tb"
          ></mat-icon>
        </button>
      </div>
      <div class="button-group">
        <label class="stroke-label">STROKE</label>
        <input
          id="custom-normal-outline-color-input"
          class="custom-color-input"
          type="color"
          [(ngModel)]="normalStrokeColor"
          (ngModelChange)="setNormalStrokeColor($event)"
        />
        <button
          matTooltip="Stroke Color"
          class="option-button color-picker-button"
          (click)="normalStrokeColorSelect()"
        >
          <mat-icon
            class="button-icon color-picker-icon"
            svgIcon="outline-color-shp-opts"
            [style.color]="normalStrokeColor"
          ></mat-icon>
        </button>
        <input
          id="custom-selected-outline-color-input"
          class="custom-color-input"
          type="color"
          [(ngModel)]="selectedStrokeColor"
          (ngModelChange)="setSelectedStrokeColor($event)"
        />
        <button
          matTooltip="Selection Stroke Color"
          class="option-button color-picker-button"
          (click)="selectedStrokeColorSelect()"
        >
          <mat-icon
            class="color-picker-icon button-icon"
            svgIcon="fill-plain-shp-opts-tb"
            [style.color]="selectedStrokeColor"
          ></mat-icon>
        </button>
        <div class="stroke-select-wrapper">
          <mat-select
            matTooltip="Line Width"
            [ngClass]="{
              stroke1: selected
                ? selectedStrokeWidth === 1
                : normalStrokeWidth === 1,
              stroke2: selected
                ? selectedStrokeWidth === 2
                : normalStrokeWidth === 2,
              stroke3: selected
                ? selectedStrokeWidth === 3
                : normalStrokeWidth === 3,
              stroke4: selected
                ? selectedStrokeWidth === 4
                : normalStrokeWidth === 4,
              stroke5: selected
                ? selectedStrokeWidth === 5
                : normalStrokeWidth === 5
            }"
            class="line-stroke-select"
            (selectionChange)="selectStroke($event.value)"
          >
            <mat-option
              value="stroke1"
              class="line-stroke-option stroke1"
            ></mat-option>
            <mat-option
              value="stroke2"
              class="line-stroke-option stroke2"
            ></mat-option>
            <mat-option
              value="stroke3"
              class="line-stroke-option stroke3"
            ></mat-option>
            <mat-option
              value="stroke4"
              class="line-stroke-option stroke4"
            ></mat-option>
            <mat-option
              value="stroke5"
              class="line-stroke-option stroke5"
            ></mat-option>
          </mat-select>
        </div>
        <div class="stroke-select-wrapper">
          <mat-select
            matTooltip="Line Pattern"
            [ngClass]="{
              solid: selected
                ? selectedStrokeType === 'solid'
                : normalStrokeType === 'solid',
              dashed: selected
                ? selectedStrokeType === 'dashed'
                : normalStrokeType === 'dashed',
              dot: selected
                ? selectedStrokeType === 'dotted'
                : normalStrokeType === 'dotted'
            }"
            class="line-stroke-select"
            (selectionChange)="selectStrokePattern($event.value)"
          >
            <mat-option
              value="solid"
              class="stroke-pattern-option solid"
            ></mat-option>
            <mat-option
              value="dashed"
              class="stroke-pattern-option dashed"
            ></mat-option>
            <mat-option
              value="dotted"
              class="stroke-pattern-option dot"
            ></mat-option>
          </mat-select>
        </div>
      </div>
    </div>
  }
</div>
