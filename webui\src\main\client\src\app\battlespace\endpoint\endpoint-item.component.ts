import { Component, Input } from '@angular/core';
import { DataService } from '../../data/data.service';
import {
  Endpoint,
  getEndpointId,
  Instruction
} from '../../data/endpoint/endpoint.model';
import { Router } from '@angular/router';

@Component({
  selector: 'vcci-endpoint-item[endpoint]',
  styleUrls: ['endpoint-item.component.css'],
  templateUrl: 'endpoint-item.component.html',
  standalone: false
})
export class EndpointItemComponent {
  @Input() endpoint: Endpoint;
  private _selectedEndpoints: string[] = [];
  @Input()
  set selectedEndpoints(selectedEndpoints: string[]) {
    this._selectedEndpoints = selectedEndpoints;
    if (this._selectedEndpoints?.includes(getEndpointId(this.endpoint))) {
      this.selected = true;
    } else {
      this.selected = false;
    }
  }
  get selectedEndpoints() {
    return this._selectedEndpoints;
  }
  protected readonly getEndpointId = getEndpointId;
  private _isProtoRouterActive = false;
  @Input()
  set isProtoRouterActive(isProtoRouterActive: boolean) {
    this._isProtoRouterActive = isProtoRouterActive;
    if (!this.isProtoRouterActive && this.selected) {
      this.data.endpoint.endpointSelected(getEndpointId(this.endpoint));
    }
  }
  get isProtoRouterActive() {
    return this._isProtoRouterActive;
  }
  selected = false;

  constructor(
    public data: DataService,
    private router: Router
  ) {}

  editEndpoint(): void {
    this.data.endpoint.openCreateEndpointDialog({
      crud: 'Update',
      model: 'Endpoint',
      selectedNode: this.endpoint.node,
      direction: this.endpoint.capability === 'DATA_RECEIVER' ? 'IN' : 'OUT',
      selectedConnection: this.endpoint.connection,
      endpointName: this.endpoint.name,
      currentParameters: this.endpoint.parameters,
      invalid: false
    });
  }

  openEndpointFilters(): void {
    this.data.node.openFiltersDialog(this.endpoint);
  }

  openAssociationDialog(): void {
    this.data.endpoint.openAssocDialog(getEndpointId(this.endpoint));
  }

  onInstructClicked(i: Instruction): void {
    this.data.endpoint.instruct(getEndpointId(this.endpoint), i);
  }

  openMetricsDialog(): void {
    this.data.endpoint.openMetricsDialog(getEndpointId(this.endpoint));
  }

  panelClicked() {
    //if the router view is open - the connection panel endpoint can be selected
    if (this.isProtoRouterActive) {
      this.data.endpoint.endpointSelected(getEndpointId(this.endpoint));
    }
  }
}
