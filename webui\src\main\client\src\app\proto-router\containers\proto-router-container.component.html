<nexus-page pageTitle="ROUTER" svgIcon="router">
  <ng-template #headerSlot>
    <button mat-icon-button [buttonSize]="32" [iconSize]="22" [disabled]="viewStyle === 'Large'"
      (click)="viewStyle = 'Large'" matTooltip="Large View">
      <mat-icon svgIcon="view-type_tiles-small" />
    </button>
    <button mat-icon-button [buttonSize]="32" [iconSize]="22" [disabled]="viewStyle === 'Medium'"
      (click)="viewStyle = 'Medium'" matTooltip="Medium View">
      <mat-icon svgIcon="view-type_tiles" />
    </button>
    <button mat-icon-button [buttonSize]="32" [iconSize]="22" [disabled]="viewStyle === 'Mini'"
      (click)="viewStyle = 'Mini'" matTooltip="Mini View">
      <mat-icon svgIcon="view-type_rows" />
    </button>
    <button mat-icon-button matTooltip="Open Legends" [buttonSize]="36" [iconSize]="24" (click)="openLegendsDialog()">
      <mat-icon svgIcon="info" />
    </button>
  </ng-template>

  <vcci-proto-router [inputs]="inputs$ | async" [outputs]="outputs$ | async" [routes]="(routes$ | async) ?? []"
  [selectedConnectionId]="selectedConnectionId$ | async" [selectedEndpointIds]="selectedEndpointIds$ | async"
  [selectedRoutes]="selectedRoutes$ | async" />
</nexus-page>
