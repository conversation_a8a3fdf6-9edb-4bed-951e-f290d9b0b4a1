<ng-container *ngIf="!!form">
  <form [formGroup]="form">
    @for (param of parameters; track param.name; let index = $index) {
      @let previous = index > 0 ? parameters[index - 1] : undefined;
      @if (param.group
      && param.group.mandatory
      && orGroups()[param.group.name].isEmpty()
      && previous?.group?.name !== param.group.name) {
        <mat-error>At least one parameter is required for this group.</mat-error>
      }
      <vcci-data-param
        [usingDefault]="usingDefault"
        [formControlName]="param.name"
        [parameter]="param"
        (networkInterfaceChanges)="onNetworkInterfaceChanges($event)"
        (getNext)="onNext($event)"/>
    }
  </form>
</ng-container>
