import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { ShapeType } from '../../gis/interface/models/map-element.model';
import { GisService } from '../../gis/interface/service/gis.service';
import { cursitActions } from '../actions/cursit.actions';
import { CursitControllerType } from '../models/cursit.model';
import { AddEntitiesDialogComponent } from './add-entities-dialog.component';

@Component({
  selector: 'vcci-cursit-toolbar',
  templateUrl: 'cursit-toolbar.component.html',
  styleUrls: ['cursit-toolbar.component.css'],
  standalone: false
})
export class CursitToolbarComponent {
  constructor(
    private gisService: GisService,
    private store: Store,
    private dialog: MatDialog
  ) {}

  activatePanMode(): void {
    this.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.Pan
      })
    );
  }

  activateBulkSelect(): void {
    this.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.BulkSelect
      })
    );
  }

  activateEntityCreation(): void {
    this.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.Create,
        controllerPayload: {
          shapeType: ShapeType.POLYLINE,
          code: 'GFG*OLKGM-****X'
          // code: 'GFG*OLAV--****X'
        }
      })
    );
  }

  addEntitiesForTest(): void {
    const dialogRef = this.dialog.open(AddEntitiesDialogComponent, {
      width: '300px'
    });

    dialogRef
      .afterClosed()
      .subscribe((result: { numberOfEntities: number | undefined }) => {
        if (result !== undefined) {
          const { numberOfEntities } = result;
          this.gisService.loadAllEntities(numberOfEntities);
        }
      });
  }

  toggleLayerManager(): void {
    // this.gisService.mapDisplay.layerManagerVisibility =  !this.gisService.mapDisplay.layerManagerVisibility;
    // this.gisService.store.dispatch(mapActions.showHideLayerManager(this.gisService.mapDisplay.layerManagerVisibility));
  }
}
