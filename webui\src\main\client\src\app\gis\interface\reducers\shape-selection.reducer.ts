/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/12/2024, 10:14 AM
 */

import { createFeature, createReducer, on } from '@ngrx/store';
import { mapActions } from '../actions/map.actions';
import { SelectedShapeStyleInfo } from '../models/gis.model';

export interface ShapeSelectionState {
  selectedShapeStyleInfo: SelectedShapeStyleInfo;
}

const initialState: ShapeSelectionState = { selectedShapeStyleInfo: {} };

export const shapeSelectionFeature = createFeature({
  name: 'selectedShape',
  reducer: createReducer(
    initialState,
    on(
      mapActions.setSelectedShapeInfo,
      (state, action) => ({ ...state, selectedShapeStyleInfo: action.payload })
    )
  )
});

export const {
  name,
  reducer,
  // this is coming from the name of the feature above (name: selectMapElement)
  selectSelectedShapeState,
  selectSelectedShapeStyleInfo
} = shapeSelectionFeature;
