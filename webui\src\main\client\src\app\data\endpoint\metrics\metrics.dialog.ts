import { Component, EventEmitter, Inject, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { popoutDialogProvider } from '../../../shared/popout-window/popout-dialog.service';
import { IPopoutWindowModal, IS_POPOUT_WINDOW } from '../../../shared/popout-window/popout-window.model';
import { DataService } from '../../data.service';
import { isEndpoint } from '../endpoint.model';

@Component({
    templateUrl: 'metrics.dialog.html',
    styleUrls: ['metrics.dialog.css'],
    providers: popoutDialogProvider(MetricsDialog),
    standalone: false
})
export class MetricsDialog implements IPopoutWindowModal {

  protected readonly isEndpoint = isEndpoint;

  isDetails = false;

  onClose = new EventEmitter<any>();
  endpoint$ = this.dataService.endpoint.endpoint$(this.data);
  metrics$ = this.dataService.endpoint.getDetailedMetrics(this.data);

  constructor(
    @Inject(IS_POPOUT_WINDOW) public isPopoutWindow: boolean | null,
    @Inject(MAT_DIALOG_DATA) private data: string,
    @Optional() public dialogRef: MatDialogRef<any>,
    private dataService: DataService
  ) {
  }

  toggleDetails(): void {
    this.isDetails = !this.isDetails;
  }

}
