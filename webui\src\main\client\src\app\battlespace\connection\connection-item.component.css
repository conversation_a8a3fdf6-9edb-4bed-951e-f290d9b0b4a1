.component {
  background-color: rgba(var(--secondary-background-light-rgb), var(--opacity));
  border-radius: 4px;
  border-left-style: solid;
  border-left-width: thick;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.end-button {
  width: 100% !important;
  height: 24px !important;
  background-color: rgba(var(--background-rgb), var(--opacity));
  border-radius: 0px;
}

.end-button-grp {
  border-top-width: 2px !important;
}

.end-button-txt {
  position: relative;
  top: 2px;
}

.end-button-icon {
  top: 1px;
}

.component.disconnected {
  border-left-color: var(--pending);
}

.component.connected,
.component.processing {
  border-left-color: var(--success);
}

.component.error,
.component.invalid {
  border-left-color: var(--error);
}

.header {
  display: flex;
  align-items: center;
  height: 32px;
}

.left-section {
  display: flex;
  align-items: center;
  margin-left: 2px;
}

.name {
  margin-left: 10px;
  margin-bottom: 0;
  word-break: break-all;
}

.right-section {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.footer {
  border-top: 2px solid var(--divider-dark);
  cursor: pointer;
  font-size: 11px;
  line-height: 14.66px;
}

.endpoint-info {
  margin-left: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.endpoint-summary {
  display: flex;
  align-items: center;
}

.in-out-info {
  min-width: 14px;
  text-align: end;
  margin: 0 2px;
}

.group-icon {
  margin-right: 2px;
}

.content {
  overflow: hidden;
}

.content p {
  margin: 0;
  border-top: rgb(64, 64, 64);
  border-top-style: solid;
}

button {
  width: 36px !important;
  height: 36px !important;
  padding: 6px !important;
}

.conn-icon[data-mat-icon-name] {
  color: var(--text);
  fill: var(--text);
  stroke: var(--text);
  height: 24px !important;
  width: 24px !important;
  font-size: 24px !important;
}

.component {
  container-type: inline-size;
}

@container (max-width: 100px) {

  .name {
    display: none;
  }

  .right-section {
    display: none;
  }

  .footer {
    display: none;
  }

  .content {
    display: none;
  }

  .left-section>button {
    margin-left: 6px;
  }

}
