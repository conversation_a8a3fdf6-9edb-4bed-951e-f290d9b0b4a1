import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { FormGroup } from '@angular/forms';
import { CreateEntityCustomComponent } from '../create-entity-custom/create-entity-custom.component';
import {
  EntityFormField,
  generateCode,
  getSymbolInfo,
  isValidCode
} from '../../../../milstd/models/milstd-2525.model';
import { FunctionIdMappingsByValue } from '../../../../milstd/models/milstd-2525b.model';

@Component({
  selector: 'nexus-create-entity-tab',
  imports: [CommonModule, MatTabsModule, CreateEntityCustomComponent],
  templateUrl: './create-entity-tab.component.html',
  styleUrl: './create-entity-tab.component.css'
})
export class CreateEntityTabComponent implements OnInit {
  @Input() entityType: string = 'Unit';
  @Input() parentForm: FormGroup;
  @Input() milsymFormFields: EntityFormField<string>[] = [];
  @Input() milsymCodeFormFields: EntityFormField<string>[] = [];

  ngOnInit() {
    // Subscribe to changes in the custom form fields
    this.subscribeToFormChanges();

    // Subscribe to milsymCode changes to update form fields
    this.subscribeToMilsymCodeChanges();
  }

  private subscribeToFormChanges() {
    // Watch for changes in the form fields that affect the MILSYM code
    const fieldsToWatch = this.milsymFormFields.map((field) => field.name);

    fieldsToWatch.forEach((field) => {
      this.parentForm.get(field)?.valueChanges.subscribe((newValue) => {
        this.updateMilsymCode(field, newValue);
      });
    });
  }

  private subscribeToMilsymCodeChanges() {
    // When milsymCode changes, update the form fields
    this.parentForm.get('milsymCode')?.valueChanges.subscribe((value) => {
      if (value && isValidCode(value)) {
        this.updateFormFieldsFromMilsymCode(value);
      }
    });
  }

  private updateMilsymCode(changedField?: string, newValue?: any) {
    // Get the most current form values
    const formValues = this.parentForm.getRawValue();

    // If we have a specific field that changed, use its new value
    if (changedField && newValue !== undefined) {
      formValues[changedField] = newValue;
    }

    let functionIDValue: string;

    // Skip if any required values are missing
    if (
      !formValues.symbolSet ||
      !formValues.affiliation ||
      !formValues.dimension ||
      !formValues.status ||
      !formValues.echelon ||
      !formValues.mobility ||
      !formValues.operationalCondition ||
      !formValues.country ||
      !formValues.functionID
    ) {
      return;
    }

    Object.entries(FunctionIdMappingsByValue(formValues.dimension)).forEach(
      (pair) => {
        if (pair[1] === formValues.functionID) {
          functionIDValue = pair[0];
        }
      }
    );

    if (!functionIDValue) {
      return;
    }

    try {
      const code = generateCode(
        formValues.symbolSet,
        formValues.affiliation,
        formValues.dimension,
        formValues.status,
        formValues.echelon,
        formValues.mobility,
        formValues.country,
        formValues.operationalCondition,
        functionIDValue
      );

      this.parentForm.get('milsymCode')?.setValue(code, { emitEvent: false });
    } catch (error) {
      console.error('Error generating code:', error);
    }
  }

  private updateFormFieldsFromMilsymCode(code: string) {
    try {
      const symbolInfo = getSymbolInfo(code);

      this.parentForm.patchValue(
        {
          symbolSet: symbolInfo.symbolSet,
          affiliation: symbolInfo.affiliation,
          dimension: symbolInfo.dimension,
          status: symbolInfo.status,
          echelon: symbolInfo.echelon,
          mobility: symbolInfo.mobility,
          country: symbolInfo.countryCode,
          operationalCondition: symbolInfo.operationalCondition,
          functionID: symbolInfo.functionId
        },
        { emitEvent: true }
      );
    } catch (error) {
      console.error('Error parsing MILSYM code:', error);
    }
  }
}
