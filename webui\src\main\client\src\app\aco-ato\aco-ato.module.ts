import { NgModule} from '@angular/core';
import { CommonModule} from '@angular/common';
import { AcoAtoPageComponent} from './containers/aco-ato-page.component';
import {AcoAtoRoutingModule} from './aco-ato-routing.module';
import {MatButtonModule} from '@angular/material/button';
import { StoreModule } from '@ngrx/store';
import { acoAtoReducer } from './reducers/aco-ato.reducer';
import {SetRtDialogComponent} from './components/set-rt-dialog/set-rt-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { EffectsModule } from '@ngrx/effects';
import { AcoAtoEffects } from './effects/aco-ato.effects';
import { MatNativeDateModule} from '@angular/material/core';
import {ImportDialogComponent} from './components/import-dialog/import-dialog.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    AcoAtoRoutingModule,
    MatButtonModule,
    AcoAtoPageComponent,
    SetRtDialogComponent,
    ImportDialogComponent,
    MatDialogModule,
    FormsModule,
    StoreModule.forFeature('acoAto', acoAtoReducer),
    EffectsModule.forFeature([AcoAtoEffects]),
    MatNativeDateModule,
  ]
})

export class AcoAtoModule {}
