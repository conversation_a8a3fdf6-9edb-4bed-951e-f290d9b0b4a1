<vcci-popout-dialog (onClose)="onClose.emit()" *ngrxLet="endpoint$ as endpoint">
  <h2 mat-dialog-title>{{endpoint.name}} Metrics</h2>
  <mat-dialog-content class="column" *ngIf="isEndpoint(endpoint) && !isDetails; else detailsTemplate">
    <ng-container
      *ngFor="let key of endpoint.metrics | object:'keys'"
      [ngTemplateOutlet]="rowItem"
      [ngTemplateOutletContext]="{key, value: endpoint.metrics[key]}"/>
  </mat-dialog-content>
  <ng-template #rowItem let-key="key" let-value="value">
    <div>{{key | string:'fromCamelCase'}}:</div>
    <div>{{value}}</div>
  </ng-template>
  <mat-dialog-actions align="end">
    <button mat-flat-button (click)="toggleDetails()">{{ isDetails ? 'Back' : 'Details' }}</button>
  </mat-dialog-actions>
</vcci-popout-dialog>
<ng-template #detailsTemplate>
  <vcci-detailed-metrics [metrics]="(metrics$ | async) ?? []" />
</ng-template>

