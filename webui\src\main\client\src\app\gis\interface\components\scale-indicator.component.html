<div id="scaleIndicator" class="scaleIndicator">
  <div *ngIf="scaleIndicator && scaleIndicator.text" class="scaleIndicatorText">{{this.scaleIndicator.text}}</div>
  <div *ngIf="scaleIndicator && scaleIndicator.style" class="scaleIndicatorBackground" style="{{this.scaleIndicator.style}}">
    <div *ngFor="let foregroundTick of this.foregroundTicks;"
         class="scaleIndicatorForeground"
         style="{{foregroundTick}}"></div>
  </div>
</div>
