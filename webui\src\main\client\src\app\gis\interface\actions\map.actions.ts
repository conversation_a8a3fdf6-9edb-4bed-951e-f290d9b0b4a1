/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/22/2023, 2:15 PM
 */
import { createActionGroup } from '@ngrx/store';
import { payload } from '@simfront/common-libs';
import {
  MapElementSelectionInfo,
  SelectedShapeStyleInfo
} from '../models/gis.model';
import { ScaleIndicator } from '../models/scale-indicator.model';

export const mapActions = createActionGroup({
  source: 'Map',
  events: {
    'Mouse Move': payload<string>(),
    'Scale Indicator': payload<ScaleIndicator>(),
    'Map Element Selection': payload<MapElementSelectionInfo>(),
    'Set Selected Shape Info': payload<SelectedShapeStyleInfo>(),
    'Hide Entity Selection Menu': payload<void>(),
    'Show Entity Selection Menu': payload<void>(),
    'Open Entity Selection Dialog': payload<void>(),
    'Close Entity Selection Dialog': payload<void>()
  }
});
