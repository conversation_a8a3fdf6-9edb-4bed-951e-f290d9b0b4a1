import { Injectable, Injector } from '@angular/core';
import { EventSourceService } from '../../shared/services/event-source.service';
import { environment } from '../../../environments/environment';
import { DataClassName, DataNotification, isDataNotification } from './events.model';
import { Store } from '@ngrx/store';

@Injectable({
  providedIn: 'root'
})
export class EventsService extends EventSourceService<DataNotification> {

  handleMessageEvent = ({ data }: MessageEvent<DataNotification>) => {
    if (isDataNotification(data)) {
      this.store.dispatch({
        type: `[${DataClassName[data.className]}] ${data.crud} ${Array.isArray(data.payload) ? 'Many' : 'One'} Received`,
        payload: data.payload
      });
    } else {
      console.warn('Received unsupported event notification:', data);
    }
  };

  override onOpen = () => {
  };
  override onMessage = this.handleMessageEvent;
  override onError = (e: Event) => console.error(e);

  constructor(private injector: Injector) {
    super(`${environment.origin}/events`);
  }

  get store(): Store {
    return this.injector.get(Store);
  }

}
