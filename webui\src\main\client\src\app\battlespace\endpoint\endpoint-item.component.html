<div
  class="component system-text"
  [ngClass]="{ selected: selected, active: isProtoRouterActive }"
  (click)="panelClicked()"
>
  <div class="header">
    <button mat-icon-button [buttonSize]="28" [iconSize]="24">
      <mat-icon>target</mat-icon>
    </button>
    <p class="name">{{ endpoint.name }}</p>
    <mat-icon class="icon"
      [svgIcon]="endpoint.capability === 'DATA_SENDER' ? 'outputs' : 'inputs'"
      [matTooltip]="endpoint.capability"
    ></mat-icon>
  </div>
  <div class="content">
    @if (endpoint.state === 'PROCESSING') {
      <button
        mat-icon-button
        matTooltip="Pause Data Communication"
        [buttonSize]="24"
        [iconSize]="18"
        (click)="$event.stopPropagation(); onInstructClicked('PAUSE')"
      >
        <mat-icon>pause</mat-icon>
      </button>
    } @else if (endpoint.state === 'REQUESTING') {
      <mat-spinner class="spinner" />
    } @else {
      <button
        mat-icon-button
        matTooltip="Start Data Communication"
        [buttonSize]="24"
        [iconSize]="18"
        (click)="$event.stopPropagation(); onInstructClicked('PLAY')"
      >
        <mat-icon>play_arrow</mat-icon>
      </button>
    }
    <div class="divider"></div>
    <button
      mat-icon-button
      matTooltip="Restart Data Communication"
      [buttonSize]="24"
      [iconSize]="18"
      (click)="
        $event.stopPropagation(); data.endpoint.restart(getEndpointId(endpoint))
      "
    >
      <mat-icon>refresh</mat-icon>
    </button>
    <button
      mat-icon-button
      matTooltip="Filter Data"
      [buttonSize]="24"
      [iconSize]="18"
      (click)="$event.stopPropagation(); openEndpointFilters()"
    >
      <mat-icon>filter_alt</mat-icon>
    </button>
    <div class="divider"></div>
    <button
      mat-icon-button
      matTooltip="Associate Data"
      [buttonSize]="24"
      [iconSize]="18"
      [disabled]="endpoint.capability === 'DATA_RECEIVER'"
      (click)="$event.stopPropagation(); openAssociationDialog()"
    >
      <mat-icon>link</mat-icon>
    </button>
    <button
      mat-icon-button
      matTooltip="Show Endpoint Metrics"
      [buttonSize]="24"
      [iconSize]="18"
      (click)="$event.stopPropagation(); openMetricsDialog()"
    >
      <mat-icon>bar_chart</mat-icon>
    </button>
    <div class="divider"></div>
    <button
      mat-icon-button
      matTooltip="Edit Endpoint Parameters"
      [buttonSize]="24"
      [iconSize]="18"
      (click)="editEndpoint()"
    >
      <mat-icon>settings</mat-icon>
    </button>
    <button
      mat-icon-button
      matTooltip="Delete Endpoint"
      [buttonSize]="24"
      [iconSize]="18"
      (click)="
        $event.stopPropagation();
        data.endpoint.openDeleteDialog(getEndpointId(endpoint))
      "
    >
      <mat-icon>delete</mat-icon>
    </button>
  </div>

  <div class="footer">
    {{ endpoint.state }}
  </div>
</div>
