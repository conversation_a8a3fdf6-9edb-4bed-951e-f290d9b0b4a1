<div class="additional-actions-toolbar">
  <div class="button-wrapper">
    <button
      matTooltip="Print CURSIT"
      (click)="printMap()">
      <mat-icon class="button-icon" svgIcon="print" />
    </button>
  </div>
<!--  <div class="button-wrapper">-->
<!--    <button class="toggle-expand-button"-->
<!--            matTooltip="{{expanded ? 'Collapse Action Toolbar' : 'Expand Action Toolbar'}}"-->
<!--            matTooltipPosition="above"-->
<!--            (click)="toggleExpansion()">-->
<!--      <mat-icon class="button-icon" [ngClass]="{'collapse': expanded}"-->
<!--                svgIcon="expand-additional-act"></mat-icon>-->
<!--    </button>-->
<!--  </div>-->

<!--  <div class="action-buttons-wrapper" [ngClass]="{'expanded': expanded}">-->
<!--    <div class="button-wrapper">-->
<!--      <button-->
<!--        matTooltip="Print CURSIT"-->
<!--        matTooltipPosition="above"-->
<!--        (click)="printMap()">-->
<!--        <mat-icon class="button-icon" svgIcon="print"></mat-icon>-->
<!--      </button>-->
<!--    </div>-->

<!--    <div class="button-wrapper">-->
<!--      <button-->
<!--        (click)="addToFavorites()">-->
<!--        <mat-icon class="button-icon" svgIcon="fav-additional-act"></mat-icon>-->
<!--      </button>-->
<!--    </div>-->

<!--    <div class="button-wrapper">-->
<!--      <button-->
<!--        (click)="upload()">-->
<!--        <mat-icon class="button-icon" svgIcon="upload-additional-act"></mat-icon>-->
<!--      </button>-->
<!--    </div>-->

<!--    <div class="button-wrapper">-->
<!--      <button-->
<!--        matTooltip="Radar"-->
<!--        matTooltipPosition="above"-->
<!--        (click)="radar()">-->
<!--        <mat-icon class="button-icon" svgIcon="radar-additional-act"></mat-icon>-->
<!--      </button>-->
<!--    </div>-->
<!--  </div>-->
</div>
