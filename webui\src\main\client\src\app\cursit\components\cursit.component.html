<div
  id="map"
  (mousemove)="isActive ? onMouseMove($event) : onRouteMove($event)"
></div>
<div class="cursit-label-div">
  <mat-icon class="cursit-icon" svgIcon="cursit"></mat-icon>
  <label class="cursit-label">CURSIT</label>
</div>
<vcci-scale-indicator [scaleIndicator]="scaleIndicator$ | async">
</vcci-scale-indicator>
<vcci-mouse-coordinate [mousePosition]="mousePosition$ | async">
</vcci-mouse-coordinate>
<vcci-layer-manager
  [show]="layerManagerVisibility$ | async"
  [allLayers]="this.ds.layer.tree$ | async"
>
</vcci-layer-manager>
<!-- <vcci-top-right-toolbar /> -->
<nexus-top-toolbar />
<!-- <vcci-toolbar-container /> -->
<vcci-map-control-toolbar />
<!-- <vcci-entity-selection-menu
  [screenCoordinates]="selectedItemScreenCoordinates$ | async"
>
</vcci-entity-selection-menu> -->
