import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
    selector: 'vcci-router-legend-item',
    template: `
    <mat-icon class="legend-icon" [ngClass]="{'scale-down-icon': scaleIconDown}" inline svgIcon="{{icon}}"/>
    <ng-content></ng-content>
  `,
    styles: [`
      :host {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 12px;
          font-style: italic;
          height: 24px;
          margin-right: 10px;
      }

      .legend-icon {
          width: 28px !important;
      }

      ::ng-deep .scale-down-icon > svg {
          transform: scale(0.5);
      }
  `],
    imports: [
        CommonModule,
        MatIconModule
    ]
})
export class RouterLegendItemComponent {
  @Input({ required: true }) icon: string = '';
  @Input() scaleIconDown = false;
}
