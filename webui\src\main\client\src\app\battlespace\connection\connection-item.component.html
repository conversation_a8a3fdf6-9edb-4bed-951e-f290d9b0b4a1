<div class="component system-text" [ngClass]="{
     'connected': connection.status === 'CONNECTED',
     'disconnected' : connection.status === 'DISCONNECTED',
     'error': connection.status === 'ERROR',
     'invalid': connection.status === 'INVALID',
     'processing': connection.status === 'PROCESSING'
     }" *ngIf="displayType === 'ALL'; else endpointsTemplate">
  <div class="header">
    <div class="left-section">
      <button mat-icon-button [buttonSize]="28" [iconSize]="20" [matTooltip]="toolTip"
              (click)="toggleExpand(true)"
              matTooltipClass="line-broken-tooltip">
        <mat-icon class="conn-icon" *ngIf="NODE_ICONS[connection.node]; else defaultMatIcon"
                  [svgIcon]="NODE_ICONS[connection.node]"/>
        <ng-template #defaultMatIcon>
          <mat-icon>hub</mat-icon>
        </ng-template>
      </button>
      <p class="name">{{ connection.name }}</p>
    </div>
    <div class="right-section">
      <button mat-icon-button [buttonSize]="28" [iconSize]="20" matTooltip="Edit Connection Parameters"
              (click)="editConnection()">
        <mat-icon>settings</mat-icon>
      </button>
      <button mat-icon-button [buttonSize]="28" [iconSize]="20" matTooltip="Delete Connection"
              (click)="deleteConnection()">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>
  <div class="content" [@expand]="expanded ? 'expanded' : 'collapsed'">
    <ng-container [ngTemplateOutlet]="endpointsTemplate"/>
    @if (showAddButton) {
      <p class="end-button-grp">
        <button mat-flat-button matTooltip="Add Endpoint to connection" class="end-button" (click)="createEndpoint()">
          <mat-icon class="end-button-icon">add_circle_outline</mat-icon>
          <span class="end-button-txt">ADD NEW ENDPOINT</span>
        </button>
      </p>
    }
  </div>
  <div class="footer" (click)="toggleExpand()">
    <div class="endpoint-info">
      <div class="endpoint-summary" [matTooltip]="endpointSummaryTooltip" matTooltipClass="line-broken-tooltip">
        @if (support.externalInputSupported || support.externalOutputSupported) {
          <div>ENDPOINTS:</div>
          @if (support.externalInputSupported) {
            <div class="in-out-info">{{ inbound }}</div>
            <mat-icon svgIcon="inputs" [size]="18"></mat-icon>
          }
          @if (support.externalOutputSupported) {
            <div class="in-out-info">{{ outbound }}</div>
            <mat-icon svgIcon="outputs" [size]="18"></mat-icon>
          }
        }
      </div>
      <div class="endpoint-summary">
        <mat-icon [size]="18" class="group-icon">sell</mat-icon>
        GROUP
        <mat-icon [@rotate]="expanded">keyboard_arrow_down</mat-icon>
      </div>
    </div>
  </div>
</div>


<ng-template #endpointsTemplate>
  <vcci-endpoint-item *ngFor="let endpoint of connection.endpoints; trackBy: endpointTrackBy" [endpoint]="endpoint"
                      [selectedEndpoints]="_selectedEndpoints"
                      [isProtoRouterActive]="data.isProtoRouterActive$ | async"/>
</ng-template>
