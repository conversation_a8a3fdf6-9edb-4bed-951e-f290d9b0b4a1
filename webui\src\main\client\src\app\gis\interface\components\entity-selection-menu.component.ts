/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/18/2024, 2:49 PM
 */
import {
  Component,
  ElementRef,
  inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { MapElementSelectionInfo } from '../models/gis.model';
import { GisService } from '../service/gis.service';
import { PopoutWindowConfig } from '../../../shared/popout-window/popout-window.model';
import { PopoutWindowService } from '../../../shared/popout-window/popout-window.service';
import { Store } from '@ngrx/store';
import { selectIsMenuVisible } from '../reducers/element-selection.reducer';
import { DataService } from '../../../data/data.service';

@Component({
  selector: 'vcci-entity-selection-menu',
  templateUrl: 'entity-selection-menu.component.html',
  styleUrls: ['entity-selection-menu.component.css'],
  standalone: false
})
export class EntitySelectionMenuComponent implements OnChanges, OnInit {
  @Input() screenCoordinates: MapElementSelectionInfo = {
    screenCoordinates: { x: 0, y: 0 },
    coordinates: [[0, 0]],
    elementId: ''
  };

  hidden: boolean = true;
  isMenuVisible: boolean = true;
  ds = inject(DataService);

  @ViewChild('entitySelectionMenu') entitySelectionMenu: ElementRef;

  constructor(
    private gisService: GisService,
    private popoutService: PopoutWindowService,
    private store: Store
  ) {}

  ngOnInit(): void {
    this.store.select(selectIsMenuVisible).subscribe((isVisible) => {
      this.isMenuVisible = isVisible;
    });
  }

  toggleLayerManager(): void {
    this.gisService.mapDisplay.toggleLayerManagerVisibility();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      this.screenCoordinates.elementId !== undefined &&
      this.screenCoordinates.elementId !== ''
    ) {
      this.hidden = false;

      const { x, y } = this.screenCoordinates.screenCoordinates;
      if (this.entitySelectionMenu !== undefined) {
        this.entitySelectionMenu.nativeElement.style.left = `${x}px`;
        this.entitySelectionMenu.nativeElement.style.top = `${y}px`;
      }
    } else {
      this.hidden = true;
    }
  }

  /**
   * Config details for the popout
   */
  getPopoutWindowConfig(): PopoutWindowConfig {
    return {
      title: 'Entity Properties',
      windowStyle: `mat-card {
        width: 100% !important;
        border-radius: 0 !important;
      }
      .name {
        margin: auto !important;
      }`
    };
  }

  /**
   * When the entity details button is clicked, display the details in a popout. A popout was chosen so
   * that multiple entity properties could be compared side by side
   */
  onClick(): void {
    this.ds.entity.openEntityDetailsDialog(
      undefined,
      this.getPopoutWindowConfig()
    );
  }
}
