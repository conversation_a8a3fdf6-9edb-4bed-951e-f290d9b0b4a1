import {
  combineReducers,
  createActionGroup,
  createFeature,
  createReducer,
  emptyProps,
  on
} from '@ngrx/store';
import {
  createEverything,
  optionalPayload,
  payload
} from '@simfront/common-libs';

import { environment } from '../../../environments/environment';

import { RootState } from '../index';

import {
  Candidate,
  OutboundObject,
  RequestType
} from './association/association.model';
import { FilterGroup } from './filter.model';
import { Parameters } from '../bs-params/bs-params.model';
import { BSParamDialogPayload } from '../bs-params/bs-params.store';

export interface Metrics {
  inputPacketCount: number;
  inputFilteredPacketCount: number;
  incomingPacketCount: number;
  outgoingPacketCount: number;
  // incomingUniqueEntityHash: number;
  // entityGeoInfo: number;

  internalTasks: number;
  tasksReceived: number;
  tasksDropped: number;
  tasksProcessed: number;
  tasksFiltered: number;
  packetsPassed?: number;
  packetsFailed?: number;
}

export interface EntityMetrics {
  id: string;
  layerId: string;
  processedTasks?: number;
  filteredTasks?: number;
}

export interface Endpoint {
  id?: number;
  name: string;
  node: string;
  capability: 'DATA_RECEIVER' | 'DATA_SENDER' | 'BOTH';
  connection: string;
  state: 'PAUSED' | 'PROCESSING' | 'ERROR' | 'REQUESTING';
  metrics?: Metrics;
  parameters?: Parameters;
  filters?: FilterGroup[];
}

export type Instruction = 'TRANSFER' | 'PLAY' | 'PAUSE';
export type Configure =
  | 'ALTER_OP_STATE'
  | 'UPDATE'
  | 'UPDATE_FILTERS'
  | 'RESET_METRICS';

export const instructionEqualState = (
  e: Endpoint,
  instruction: Instruction
) => {
  switch (instruction) {
    case 'TRANSFER':
    case 'PLAY':
      return e.state === 'PROCESSING';
    case 'PAUSE':
      return e.state === 'PAUSED';
    default:
      return false;
  }
};

export const getEndpointId = (e: Endpoint) => `${e.node}:${e.name}`;
export const extractEndpointInfo = (id: string) => ({
  node: id.split(':')[0],
  endpoint: id.split(':')[1]
});
export const isEndpoint = (val: any): val is Endpoint =>
  val.name !== undefined &&
  val.node !== undefined &&
  val.capability !== undefined &&
  val.connection !== undefined &&
  val.state !== undefined;

export enum ConfigureInstruction {
  AlterOpState = 'ALTER_OP_STATE',
  Update = 'UPDATE',
  UpdateFilters = 'UPDATE_FILTERS',
  resetMetrics = 'RESET_METRICS'
}

export const isInputEndpoint = (endpoint: Endpoint): boolean =>
  endpoint.capability === 'DATA_RECEIVER';
type EndpointMinimum = Pick<Endpoint, 'name' | 'node'>;
export type ConfigurePayload =
  | { id: string; instruction: ConfigureInstruction; endpoint: EndpointMinimum }
  | {
      id: string;
      instruction: ConfigureInstruction.UpdateFilters;
      endpoint: EndpointMinimum & { filters: FilterGroup[] };
    };

export const { reducer: adapter, facade: EndpointFacadeBase } =
  createEverything<RootState, Endpoint, 'endpoint', never, string>(
    'endpoint',
    getEndpointId,
    `${environment.origin}/endpoints`,
    (state) => state.endpoint.adapter
  );

export const EndpointActions = createActionGroup({
  source: 'endpoint',
  events: {
    Restart: payload<string>(),
    Instruct: payload<{ id: string; instruction: Instruction }>(),
    'Instruct Success': payload<{ id: string; instruction: Instruction }>(),
    Configure: payload<ConfigurePayload>(),
    'Open Create Dialog': optionalPayload<BSParamDialogPayload>(),
    'Create Dialog Dismissed': emptyProps(),
    'Open Assoc Dialog': payload<string>(),
    'Read Candidates': payload<{ id: string; type: RequestType }>(),
    'Read Candidates Success': payload<Candidate[]>(),
    'Read Associations': payload<string>(),
    'Read Associations Success': payload<OutboundObject[]>(),
    'Apply Associations': payload<string>(),
    'Apply Associations Success': emptyProps(),
    Associate: payload<OutboundObject>(),
    Dissociate: payload<string>(),
    'Assoc Dialog Dismissed': emptyProps(),
    'Open Metrics Dialog': payload<string>(),
    'Dismiss Metrics Dialog': emptyProps()
  }
});

// TODO: temp fix to have busy for assoc dialog table
const association = createReducer(
  {
    candidates: [],
    associations: [],
    busy: false,
    instruction: {}
  } as
    | {
        candidates: Candidate[];
        associations: OutboundObject[];
        busy: boolean;
        instruction: { id: string; instruction: Instruction };
      }
    | undefined,
  on(EndpointActions.readCandidates, (state) => ({ ...state, busy: true })),
  on(EndpointActions.readCandidatesSuccess, (state, action) => ({
    ...state,
    candidates: action.payload,
    busy: false
  })),
  on(EndpointActions.readAssociationsSuccess, (state, action) => ({
    ...state,
    associations: action.payload
  })),
  on(EndpointActions.associate, (state, action) => ({
    ...state,
    associations: [...state.associations, action.payload]
  })),
  on(EndpointActions.dissociate, (state, action) => ({
    ...state,
    associations: state.associations.filter((a) => a.srcId !== action.payload)
  })),
  on(EndpointActions.instructSuccess, (state, action) => ({
    ...state,
    instruction: action.payload
  })),
  on(EndpointActions.openCreateDialog, (state) => ({
    ...state
  }))
);

const combined = combineReducers({
  adapter,
  association
});

const endpointFeature = createFeature({
  name: 'endpoint',
  reducer: combined
});

export const {
  name,
  reducer: endpointReducer,
  selectAdapter: selectEndpointAdapter,
  selectAssociation: selectEndpointAssociation
} = endpointFeature;
