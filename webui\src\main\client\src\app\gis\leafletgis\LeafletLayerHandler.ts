import { Layer, LayerGroup, stamp, TileLayer } from 'leaflet';
import { mapActions } from '../interface/actions/map.actions';
import { findLayerAndGroupLevel, getLayerInfo, ParentLayerInfo } from '../interface/gis-util';
import { MapLayerHandler } from '../interface/LayerHandler';
import { MapLayer } from '../interface/MapLayer';
import { AbstractMapLayerGroup, MapLayerGroup } from '../interface/MapLayerGroup';
import { LeafletLayerGroup } from './LeafletLayerGroup';
import { LeafletMap } from './LeafletMap';
import { LeafletMapDisplay } from './LeafletMapDisplay';
import { leafletLayer } from './utility';
import { LayerManagerActions, LayerVisibility } from '../../data/layer-manager/layer-manager.model';

export class LeafletLayerHandler extends MapLayerHandler<Layer, LayerGroup> {
  leafletMap: LeafletMap;

  constructor(mapDisplay: LeafletMapDisplay) {
    super(mapDisplay);
    this.leafletMap = mapDisplay.leafletMap;
  }

  gisAddLayer(layer: MapLayer<Layer> | MapLayerGroup<Layer, LayerGroup>): void {
    // If parent name of the layer to be added is defined
    if (layer.parentId === undefined) {
      // If we can't find the parent layer
      layer.parentId = '-1';
    }

    const parentLayer = this.layerCache[layer.parentId];
    if (parentLayer && parentLayer instanceof AbstractMapLayerGroup) {
      // If we find the parent layer
      parentLayer.addLayer(layer);
      // const layerInfo = getLayerInfo(parentLayer);
      // this.mapDisplay.store.dispatch(LayerManagerActions.updateLayer(layerInfo));
    }
      const layerInfo = getLayerInfo(layer);
      this.mapDisplay.store.dispatch(LayerManagerActions.addLayer(layerInfo));

  }

  createParentLayer(groupName: string, layerId?: string, parentId?: string, dispatch?: boolean): ParentLayerInfo<Layer, LayerGroup> {
    const parentLeafletLayer = leafletLayer(new LayerGroup());
    parentLeafletLayer.layerName = groupName;
    parentLeafletLayer.layerId = layerId ? layerId : stamp(parentLeafletLayer).toString();
    const parentLayer = new LeafletLayerGroup(this.mapDisplay as LeafletMapDisplay, parentLeafletLayer);
    parentLayer.layerId = parentLeafletLayer.layerId;
    parentLayer.layerName = parentLeafletLayer.layerName;
    parentLayer.layerSource = 'System';
    parentLayer.layerType = 'Parent';
    parentLayer.parentId = parentId ? parentId : '-11';
    parentLayer.setVisible(true);
    const layerInfo = getLayerInfo(parentLayer);
    if (!(parentLayer.layerId in this.layerCache)) {
      this.layerCache[parentLayer.layerId] = parentLayer;
    }

    if (dispatch) {
      this.mapDisplay.store.dispatch(
        LayerManagerActions.addLayer(getLayerInfo(parentLayer))
      );
    }
    return {parentLayer, layerInfo};
  }

  gisRemoveLayer(layer: MapLayer<Layer> | MapLayerGroup<Layer, LayerGroup>): void {
    this.leafletMap.removeLayer(layer.getGISLayer());
  }

  mapOpacity(opacity: number): void {
    Object.values(this.layerCache).forEach(
      layer => {
        this.layerOpacity(layer.layerId, opacity);
      }
    );
  }

  layerOpacity(layerId: string, opacity: number) {
    const layer = this.layerCache[layerId];
    if (!(layer instanceof LeafletLayerGroup)) {
      const leafletLyr = layer.getGISLayer();
      if (leafletLyr instanceof TileLayer) {
        leafletLyr.setOpacity(opacity);
      } else {
        try {
          (leafletLyr as L.Polygon | L.Polyline).setStyle({
            color: `rgb(0, 0, 0, ${opacity})`,
            fillColor: 'rgb(131, 149, 170)',
            fillOpacity: 0.3,
            weight: 1
          });
        } catch (e) {
          return;
        }
      }
    }
  }


//   this.service.requestTacticalGraphics({
//                                          milSymDTOItemList: [
//                                            {
//                                              id: '1',
//                                              name: 'deneme',
//                                              description: 'nasil',
//                                              symbolCode: 'GHGPSLA-------X',
//                                              controlPoints:
// eslint-disable-next-line max-len
//                                              '66.26700036208742,30.62755038706961 66.27555681517738,30.64727776502703 66.25654247497746,30.64632704801704',
//                                              pixelWidth: 800,
//                                              pixelHeight: 600,
//                                              bbox: '66.25,30.60,66.28,30.65',
//                                              modifiers: ''
//                                            }, {
//                                              id: '2',
//                                              name: 'deneme',
//                                              description: 'nasil',
//                                              symbolCode: 'GHGPSLA-------X',
// eslint-disable-next-line max-len
//                                              controlPoints: '66.26700036208742,30.62755038706961 66.27555681517738,30.64727776502703 66.25654247497746,30.64632704801704',
//                                              pixelWidth: 800,
//                                              pixelHeight: 600,
//                                              bbox: '66.25,30.60,66.28,30.65',
//                                              modifiers: ''
//                                            }
//                                          ]
//                                        }).subscribe({
//                                                       next: (receivedTacticalGraphicsList) => {
//   receivedTacticalGraphicsList.map((item) => {
//   let parsedTacticalGraphics =  JSON.parse(item) as ReceivedTacticalGraphics;
//   let receivedTacticalGraphics: ReceivedTacticalGraphics = {
//     type: parsedTacticalGraphics.type,
//     polygons: parsedTacticalGraphics.polygons,
//     lines: parsedTacticalGraphics.lines,
//     labels: parsedTacticalGraphics.labels
//   };
//
//   receivedTacticalGraphics.lines.forEach((line: Line) => {
//   line.line.forEach((coords: LatLngTuple) =>{
//   console.log(coords);
// })
// });
// });
//
// },
// error: (error) => {
//   console.error('POST request error', error);
// }
// });
}
