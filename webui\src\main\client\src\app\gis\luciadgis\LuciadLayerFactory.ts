import { createDefaultMGRSGrid } from '@luciad/ria-milsym/grid/mgrs/MGRSGrid';
import { MIL_STD_2525b } from '@luciad/ria-milsym/symbology/military/MIL_STD_2525b';
import { MilitarySymbologyPainter, MilSymStyle } from '@luciad/ria-milsym/symbology/military/MilitarySymbologyPainter';
import { WMSCapabilities } from '@luciad/ria/model/capabilities/WMSCapabilities';
import { WMTSCapabilities } from '@luciad/ria/model/capabilities/WMTSCapabilities';
import {
  WMTSCapabilitiesLayer,
  WMTSCapabilitiesLayerStyle
} from '@luciad/ria/model/capabilities/WMTSCapabilitiesLayer';
import { Feature } from '@luciad/ria/model/feature/Feature';
import { FeatureModel } from '@luciad/ria/model/feature/FeatureModel';
import { MemoryStore } from '@luciad/ria/model/store/MemoryStore';
import { UrlStore } from '@luciad/ria/model/store/UrlStore';
import { WMSTileSetModel } from '@luciad/ria/model/tileset/WMSTileSetModel';
import { WMTSTileSetModel, WMTSTileSetModelCreateOptions } from '@luciad/ria/model/tileset/WMTSTileSetModel';
import { LonLatPointFormat } from '@luciad/ria/shape/format/LonLatPointFormat';
import { Point } from '@luciad/ria/shape/Point';
import { ShapeType } from '@luciad/ria/shape/ShapeType';
import { BasicFeaturePainter } from '@luciad/ria/view/feature/BasicFeaturePainter';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { GridLayer } from '@luciad/ria/view/grid/GridLayer';
import { LonLatGrid } from '@luciad/ria/view/grid/LonLatGrid';
import { Layer } from '@luciad/ria/view/Layer';
import { LayerType as LuciadLayerType } from '@luciad/ria/view/LayerType';
import { FillStyle } from '@luciad/ria/view/style/FillStyle';
import { LineStyle } from '@luciad/ria/view/style/LineStyle';

// import { Shape } from '@luciad/ria/shape/Shape';
// import { Point } from '@luciad/ria/shape/Point';
// import { ClusterShapeProvider } from
// '@luciad/ria/view/feature/transformation/ClusterShapeProvider';
import { ShapeStyle } from '@luciad/ria/view/style/ShapeStyle';
import { RasterTileSetLayer } from '@luciad/ria/view/tileset/RasterTileSetLayer';
import { WMSTileSetLayer } from '@luciad/ria/view/tileset/WMSTileSetLayer';
import { FeatureProperties } from '../../shared/util/util.settings';
import { MapLayerFactory } from '../interface/LayerFactory';
import { MapLayer } from '../interface/MapLayer';
import { GridType, LayerType } from '../../data/layer-manager/layer-manager.model';
import { worldShapeStyle } from './luciad-model';
import { LuciadCompositePainter } from './LuciadCompositePainter';
import { IconFactory } from './LuciadIconFactory';

import { LuciadMapDisplay } from './LuciadMapDisplay';
import { LuciadMapLayer } from './LuciadMapLayer';
import { LuciadMilitarySymbologyClassifier } from './LuciadMilitarySymbologyClassifier';
import { LuciadShapePainter } from './LuciadShapePainter';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { LoadingStrategy } from '@luciad/ria/view/feature/loadingstrategy/LoadingStrategy';
import { LoadSpatially } from '@luciad/ria/view/feature/loadingstrategy/LoadSpatially';
import { create } from '@luciad/ria/view/feature/transformation/ClusteringTransformer';
import { AffiliationClassifier } from './luciad.utils';
// import { ChainedEditController } from './controllers/composite.controller';
// import { BulkSelectController } from './controllers/bulk-select-controller';
// import { AffiliationClassifier } from './luciad.utils';

// class ClusterShape extends ClusterShapeProvider {
//   override getShape(composingElements: Feature[]): Shape {
//     if (composingElements[0].shape instanceof Point) {
//       const { x } = composingElements[0].shape as Point;
//       const { y }  = composingElements[0].shape as Point;
//       console.log(`${x} ${y}`);
//       return createCircleByCenterPoint(
//         composingElements[0].shape.reference,
//         createPoint(composingElements[0].shape.reference, [x, y]),
//         200
//       );
//     }
//     return undefined;
//   }
// }

const normalDefaultStyle: ShapeStyle = {
  stroke: {
    color: 'rgb(0, 0, 255)',
    width: 1
  },
  fill: {
    color: 'rgba(0,0,0, 0.2)'
    //   image: new HatchedImageBuilder()
    //     .patterns([HatchedImageBuilder.Pattern.SLASH, HatchedImageBuilder.Pattern.BACKGROUND])
    //     .lineColor('rgba( 0, 0, 0, 1)')
    //     .lineWidth(1)
    //     .patternSize(10, 10)
    //     .backgroundColor('rgba(0 ,0 , 0, 0)')
    //     .build()
  }
};

const selectedDefaultStyle: ShapeStyle = {
  stroke: {
    color: 'rgb(255,213,0)',
    width: 1
  },
  fill: { color: 'rgb(0,0,0, 0.2)' }
};

const gridFallbackStyle: LonLatGrid.StyleSettings = {
  labelFormat: new LonLatPointFormat({ pattern: 'lat(+DM),lon(+DM)' }),
  originLabelFormat: new LonLatPointFormat({ pattern: 'lat(+D),lon(+D)' }),
  originLineStyle: { color: 'rgba(230, 20, 20, 0.6)', width: 2 },
  lineStyle: { color: 'rgba(201,19,19,0.6)', width: 1 },
  originLabelStyle: {
    fill: 'rgba(210,210,210,0.8)',
    halo: 'rgba(230, 20, 20, 0.8)',
    haloWidth: 3,
    font: '12px sans-serif'
  },
  labelStyle: {
    fill: 'rgb(220,220,220)',
    halo: 'rgb(102,102,102)',
    haloWidth: 3,
    font: '12px sans-serif'
  }
};

const WMTSDataLoader = {
  /**
   * Creates a layer for WMTS data.
   *
   * Summary:
   * - Create a WMTS tileset model for a given url, layer identifier / layer name and options.
   * - Create a @luciad/ria/view/tileset/RasterTileSetLayer
   */
  createLayer: async (
    url: string,
    layerIdentifier: string,
    layerName: string,
    options?: WMTSTileSetModelCreateOptions
  ): Promise<RasterTileSetLayer> => {
    const model = await WMTSTileSetModel.createFromURL(url, { layer: layerIdentifier }, options);
    return new RasterTileSetLayer(model, {
      label: `${layerName} (WMTS)`,
      layerType: LuciadLayerType.BASE
    });
  },

  createLayerFromCapabilities: (
    capabilities: WMTSCapabilities,
    layer: WMTSCapabilitiesLayer,
    style: WMTSCapabilitiesLayerStyle,
    options?: WMTSTileSetModelCreateOptions
  ): RasterTileSetLayer => {
    const model = WMTSTileSetModel.createFromCapabilities(capabilities, {
      layer: layer.identifier,
      style: style.identifier
    }, options);
    return new RasterTileSetLayer(model, {
      label: `${layer.title} (WMTS)`,
      layerType: LuciadLayerType.BASE
    });
  }
};

export class LuciadLayerFactory extends MapLayerFactory<Layer> {
  mapDisplay: LuciadMapDisplay;

  idCounter = 0;

  constructor(mapDisplay: LuciadMapDisplay) {
    super();
    this.mapDisplay = mapDisplay;
  }

  // eslint-disable-next-line class-methods-use-this
  createGridLayer(gridType: GridType): MapLayer<Layer> | null {
    const gridMapLayer = new LuciadMapLayer();
    switch (gridType) {
      case 'MGRS': {
        const primaryColor = 'rgb(0,0,0)';
        const secondaryColor = 'rgb(0, 255, 0)';
        const tertiaryColor = 'rgb(0, 0, 255)';
        const mgrsGrid = createDefaultMGRSGrid({
          scaleMultiplier: 1.5, // make the grid more coarse
          primaryLineStyle: { color: primaryColor, width: 1 },
          primaryLabelStyle: { font: '12px monospace', fill: primaryColor },
          secondaryLineStyle: { color: secondaryColor, width: 2 },
          secondaryLabelStyle: { font: '14px monospace', fill: secondaryColor },
          tertiaryLineStyle: { color: tertiaryColor, width: 3 },
          tertiaryLabelStyle: { font: '16px monospace', fill: tertiaryColor }
        });
        const mgrsGridLayer = new GridLayer(mgrsGrid);
        mgrsGridLayer.label = 'MGRS Grid Layer';
        gridMapLayer.setGISLayer(mgrsGridLayer);
        gridMapLayer.layerId = mgrsGridLayer.id;
        gridMapLayer.layerName = 'MGRS Grid Layer';
        gridMapLayer.layerType = 'Grid';
        break;
      }
      case 'LatLon': {
        const settings = [
          { scale: 40000.0E-9, deltaLon: 1 / 60, deltaLat: 1 / 60 },
          { scale: 20000.0E-9, deltaLon: 1 / 30, deltaLat: 1 / 30 },
          { scale: 10000.0E-9, deltaLon: 1 / 10, deltaLat: 1 / 10 },
          { scale: 5000.0E-9, deltaLon: 1 / 2, deltaLat: 1 / 2 },
          { scale: 1000.0E-9, deltaLon: 1, deltaLat: 1 },
          { scale: 200.0E-9, deltaLon: 5, deltaLat: 5 },
          { scale: 20.0E-9, deltaLon: 10, deltaLat: 10 },
          { scale: 9.0E-9, deltaLon: 20, deltaLat: 20 },
          { scale: 5.0E-9, deltaLon: 30, deltaLat: 30 },
          { scale: 0, deltaLon: 45, deltaLat: 45 }];
        const lonLatGrid = new LonLatGrid(settings);
        lonLatGrid.fallbackStyle = gridFallbackStyle;
        const degreesOnlyFormat = new LonLatPointFormat({ pattern: 'lat(+D),lon(+D)' });
        lonLatGrid.setStyle(lonLatGrid.scales.indexOf(0), { labelFormat: degreesOnlyFormat });
        lonLatGrid.setStyle(lonLatGrid.scales.indexOf(5.0E-9), { labelFormat: degreesOnlyFormat });
        lonLatGrid.setStyle(lonLatGrid.scales.indexOf(9.0E-9), { labelFormat: degreesOnlyFormat });
        lonLatGrid.setStyle(lonLatGrid.scales.indexOf(20.0E-9), { labelFormat: degreesOnlyFormat });
        lonLatGrid.setStyle(lonLatGrid.scales.indexOf(200.0E-9), { labelFormat: degreesOnlyFormat });
        const lonLatGridLayer = new GridLayer(lonLatGrid);
        lonLatGridLayer.label = 'LatLon Grid Layer';
        gridMapLayer.setGISLayer(lonLatGridLayer);
        gridMapLayer.layerId = lonLatGridLayer.id;
        gridMapLayer.layerName = 'LatLon Grid Layer';
        gridMapLayer.layerType = 'Grid';
        break;
      }
      default:
        throw Error('Not a Supported Grid Layer Type');
    }

    return gridMapLayer;
  }

  // eslint-disable-next-line class-methods-use-this
  gisCreateTiledLayer(url: string): Promise<MapLayer<Layer>> | null {
    return null;
  }

  // eslint-disable-next-line class-methods-use-this
  protected gisCreateWMSLayer(url: string, layers: string[]): Promise<MapLayer<Layer>> {
    const capabilities = WMSCapabilities.fromURL(url);
    return capabilities.then(caps => {
      const model = WMSTileSetModel.createFromCapabilities(caps, layers.map(layer => ({ layer })));
      const wmsLayer = new WMSTileSetLayer(model, { layerType: LuciadLayerType.BASE });
      // wmsLayer.model.layers = ['rivers'];
      const lcdLayer = new LuciadMapLayer(wmsLayer, wmsLayer.id);
      return lcdLayer;
    });
  }

  // eslint-disable-next-line class-methods-use-this
  async gisCreateWMTSLayer(url: string, layers: string[]): Promise<MapLayer<Layer>> {
    const capabilities = WMTSCapabilities.fromURL(url);
    const caps = await capabilities;
    caps.layers.forEach(capsLayer => {
      console.log(capsLayer.title);
    });
    const wmtsLayer = WMTSDataLoader.createLayerFromCapabilities(
      caps,
      caps.layers[0],
      caps.layers[0].styles[0]
    );
    // wmsLayer.model.layers = ['rivers'];
    const lcdLayer = new LuciadMapLayer(wmtsLayer, wmtsLayer.id, wmtsLayer.label);
    return lcdLayer;
  }

  private static getControlFtPainter(): LuciadShapePainter {
    const bfp = new LuciadShapePainter();
    const normal = { selected: false, level: 10 };
    const selected = { selected: true, level: 10 };
    bfp.setStyle(ShapeType.POLYLINE, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.BOUNDS, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.SHAPE_LIST, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.POLYGON, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.COMPLEX_POLYGON, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.ARC, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.ARC_BAND, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.CIRCULAR_ARC, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.SECTOR, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.CIRCLE, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.ELLIPSE, normal, normalDefaultStyle);
    bfp.setStyle(ShapeType.GEO_BUFFER, normal, normalDefaultStyle);

    bfp.setStyle(ShapeType.POLYLINE, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.BOUNDS, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.SHAPE_LIST, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.POLYGON, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.COMPLEX_POLYGON, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.ARC, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.ARC_BAND, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.CIRCULAR_ARC, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.SECTOR, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.CIRCLE, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.ELLIPSE, selected, selectedDefaultStyle);
    bfp.setStyle(ShapeType.GEO_BUFFER, selected, selectedDefaultStyle);

    bfp.setStyle(ShapeType.POINT, normal, {
      image: IconFactory.rectangle({
        width: 15,
        height: 15,
        fill: (normalDefaultStyle.fill as FillStyle).color,
        stroke: (normalDefaultStyle.stroke as LineStyle).color
      })
    });
    bfp.setStyle(ShapeType.POINT, selected, {
      image: IconFactory.rectangle({
        width: 15,
        height: 15,
        fill: (selectedDefaultStyle.fill as FillStyle).color,
        stroke: (selectedDefaultStyle.stroke as LineStyle).color
      })
    });

    return bfp;
  }

  createShapeDrawingLayer(layerName: string, layerId?: string): MapLayer<Layer> {
    const shapeDrawingMapLayer = new LuciadMapLayer();
    const cfp = LuciadLayerFactory.getControlFtPainter();
    const drawingMemoryStore = new MemoryStore();
    const featureModel = new FeatureModel(
      drawingMemoryStore,
      { reference: this.mapDisplay.getReference() }
    );
    const shapeDrawingLayer = new FeatureLayer(
      featureModel,
      {
        selectable: true,
        editable: true,
        visible: true,
        label: layerName,
        layerType: LuciadLayerType.DYNAMIC,
        id: layerId,
        painter: cfp
      }
    );
    shapeDrawingMapLayer.setGISLayer(shapeDrawingLayer);
    shapeDrawingMapLayer.layerName = layerName;
    shapeDrawingMapLayer.layerId = layerId;
    shapeDrawingMapLayer.layerType = 'Feature';
    return shapeDrawingMapLayer;
  }


  /**
   * Creates the military symbol layer for displaying mil symbology. Spatial querying is enabled right now, the reference
   * must be of type Equidistant for it to allow us to do this. This also sets up the configuration for the mil symbol painter
   * used to show how mil symbols are displayed on the cursit. Clustering of shapes at certain levels is also enabled, the params
   * can be changed as necessary here.
   * @param name the name for the layer
   * @param layerId the ID for the layer
   */

  gisCreateMilitarySymbolLayer(name: string, layerId: string): MapLayer<Layer> {

    //enable spatial queries by setting spatialIndex to true
    const ms = new MemoryStore({
      spatialIndex: true,
      reference: this.mapDisplay.getReference()
    });


    const fm = new FeatureModel(ms, { reference: this.mapDisplay.getReference() });


    const { map } = this.mapDisplay;
    const { mapDisplay } = this;
    const msp = new MilitarySymbologyPainter(MIL_STD_2525b, {
      codeFunction: (feature: Feature) => (feature.properties['code'] ? feature.properties['code'] : ''),
      // codeFunction: (feature: Feature) => (isCluster(feature) ? `S${(clusteredFeatures(feature)[0].properties as any).code.charAt(1)}GP------*****` : (feature.properties as any).code),
      // eslint-disable-next-line max-len
      // modifiers: (feature: Feature) => ({ uniqueDesignation: isCluster(feature) ? clusteredFeatures(feature).length : 'deneme' }),
      modifiers: this.getModifiers,
      style(feature, paintState): MilSymStyle {
        // We return our style. Note that it is also possible to have feature-specific
        // styling if you choose to use the feature parameter.
        // const isEditing = map?.controller instanceof ChainedEditController;
        // const isSelecting = map?.controller instanceof BulkSelectController;
        // console.log(`editing: ${isEditing}`);
        // console.log(`isSelecting: ${isSelecting}`);\
        let directionArrowColor;
        let Friend;
        let Hostile;
        const symbolCode = feature.properties['code'];
        if (symbolCode) {
          directionArrowColor = '#000000';
          const affiliationCode = feature.properties['code'].charAt(1);
          switch (affiliationCode) {
            case 'F':
              directionArrowColor = '#80E0FF';
              break;
            case 'U':
              directionArrowColor = '#FFFF00';
              break;
            case 'H':
              directionArrowColor = '#FF8080';
              break;
            default:
              directionArrowColor = '#000000';
          }
          const { shape } = feature;
          Friend = (shape instanceof Point && feature.properties['code'].charAt(0) === 'G')
          || !(shape instanceof Point) ? '#0000FF' : '#80E0FF';
          Hostile = (shape instanceof Point && feature.properties['code'].charAt(0) === 'G')
          || !(shape instanceof Point) ? 'rgb(255, 0, 0)' : '#FF8080';
        }
        return {
          color: directionArrowColor,
          selectionColor: '#ffd500',
          affiliationColor: {
            Friend,
            'Assumed Friend': Friend,
            'Exercise Assumed Friend': Friend,
            Hostile
          },
          lineWidth: 1,
          iconSize: mapDisplay.iconSize,
          skeleton: paintState.selected,
          rectangle: paintState.selected
        };
      }
    });

    const luciadCompositePainter = new LuciadCompositePainter(
      msp,
      new LuciadMilitarySymbologyClassifier(MIL_STD_2525b)
    );
    // msp.paintLabel = (labelCanvas, feature, shape): void => {
    //   // map.on('MapChange', () => {
    //   //   console.log(`Camera Z: ${map.mapScale}`);
    //   // });
    //   const lbl = feature.properties['label'];
    //   labelCanvas.drawLabel(
    //     `<div style="color: black; font-weight: bold;">${lbl}</div>`,
    //     shape,
    //     { positions: PointLabelPosition.NORTH_EAST, offset: 30 }
    //   );
    // };

    const fl = new FeatureLayer(fm, {
      layerType: LuciadLayerType.DYNAMIC,
      painter: luciadCompositePainter,
      label: name,
      ...(layerId !== undefined ? { id: layerId } : {}),
      transformer: create({
        defaultParameters: {
          clusterSize: 350,
          minimumPoints: 3
        }
      })

      // transformer: create({
      //   defaultParameters: {
      //   defaultParameters: {
      //     clusterSize: 200,
      //     minimumPoints: 4,
      //     clusterShapeProvider: new FooShapeProvider()
      //   }
      // })
    });


    const luciadMapLayer: LuciadMapLayer = new LuciadMapLayer(fl, layerId, fl.label);
    luciadMapLayer.layerName = fl.label;
    this.layersHash[luciadMapLayer.layerId] = luciadMapLayer;


    return luciadMapLayer;
  }

  getModifiers(f: Feature): object {
    return (f.properties as FeatureProperties).modifiers;
  }

  createFeatureLayer(
    filePath: string,
    layerType: LayerType
  ): LuciadMapLayer | Promise<LuciadMapLayer> {
    const featurePainter = new BasicFeaturePainter();
    const state = { selected: false, level: 10 };
    featurePainter.setStyle(ShapeType.POLYGON, state, worldShapeStyle);
    featurePainter.setStyle(ShapeType.COMPLEX_POLYGON, state, worldShapeStyle);
    const featureLayer = new FeatureLayer(
      new FeatureModel(
        new UrlStore({
          target: filePath,
          credentials: false
        })
        // { reference: coordinateReference }
      ),
      {
        selectable: false,
        editable: false,
        visible: true,
        label: 'World',
        layerType: LuciadLayerType.BASE,
        painter: featurePainter
      }
    );

    const featureMapLayer = new LuciadMapLayer(
      featureLayer,
      featureLayer.id,
      featureLayer.label
    );
    featureMapLayer.layerType = layerType;

    return featureMapLayer;
  }
}
