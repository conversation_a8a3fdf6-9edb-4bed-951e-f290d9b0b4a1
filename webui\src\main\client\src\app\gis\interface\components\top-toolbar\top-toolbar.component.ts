import { Component, inject, signal, WritableSignal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatSliderModule } from '@angular/material/slider';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { cursitActions } from 'src/app/cursit/actions/cursit.actions';
import { environment } from '../../../../../environments/environment';
import { GisService } from '../../service/gis.service';
import { DataService } from 'src/app/data/data.service';
import {
  defaultExpandCollapse,
  twoStateAnimation
} from 'src/app/shared/util/animation';
import { defaultRotate } from 'src/app/shared/util/animation';
import { SettingsSliderComponent } from '../settings-slider/settings-slider.component';
import { EntityMenuComponent } from '../entity-menu/entity-menu.component';
import { DrawingMenuComponent } from '../drawing-menu/drawing-menu.component';
import { MovementMenuComponent } from '../movement-menu/movement-menu.component';

export const toolbarExpanded: WritableSignal<boolean> = signal(true);
@Component({
  selector: 'nexus-top-toolbar',
  imports: [
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatMenuModule,
    MatSliderModule,
    MatDividerModule,
    CommonModule,
    FormsModule,
    SettingsSliderComponent,
    EntityMenuComponent,
    DrawingMenuComponent,
    MovementMenuComponent
  ],
  templateUrl: './top-toolbar.component.html',
  styleUrl: './top-toolbar.component.css',
  animations: [
    defaultExpandCollapse,
    defaultRotate('rotate', 0, -180),
    twoStateAnimation(
      'custom',
      {
        transform: 'scaleY(1)'
      },
      {
        transform: 'scaleY(0)'
      }
    )
  ]
})
export class TopToolbarComponent {
  gisService = inject(GisService);
  data = inject(DataService);
  bs = inject(MatBottomSheet);
  filterActive: boolean = false;
  mapOpacitySliderValue = 1.0;
  elementSizeValue = 50;

  readonly env = environment;
  readonly toolbarExpanded = toolbarExpanded;

  filterAction(): void {
    this.gisService.store.dispatch(cursitActions.openFilterDialog());
  }

  editMapAction(): void {}

  mapServersAction(): void {}

  resetMapOpacity(): void {
    this.mapOpacitySliderValue = 1.0;
    this.gisService.setBackgroundMapOpacity(1.0);
  }

  showHideLayerList(): void {
    this.gisService.mapDisplay.toggleLayerManagerVisibility();
  }

  centerSelected(): void {
    this.gisService.mapDisplay.centerMapOnSelectedElement();
  }

  elementActions(): void {}

  showDetails(): void {}

  toggleExpand(): void {
    toolbarExpanded.update((v) => !v);
  }
}
