/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/6/2024, 11:16 AM
 */
import { HierarchicalSymbology } from '@luciad/ria-milsym/symbology/HierarchicalSymbology';
import { MilitarySymbol } from '@luciad/ria-milsym/symbology/military/MilitarySymbol';
import { Feature } from '@luciad/ria/model/feature/Feature';
import { Classifier } from '@luciad/ria/view/feature/transformation/Classifier';

export class LuciadMilitarySymbologyClassifier extends Classifier {
  private readonly _symbology: HierarchicalSymbology;

  constructor(symbology: HierarchicalSymbology) {
    super();
    this._symbology = symbology;
  }

  override getClassification(feature: Feature): string {
    const symbol = new MilitarySymbol(this._symbology, feature.properties['code']);
    const latestSymbologies: boolean = this._symbology.name === 'APP_6C'
      || this._symbology.name === 'APP_6D'
      || this._symbology.name === 'MIL_STD_2525d';
    const category = latestSymbologies ? symbol.code.substring(4, 6) : symbol.code.charAt(2);
    const { affiliation } = (symbol as any);

    // Sea Surface, Sea Subsurface and MineWarfare units are not clustered
    if (latestSymbologies) {
      if (category === '30' || category === '35' || category === '36') {
        return 'SeaUnit';
      }
    } else if (category === 'U' || category === 'S') {
      return 'SeaUnit';
    }

    return `${category}_${affiliation}`;
  }
}
