import { createEverything } from '@simfront/common-libs';
import { environment } from '../../../environments/environment';
import { RootState } from '../index';

export interface DataPath {
  id: string;
  name: string;
  pathStart: DataPathPart;
  pathEnd: DataPathPart;
  isTransferAllDataRoute: boolean;
}

export interface DataPathPart {
  node: string;
  endpoint: string;
}

export interface DataPathRoute {
  name: string;
  isTransferAllDataRoute: boolean;
  input: string;
  output: string;
}

export const isValidDataPath = (data: DataPath): boolean =>
  !!data.name && !!data.pathStart && !!data?.pathStart?.node && !!data?.pathStart?.endpoint
  && !!data.pathEnd && !!data?.pathEnd?.node && !!data?.pathEnd?.endpoint;

export const getDataPathRoutes = (dataPaths: DataPath[]): DataPathRoute[] => dataPaths
  .filter(isValidDataPath)
  .map(getDataPathRoute);

export const getDataPathRoute = (d: DataPath): DataPathRoute => ({
  name: d.name,
  input: getDataPathPartRoute(d.pathStart),
  output: getDataPathPartRoute(d.pathEnd),
  isTransferAllDataRoute: d.isTransferAllDataRoute
});

export const getDataPathPartRoute = (d: DataPathPart): string => `${d.node}:${d.endpoint}`;

export const {
  reducer: dataPathReducer,
  facade: DataPathFacadeBase,
  initialState
} = createEverything<RootState, DataPath, 'dataPath', 'name', string>('dataPath', d => d.name, `${environment.origin}/datapaths`, state => state.dataPath ?? initialState);
