import * as L from 'leaflet';
import { LatLng, LatLngExpression, LeafletEventHandlerFn, Point } from 'leaflet';
import { Container, Renderer } from 'pixi.js';

// declare function projectionZoom(map: L.Map): number;

interface PixiOverlayOptions {
  padding?: number;
  forceCanvas?: boolean;
  doubleBuffering?: boolean;
  resolution?: number;

  projectionZoom?(map: L.Map): number;

  destroyInteractionManager?: boolean;
  autoPreventDefault?: boolean;
  preserveDrawingBuffer?: boolean;
  clearBeforeRender?: boolean;

  shouldRedrawOnMove?(): boolean;

  pane?: 'markerPane' | 'overlayPane' | 'shadowPane' | 'tilePane' | 'mapPane' | 'tooltipPane' | 'popupPane';
  zIndex?: number;
}

declare class LeafletPixiContainer extends L.Layer {
  constructor(options?: PixiOverlayOptions);

  prevZoom: number;

  isRedrawing(): boolean;

  set redrawing(value: boolean);

  get redrawing(): boolean;

  redraw(data?: any): Promise<void>;

  destroy(): void;

  bringToFront(): this;

  getSymbolsArray(): Container[];

  getFirstRenderingArray(): Container[];

  addGraphics(graphics: Container): void;

  removeGraphicsAt(index: number): void;

  removeGraphics(graphics: Container): void;

  bringToBack(): this;

  setOptions(options: PixiOverlayOptions): void;

  getOptions(): PixiOverlayOptions;

  project(latLng: LatLngExpression, zoom?: number): Point;

  unProject(point: Point, zoom?: number): LatLng;

  getContainer(): Container;

  getRenderer(): Renderer;

  getEvents(): { [p: string]: LeafletEventHandlerFn };

  redrawAll(): void;

  getZoom(): number;

  getScale(zoom: number): number;

  setMarkerScale(scale: number): void;
}
