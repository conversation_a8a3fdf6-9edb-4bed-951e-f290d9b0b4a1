.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
  border-radius: 0;
  min-width: 0;
  white-space: nowrap;
  transition: font-size 0.3s ease;
}

.menu-button:hover {
  background-color: var(--calian-gradient);
}

.menu-button .mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  margin-left: 4px;
  flex-shrink: 0;
}

@media (max-width: 1366px) {
  .menu-button {
    font-size: 0;
  }

  .menu-button .mat-icon {
    margin: 0;
  }
}
