/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/8/2024, 8:17 AM
 */
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { Layer } from '@luciad/ria/view/Layer';
import { LayerGroup } from '@luciad/ria/view/LayerGroup';
import { LayerTreeNode } from '@luciad/ria/view/LayerTreeNode';
import { LayerTreeVisitor } from '@luciad/ria/view/LayerTreeVisitor';
import { RasterTileSetLayer } from '@luciad/ria/view/tileset/RasterTileSetLayer';
import { MapLayer } from '../interface/MapLayer';
import { AbstractMapLayerGroup, MapLayerGroup } from '../interface/MapLayerGroup';
import { GridType } from '../../data/layer-manager/layer-manager.model';
import { LuciadMapDisplay } from './LuciadMapDisplay';
import VisitOrder = LayerTreeNode.VisitOrder;
import ReturnValue = LayerTreeVisitor.ReturnValue;

class LayersVisitor implements LayerTreeVisitor {
  visibility: boolean;
  gridType: GridType;

  constructor(visibility: boolean, gridType: GridType) {
    this.visibility = visibility;
    this.gridType = gridType;
  }

  visitLayer(layer: Layer): LayerTreeVisitor.ReturnValue {
    if (layer.label.includes('Grid') && !layer.label.includes(this.gridType)) {
      layer.visible = false;
    } else {
      layer.visible = this.visibility;
    }

    return ReturnValue.CONTINUE;
  }

  visitLayerGroup(layerGroup: LayerGroup): LayerTreeVisitor.ReturnValue {
    layerGroup.visible = this.visibility;
    return ReturnValue.CONTINUE;
  }
}

export class LuciadLayerGroup extends AbstractMapLayerGroup<Layer, LayerGroup> {
  constructor(layerGroup: LayerGroup, mapDisplay: LuciadMapDisplay) {
    super(mapDisplay);
    this.layerGroup = layerGroup;
  }

  addLayer(mapLayer: MapLayer<Layer> | MapLayerGroup<Layer, LayerGroup>): void {
    this.childLayers[mapLayer.layerId] = mapLayer;
    const layer = mapLayer.getGISLayer ? mapLayer.getGISLayer() : (mapLayer as LuciadLayerGroup).getGISGroupLayer();
    if (layer instanceof RasterTileSetLayer) {
      this.layerGroup.addChild(layer, 'bottom');
    } else if (layer instanceof FeatureLayer || layer instanceof Layer) {
      this.layerGroup.addChild(layer);
    }
  }

  removeLayer(mapLayer?: MapLayer<Layer>, mapLayerId?: string, mapLayerName?: string): void {
    if (mapLayer) {
      delete this.childLayers[mapLayer.layerId];
      this.layerGroup.removeChild(mapLayer.getGISLayer());
    } else if (mapLayerId) {
      delete this.childLayers[mapLayerId];
      const indexOfLayer = this.layerGroup.children.indexOf(mapLayer.getGISLayer());
      this.layerGroup.children.splice(indexOfLayer, 1);
    } else if (mapLayerName) {

    }
  }

  getVisibility(): boolean {
    return this.layerGroup.visible;
  }

  setVisible(visible: boolean): void {
    this._visible = visible;
    this.layerGroup.visible = visible;
    const layersVisitor = new LayersVisitor(visible, this.mapDisplay.gridType);
    this.layerGroup.visitChildren(layersVisitor, VisitOrder.TOP_DOWN);
  }

  getLayer(layerName?: string, layerId?: string): MapLayer<Layer> {
    return null;
  }

  getLayerID(): string {
    return this.layerGroup.id;
  }

  setGISGroupLayer(layer: LayerGroup): void {
    this.layerGroup = layer;
  }

  getGISGroupLayer(): LayerGroup {
    return this.layerGroup;
  }
}
