export type Protocol = 'http' | 'https';

export interface Environment {
  production: boolean;
  host:       string;
  origin:     string;
  cursit:     boolean;
}

export const createEnvironment = (
  production: boolean,
  protocol: Protocol,
  hostname: string,
  port: number,
  cursit: boolean
): Environment => {
  const host = `${hostname}:${port}`;
  return {
    production,
    host,
    origin: `${protocol}://${host}`,
    cursit
  };
};
