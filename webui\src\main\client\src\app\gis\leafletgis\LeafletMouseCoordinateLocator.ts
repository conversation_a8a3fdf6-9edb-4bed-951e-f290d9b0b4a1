import { LatLng, Map } from 'leaflet';
import * as mgrs from 'mgrs';
import { mapActions } from '../interface/actions/map.actions';
import { MouseCoordinateLocator, MouseCoordinateType, MousePos } from '../interface/MouseCoordinateLocator';
import { latLonFormatter } from './CoordinateFormatter';

export class LeafletMouseCoordinateLocator extends MouseCoordinateLocator {
  protected override gisLocateMouseCoordinate(
    coordinateType: MouseCoordinateType,
    mousePos: MousePos
  ): void {
    const map: Map = <Map> this.mapDisplay.map;
    const latLon: LatLng = map.containerPointToLatLng([mousePos[0], mousePos[1]]);
    if (coordinateType === 'LatLon') {
      this.mapDisplay.store.dispatch(
        mapActions.mouseMove(latLonFormatter([latLon.lat, latLon.lng]))
      );
    } else if (coordinateType === 'MGRS') {
      this.mapDisplay.store.dispatch(mapActions.mouseMove(mgrs.forward([latLon.lng, latLon.lat])));
    }
  }
}
