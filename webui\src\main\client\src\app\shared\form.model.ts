import {
  AbstractControl,
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
  ValidationErrors
} from '@angular/forms';
import { WritableSignal } from '@angular/core';
import { DataParameter } from '../data/bs-params/bs-params.model';

export const updateFormGroup$ = <T>(
  fg$: WritableSignal<FormGroup>,
  collection: T[],
  getName: (i: T) => string,
  onGroup?: (fg: FormGroup, i: T) => void,
  remove = true
) => {
  const fg = remove ? new FormGroup({}) : fg$();
  collection.forEach((item) => {
    const name = getName(item);
    let group = fg$().get(name);
    if (!group) {
      group = new FormGroup({});
      fg.addControl(name, group);
    }
    if (group instanceof FormGroup && onGroup) {
      onGroup(group, item);
    }
  });
  fg$.set(fg);
};

export const updateFormGroup = <T>(
  fg: FormGroup,
  collection: T[],
  getName: (item: T) => string,
  onGroup?: (fg: FormGroup, item: T) => void
) => {
  const names = new Set<string>(collection.map(getName));
  collection.forEach((item) => {
    const name = getName(item);
    let group = fg.get(name);
    if (!group) {
      group = new FormGroup({});
      fg.addControl(name, group);
    }
    if (group instanceof FormGroup && onGroup) {
      onGroup(group, item);
    }
  });
  // Remove controls that no longer exist
  Object.keys(fg.controls).forEach((controlName) => {
    if (!names.has(controlName)) {
      fg.removeControl(controlName);
    }
  });
};

export const updateFormControl = <T>(
  fg: FormGroup,
  collection: T[],
  getName: (item: T) => string,
  getValue: (item: T) => string[] | string | boolean,
  onAdd?: (item: T) => void
) => {
  const names = new Set<string>(collection.map(getName));
  collection.forEach((item) => {
    const name = getName(item);
    const control = fg.get(name);
    if (control) {
      control.patchValue(getValue(item));
    } else {
      fg.addControl(name, new FormControl(getValue(item)));
    }
    if (onAdd) {
      onAdd(item);
    }
  });
  // Remove controls that no longer exist
  Object.keys(fg.controls).forEach((controlName) => {
    if (!names.has(controlName)) {
      fg.removeControl(controlName);
    }
  });
};

export const updateFormValidators = (
  fc: FormControl,
  validatorFns: ValidatorFn[]
): void => {
  if (!fc) {
    return;
  }
  const currentValidators = fc.validator ? [fc.validator] : [];
  fc.addValidators([
    ...currentValidators,
    ...validatorFns.filter((fn) => !formHasValidator(fc, fn))
  ]);
  fc.updateValueAndValidity();
};

export const formHasValidator = (
  fc: FormControl,
  validatorFn: ValidatorFn
): boolean => {
  if (!fc?.validator) {
    return false;
  }
  const validators = fc.validator({} as AbstractControl);
  return (
    validators &&
    Object.keys(validators).some((key) => validatorFn === Validators[key])
  );
};

/**
 * Validates that at least one parameter in a form group is enabled/editable
 */
const hasEnabledParameter = (control: AbstractControl): boolean => {
  if (control instanceof FormGroup) {
    return Object.values(control.controls).some((control) =>
      hasEnabledParameter(control)
    );
  }
  return control.enabled;
};

/**
 * Validator function that checks if at least one field in a form group is enabled/editable
 * Use this for form groups to validate that at least one field is enabled/editable
 */
export const ParameterEnabledValidator = (): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    return hasEnabledParameter(control) ? null : { noEnabledParameters: true };
  };
};
