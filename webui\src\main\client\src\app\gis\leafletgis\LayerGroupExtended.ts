/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/12/2024, 7:12 AM
 */
import { LayerGroup } from 'leaflet';

export class LayerGroupExtended extends LayerGroup {
  private parentGroup: LayerGroupExtended;

  set parent(parent: LayerGroupExtended) {
    this.parentGroup = parent;
  }

  get parent(): LayerGroupExtended {
    return this.parentGroup;
  }

  setVisible(visible: boolean): void {
    if (visible) {
      this.parentGroup.removeLayer(this);
    } else {
      this.parentGroup.addLayer(this);
    }
  }
}
