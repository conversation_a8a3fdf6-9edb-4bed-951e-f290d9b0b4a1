// things here will be periodically moved to common-libs-web
import {
  asyncScheduler,
  Observable,
  OperatorFunction,
  Subscription
} from 'rxjs';

export const processArrayItemsIdleCallback = <T>(
  arr: T[],
  process: (item: T) => void
): void => {
  let index = 0;
  const processItem = (deadline: IdleDeadline) => {
    while (deadline.timeRemaining() > 0 && index < arr.length) {
      const item = arr[index];
      process(item);
      index += 1;
    }

    if (index < arr.length) {
      requestIdleCallback(processItem);
    }
  };
  requestIdleCallback(processItem);
};

export const processLargeArrayByChunk = <T>(
  arr: T[],
  chunkSize: number,
  process: (i: T) => void
): void => {
  let index: number = 0;
  const doChunk = () => {
    let count: number = chunkSize;
    while (count > 0 && index < arr.length) {
      count -= 1;
      const item = arr[index];
      process(item);
      index += 1;
    }
    if (index < arr.length) {
      setTimeout(doChunk, 0);
    }
  };
  doChunk();
};

export const processLargeArrayByTime = <T>(
  arr: T[],
  msTime: number,
  process: (i: T) => void
): void => {
  let index: number = 0;
  const doChunk = () => {
    const startTime = Date.now();
    while (index < arr.length && Date.now() - startTime < msTime) {
      let item = arr[index];
      process(item);
      index += 1;
    }
    if (index < arr.length) {
      setTimeout(doChunk, 0);
    }
  };
  doChunk();
};

export const bufferTimeById =
  <T, K>(
    bufferTimeSpan: number,
    selectId: (t: T) => K
  ): OperatorFunction<T, T[]> =>
  (source: Observable<T>) =>
    new Observable<T[]>((subscriber) => {
      let bufferMap = new Map<K, T>();
      let bufferTimer: Subscription | null = null;

      const emitBuffer = () => {
        if (bufferMap.size > 0) {
          subscriber.next([...bufferMap.values()]);
          bufferMap = new Map<K, T>();
        }
        bufferTimer?.unsubscribe();
        bufferTimer = null;
      };

      const addToBuffer = (value: T) => {
        bufferMap.set(selectId(value), value);
        if (!bufferTimer) {
          bufferTimer = asyncScheduler.schedule(() => {
            emitBuffer();
          }, bufferTimeSpan);
        }
      };

      const subscription = source.subscribe({
        next(value: T) {
          addToBuffer(value);
        },
        error(err) {
          subscriber.error(err);
        },
        complete() {
          emitBuffer();
          subscriber.complete();
        }
      });

      return () => {
        subscription.unsubscribe();
        bufferTimer?.unsubscribe();
      };
    });

export const removeKey = <T extends object, K extends keyof T>(
  obj: T,
  key: K
): Omit<T, K> => {
  const { [key]: _, ...rest } = obj;
  return rest as Omit<T, K>;
};

export const nullishOrEmpty = (val: any): boolean => {
  if (val === null || val === undefined || val === '') {
    return true;
  }
  if (Array.isArray(val) && val.length === 0) {
    return true;
  }
  return typeof val === 'boolean'
    ? true
    : typeof val === 'object' && Object.keys(val).length === 0;
};

/**
 * Generic function to validate an object against a set of expected properties and their types
 * @param obj The object to validate
 * @param schema Object describing the expected properties and their types
 * @returns Boolean indicating if the object matches the schema
 */
export const validateObject = (
  obj: any,
  schema: Record<string, string | string[] | Record<string, any>>
): boolean => {
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  // Check if all required properties exist and are of the right type
  for (const [key, expectedType] of Object.entries(schema)) {
    // Check if property exists
    if (!(key in obj)) {
      return false;
    }

    // Handle different types of validation
    if (typeof expectedType === 'string') {
      // Simple type check
      if (typeof obj[key] !== expectedType) {
        return false;
      }
    } else if (Array.isArray(expectedType)) {
      // Enum check
      if (!expectedType.includes(obj[key])) {
        return false;
      }
    } else if (typeof expectedType === 'object') {
      // Nested object check
      if (!validateObject(obj[key], expectedType)) {
        return false;
      }
    }
  }

  return true;
};
