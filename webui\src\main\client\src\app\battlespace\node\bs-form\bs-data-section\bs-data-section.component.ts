import {
  Component,
  inject,
  Input,
  signal,
  WritableSignal
} from '@angular/core';
import {
  DataParameter,
  DataSection,
  PAGE_FORM,
  PARENT_FORM,
  SECTION_FORM
} from '../../../../data/bs-params/bs-params.model';
import { BsDataParamsComponent } from '../bs-data-params/bs-data-params.component';
import { FormGroup } from '@angular/forms';
import { BsDataParamBase } from '../bs-data-param/bs-data-param.base';

@Component({
  selector: 'vcci-bs-data-section',
  templateUrl: 'bs-data-section.component.html',
  styleUrl: 'bs-data-section.component.css',
  imports: [BsDataParamsComponent],
  providers: [
    {
      provide: SECTION_FORM,
      useFactory: () => signal(new FormGroup({}))
    }
  ]
})
export class BsDataSectionComponent extends BsDataParamBase {
  parentFormGroup$: WritableSignal<FormGroup> = inject(PARENT_FORM, {
    skipSelf: true
  });
  pageFormGroup$?: WritableSignal<FormGroup> = inject(PAGE_FORM, {
    optional: true,
    skipSelf: true
  });
  sectionFormGroup$: WritableSignal<FormGroup> = inject(SECTION_FORM, {
    self: true
  });

  @Input({ required: true }) set dataSection(dataSection: DataSection) {
    this.sectionName = dataSection.sectionName;
    this.parameters = Object.values(dataSection.parameters);
    const sectionFormGroup =
      this.pageFormGroup$()?.get('sections')?.get(this.sectionName) ||
      this.parentFormGroup$()?.get('sections')?.get(this.sectionName);
    if (sectionFormGroup instanceof FormGroup) {
      this.sectionFormGroup$.set(sectionFormGroup);
    }
  }

  sectionName: string = '';
  parameters: DataParameter[] = [];
}
