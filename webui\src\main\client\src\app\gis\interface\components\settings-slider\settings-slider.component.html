<div class="slider-wrapper" mat-menu-item disableRipple>
  <div class="slider-background-wrapper" matTooltip="Set Map Opacity">
    <mat-icon
      class="slider-background-world"
      svgIcon="map-opacity-tr-tb"
    ></mat-icon>
    <mat-icon
      class="slider-background-scale"
      svgIcon="map-opacity-scale-tr-tb"
    ></mat-icon>
    <mat-slider class="map-opacity-slider" max="1" min="0" step="0.1">
      <input
        (click)="$event.stopPropagation()"
        (valueChange)="setBackgroundMapOpacity($event)"
        [value]="mapOpacitySliderValue"
        matSliderThumb
      />
    </mat-slider>
  </div>
</div>

<div class="slider-wrapper" mat-menu-item disableRipple>
  <div class="slider-background-wrapper" matTooltip="Resize Icons">
    <mat-icon
      class="element-resize-scale"
      svgIcon="element-resize-tr-tb"
    ></mat-icon>
    <mat-slider class="map-opacity-slider" max="100" min="0" step="10">
      <input
        (click)="$event.stopPropagation()"
        (valueChange)="setElementSize($event)"
        [value]="elementSizeValue"
        matSliderThumb
      />
    </mat-slider>
  </div>

  <button
    mat-button
    [ngClass]="showLabels ? 'active' : 'inActive'"
    (click)="toggleLabels($event)"
  >
    {{ showLabels ? 'Hide Labels' : 'Show Labels' }}
  </button>
</div>
