import {
  AfterViewInit,
  Component,
  ElementRef,
  Inject,
  Input,
  NgZone,
  OnDestroy
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { DataService } from '../../data/data.service';
import { Settings } from '../../data/settings/settings.model';
import { MapElementSelectionInfo } from '../../gis/interface/models/gis.model';
import { ScaleIndicator } from '../../gis/interface/models/scale-indicator.model';
import { selectMouseCoordinate } from '../../gis/interface/reducers/coordinate.reducer';
import { selectMapElementInformation } from '../../gis/interface/reducers/element-selection.reducer';
import { selectScaleIndicator } from '../../gis/interface/reducers/scale-indicator.reducer';
import {
  GIS_ENGINE,
  GISEngine,
  GisService
} from '../../gis/interface/service/gis.service';
import { LeafletTacticalGraphicsService } from '../../gis/leafletgis/service/LeafletTacticalGraphicsService';
import { cursitActions } from '../actions/cursit.actions';
import { selectLayerManagerVisibility } from '../reducers/cursit.reducer';
import { TopToolbarComponent } from '../../gis/interface/components/top-toolbar/top-toolbar.component';

@Component({
  selector: 'vcci-cursit',
  templateUrl: './cursit.component.html',
  styleUrls: ['./cursit.component.scss'],
  standalone: false
})
export class CursitComponent implements AfterViewInit, OnDestroy {
  private _settings: Settings;
  private _observer: ResizeObserver;
  private resizeSubscribe: Subscription;
  @Input() set settings(settings: Settings) {
    this._settings = settings;
    this.gisService.updateSettings(this._settings);
  }
  @Input() mapRef: HTMLElement;

  get settings(): Settings {
    return this._settings;
  }

  isActive = true;
  scaleIndicator$: Observable<ScaleIndicator> =
    this.gisService.store.select(selectScaleIndicator);

  // mousePosition: string;
  mousePosition$: Observable<string> = this.gisService.store.select(
    selectMouseCoordinate
  );

  layerManagerVisibility$: Observable<boolean> = this.gisService.store.select(
    selectLayerManagerVisibility
  );

  selectedItemScreenCoordinates$: Observable<MapElementSelectionInfo> =
    this.gisService.store.select(selectMapElementInformation);

  constructor(
    private tacticalGraphicsService: LeafletTacticalGraphicsService,
    private gisService: GisService,
    protected ds: DataService,
    private ngZone: NgZone,
    private store: Store,
    @Inject(GIS_ENGINE) private gisEngine: GISEngine
  ) {}
  // ngOnInit(): void {
  //   this.layerData$ = combineLatest([
  //     this.layerList$,
  //     this.gridLayerList$,
  //     this.shapeLayerList$,
  //     this.layerManagerVisibility$
  //   ]).pipe(
  //     map(([layerList, gridLayerList, shapeLayerList, layerManagerVisibility]) => ({
  //       layerList,
  //       gridLayerList,
  //       shapeLayerList,
  //       layerManagerVisibility
  //     }))
  //   );
  // }

  ngAfterViewInit(): void {
    this.gisService.initMap().then(() => {
      this.gisService.addElements(this.settings).then();
      const map = this.mapRef;
      this._observer = new ResizeObserver(() => {
        this.store.dispatch(cursitActions.triggerMapResize());
      });
      this._observer.observe(map);
    });
  }
  // getRandomLatLon(): [number, number] {
  //   const minLat = -90;
  //   const maxLat = 90;
  //   const minLon = -180;
  //   const maxLon = 180;
  //   const lat = Math.random() * (maxLat - minLat) + minLat;
  //   const lon = Math.random() * (maxLon - minLon) + minLon;
  //   return [lon, lat];
  // }
  //
  //
  //
  // getRandomFeatures(count: number): Feature[] {
  //   const features = [];
  //   for (let i = 0; i < count; i++) {
  //     features.push(new Feature(createPoint(this.reference, this.getRandomLatLon()), {
  //       code: 'SFGPUCMS--*****',
  //     }));
  //   }
  //   return features;
  // }

  // static getRandomCode(): string {
  //   const infantry = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCI---*****`;
  //   const engineer = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCE---*****`;
  //   const medic = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSM---*****`;
  //   const maintenance = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSX---*****`;
  //   const nuclear = `G${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}MPNZ----****X`;
  //   return [infantry, engineer, medic, maintenance, nuclear][
  //     Math.floor(Math.random() * 5)
  //   ];
  // }

  // async addElementsToLayerAsync(milSymLayer: Layer): Promise<void> {
  //   const coordinates1 = semiRandomCoordinates(100, [0, 0], 1000000);
  //   const coordinates2 = semiRandomCoordinates(100, [0, 0], 1000000);
  //
  //   await this.processCoordinatesAsync(coordinates1, milSymLayer);
  //   await this.processCoordinatesAsync(coordinates2, milSymLayer);
  // }

  // async processCoordinatesAsync(coordinates: Coordinate[], layer: Layer): Promise<void> {
  //   let countId = 0;
  //
  //   // eslint-disable-next-line no-restricted-syntax
  //   for (const coordinate of coordinates) {
  //     const element = this.gisService.elementFactory.createMilitaryElement(
  //       CursitComponent.getRandomCode(),
  //       countId.toString(),
  //       countId.toString(),
  //       MilitaryElementType.SYMBOL,
  //       coordinate,
  //       this.gisService.reference
  //     );
  //
  //     layer.addElement(element);
  //     // TODO: - Nebi - Just to try
  //     // element.updateLocation([0, 0]);
  //     countId += 1;
  //
  //     // Add a small delay between iterations to prevent blocking the UI thread
  //     // await new Promise<void>((resolve) => setTimeout(resolve, 100));
  //
  //     // Redraw the layer every 100 milliseconds
  //     // if (countId % 1000 === 0) {
  //     //   lyr.redraw();
  //     //   // console.log(countId);
  //
  //     // }
  //
  //     // Redraw the layer after processing all coordinates
  //     // await layer.redraw();
  //     const someCoordinates = [[42, -81], [40, -82]];
  //     const gisElement = this.gisService.elementFactory.createMilitaryElement(
  //       'GHMPOADC--****X',
  //       '1023498234',
  //       'deneme',
  //       MilitaryElementType.TACTICAL_LINE,
  //       someCoordinates,
  //       this.gisService.reference
  //     );
  //
  //     layer.addElement(gisElement);
  //   }
  // }

  // async addEntitiesToCursit(entities: Entity[]): Promise<void> {
  //   return new Promise<void>((resolve, reject) => {
  //     console.log(entities.length);
  //     entities.forEach(entity => {
  //       const id = extractId(entity);
  //       // console.log(`Here is the ID: ${id}`);
  //       const coordinate = extractCoordinates(entity);
  //       let militarySymbologyLayer: Lyr;
  //       // loop through all battle space layers
  //       entity.battleSpaceLayers.forEach(layer => {
  //         // check if the layer already exists on the map
  //         const existingLayer = this.gisService.layerHandler.getLayer(layer.name);
  //         // if layer exist, the use the existing layer
  //         if (existingLayer) {
  //           militarySymbologyLayer = existingLayer;
  //           // if layer does not exist, then create a new military symbology layer
  //           // and add it to map using layer handler
  //         } else {
  //           militarySymbologyLayer = this.gisService.createMilitarySymbolLayer(layer.name);
  //           this.gisService.layerHandler.addLayer(militarySymbologyLayer);
  //         }
  //         // this military symbology layer is selectable, but not editable, because data is
  //         // received from a microservice.
  //         militarySymbologyLayer.setSelectable(true);
  //         militarySymbologyLayer.setEditable(false);
  //         // check if the map element is already on the layer
  //         const existingElement = militarySymbologyLayer.getElement(id);
  //         // if the map element is on the layer, just update its location
  //         if (existingElement) {
  //           existingElement.updateLocation(coordinate);
  //         } else {
  //           // if the entity does not exist in layer, create new one and add to layer
  //           const symbolCode = extractSymbolCode(entity);
  //           const cursitElement = this.gisService.createElement(
  //             symbolCode,
  //             id,
  //             entity.name,
  //             MilitaryElementType.SYMBOL,
  //             coordinate,
  //             this.gisService.reference
  //           );
  //           militarySymbologyLayer.addElement(cursitElement);
  //         }
  //       });
  //       if (militarySymbologyLayer) {
  //         militarySymbologyLayer.redraw();
  //       }
  //     });
  //   });
  // }

  onMouseMove(mouseEvent: MouseEvent): void {
    // this.mousePosition = this.gisService.getMapCoordinateFromMouseEvent(mouseEvent);
  }

  onRouteMove(mouseEvent: MouseEvent): void {
    // console.log(`Route move ${mouseEvent.x} ${mouseEvent.y}`);
  }
  ngOnDestroy() {
    this._observer?.disconnect();
    this.resizeSubscribe?.unsubscribe();
  }
}
