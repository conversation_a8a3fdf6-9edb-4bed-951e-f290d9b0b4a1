import { Pipe, PipeTransform } from '@angular/core';
import { Filter, hasFilterValue } from '../../data/endpoint/filter.model';

@Pipe({
  name: 'filterHasValue',
  pure: true,
  standalone: true
})
export class FilterHasValuePipe implements PipeTransform {
  onColor: 'primary' = 'primary';

  transform(filter: Filter): 'primary' | null {
    return this.getColor(hasFilterValue(filter));
  }

  getColor(isOn: boolean): 'primary' | null {
    return isOn ? this.onColor : null;
  }
}
