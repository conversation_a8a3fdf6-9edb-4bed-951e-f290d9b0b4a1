import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges
} from '@angular/core';
import { ConnectionPanelDisplay } from '../../core/reducers/layout.reducer';
import {
  canCreateInput,
  canCreateOutput,
  getConnectionId
} from '../../data/connection/connection.model';
import { ConnWithEndpoint, DataService } from '../../data/data.service';
import { Endpoint, getEndpointId } from '../../data/endpoint/endpoint.model';
import { NODE_ICONS, NodeSupport } from '../../data/node/node.model';
import {
  defaultExpandCollapse,
  defaultRotate
} from '../../shared/util/animation';

@Component({
  selector: 'vcci-connection-item[connection]',
  styleUrls: ['connection-item.component.css'],
  templateUrl: 'connection-item.component.html',
  animations: [defaultExpandCollapse, defaultRotate('rotate', 0, -180)],
  standalone: false
})
export class ConnectionItemComponent implements OnChanges {
  @Input({ required: true }) connection: ConnWithEndpoint;
  @Input() displayType: ConnectionPanelDisplay = 'ALL';
  @Input() support: NodeSupport | undefined;
  @Input('expanded')
  set masterExpanded(expanded: boolean) {
    this.expanded = this._selectedEndpoints?.length > 0 || expanded;
  }
  protected _selectedEndpoints: string[] = [];
  @Input()
  set selectedEndpoints(selectedEndpoints: string[]) {
    this._selectedEndpoints = selectedEndpoints;
    this.expanded = this._selectedEndpoints?.length > 0 || this.expanded;
  }
  get selectedEndpoints() {
    return this._selectedEndpoints;
  }
  endpoint: Endpoint;

  @Output() onExpanded = new EventEmitter<boolean>();

  protected readonly NODE_ICONS = NODE_ICONS;
  protected readonly getConnectionId = getConnectionId;
  endpointTrackBy = (_: number, i: Endpoint) => getEndpointId(i);

  expanded = true;
  showAddButton = false;
  canCreateOutput = false;
  canCreateInput = false;

  inbound = 0;
  outbound = 0;

  get toolTip(): string {
    return `
        Node: ${this.connection.node}
        Name: ${this.connection.name}
      `;
  }

  get endpointSummaryTooltip(): string {
    return `
      ${this.support?.externalInputSupported ? `DATA RECEIVER: ${this.inbound}` : ''}
      ${this.support?.externalOutputSupported ? `DATA SENDER: ${this.outbound}` : ''}
    `;
  }

  constructor(public data: DataService) {}

  createEndpoint(): void {
    if (this.support === undefined) {
      return;
    }
    this.data.connection.openCreateConnectionDialog({
      crud: 'Create',
      model: 'Endpoint',
      selectedNode: this.connection.node,
      direction:
        this.canCreateInput && this.connection.direction !== 'OUT'
          ? 'IN'
          : 'OUT',
      selectedConnection: this.connection.name
    });
  }

  editConnection(): void {
    this.data.connection.openCreateConnectionDialog({
      crud: 'Update',
      model: 'Connection',
      selectedNode: this.connection.node,
      direction: this.connection.direction,
      selectedConnection: this.connection.name,
      currentParameters: this.connection.parameters,
      invalid: false
    });
  }

  deleteConnection(): void {
    this.data.connection.openDeleteDialog(getConnectionId(this.connection));
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['connection']) {
      const previous: ConnWithEndpoint = changes['connection'].previousValue;
      const current: ConnWithEndpoint = changes['connection'].currentValue;
      if (
        changes['connection'].firstChange ||
        current.endpoints.length !== previous.endpoints.length
      ) {
        this.checkIfSupportsNewEndpoint();
      }
      this.inbound = this.connection.endpoints.filter(
        (e) => e.capability === 'DATA_RECEIVER'
      ).length;
      this.outbound = this.connection.endpoints.filter(
        (e) => e.capability === 'DATA_SENDER'
      ).length;
    } else if (changes['support']) {
      const previous: NodeSupport | undefined =
        changes['support'].previousValue;
      const current: NodeSupport | undefined = changes['support'].currentValue;
      if (JSON.stringify(previous) !== JSON.stringify(current)) {
        this.checkIfSupportsNewEndpoint();
      }
    }
  }

  checkIfSupportsNewEndpoint(): void {
    if (this.support === undefined) {
      this.showAddButton = false;
    } else {
      this.canCreateInput =
        this.connection.direction !== 'OUT' &&
        canCreateInput(this.support, this.connection.endpoints);
      this.canCreateOutput =
        this.connection.direction !== 'IN' &&
        canCreateOutput(this.support, this.connection.endpoints);
      this.showAddButton = this.canCreateInput || this.canCreateOutput;
    }
  }

  toggleExpand(value?: boolean): void {
    this.expanded = value ?? !this.expanded;
    this.onExpanded.emit(this.expanded);
  }
}
