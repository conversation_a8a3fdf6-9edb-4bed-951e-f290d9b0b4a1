import {
  Component,
  computed,
  OnInit,
  TemplateRef,
  viewChild,
  ViewChild
} from '@angular/core';
import {
  SFMap,
  TableAction,
  TableColumn,
  TableComponent,
  TableSelectionMode
} from '@simfront/common-libs';

import { DataService } from '../../data/data.service';
import {
  Node,
  NODE_ICONS,
  NodeStatus,
  NodeTransientStatus
} from '../../data/node/node.model';
import { MatSelectChange } from '@angular/material/select';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'vcci-node',
  templateUrl: './node.component.html',
  styleUrls: ['./node.component.scss'],
  standalone: false
})
export class NodeComponent implements OnInit {
  readonly NODE_ICONS = NODE_ICONS;
  nodes = toSignal<Node[]>(this.ds.node.all$);
  selectedNodes: Node[] = [];
  alteredHosts: SFMap<string> = {};
  distributedMode = false;

  tableActions: TableAction<Node>[] = [
    {
      label: 'Start',
      buttonId: 'start-button',
      tooltip: 'Start selected components',
      icon: 'play_arrow',
      action: (components: Node[]) => {
        this.startNodes(components);
      }
    },
    {
      label: 'Stop',
      buttonId: 'stop-button',
      tooltip: 'Stop selected components',
      icon: 'stop',
      action: (components: Node[]) => {
        this.stopNodes(components);
      },
    }
  ];

  selectionMode = TableSelectionMode.Multi;
  @ViewChild('hostDropdown', { static: true }) hostDropDown: TemplateRef<Node>;
  @ViewChild('statusIndicator', { static: true })
  statusIndicator!: TemplateRef<Node>;
  @ViewChild('startStopButton', { static: true })
  startStopButton!: TemplateRef<Node>;
  @ViewChild('nodeName', { static: true })
  nodeName!: TemplateRef<Node>;
  table = viewChild.required(TableComponent);

  columns: TableColumn<Node>[] = [];

  total = computed(() => this.nodes().length);
  running = computed(
    () => this.nodes().filter((n) => n.launchable.status === 'ON').length
  );
  ready = computed(
    () =>
      this.nodes().filter(
        (n) =>
          n.launchable.status === 'OFF' &&
          n.launchable.transientStatus === 'NONE'
      ).length
  );
  failed = computed(
    () => this.nodes().filter((n) => n.launchable.status === 'ERROR').length
  );
  busy = computed(
    () =>
      this.nodes().filter(
        (n) =>
          n.launchable.transientStatus === 'STARTING' ||
          n.launchable.transientStatus === 'TERMINATING'
      ).length
  );

  constructor(private ds: DataService) {}

  ngOnInit(): void {
    this.columns = [
      {
        header: 'Start/Stop',
        prop: (c) => this.getStatus(c),
        type: 'custom',
        template: this.startStopButton
      },
      { header: 'Name',
        prop: (n) => this.getNodeName(n),
        type: 'custom',
        template: this.nodeName
      },
      {
        header: 'Status',
        prop: (c) => this.getStatus(c),
        type: 'custom',
        template: this.statusIndicator
      },
      // {
      //   header: 'Status Time',
      //   prop: (n) => new Date(n.launchable.statusTime).toLocaleTimeString()
      // },
      {
        header: 'Host/IP/Workstation',
        prop: (n) => this.getHost(n),
        type: 'custom',
        template: this.hostDropDown
      }
    ];
  }

  getNodeName(n: Node): string {
    if (n.launchable.displayName) {
      return `${n.launchable.displayName} (${n.launchable.identifier})`;
    }
    return n.launchable.identifier || n.name;
  }

  trackBy = (n: Node) => n.name;

  onHostChange(change: MatSelectChange, node: Node): void {
    this.alteredHosts[node.name] = change.value;
  }

  onNodeSelect(nodes: Node[]): void {
    this.selectedNodes = nodes;
  }

  startNodes(nodes: Node[]): void {
    nodes.forEach((node) => {
      this.ds.node.activateNode({
        host: this.getHost(node),
        port: -1,
        args: [],
        identity: node.name,
        environment: []
      });
    });
    this.table().clearSelection();
  }

  stopNodes(nodes: Node[]): void {
    nodes.forEach((node) => {
      this.ds.node.deactivateNode(node.name);
    });
    this.table().clearSelection();
  }

  toggleDistributedMode(): void {
    this.distributedMode = !this.distributedMode;
  }

  getStatus(node: Node): NodeStatus | NodeTransientStatus {
    return node.launchable.transientStatus === 'NONE'
      ? node.launchable.status
      : node.launchable.transientStatus;
  }

  isNodeRunning(node: Node): boolean {
    return this.getStatus(node) === 'ON' || this.getStatus(node) === 'STARTING';
  }

  getHost(node: Node): string {
    if (this.getStatus(node) === 'ON' || this.getStatus(node) === 'STARTING') {
      return node.host;
    }
    if (!this.distributedMode) {
      return '127.0.0.1';
    }
    if (this.alteredHosts[node.name]) {
      return this.alteredHosts[node.name];
    }
    if (node.host === 'localhost') {
      return node.launchable.selectedHost;
    }
    return node.host;
  }
}
