/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/26/2024, 10:33 AM
 */

export enum ShapeType {
  POINT = 1,
  POLYLINE = 2,
  POLYGON = 4,
  COMPLEX_POLYGON = 8,
  SHAPE_LIST = 16,
  CIRCLE = 64,
  CIRCLE_BY_CENTER_POINT = 128,
  CIRCLE_BY_3_POINTS = 256,
  BOUNDS = 512,
  CIRCULAR_ARC = 4096,
  CIRCULAR_ARC_BY_CENTER_POINT = 8192,
  CIRCULAR_ARC_BY_3_POINTS = 16384,
  CIRCULAR_ARC_BY_BULGE = 32768,
  ELLIPSE = 131072,
  GEO_BUFFER = 262144,
  ARC = 524288,
  ARC_BAND = 1048576,
  EXTRUDED_SHAPE = 2097152,
  SECTOR = 4194304,
  ORIENTED_BOX = 8388608
}

export enum LocationCategoryCode {
  Ellipse = 'ELLPSE',
  FanArea = 'FAN AREA',
  Line    = 'LINE',
  Polygon = 'PLYGAR',
  Point   = 'POINT'
}
export enum ControlFeatureLocationType {
  Point   = 'POINT',
  Line    = 'LN',
  Ellipse = 'ELLPSE',
  FanArea = 'FA'
}

export interface PointLocation {
  '@type':  ControlFeatureLocationType.Point;
  lat:      number;
  lon:      number;
}

export interface Location {
  latitude:   number;
  longitude:  number;
}

export interface LineLocation {
  '@type':    ControlFeatureLocationType.Line;
  locations:  Location[];
}

export interface EllipseLocation {
  '@type':    ControlFeatureLocationType.Ellipse;
  jCenterPt:  Location;
  jXRadius:   number;
  jYRadius:   number;
  jRotation:  number;
}

export interface FanAreaLocation {
  '@type':            ControlFeatureLocationType.FanArea;
  jCenterPt:          Location;
  jMaxRadius:         number;
  jMinRadius:         number;
  jRotation:          number;
  jOrientationAngle:  number;
  jSectorAngle:       number;
}

export type ControlFeatureLocation = PointLocation | LineLocation | EllipseLocation | FanAreaLocation;
