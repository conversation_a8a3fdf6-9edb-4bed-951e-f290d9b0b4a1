import { InjectionToken } from '@angular/core';
import { LeafletService } from './leaflet/leaflet.service';
import { GisService } from './gis.service';

export type GIS_ENGINE = 'leaflet' | 'luciad' | 'luciad3d';
export const GIS_ENGINE_TOKEN = new InjectionToken<GIS_ENGINE>('GIS_ENGINE_TOKEN');
export interface LayerOptions {
  opacity: number;
  zIndex: number;
}

const GIS_SERVICE_FACTORY = (engine: GIS_ENGINE): GisService => {
  return new LeafletService();
};
export const GIS_SERVICE_PROVIDER = {
  provide: GisService,
  useFactory: GIS_SERVICE_FACTORY,
  deps: [GIS_ENGINE_TOKEN],
};

export enum ShapeType {
  POINT = 1,
  POLYLINE = 2,
  POLYGON = 4,
  COMPLEX_POLYGON = 8,
  SHAPE_LIST = 16,
  CIRCLE = 64,
  CIRCLE_BY_CENTER_POINT = 128,
  CIRCLE_BY_3_POINTS = 256,
  BOUNDS = 512,
  CIRCULAR_ARC = 4096,
  CIRCULAR_ARC_BY_CENTER_POINT = 8192,
  CIRCULAR_ARC_BY_3_POINTS = 16384,
  CIRCULAR_ARC_BY_BULGE = 32768,
  ELLIPSE = 131072,
  GEO_BUFFER = 262144,
  ARC = 524288,
  ARC_BAND = 1048576,
  EXTRUDED_SHAPE = 2097152,
  SECTOR = 4194304,
  ORIENTED_BOX = 8388608
}
