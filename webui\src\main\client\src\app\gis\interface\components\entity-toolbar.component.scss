@import 'toolbar-mixins';

.toolbar-wrapper {
  @include toolbar-standards
}

.toolbar {
  @include toolbar-styling;
}

.button-wrapper {
  @include button-wrapper-styling;
}

.shape-group {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  text-align: center;
}

button {
  @include toolbar-button-styling;
}

.expanded {
  @include expanded-button-styling;
}

:host ::ng-deep .expanded .button-icon .foregroundColor {
  @include expanded-button-styling;
}

button:hover {
  @include button-hover-styling;
}

button:active {
  @include button-active-styling;
}

::ng-deep .button-icon {
  @include toolbar-button-icon-styling;
}
