import { Component, Input } from '@angular/core';
import { EntityMetrics } from '../endpoint.model';
import { TableColumn, TableSelectionMode } from '@simfront/common-libs';

@Component({
    selector: 'vcci-detailed-metrics',
    templateUrl: 'detailed-metrics.component.html',
    styleUrls: ['detailed-metrics.component.css'],
    standalone: false
})
export class DetailedMetricsComponent {

  @Input({ alias: 'metrics', required: true }) dataSource: EntityMetrics[] = [];

  columns: TableColumn<EntityMetrics>[] = [
    { header: 'Layer Id', prop: m => m.layerId },
    { header: 'Entity Id', prop: m => m.id },
    { header: 'Processed Tasks', prop: m => m?.processedTasks ?? '0' },
    { header: 'Filtered Tasks', prop: m => m?.filteredTasks ?? '0' },
  ];
  selectionMode: TableSelectionMode = TableSelectionMode.None;
  trackBy = (e: EntityMetrics) => `${e.layerId}:${e.id}`;

}
