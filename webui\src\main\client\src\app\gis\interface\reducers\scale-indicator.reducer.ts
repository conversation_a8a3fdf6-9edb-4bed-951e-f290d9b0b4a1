import { createFeature, createReducer, on } from '@ngrx/store';
import { mapActions } from '../actions/map.actions';
import { ScaleIndicator } from '../models/scale-indicator.model';

export interface ScaleIndicatorState {
  scaleIndicator: ScaleIndicator | null;
}

export const initialState: ScaleIndicatorState = { scaleIndicator: null };

const scaleIndicatorFeature = createFeature({
  name: 'scaleIndicator',
  reducer: createReducer(
    initialState,
    on(mapActions.scaleIndicator, (state, action) => ({ ...state, scaleIndicator: action.payload }))
  )
});

export const {
  name,
  reducer,
  selectScaleIndicatorState,
  selectScaleIndicator
} = scaleIndicatorFeature;
