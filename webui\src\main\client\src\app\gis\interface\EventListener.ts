export interface MouseListener {
  /**
   * Invoked when the mouse button has been clicked (pressed
   * and released) on a component.
   * @param e the event to be processed
   */
  mouseClick?(event: MouseEvent): void;

  /**
   * Invoked when a mouse button has been pressed on a component.
   * @param e the event to be processed
   */
  mouseDown?(event: MouseEvent): void;

  /**
   * Invoked when a mouse button has been released on a component.
   * @param e the event to be processed
   */
  mouseUp?(event: MouseEvent): void;

  /**
   * Invoked when the mouse enters a component.
   * @param e the event to be processed
   */
  mouseEntered?(event: MouseEvent): void;

  /**
   * Invoked when the mouse exits a component.
   * @param e the event to be processed
   */
  mouseLeave?(event: MouseEvent): void;

  mouseMove?(event: MouseEvent): void;

  mouseDoubleClick?(event: MouseEvent): void;
}
