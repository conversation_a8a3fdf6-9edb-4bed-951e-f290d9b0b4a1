@import 'toolbar-mixins';

.map-control-toolbar {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 0px;
  left: 10px;
  z-index: 450;
  justify-content: center;
}

button {
  @include toolbar-button-styling;
  margin-bottom: 2px;
  border: unset;

  &.label-button{
    @include label-button-styling;
    margin-top: 5px;
    margin-left: 5px;
  }
}

.map-controller-button-group {
  margin-bottom: 10px;
}

button:hover {
  @include button-hover-styling;
}

button:active {
  @include button-active-styling;
}

::ng-deep .button-icon {
  @include toolbar-button-icon-styling;
}

.map-controller-button-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.active-button {
  background-color: $active-button-background-color;
  color: $active-icon-color;
}

:host ::ng-deep .active-button .foregroundColor {
  fill: $active-icon-color;
}
