import { SFMap } from '@simfront/common-libs';

import { UniqueId } from '../entity/id.model';

export enum FilterLocation {
  Input = 'INPUT',
  Output = 'OUTPUT',
  AllLocations = 'ALL_LOCATIONS'
}

export enum FilterType {
  Int = 'INT_ATTRIBUTE_FILTER',
  Long = 'LONG_ATTRIBUTE_FILTER',
  String = 'STRING_ATTRIBUTE_FILTER',
  Double = 'DOUBLE_ATTRIBUTE_FILTER',
  Options = 'OPTIONS_FILTER',
  Geo = 'GEO_FILTER',
  FollowEntity = 'FOLLOW_ENTITY_FILTER',
  Entity = 'ENTITY_FILTER',
  Custom = 'CUSTOM_FILTER'
}

export interface FilterInfo {
  name: string;
  type: FilterType;
  location: FilterLocation;
  attributeName: string;
  options: string[];
  allowMultiple: boolean;
  description: string;
}

export interface BaseFilter {
  type: FilterType;
  name: string;
  groupId: number;
  databaseId: number;
  description: string;
}

export interface OptionFilter extends BaseFilter {
  options: string[];
  selectedOptions: string[];
}

export interface AttributeFilter<T> extends BaseFilter {
  attributeName: string;
  valueOptions?: T[];
  value: T;
  max?: T;
  min?: T;
}

export interface EntityFilter extends BaseFilter {
  fullRouteExcludedEntities: UniqueId[];
  partialRouteIncludedEntities: UniqueId[];
}

export interface GeoFilter extends BaseFilter {
  areaLabel: string;
  firstPointLatitude: number;
  firstPointLongitude: number;
  secondPointLatitude: number;
  secondPointLongitude: number;
  radius: number;
  categoryCode: string;
}

export interface FollowEntityFilter extends BaseFilter {
  uniqueId: string;
  eastWestDistanceMetres: number | null;
  northSouthDistanceMetres: number | null;
  isInternalId: boolean;
}

export type Filter =
  | OptionFilter
  | AttributeFilter<number | string>
  | EntityFilter
  | GeoFilter
  | FollowEntityFilter;

export type FilterGroup = SFMap<Filter>;

export const isOptionFilter = (val: Filter): val is OptionFilter =>
  (val as any)?.options !== undefined &&
  (val as any)?.selectedOptions !== undefined;
export const isAttributeFilter = (val: Filter): val is AttributeFilter<any> =>
  (val as any)?.attributeName !== undefined &&
  (val as any)?.value !== undefined;
export const isEntityFilter = (val: Filter): val is EntityFilter =>
  (val as any)?.fullRouteExcludedEntities !== undefined &&
  (val as any)?.partialRouteIncludedEntities !== undefined;
export const isFollowEntityFilter = (val: Filter): val is FollowEntityFilter =>
  val?.type === 'FOLLOW_ENTITY_FILTER';
export const isGeoFilter = (val: Filter): val is GeoFilter =>
  val?.type === 'GEO_FILTER';

export const checkFilterLocation = (
  f: FilterInfo,
  direction: 'DATA_RECEIVER' | 'DATA_SENDER' | 'BOTH'
): boolean =>
  f.location === FilterLocation.AllLocations ||
  direction === 'BOTH' ||
  (f.location === FilterLocation.Input && direction === 'DATA_RECEIVER') ||
  (f.location === FilterLocation.Output && direction === 'DATA_SENDER');

export const hasFilterValue = (filter: Filter): boolean => {
  if (isOptionFilter(filter)) {
    return filter.selectedOptions.length > 0;
  } else if (isAttributeFilter(filter)) {
    return filter.value !== null && filter.value !== undefined;
  } else if (isEntityFilter(filter)) {
    return (
      filter.fullRouteExcludedEntities.length > 0 ||
      filter.partialRouteIncludedEntities.length > 0
    );
  } else if (isFollowEntityFilter(filter)) {
    return filter.uniqueId !== null && filter.uniqueId !== undefined;
  } else if (isGeoFilter(filter)) {
    return (
      filter.firstPointLatitude > 0 ||
      filter.firstPointLongitude > 0 ||
      filter.secondPointLatitude > 0 ||
      filter.secondPointLongitude > 0
    );
  }
  return false;
};

export const createEmptyFilter = (
  info: FilterInfo,
  groupId: number
): Filter | undefined => {
  const filter = {
    name: info.name,
    description: info.description,
    databaseId: -1,
    groupId,
    type: info.type
  };
  switch (info.type) {
    case FilterType.Int:
    case FilterType.Long:
    case FilterType.Double:
    case FilterType.String:
      return { ...filter, attributeName: info.attributeName, value: '' };
    case FilterType.Options:
      return { ...filter, options: info.options, selectedOptions: [] };
    case FilterType.Geo:
      return {
        ...filter,
        areaLabel: '',
        firstPointLatitude: 0,
        firstPointLongitude: 0,
        secondPointLatitude: 0,
        secondPointLongitude: 0,
        radius: 0,
        categoryCode: ''
      };
    case FilterType.FollowEntity:
      return {
        ...filter,
        uniqueId: '',
        eastWestDistanceMetres: null,
        northSouthDistanceMetres: null,
        isInternalId: true
      };
    case FilterType.Entity:
      return {
        ...filter,
        fullRouteExcludedEntities: [],
        partialRouteIncludedEntities: []
      };
    case FilterType.Custom:
      return undefined;
  }
};
