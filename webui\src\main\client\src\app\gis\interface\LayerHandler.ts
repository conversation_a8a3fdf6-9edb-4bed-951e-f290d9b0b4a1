import { SFMap } from '@simfront/common-libs';
import { MapDisplay } from './MapDisplay';
import { MapLayer } from './MapLayer';
import { MapLayerGroup } from './MapLayerGroup';
import {ParentLayerInfo} from "./gis-util";

export interface LayerHandler<L, G> {
  addLayer: (layer: MapLayer<L>) => void;
  addToLayerCache: (layerId: string, layer: MapLayer<L>) => void;
  removeLayer: (layer: MapLayer<L>) => void;
  removeLayerByName(layerName: string): void;
  getLayer: (id: string) => MapLayer<L> | undefined;
  getLayerByName: (layerName: string) => MapLayer<L> | undefined;
  getLayerList(): MapLayer<L>[];
  rootLayer: MapLayerGroup<L, G>;
  mapOpacity: (opacity: number) => void;
  layerOpacity: (layer, opacity:number) => void;
  redrawAllLayers: () => void;
  createParentLayer: (groupName: string, layerId?: string, parentId?: string, dispatch?: boolean) => ParentLayerInfo<L, G>;
}

export abstract class MapLayerHandler<L, G> implements LayerHandler<L, G> {
  protected layerCache: SFMap<MapLayer<L>> = {};
  _rootLayer: MapLayerGroup<L, G>;
  mapDisplay: MapDisplay<L, G>;

  protected constructor(mapDisplay: MapDisplay<L, G>) {
    this.mapDisplay = mapDisplay;
  }

  set rootLayer(root: MapLayerGroup<L, G>) {
    this._rootLayer = root;
  }

  get rootLayer(): MapLayerGroup<L, G> {
    return this._rootLayer;
  }

  addLayer(layer: MapLayer<L>): void {
    if (layer && !(layer.layerId in this.layerCache)) {
      this.layerCache[layer.layerId] = layer;
      this.gisAddLayer(layer);
    }
  }

  addToLayerCache(layerId: string, layer: MapLayer<L>): void {
    this.layerCache[layerId] = layer;
  }

  removeLayer(layer: MapLayer<L>): void {
    delete this.layerCache[layer.layerId];
    this.gisRemoveLayer(layer);
  }

  removeLayerByName(layerName: string): void {
    const layer = this.layerCache[layerName];
    delete this.layerCache[layerName];
    this.gisRemoveLayer(layer);
  }

  getLayer(id: string): MapLayer<L> | undefined {
    return this.layerCache[id];
  }

  getLayerByName(layerName: string): MapLayer<L> | undefined {
    return Object.values(this.layerCache).find(layer => layer.layerName === layerName);
  }

  abstract mapOpacity(opacity: number): void;

  abstract layerOpacity(layerId:string, opacity: number): void;

  abstract gisAddLayer(layer: MapLayer<L>): void;

  abstract gisRemoveLayer(layer: MapLayer<L>): void;

  abstract createParentLayer(groupName: string, layerId?: string, parentId?: string, dispatch?: boolean): ParentLayerInfo<L, G>;

  getLayerList(): MapLayer<L>[] {
    return Object.values(this.layerCache);
  }

  redrawAllLayers(): void {
    this.getLayerList().forEach(layer => {
      if (layer.isFeatureLayer) {
        layer.redraw();
      }
    });
  }

  // protected addLayerToLayerTree(layerInf: LayerInfo, parentName?: string): void {
  //   if (parentName) {
  //     const parentInfo = TreeFunctions.find(
  //       this.layerTree,
  //       layerInfo => layerInfo.layerName === parentName,
  //       child => child.children
  //     );
  //     if (parentInfo) {
  //       parentInfo.children.push(layerInf);
  //     }
  //   } else if (this.layerTree.length === 0) {
  //     this.layerTree.push(layerInf);
  //   } else {
  //     this.layerTree[0].children.push(layerInf);
  //   }
  //
  //   this.mapDisplay.store.dispatch(mapActions.addLayer(layerInf));
  // }
}
