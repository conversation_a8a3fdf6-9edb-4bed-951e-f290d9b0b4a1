import { Coordinate } from '../../cursit/models/cursit.model';
import { ShapeType } from '../interface/models/map-element.model';
import { LeafletMap } from './LeafletMap';
import { LeafletGraphics } from './LeafletGraphics';
import { Point } from 'pixi.js';
import { LeafletMapDisplay } from './LeafletMapDisplay';

/**
 * LeafletCorridor class for rendering corridors on a Leaflet map.
 */
export class LeafletCorridor extends LeafletGraphics {
  /**
   * Creates a new LeafletCorridor instance
   *
   * @param id Unique identifier for the corridor
   * @param map LeafletMap instance to render on
   * @param type ShapeType of the corridor
   * @param catCode Category code for styling
   * @param coordinates Array of coordinates forming the corridor
   * @param label Label for the corridor
   * @param options Object containing additional properties (width)
   */
  constructor(
    id: string,
    map: LeafletMapDisplay,
    type: ShapeType,
    catCode: string,
    coordinates: Coordinate[],
    label: string,
    options: {
      width: number;
    }
  ) {
    super(id, map, type, catCode, coordinates, label, options);

    this.drawCorridor();
  }

  drawCorridor(): void {
    // ──────────────────────────────────────────
    // 1. PREPARE GEOMETRY & CONVERT UNITS
    // ──────────────────────────────────────────

    this._graphicsElement.clear();

    // Calculate the current zoom scale
    const scale = this.getZoomScale(this._map.map.getZoom());
    const adjustedStrokeWidth = this._strokeWidth / scale;

    // Center points of the corridor
    const centerPoints = this._locations.map((location) =>
      this._map.map.projectFromLatLonToPoint([location[0], location[1]])
    );

    // TODO: CONVERT THIS CONVERSION TO SCALE FACTOR RATHER THAN MANUAL CALCULATION WHEN THE CURSIT WEB WORKER IS IMPLEMENTED
    // Convert corridor width (meters) to pixels using a reference latitude
    const referenceLat = this._locations[0][0];
    const metersPerPixel =
      (156543.0339 * Math.cos((referenceLat * Math.PI) / 180)) /
      Math.pow(2, 12);
    const corridorWidthPixels = this._options.width / metersPerPixel;
    const halfWidth = corridorWidthPixels / 2;

    const leftOffsetPoints: Point[] = [];
    const rightOffsetPoints: Point[] = [];

    const n = centerPoints.length;

    // ──────────────────────────────────────────
    // 2. COMPUTE END-CAP OFFSETS FOR FIRST POINT
    // ──────────────────────────────────────────

    const p0 = centerPoints[0];
    const p1 = centerPoints[1];
    const dStart = this.normalize({ x: p1.x - p0.x, y: p1.y - p0.y });
    // Define left and right normals for the starting segment.
    const leftNormalStart = { x: -dStart.y, y: dStart.x };
    const rightNormalStart = { x: dStart.y, y: -dStart.x };

    leftOffsetPoints.push(
      new Point(
        p0.x + leftNormalStart.x * halfWidth,
        p0.y + leftNormalStart.y * halfWidth
      )
    );
    rightOffsetPoints.push(
      new Point(
        p0.x + rightNormalStart.x * halfWidth,
        p0.y + rightNormalStart.y * halfWidth
      )
    );

    // ──────────────────────────────────────────
    // 3. COMPUTE OFFSET POINTS FOR INTERIOR POINTS WITH MITER JOINS
    // ──────────────────────────────────────────

    // For each interior point, use a miter join to compute the proper offset corner.
    for (let i = 1; i < n - 1; i++) {
      const pointPrev = centerPoints[i - 1];
      const pointCurr = centerPoints[i];
      const pointNext = centerPoints[i + 1];

      // --- LEFT SIDE ---
      // Compute the unit direction vectors for the two segments.
      const dA = this.normalize({
        x: pointCurr.x - pointPrev.x,
        y: pointCurr.y - pointPrev.y
      });
      const dB = this.normalize({
        x: pointNext.x - pointCurr.x,
        y: pointNext.y - pointCurr.y
      });
      // Left normals for each segment.
      const nA = { x: -dA.y, y: dA.x };
      const nB = { x: -dB.y, y: dB.x };

      // The offset position along each segment at pointCurr.
      const offsetA = {
        x: pointCurr.x + nA.x * halfWidth,
        y: pointCurr.y + nA.y * halfWidth
      };
      const offsetB = {
        x: pointCurr.x + nB.x * halfWidth,
        y: pointCurr.y + nB.y * halfWidth
      };

      // Create two offset lines that originate near pointCurr.
      // • LineA: passes through offsetA with direction dA.
      // • LineB: passes through offsetB with direction dB.
      // Miter join – intersect offset lines to get a proper left offset corner.
      const leftJoin = this.intersectLines(offsetA, dA, offsetB, dB);
      leftOffsetPoints.push(
        new Point(
          leftJoin ? leftJoin.x : offsetA.x,
          leftJoin ? leftJoin.y : offsetA.y
        )
      );

      // --- RIGHT SIDE ---
      // For the right side use the opposite normals.
      // Right normal for a segment can be defined as (d.y, -d.x).
      const rightOffsetA = {
        x: pointCurr.x + dA.y * halfWidth,
        y: pointCurr.y - dA.x * halfWidth
      };
      const rightOffsetB = {
        x: pointCurr.x + dB.y * halfWidth,
        y: pointCurr.y - dB.x * halfWidth
      };

      const rightJoin = this.intersectLines(rightOffsetA, dA, rightOffsetB, dB);
      rightOffsetPoints.push(
        new Point(
          rightJoin ? rightJoin.x : rightOffsetA.x,
          rightJoin ? rightJoin.y : rightOffsetA.y
        )
      );
    }

    // ──────────────────────────────────────────
    // 4. COMPUTE END-CAP OFFSETS FOR LAST POINT
    // ──────────────────────────────────────────

    // For the last center point, compute an end-cap offset.
    const pointLast = centerPoints[n - 1];
    const pointPrev = centerPoints[n - 2];
    const endCapDirection = this.normalize({
      x: pointLast.x - pointPrev.x,
      y: pointLast.y - pointPrev.y
    });
    const leftNormalEndCap = { x: -endCapDirection.y, y: endCapDirection.x };
    const rightNormalEndCap = { x: endCapDirection.y, y: -endCapDirection.x };

    leftOffsetPoints.push(
      new Point(
        pointLast.x + leftNormalEndCap.x * halfWidth,
        pointLast.y + leftNormalEndCap.y * halfWidth
      )
    );
    rightOffsetPoints.push(
      new Point(
        pointLast.x + rightNormalEndCap.x * halfWidth,
        pointLast.y + rightNormalEndCap.y * halfWidth
      )
    );

    // ──────────────────────────────────────────
    // 5. DRAW THE CORRIDOR
    // ──────────────────────────────────────────

    // Start on the left side and continue along the right side in reverse order
    const combinedPoints = [
      ...leftOffsetPoints,
      ...rightOffsetPoints.reverse()
    ];

    this._graphicsElement.poly(combinedPoints, true);

    this._graphicsElement.fill({
      color: this.getFillColorFromCatCode(this._catCode),
      alpha: 0.2
    });

    this._graphicsElement.stroke({
      width: adjustedStrokeWidth,
      color: this.selected
        ? 'red'
        : this.getFillColorFromCatCode(this._catCode),
      alignment: 0.5
    });
  }

  /**
   * Produces a unit vector
   * @param v Vector to normalize
   * @returns Normalized vector
   */
  normalize(v: { x: number; y: number }): { x: number; y: number } {
    const len = Math.hypot(v.x, v.y);
    return { x: v.x / len, y: v.y / len };
  }

  /**
   * Computes where two offset lines intersect
   * This is used to 'miter' two offset segments together
   * @param p1 Point on the first line
   * @param d1 Direction of the first line
   * @param p2 Point on the second line
   * @param d2 Direction of the second line
   * @returns Intersection point or null if lines are parallel
   */
  intersectLines(
    p1: { x: number; y: number },
    d1: { x: number; y: number },
    p2: { x: number; y: number },
    d2: { x: number; y: number }
  ): { x: number; y: number } | null {
    const det = d1.x * d2.y - d1.y * d2.x;
    if (Math.abs(det) < 1e-6) return null;
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    const t = (dx * d2.y - dy * d2.x) / det;
    return { x: p1.x + t * d1.x, y: p1.y + t * d1.y };
  }

  redraw(): void {
    this.drawCorridor();
    this.addLabel();
  }

  setSpeedAndBearing(speed: number, bearing: number): void {}

  setSymbolCode(symbolCode: string): void {}
}
