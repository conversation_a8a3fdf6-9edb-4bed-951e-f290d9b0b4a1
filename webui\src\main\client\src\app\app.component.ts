import '../license-loader';

import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatAccordion } from '@angular/material/expansion';
import { MatSelectionListChange } from '@angular/material/list';
import { MatRadioChange } from '@angular/material/radio';

import { EMap, SFMap } from '@simfront/common-libs';

import { DataService } from './data/data.service';
import { Entity } from './data/entity/entity.model';
import { FileChooserComponent } from './file-chooser-component/file-chooser-component.component';
import { DEFAULT_SETTINGS } from './data/settings/settings.model';

declare let armyc2: any;
declare let java: any;
declare let sec: any;

export const { RendererUtilities } = armyc2.c2sd.renderer.utilities;
export const { RendererSettings } = armyc2.c2sd.renderer.utilities;
export const { SymbolUtilities } = armyc2.c2sd.renderer.utilities;
export const { SymbolDefTable } = armyc2.c2sd.renderer.utilities;
export const { UnitDefTable } = armyc2.c2sd.renderer.utilities;
export const { ModifiersTG } = armyc2.c2sd.renderer.utilities;
export const { ModifiersUnits } = armyc2.c2sd.renderer.utilities;
export const { MilStdAttributes } = armyc2.c2sd.renderer.utilities;
export const SecWebRenderer = sec.web.renderer.SECWebRenderer;
export const stdIconRenderer = armyc2.c2sd.renderer.MilStdIconRenderer;

export enum Display {
  Router = 'Router',
  Cursit = 'Cursit',
  Add = 'Add'
}

@Component({
  selector: 'vcci-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  standalone: false
})
export class AppComponent implements OnInit {
  dialogRef: MatDialogRef<FileChooserComponent>;
  title = 'client';
  bsn = {};
  entities: Entity[] = [];
  entityCache: EMap<Entity> = {};
  connectionImages: { [name: string]: string } = {
    ABACUS: 'abacus.png',
    FFI: 'ffi.png',
    NFFI: 'nffi.png',
    TSI: 'tsi.png',
    DIS: 'dis.png',
    OTH: 'oth.png'
  };

  // readyState$ = this.store.select(selectReadyState);
  // lastMessage$ = this.store.select(selectLastMessage);
  // busy$ = this.store.select(selectBusy);

  showFiller = false;
  step = 1;

  connections = [
    'ABACUS',
    'BATTLEVIEW',
    'DDS',
    'DIS',
    'FFI',
    'OTH',
    'TSI',
    'VMF'
  ];
  Display = Display;
  display = Display.Cursit;

  filters: SFMap<string>[] = [];

  selectedConnection = 'None';
  createdConnections: SFMap<string>[] = [];
  nodeConnections: string[] = [];

  setStep(index: number): void {
    this.step = index;
  }

  nextStep(): void {
    this.step += 1;
  }

  prevStep(): void {
    this.step -= 1;
  }

  @ViewChild(MatAccordion) accordion!: MatAccordion;

  // gets all the available microservices from the NEXUS APP LAYER
  constructor(private ds: DataService) {
    this.ds.settings.readOne('user', 0, DEFAULT_SETTINGS);
  }

  ngOnInit(): void {
    // this.openGISEngineSelectionDialog();
    RendererSettings.setSymbologyStandard(RendererSettings.Symbology_2525C);
    // RendererSettings.setSinglePointSymbolOutlineWidth(0);
  }

  getNodes(): Node[] {
    return Object.values(this.bsn);
  }

  openGISEngineSelectionDialog(): void {
    // const dialogRef = this.dialog.open(SelectGisengineDialogComponent);
    //
    // dialogRef.afterClosed().subscribe((selectedOption: GISEngine) => {
    //   if (selectedOption) {
    //     // Based on the selected option, initialize your app
    //     this.gisService.gisEngine = selectedOption;
    //     this.gisService.initMap().then(() => {
    //       this.gisService.addElements();
    //       this.ds.entity.readAll();
    //     });
    //   } else {
    //     // Handle cancellation or default behavior
    //     console.log('Dialog closed without selecting an option.');
    //   }
    // });
  }

  onDirectionSelected(event: MatRadioChange): void {
    console.log(this.selectedConnection);
    this.display = Display.Add;
  }

  onClose(): void {}

  onSelectConn(mslc: MatSelectionListChange): void {
    const selection = mslc.source.selectedOptions;
    if (!selection.isEmpty()) {
      this.selectedConnection = selection.selected[0].value;
      // this.onOpen(this.selectedConnection);
      this.nextStep();
    }
  }

  onNewConnection(createdConnection: SFMap<string>): void {
    this.createdConnections.push(createdConnection);
    this.filters.push(createdConnection);
    this.selectedConnection = 'None';
    this.step = 1;
  }

  getConnectionImage(connName: string): string | null | undefined {
    let imageName: string | undefined | null = null;
    imageName = this.connectionImages[connName] ?? 'default_dark.png';
    return imageName;
  }
}
