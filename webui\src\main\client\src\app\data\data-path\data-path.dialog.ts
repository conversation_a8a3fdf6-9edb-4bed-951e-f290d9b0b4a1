import { Component } from '@angular/core';

import { DialogOutParams, FacadeDialogContentComponent } from '@simfront/common-libs';

import { DataPath } from './data-path.model';

@Component({
  selector: 'vcci-data-path-delete-dialog',
  template: `
    <div class="m-container">
      <p>Are you sure you want to delete the route:</p>
      <ul style="padding: 0 30px;">
        <li>{{id}}</li>
      </ul>
    </div>
  `,
  standalone: true,
  styles: [`
    .m-container {
      margin: 5px;
      padding: 0 20px;
      max-width: 80vw;
    }

    ::ng-deep .mat-mdc-dialog-content:has(vcci-data-path-delete-dialog) ~ .mat-mdc-dialog-actions {
        justify-content: flex-end !important;
        padding: 20px !important;
    }
  `]
})
export class DataPathDialog extends FacadeDialogContentComponent<DataPath, 'id', string> {
  id = '';

  constructor() {
    super();
    this.id = this.data.inParams.crud === 'Delete' && this.data.inParams.quantity === 'One' ? this.data.inParams.id : 'Not implemented.';
  }

  override outData(): DialogOutParams<DataPath, 'id', string> {
    if (this.data.inParams.crud === 'Delete' && this.data.inParams.quantity === 'One') {
      return { ...this.data.inParams, id: this.data.inParams.id };
    }
    throw new Error(`Crud Operation '${this.data.inParams.crud}' is not supported`);
  }
}
