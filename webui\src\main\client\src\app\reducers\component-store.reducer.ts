import { createAction, createReducer, on, props, Store } from '@ngrx/store';
import { debounceTime, distinctUntilChanged, filter, Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export const initialState: any = {};
export const updateComponentState = createAction(
  '[Component Store] Update Action',
  props<{ componentName; componentState }>()
);
export const removeComponentState = createAction(
  '[Component Store] Remove Action',
  props<{ componentName: string }>()
);

export const componentStateReducer = createReducer(
  initialState,
  on(updateComponentState, (state, { componentName, componentState }) => {
    return { ...state, [componentName]: { ...componentState } };
  }),
  on(removeComponentState, (state, { componentName }) => {
    const { [componentName]: removedKey, ...newObject } = state;
    return newObject;
  })
);

export const linkToGlobalState = (
  componentState$: Observable<any>,
  componentName: string,
  globalStore: Store
) => {
  componentState$
    .pipe(
      filter(() => !environment.production),
      distinctUntilChanged(
        (prev, next) => JSON.stringify(prev) === JSON.stringify(next)
      ),
      debounceTime(250)
    )
    .subscribe((componentState) => {
      globalStore.dispatch(
        updateComponentState({ componentName, componentState })
      );
    });
};

export const unlinkFromGlobalState = (
  componentName: string,
  globalStore: Store
) => {
  globalStore.dispatch(removeComponentState({ componentName }));
};
