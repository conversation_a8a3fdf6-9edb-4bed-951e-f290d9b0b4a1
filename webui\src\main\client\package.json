{"name": "nexus_ui", "version": "0.0.0", "main": "app.js", "scripts": {"preinstall": "npx only-allow pnpm", "pnpm:config": "ng config -g cli.packageManager pnpm", "ng": "ng", "start": "ng serve --open", "build": "ng build --base-href /nexus/", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "lint:fix": "ng lint --fix", "check": "prettier --check .", "format": "prettier --write .", "update:lib": "pnpm remove @simfront/common-libs && pnpm add @simfront/common-libs@newarch -w", "install:local:lib": "pnpm install file:../../../../../../common-libs-web/web/angular/dist/common-libs -w", "sf:install": "pnpm install --frozen-lockfile"}, "private": true, "dependencies": {"@angular/animations": "^19.2.9", "@angular/cdk": "^19.2.14", "@angular/common": "^19.2.9", "@angular/compiler": "^19.2.9", "@angular/core": "^19.2.9", "@angular/forms": "^19.2.9", "@angular/material": "^19.2.14", "@angular/platform-browser": "^19.2.9", "@angular/platform-browser-dynamic": "^19.2.9", "@angular/router": "^19.2.9", "@bluehalo/ngx-leaflet": "^19.0.0", "@bluehalo/ngx-leaflet-markercluster": "^19.0.0", "@luciad/ria": "2022.1.18", "@luciad/ria-geometry": "2022.1.18", "@luciad/ria-milsym": "2022.1.18", "@ngrx/component": "19.2.0", "@ngrx/component-store": "^19.2.0", "@ngrx/effects": "^19.2.0", "@ngrx/entity": "^19.2.0", "@ngrx/operators": "^19.2.0", "@ngrx/store": "^19.2.0", "@ngrx/store-devtools": "^19.2.0", "@simfront/common-libs": "6.0.0-SNAPSHOT.68", "@turf/turf": "^7.2.0", "bowser": "^2.11.0", "dexie": "^4.0.11", "dom-to-image-more": "^3.5.0", "eventemitter3": "^5.0.1", "geojson-vt": "^3.2.1", "leader-line": "^1.0.8", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-ruler": "^1.0.0", "leaflet.markercluster": "^1.5.3", "mgrs": "^2.1.0", "milsymbol": "3.0.0", "ngx-cookie-service": "^19.1.2", "ol": "^7.5.2", "pixi.js": "^8.9.2", "raw-loader": "^4.0.2", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular-eslint/builder": "19.1.0", "@angular-eslint/eslint-plugin": "19.1.0", "@angular-eslint/eslint-plugin-template": "19.1.0", "@angular-eslint/schematics": "19.1.0", "@angular-eslint/template-parser": "19.1.0", "@angular/cli": "~19.1.9", "@angular/compiler-cli": "^19.2.9", "@types/jasmine": "~5.1.8", "@types/leaflet": "^1.9.17", "@types/leaflet.markercluster": "^1.5.5", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "angular-eslint": "^19.3.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "husky": "^8.0.3", "jasmine-core": "~5.5.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^13.3.0", "prettier": "^3.5.3", "prettier-eslint": "^16.4.1", "typescript": "5.7.3", "typescript-eslint": "^8.32.0", "webpack": "^5.99.8"}}