import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NodeFacade } from '../node/node.facade';
import { createEffect, ofType } from '@ngrx/effects';
import {
  concatMap,
  mergeMap,
  map,
  Observable,
  switchMap,
  withLatestFrom,
  catchError,
  of
} from 'rxjs';
import { GenericDialog } from 'src/app/shared/generic-dialog/generic-dialog.dialog';
import {
  CreateRoute,
  Route,
  RouteActions,
  RouteFacadeBase
} from './route.model';
import { CreateRouteComponent } from 'src/app/gis/interface/components/create-route/create-route.component';

@Injectable({ providedIn: 'root' })
export class RouteFacade extends RouteFacadeBase {
  nodeFacade = inject(NodeFacade);
  http = inject(HttpClient);

  constructor() {
    super();
  }

  openRouteCreationDialog(): void {
    this.store.dispatch(RouteActions.openRouteCreationDialog());
  }

  saveRoute(route: CreateRoute): void {
    this.store.dispatch(RouteActions.saveRoute(route));
  }

  openRouteCreationDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RouteActions.openRouteCreationDialog),
      withLatestFrom(this.nodeFacade.simMovementNode$),
      switchMap(([action, simMove]) =>
        simMove?.launchable.status === 'ON'
          ? this.dialog
              .open(CreateRouteComponent, {
                width: '450px',
                hasBackdrop: false,
                position: {
                  top: '200px',
                  right: '50px'
                }
              })
              .afterClosed()
          : this.dialog
              .open(GenericDialog, {
                data: {
                  title: 'Cannot Create Routes without SIM_MOVE',
                  message:
                    'SIM_MOVE is currently turned OFF. Turn on the node to create routes.'
                }
              })
              .afterClosed()
      ),
      map(() => RouteActions.closeRouteCreationDialog())
    )
  );

  saveRoute$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RouteActions.saveRoute),
      map((action) => action.payload),
      mergeMap((route) =>
        this.saveRouteAPI(route).pipe(
          concatMap((route) => [
            this.actions.createOneReceived(route),
            RouteActions.saveRouteSuccess()
          ]),
          catchError((_) => of(this.actions.createOneFailed()))
        )
      )
    )
  );

  saveRouteAPI(route: CreateRoute): Observable<Route> {
    return this.http.post<Route>(this.endpoint, route);
  }
}
