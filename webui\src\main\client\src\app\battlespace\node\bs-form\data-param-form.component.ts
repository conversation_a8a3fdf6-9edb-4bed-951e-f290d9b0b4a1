import {
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  signal,
  WritableSignal
} from '@angular/core';
import {
  DataPage,
  DataParameter,
  DataSection,
  getIdentifierPath,
  GetNext,
  GROUP_CONTAINER,
  Instruction,
  normalizeParamVal,
  Parameters,
  PARENT_FORM
} from '../../../data/bs-params/bs-params.model';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  updateFormControl,
  updateFormGroup,
  updateFormGroup$
} from '../../../shared/form.model';
import { BsDataPagesComponent } from './bs-data-pages/bs-data-pages.component';
import { BsDataParamsComponent } from './bs-data-params/bs-data-params.component';
import { SFMap } from '@simfront/common-libs';
import { SelectionModel } from '@angular/cdk/collections';
import { BsDataSectionsComponent } from './bs-data-sections/bs-data-sections.component';
import { filter, map, Observable, switchMap, withLatestFrom } from 'rxjs';
import { outputFromObservable, toObservable } from '@angular/core/rxjs-interop';

@Component({
  selector: 'vcci-data-param-form',
  templateUrl: './data-param-form.component.html',
  imports: [
    ReactiveFormsModule,
    BsDataPagesComponent,
    BsDataParamsComponent,
    BsDataSectionsComponent
  ],
  styleUrl: './data-param-form.component.css',
  providers: [
    {
      provide: PARENT_FORM,
      useFactory: () => signal(new FormGroup({}))
    },
    {
      provide: GROUP_CONTAINER,
      useFactory: () => signal<SFMap<SelectionModel<string>>>({})
    }
  ]
})
export class DataParamFormComponent {
  // TODO: Identifier Changed.
  fg$: WritableSignal<FormGroup> = inject(PARENT_FORM, { self: true });
  identifierPath$: WritableSignal<string | null> = signal(null);
  identifierValue$: Observable<string> = toObservable(
    this.identifierPath$
  ).pipe(
    withLatestFrom(toObservable(this.fg$)),
    map(([path, fg]) => fg.get(path)),
    filter((control) => !!control),
    switchMap((c) => c.valueChanges)
  );

  identifierChanged = outputFromObservable(this.identifierValue$);
  orGroups: WritableSignal<SFMap<SelectionModel<string>>> =
    inject(GROUP_CONTAINER);

  get form(): FormGroup {
    return this.fg$();
  }

  @Input() usingDefault = false;
  @Input() formType: 'Connection' | 'Endpoint' = 'Connection';
  private _pages: DataPage[] = [];
  @Input() set pages(pages: DataPage[]) {
    this._pages = pages;
    this.addPagesGroup();
    const identifier = getIdentifierPath(this._pages);
    if (identifier) {
      this.identifierPath$.set(identifier);
    }
  }
  get pages() {
    return this._pages;
  }

  private _sections: DataSection[] = [];
  @Input() set sections(sections: DataSection[]) {
    this._sections = sections;
    this.addSectionGroup();
    const identifier = getIdentifierPath(this._sections);
    if (identifier) {
      this.identifierPath$.set(identifier);
    }
  }
  get sections() {
    return this._sections;
  }

  private _parameters: DataParameter[] = [];
  @Input() set parameters(parameters: DataParameter[]) {
    this._parameters = parameters;
    this.addParameterGroup();
    const identifier = getIdentifierPath(this._parameters);
    if (identifier) {
      this.identifierPath$.set(identifier);
    }
  }
  get parameters() {
    return this._parameters;
  }

  @Output() getNext = new EventEmitter<GetNext>();
  @Output() networkInterfaceChanges = new EventEmitter<string>();

  addPagesGroup(): void {
    updateFormGroup$(
      this.fg$,
      ['pages'],
      (p) => p,
      this.addPages.bind(this),
      false
    );
  }

  addSectionGroup(): void {
    updateFormGroup$(
      this.fg$,
      ['sections'],
      (s) => s,
      (fg) => this.addSections(fg, this.sections),
      false
    );
  }

  addParameterGroup(): void {
    updateFormGroup$(
      this.fg$,
      ['parameters'],
      (p) => p,
      (fg) => this.addParameters(fg, this.parameters),
      false
    );
  }

  addPages(fg: FormGroup, _: string): void {
    updateFormGroup(fg, this.pages, (p) => p.pageName, this.addPage.bind(this));
  }

  addPage(fg: FormGroup, page: DataPage): void {
    // Add parameters group
    updateFormGroup(
      fg,
      ['parameters'],
      (p) => p,
      (fg) => this.addParameters(fg, Object.values(page.parameters))
    );
    // Add sections
    updateFormGroup(
      fg,
      ['sections'],
      (s) => s,
      (fg) => this.addSections(fg, Object.values(page.sections))
    );
  }

  addSections(fg: FormGroup, sections: DataSection[]): void {
    updateFormGroup(
      fg,
      sections,
      (s) => s.sectionName,
      this.addSection.bind(this)
    );
  }

  addSection(fg: FormGroup, section: DataSection) {
    updateFormGroup(
      fg,
      ['parameters'],
      (p) => p,
      (fg) => this.addParameters(fg, Object.values(section.parameters))
    );
  }

  addParameters(fg: FormGroup, parameters: DataParameter[]): void {
    updateFormControl(
      fg,
      parameters,
      (p) => p.name,
      (p) => normalizeParamVal(p),
      this.updateOrGroup.bind(this)
    );
  }

  updateOrGroup(param: DataParameter): void {
    if (!param.group) {
      return;
    }
    this.orGroups.update((orGroups) =>
      !orGroups[param.group.name]
        ? {
            ...orGroups,
            [param.group.name]: new SelectionModel<string>(
              param.group.groupType !== 'SINGLE_SELECT'
            )
          }
        : orGroups
    );
  }

  onNetworkInterfaceChange(event: string): void {
    this.networkInterfaceChanges.emit(event);
  }

  onNext(event: DataParameter): void {
    if (event.hasNext) {
      this.getNext.emit({
        requestParameter: event.name,
        instruction: Instruction.GetNextParam,
        parameters: this.getDataParameterValues()
      });
    }
  }

  private getParamValue(param: DataParameter, path: string): DataParameter {
    return { ...param, value: this.fg$().get(path)?.value };
  }

  private getParamsMap(
    params: DataParameter[],
    path: string = ''
  ): SFMap<DataParameter> {
    return params.reduce<SFMap<DataParameter>>((acc, p) => {
      acc[p.name] = this.getParamValue(p, `${path}parameters.${p.name}`);
      return acc;
    }, {});
  }

  private getSectionsMap(
    sections: DataSection[],
    path: string = ''
  ): SFMap<DataParameter> {
    return sections.reduce<SFMap<DataParameter>>((acc, s) => {
      return {
        ...acc,
        ...this.getParamsMap(
          Object.values(s.parameters),
          `${path}sections.${s.sectionName}.`
        )
      };
    }, {});
  }

  private getPagesMap(pages: DataPage[]): SFMap<DataParameter> {
    return pages.reduce<SFMap<DataParameter>>((acc, page) => {
      const path = `pages.${page.pageName}.`;
      return {
        ...acc,
        ...this.getSectionsMap(Object.values(page.sections), path),
        ...this.getParamsMap(Object.values(page.parameters), path)
      };
    }, {});
  }

  getDataParameterValues(): SFMap<DataParameter> {
    return {
      ...this.getParamsMap(this.parameters),
      ...this.getSectionsMap(this.sections),
      ...this.getPagesMap(this.pages)
    };
  }

  getParameterValues(): Parameters {
    let acc = this._getPagesValues();
    acc = this._getSectionValues(this.sections, acc);
    return this._getParameterValues(this.parameters, acc);
  }

  private _getPagesValues(): Parameters {
    return this.pages.reduce<Parameters>(
      (acc, page) => {
        return this._getPageValues(page, acc);
      },
      { listParameters: {}, singleParameters: {} }
    );
  }

  private _getPageValues(page: DataPage, init: Parameters): Parameters {
    const a = this._getParameterValues(
      Object.values(page.parameters),
      init,
      `pages.${page.pageName}.`
    );
    return this._getSectionValues(
      Object.values(page.sections),
      a,
      `pages.${page.pageName}.`
    );
  }

  private _getSectionValues(
    sections: DataSection[],
    init = { listParameters: {}, singleParameters: {} },
    path = ''
  ): Parameters {
    return sections.reduce<Parameters>((acc, s) => {
      return this._getParameterValues(
        Object.values(s.parameters),
        acc,
        `${path}sections.${s.sectionName}.`
      );
    }, init);
  }

  private _getParameterValues(
    parameters: DataParameter[],
    init = { listParameters: {}, singleParameters: {} },
    path = ''
  ): Parameters {
    return parameters
      .filter((f) => {
        const form = this.fg$().get(`${path}parameters.${f.name}`);
        return (
          !form.invalid &&
          form.enabled &&
          f.editable &&
          (!f.group || this.orGroups()[f.group?.name]?.isSelected(f.name))
        );
      })
      .reduce<Parameters>((p, c) => {
        const value = this.fg$().get(`${path}parameters.${c.name}`).value;
        if (c.valueList) {
          p.listParameters[c.name] = {
            id: 0,
            key: c.name,
            singleParameters: value.map((v) => ({
              id: 0,
              key: c.name,
              value: v
            }))
          };
          return p;
        } else {
          p.singleParameters[c.name] = { id: 0, key: c.name, value };
          return p;
        }
      }, init);
  }
}
