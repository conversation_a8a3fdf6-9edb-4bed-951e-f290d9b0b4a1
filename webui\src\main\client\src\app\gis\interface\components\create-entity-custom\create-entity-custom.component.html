<div class="custom-form-section">
  <h3>Custom {{ entityType }} Definition</h3>
  <div class="code-definition">
    <form [formGroup]="parentForm">
      <div class="milsym-form-container">
        @for (field of milsymFormFields; track field.name) {
          @if (field.name === 'unitFormation') {
            <!-- unit formation radio group -->
            <mat-radio-group formControlName="{{ field.name }}">
              @for (option of field.options; track $index) {
                <mat-radio-button [value]="option">{{
                  option
                }}</mat-radio-button>
              }
            </mat-radio-group>
          } @else {
            <div class="milsym-form-field">
              <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                @if (field.type === 'select') {
                  <mat-select formControlName="{{ field.name }}">
                    @for (option of field.options; track $index) {
                      <mat-option [value]="option">{{ option }}</mat-option>
                    }
                  </mat-select>
                } @else if (field.type === 'text') {
                  <input
                    formControlName="{{ field.name }}"
                    matInput
                    type="text"
                  />
                }
              </mat-form-field>
            </div>
          }
        }
      </div>
    </form>
  </div>
</div>
