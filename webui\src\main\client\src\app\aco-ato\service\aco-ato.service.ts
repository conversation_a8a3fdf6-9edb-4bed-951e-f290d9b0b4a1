import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable( {providedIn: 'root'})
export class AcoAtoService {
  constructor(private http: HttpClient) {}

  saveRapTime(rapTime: number): Observable<any> {
    return this.http.post('http://localhost:8086/simulation/time', rapTime, {
      headers: new HttpHeaders({ 'Content-type': 'application/json' }),
      responseType: 'text'
    });
  }
  uploadFiles(payload){
    return this.http.post('http://localhost:8086/simulation/load', payload)
  }

}
