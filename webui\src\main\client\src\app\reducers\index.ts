import { ActionReducerMap, MetaReducer } from '@ngrx/store';

import { environment } from '../../environments/environment';
import * as fromCursit from '../cursit/reducers/cursit.reducer';

import * as fromMouseMove from '../gis/interface/reducers/coordinate.reducer';
import * as fromElementSelection from '../gis/interface/reducers/element-selection.reducer';
import * as fromScaleIndicator from '../gis/interface/reducers/scale-indicator.reducer';
import { componentStateReducer } from './component-store.reducer';

export interface State {
  cursit: fromCursit.State;
  mousemove: fromMouseMove.MouseCoordinateState;
  scaleIndicator: fromScaleIndicator.ScaleIndicatorState;
  elementSelection: fromElementSelection.MapElementSelectionState;
  componentStores?: any
}

export const reducers: ActionReducerMap<State> = {
  cursit: fromCursit.reducer,
  mousemove: fromMouseMove.reducer,
  scaleIndicator: fromScaleIndicator.reducer,
  elementSelection: fromElementSelection.reducer,
  ...(environment.production ? {} : { componentStores: componentStateReducer })
};

export const metaReducers: MetaReducer<State>[] = !environment.production ? [] : [];

