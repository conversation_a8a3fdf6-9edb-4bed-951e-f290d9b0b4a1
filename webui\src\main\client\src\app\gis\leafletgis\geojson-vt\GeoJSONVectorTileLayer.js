/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/16/2024, 10:27 AM
 */

import geojsonvt from 'geojson-vt';
import * as L from 'leaflet';

export const GeoJSONVectorTileLayer = L.GridLayer.extend({
  options: {
    async: false,
  },

  initialize: function (geojson, options) {
    L.setOptions(this, options);
    L.GridLayer.prototype.initialize.call(this, options);
    this.tileIndex = geojsonvt(geojson, this.options);
  },

  createTile: function (coords) {
    // create a <canvas> element for drawing
    const tile = L.DomUtil.create("canvas", "leaflet-tile");
    // setup tile width and height according to the options
    const size = this.getTileSize();
    tile.width = size.x;
    tile.height = size.y;
    // get a canvas context and draw something on it using coords.x, coords.y and coords.z
    const ctx = tile.getContext("2d");
    // return the tile so it can be rendered on screen
    const tileInfo = this.tileIndex.getTile(coords.z, coords.x, coords.y);
    const features = tileInfo ? tileInfo.features : [];
    for (let i = 0; i < features.length; i++) {
      const feature = features[i];
      this.drawFeature(ctx, feature);
    }
    return tile;
  },

  drawFeature: function (ctx, feature) {
    let p;
    let j;
    const typeChanged = type !== feature.type,
      type = feature.type;
    ctx.beginPath();
    if (this.options.style) this.options.style instanceof Function ? this.setStyle(ctx, this.options.style(feature.tags)) : this.setStyle(ctx, this.options.style);
    if (type === 2 || type === 3) {
      for (j = 0; j < feature.geometry.length; j++) {
        const ring = feature.geometry[j];
        for (let k = 0; k < ring.length; k++) {
          p = ring[k];
          if (k) ctx.lineTo(p[0] / 16.0, p[1] / 16.0);
          else ctx.moveTo(p[0] / 16.0, p[1] / 16.0);
        }
      }
    } else if (type === 1) {
      for (j = 0; j < feature.geometry.length; j++) {
        p = feature.geometry[j];
        ctx.arc(p[0] / 16.0, p[1] / 16.0, 2, 0, Math.PI * 2, true);
      }
    }
    if (type === 3) ctx.fill(this.options.style.fillRule || "evenodd");

    ctx.stroke();
  },

  setStyle: function (ctx, style) {
    let color;
    const stroke = style.stroke || true;
    if (stroke) {
      ctx.lineWidth = style.weight || 5;
      color = this.setOpacity(style.color, style.opacity);
      ctx.strokeStyle = color;
    } else {
      ctx.lineWidth = 0;
      ctx.strokeStyle = {};
    }
    const fill = style.fill || true;
    if (fill) {
      ctx.fillStyle = style.fillColor || "#03f";
      color = this.setOpacity(style.fillColor, style.fillOpacity);
      ctx.fillStyle = color;
    } else {
      ctx.fillStyle = {};
    }
  },

  setOpacity: function (color, opacity) {
    if (opacity) {
      var color = color || "#03f";
      if (color.iscolorHex()) {
        var colorRgb = color.colorRgb();
        return (
          "rgba(" +
          colorRgb[0] +
          "," +
          colorRgb[1] +
          "," +
          colorRgb[2] +
          "," +
          opacity +
          ")"
        );
      } else {
        return color;
      }
    } else {
      return color;
    }
  },
})



String.prototype.iscolorHex = function () {
  const sColor = this.toLowerCase();
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  return reg.test(sColor);
};

String.prototype.colorRgb = function () {
  let i;
  let sColor = this.toLowerCase();
  if (sColor.length === 4) {
    let sColorNew = "#";
    for (i = 1; i < 4; i += 1) {
      sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
    }
    sColor = sColorNew;
  }
  const sColorChange = [];
  for (i = 1; i < 7; i += 2) {
    sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
  }
  return sColorChange;
};
