import { Coordinate } from '../../cursit/models/cursit.model';
import { ShapeType } from '../interface/models/map-element.model';
import { LeafletMap } from './LeafletMap';
import { LeafletGraphics } from './LeafletGraphics';
import { LeafletMapDisplay } from './LeafletMapDisplay';

/**
 * LeafletFanArea class for rendering fan areas on a Leaflet map
 * Extends LeafletGraphics to handle fan area rendering based on BattleSpaceFanArea data
 */

export class LeafletFanArea extends LeafletGraphics {
  /**
   * Creates a new LeafletFanArea instance
   *
   * @param id Unique identifier for the fan area
   * @param map LeafletMap instance to render on
   * @param type ShapeType of the fan area
   * @param catCode Category code for styling
   * @param coordinates Array of coordinates forming the fan area
   * @param options  object containing additional properties
   */
  constructor(
    id: string,
    map: LeafletMapDisplay,
    type: ShapeType,
    catCode: string,
    coordinates: Coordinate[],
    label: string,
    options?: {
      maximumRange?: number;
      minimumRange?: number;
      orientationAngle?: number;
      sectorAngle?: number;
    }
  ) {
    super(id, map, type, catCode, coordinates, label, options);

    // Create an arc
    this.drawFanArea();
  }

  private drawFanArea(): void {
    // Clear existing graphics
    this._graphicsElement.clear();

    const centerLon = this._locations[0][1];
    const centerLat = this._locations[0][0];

    /**
     * The data source uses Luciad 0 degrees as East, but Leaflet uses 0 degrees as North.
     * This means we need to add 90 degrees to the orientation angle and subtract 70.5 degrees (Offset)
     * to convert to Leaflet's coordinate system. To achieve a 1 to 1 mapping, we need to add 360 degrees
     * and take the modulus with 360 degrees.
     */
    const orientationDeg =
      (this._options.orientationAngle + 90 - 70.5 + 360) % 360;
    const sectorDeg = this._options.sectorAngle;

    const minRange = this._options.minimumRange;
    const maxRange = this._options.maximumRange;

    // Determine start and end bearing (fan edges)
    const startBearing = orientationDeg - sectorDeg / 2;
    const endBearing = orientationDeg + sectorDeg / 2;

    // Arc resolution (more steps = smoother arc)
    const steps = 64;

    // Stroke scaling
    const scale = this.getZoomScale(this._map.map.getZoom());
    const adjustedStrokeWidth = this._strokeWidth / scale;

    // Build outer arc points (maxRange)
    const outerPoints: { x: number; y: number }[] = [];
    for (let i = 0; i <= steps; i++) {
      const fraction = i / steps;
      const bearing = startBearing + fraction * (endBearing - startBearing);

      // Geodesic helper to compute destination point
      const { lat, lon } = this.computeDestinationPoint(
        centerLat,
        centerLon,
        maxRange,
        bearing
      );
      // Project to map coordinates
      const projected = this._map.map.projectFromLatLonToPoint([lat, lon]);
      outerPoints.push({ x: projected.x, y: projected.y });
    }

    // Build inner arc points (minRange), in reverse order
    const innerPoints: { x: number; y: number }[] = [];
    for (let i = steps; i >= 0; i--) {
      const fraction = i / steps;
      const bearing = startBearing + fraction * (endBearing - startBearing);

      const { lat, lon } = this.computeDestinationPoint(
        centerLat,
        centerLon,
        minRange,
        bearing
      );
      const projected = this._map.map.projectFromLatLonToPoint([lat, lon]);
      innerPoints.push({ x: projected.x, y: projected.y });
    }

    const allPoints = [...outerPoints, ...innerPoints];

    this._graphicsElement.poly(allPoints, true);

    // Need fill to handle element selection
    this._graphicsElement.fill({
      color: this.getFillColorFromCatCode(this._catCode),
      alpha: 0.01
    });

    // Stroke
    this._graphicsElement.stroke({
      width: adjustedStrokeWidth,
      color: this.selected
        ? 'red'
        : this.getFillColorFromCatCode(this._catCode),
      alignment: 0.5
    });
  }

  redraw(): void {
    this.drawFanArea();
    this.addLabel();
  }

  setSpeedAndBearing(speed: number, bearing: number): void {
    // throw new Error('Method not implemented.');
  }

  setSymbolCode(symbolCode: string): void {
    // throw new Error('Method not implemented.');
  }
}
