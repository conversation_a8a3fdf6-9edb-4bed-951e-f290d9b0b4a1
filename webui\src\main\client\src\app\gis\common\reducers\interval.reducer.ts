/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/14/2023, 11:47 AM
 */

import { createFeature, createReducer, createSelector, on } from '@ngrx/store';

import { layerCommand, setLayerRedrawInterval } from '../actions/interval.actions';

export const RedrawMode = {
  paused: 'Paused',
  running: 'Running'
} as const;

export type Mode = typeof RedrawMode[keyof typeof RedrawMode];

export interface RedrawIntervalState {
  redrawInterval: number;
  mode: Mode;
}

export const initialState: RedrawIntervalState = {
  redrawInterval: 1000,
  mode: 'Running'
};

export const layerRedrawIntervalFeature = createFeature({
  name: 'redrawInterval',
  reducer: createReducer(
    initialState,
    on(setLayerRedrawInterval, (state, action) => ({ ...state, redrawInterval: action.payload })),
    on(layerCommand, (state, action) => ({ ...state, mode: action.payload === 'Pause' ? RedrawMode.paused : RedrawMode.running }))
  ),
  extraSelectors: ({ selectRedrawIntervalState }) => ({
    getRedrawInterval: createSelector(
      selectRedrawIntervalState,
      state => (state.mode === RedrawMode.running
        ? state.redrawInterval
        : 86400000)
    )
  })
});

export const {
  name,
  reducer,
  selectRedrawIntervalState,
  selectRedrawInterval,
  getRedrawInterval
} = layerRedrawIntervalFeature;

