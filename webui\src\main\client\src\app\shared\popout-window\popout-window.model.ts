import { ComponentType } from '@angular/cdk/overlay';
import { Component, EventEmitter, HostListener, InjectionToken, OnDestroy } from '@angular/core';
import { PopoutWindowService } from './popout-window.service';

export type PopoutDialogType<C extends IPopoutWindowModal> = ComponentType<C>;
export const POPOUT_DIALOG_TYPE = <C extends IPopoutWindowModal>() =>  new InjectionToken<PopoutDialogType<C>>('POPOUT_DIALOG_DATA');
export const POPOUT_WINDOW_DATA = new InjectionToken<any>('POPOUT_WINDOW_DATA');
export const IS_POPOUT_WINDOW = new InjectionToken<boolean>('IS_POPOUT_WINDOW');

export interface PopoutWindowConfig<T = any> {
  title?: string;
  injectorToken?: InjectionToken<T>;
  data?: T;
  suppressClonedStyles?: boolean;
  windowStyleUrl?: string;
  windowStyle?: string;
  rect?: PopoutWindowRect;
}

export interface IPopoutWindowModal {
  isPopoutWindow: boolean;
  onClose: EventEmitter<any>;
}

export interface PopoutWindowRect {
  height: number;
  width: number;
  x: number;
  y: number;
}

export interface AbstractPopoutWindow<T, C extends IPopoutWindowModal> {
  afterClosed?(): void;
  afterOpened?(): void;
}

export interface IPopoutWindow {
  window: Window | null;
  document: Document | null;
  observer: MutationObserver | undefined;
  observeStyleChanges: () => void;
  toggleDarkMode: (darkMode: boolean) => void;
}

@Component({
    template: '',
    standalone: false
})
export abstract class AbstractPopoutWindow<T, C extends IPopoutWindowModal> implements OnDestroy {
  private _window: Window | null = null;
  set window(w: Window | null) {
    this._window = w;
    if (this._window) {
      this._window.addEventListener('unload', () => this.close());
    }
  }

  get window(): Window | null {
    return this._window;
  }

  get document(): Document | undefined {
    return this._window?.document;
  }

  private _observer: MutationObserver | undefined;
  set observer(o: MutationObserver | undefined) {
    this._observer = o;
  }

  get observer(): MutationObserver | undefined {
    return this._observer;
  }

  private _config: PopoutWindowConfig<T> | undefined;
  public set config(config: PopoutWindowConfig | undefined) {
    this._config = config;
  }

  public get config(): PopoutWindowConfig<T> | undefined {
    return this._config;
  }

  @HostListener('window:beforeunload')
  private beforeunloadHandler(): void {
    this.close();
  }

  protected constructor() {
    PopoutWindowService.getDarkMode().subscribe(darkMode => this.toggleDarkMode(darkMode));
  }

  abstract open(component?: ComponentType<C>, config?: PopoutWindowConfig<T>): void;

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.close();
  }

  close(): void {
    if (this._window) {
      this._window.close();
      this._window = null;
    }
    if (this.afterClosed) {
      this.afterClosed();
    }
  }

  toggleDarkMode(darkMode: boolean): void {
    // if (this._window != null) {
    //   if (darkMode) {
    //     this._window.document.body.classList.add(DARK_CLASS_NAME);
    //   } else {
    //     this._window.document.body.classList.remove(DARK_CLASS_NAME);
    //   }
    // }
  }

  observeStyleChanges(): void {
    if (this._observer) {
      this._observer.disconnect();
    }
    this._observer = PopoutWindowService.createStyleObserver(this._window);
  }
}

// Dummy PopoutWindow to instantiate class.
export class PopoutWindow<T = any, C extends IPopoutWindowModal = any> extends AbstractPopoutWindow<T, C> {
  component: ComponentType<C>;

  constructor() {
    super();
  }

  open(component?: ComponentType<C>, config?: PopoutWindowConfig<T>): void {
    this.component = component;
    super.config = config;
  }
}
