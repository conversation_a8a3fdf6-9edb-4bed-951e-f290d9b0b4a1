:host {
  position: relative;
  display: grid;
  grid-template-rows: min-content min-content auto min-content min-content min-content;
  height: 100%;
  container-type: inline-size;
  gap: 5px;
}

.example-card {
  max-width: 400px;
}

.example-action-buttons {
  padding-bottom: 20px;
}

.conn-container {
  height: 100%;
  overflow-y: auto;
}

.conn-toggle {
  margin: 10px;
}

mat-card-subtitle {
  width: 200px;
}

.conn-expand-button {
  float: right;
  position: relative;
  right: 2px;
}

.conn-progress-indicator {
  background-color: rgba(255, 255, 255, 0.2);
  box-sizing: border-box;
  border-radius: 5px;
  height: 36px;
  width: 100%;
}

::ng-deep .mat-mdc-card-header-text {
  width: 100%;
}

.control-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 5px;
}

.control-buttons.expanded {
  position: absolute;
  flex-direction: row;
  flex-wrap: nowrap;
  top: 0;
}

.control-buttons.expanded.left {
  right: 0;
  flex-direction: row-reverse;
}

.control-buttons.expanded.right {
  left: 0;
}

.align-right {
  text-align: right;
}

.connection-actions {
  padding-bottom: 0;
}

.connection-actions button {
  width: 100%
}

.header-actions {
 width: 100%;
}

.header-actions button {
  width: 100%;
}

@container (max-width: 100px) {

  mat-card-title,
  mat-card-subtitle,
  mat-card-header {
    display: none;
  }

  mat-card-content {
    padding: 0 5px;
  }

  .conn-expand-button {
    float: unset !important;
    right: 0;
  }

  mat-card-content.left {
    transform: translate(-2px, 0);
  }

  mat-card-content.right {
    transform: translate(3px, 0);
  }

  .connection-actions {
    padding: 0;
    margin: 0 auto;
  }

  .connection-actions span {
    display: none;
  }

  .connection-actions button {
    min-width: 52px;
  }

  .connection-actions mat-icon {
    margin-left: 0;
    margin-right: 0;
  }

  .connection-actions.right {
    margin-left: 7px;
  }

  .popout-button {
    display: none;
  }
}
