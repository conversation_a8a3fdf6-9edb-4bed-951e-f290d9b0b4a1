.node-table-container {
  max-height: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: 1fr auto;
  overflow: hidden;
}

.host-selection {
  min-width: 100%;

  mat-select {
    border: solid 1px var(--divider-light);
    padding: 5px;
    border-radius: 5px;
  }
}

::ng-deep .node-table {
  overflow: auto;

  thead {
    background-color: var(--secondary-background-dark) !important;
  }

  .component-name {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      color: var(--text);
      stroke: var(--text);
      fill: var(--text);
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 15px;
    color: var(--text-secondary);
    width: min-content;
    height: min-content;
    border: solid 2px;

    &.on {
      background-color: rgba(var(--success-rgb), var(--opacity-low));
      border-color: var(--success);

      mat-icon {
        color: var(--success);
      }
    }

    &.error,
    &.timed_out {
      background-color: rgba(var(--error-rgb), var(--opacity-low));
      border-color: var(--error);

      mat-icon {
        color: var(--error);
      }
    }

    &.off {
      border-color: var(--text-secondary);

      mat-icon {
        color: var(--text-secondary);
      }
    }

    &.unlicensed {
      background-color: rgba(var(--error-dark-rgb), var(--opacity-low));
      border-color: var(--error-dark);

      mat-icon {
        color: var(--error-dark);
      }
    }

    &.starting,
    &.terminating {
      background-color: rgba(var(--pending-rgb), var(--opacity-low));
      border-color: var(--pending);

      .mat-mdc-progress-spinner circle,
      .mat-mdc-spinner circle {
        stroke: var(--pending);
      }
    }
  }
}

.start-button,
.stop-button {
  background-color: transparent;
  box-shadow: none;
  border-radius: 5px;
  height: 40px;
}

.start-button {
  color: var(--success);
  border: solid 1px var(--success);
}

.stop-button {
  color: var(--error);
  border: solid 1px var(--error);
}

.table-footer {
  display: flex;
  justify-content: space-between;
  background-color: rgba(var(--secondary-background-dark-rgb), var(--opacity));
  padding: 12px;

  .table-actions {
    display: flex;
    gap: 10px
  }

  .system-stats {
    display: flex;

    .stat-item {
      display: flex;
      margin-left: 24px;
      align-items: center;

      .stat-label {
        margin-right: 8px;
        color: #aaa;
      }

      .stat-value {
        font-weight: 500;
        font-size: 16px;

        &.success {
          color: var(--success);
        }

        &.error {
          color: var(--error);
        }

        &.pending {
          color: var(--pending);
        }
      }
    }
  }
}

.distributed-mode-button {
  background-color: var(--midnight-blue) !important;
  color: var(--border) !important;

  mat-icon {
    color: var(--border) !important;
    stroke: var(--border) !important;
  }
}

.distributed-mode-button.active {
  background-color: var(--active) !important;
  color: var(--text) !important;

  mat-icon {
    color: var(--text) !important;
    stroke: var(--text) !important;
  }
}
