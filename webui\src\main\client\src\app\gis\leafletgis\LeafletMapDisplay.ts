import { Ng<PERSON>one } from '@angular/core';
import { Store } from '@ngrx/store';
import {
  Control,
  FeatureGroup,
  LatLng,
  latLng,
  LatLngBounds,
  latLngBounds,
  LatLngExpression,
  Layer,
  LayerGroup,
  LeafletMouseEvent,
  MapOptions
} from 'leaflet';
import { Container, EventBoundary, Graphics, Point } from 'pixi.js';
import { CursitControllerType } from '../../cursit/models/cursit.model';
import { mapActions } from '../interface/actions/map.actions';
import { MapInputHandler } from '../interface/InputHandler';
import { AbstractMapDisplay } from '../interface/MapDisplay';
import { MapLayer } from '../interface/MapLayer';
import {
  ScaleIndicator,
  Unit
} from '../interface/models/scale-indicator.model';
import {
  MouseCoordinateStd,
  MouseCoordinateType
} from '../interface/MouseCoordinateLocator';
import { GisService } from '../interface/service/gis.service';
import { LeafletElementFactory } from './LeafletElementFactory';
import { LeafletElementHandler } from './LeafletElementHandler';
import { LeafletEntity } from './LeafletEntity';
import { LeafletLayerFactory } from './LeafletLayerFactory';
import { LeafletLayerGroup } from './LeafletLayerGroup';
import { LeafletLayerHandler } from './LeafletLayerHandler';
import { LeafletMap } from './LeafletMap';
import { LeafletMouseCoordinateLocator } from './LeafletMouseCoordinateLocator';
import { LeafletTacticalGraphics } from './LeafletTacticalGraphics';
import { LeafletTacticalGraphicsService } from './service/LeafletTacticalGraphicsService';
import {
  GeoFilterPoints,
  selectActiveGeoArea,
  selectCursitController,
  selectIsGeoDraw
} from '../../cursit/reducers/cursit.reducer';
import 'leaflet-ruler';
import { GISElement } from '../interface/GISElement';
import 'leaflet-draw';
import { cursitActions } from '../../cursit/actions/cursit.actions';
import { BoundingBox } from 'src/app/data/entity/entity.model';
import { MilitarySymbologyContainer } from './leaflet-symbology-layer/MilitarySymbologyContainer';
import { LeafletGraphics } from './LeafletGraphics';
import { LeafletSelectionMenu } from './LeafletSelectionMenu';

export class LeafletMapDisplay extends AbstractMapDisplay<Layer, LayerGroup> {
  leafletMap: LeafletMap;
  dragged = false;
  selectedElement: Container | null;
  selected = false;
  mouseDown = false;
  militarySymbolLayer: MilitarySymbologyContainer;
  startPosition: LatLng | null;
  service: LeafletTacticalGraphicsService;
  rulerControl:
    | (Control & { _toggleMeasure: () => void; _closePath: () => void })
    | null = null;
  geoFilterFeature: FeatureGroup;
  selectionMenuElement: LeafletSelectionMenu;
  currentTarget = [];

  constructor(
    private gisService: GisService,
    store: Store,
    service: LeafletTacticalGraphicsService,
    ngZone: NgZone
  ) {
    super(store, ngZone);

    this.selectionMenuElement = new LeafletSelectionMenu(this.store);

    this.iconSize = 1;
    // Adding new properties to Leaflet Layer
    (Layer.prototype as any).layerName = '';
    (Layer.prototype as any).layerId = '';
    (Layer.prototype as any)._visible = true;
    (Layer.prototype as any).visible = function (visible: boolean): void {
      if (visible) {
        this.removeFrom(this._map);
      } else {
        this.addTo(this._map);
      }
      this._visible = visible;
    };
    this.service = service;
    const centroid: LatLngExpression = [0.0, 0.0];

    this.leafletMap = new LeafletMap('map', {
      // editable: true,
      center: centroid,
      zoom: 3,
      attributionControl: false,
      maxBoundsViscosity: 1.0,
      maxZoom: 21,
      minZoom: 2.5,
      zoomControl: false
    });

    //todo this section deals with drawing geo filter areas. Should be moved to be generic with GIS service
    this.geoFilterFeature = new L.FeatureGroup();
    this.leafletMap.addLayer(this.geoFilterFeature);

    // listener for when rectangle is being drawn
    this.leafletMap.on((L as any).Draw.Event.CREATED, (event: any) => {
      this.geoFilterFeature.clearLayers();

      const layer = event.layer;
      // Add the drawn rectangle to the feature group
      this.geoFilterFeature.addLayer(layer);
      if (layer instanceof L.Rectangle) {
        const bounds = layer.getBounds();
        const geoFilterPoints: GeoFilterPoints = {
          p1: {
            latitude: bounds.getNorthEast().lat,
            longitude: bounds.getNorthEast().lng
          },
          p2: {
            latitude: bounds.getSouthWest().lat,
            longitude: bounds.getSouthWest().lng
          }
        };
        this.store.dispatch(cursitActions.geoFilterDrawn(geoFilterPoints));
      }
    });

    // when draw stops remove drawing ability
    this.leafletMap.on((L as any).Draw.Event.DRAWSTOP, (event: any) => {
      this.store.dispatch(cursitActions.toggleGeoDraw());
    });

    // add active geo area to cursit, clear the active geo filter layer everytime
    this.store.select(selectActiveGeoArea).subscribe((area) => {
      this.geoFilterFeature.clearLayers();

      if (area) {
        const bounds = L.latLngBounds(
          new LatLng(area.p1.latitude, area.p1.longitude),
          new LatLng(area.p2.latitude, area.p2.longitude)
        );
        const rectangleArea = L.rectangle(bounds);
        rectangleArea.addTo(this.geoFilterFeature);
      }
    });

    const drawRectangle = new (L as any).Draw.Rectangle(this.leafletMap);
    drawRectangle.setOptions({ showArea: false });
    this.store.select(selectIsGeoDraw).subscribe((isActive) => {
      if (isActive) {
        drawRectangle.enable();
      }
    });

    this.store.select(selectCursitController).subscribe((controller) => {
      switch (controller.controllerType) {
        case CursitControllerType.BulkSelect:
          this.leafletMap.dragging.disable();
          this.leafletMap.doubleClickZoom.disable();
          this.leafletMap.boxZoom.disable();
          this.leafletMap.keyboard.disable();
          break;
        case CursitControllerType.Pan:
          this.leafletMap.dragging.enable();
          this.leafletMap.doubleClickZoom.enable();
          this.leafletMap.boxZoom.enable();
          this.leafletMap.keyboard.enable();
          break;
        case CursitControllerType.Measuring:
          this.enableRuler();
          break;
        default:
          break;
      }
      this.checkMeasuring();
    });

    this.leafletMap.whenReady(() => {
      setTimeout(() => {
        this.leafletMap.pixiContainer
          ?.getContainer()
          .addChild(this.selectionMenuElement);
      }, 1000);

      this.updateScaleIndicator();
      this.militarySymbolLayer?.redraw();
    });

    this.leafletMap.on('move', () => {
      if (this.selectedElement) {
        if (this.selectedElement instanceof LeafletEntity) {
          const { x, y } = this.leafletMap.latLngToContainerPoint(
            this.selectedElement.location
          );
          this.store.dispatch(
            mapActions.mapElementSelection({
              elementId: this.selectedElement.id,
              screenCoordinates: {
                x,
                y
              },
              coordinates: [
                [
                  this.selectedElement.location[0],
                  this.selectedElement.location[1]
                ]
              ]
            })
          );
        } else if (
          this.selectedElement instanceof LeafletTacticalGraphics ||
          this.selectedElement instanceof LeafletGraphics
        ) {
          const coordinates = this.selectedElement.locationsAsCoordinate;
          const { x, y } = this.leafletMap.latLngToContainerPoint(
            coordinates[0]
          );
          this.store.dispatch(
            mapActions.mapElementSelection({
              elementId: this.selectedElement.id,
              screenCoordinates: {
                x,
                y
              },
              coordinates
            })
          );
        }
      }
      this.updateScaleIndicator();
    });

    const southWest = latLng(-89.98155760646617, -180);
    const northEast = latLng(89.99346179538875, 180);
    const bounds = latLngBounds(southWest, northEast);
    this.leafletMap.setMaxBounds(bounds);

    this.leafletMap.on('drag', () => {
      this.leafletMap.panInsideBounds(bounds, { animate: false });
    });

    this.elementFactory = new LeafletElementFactory(this);
    this.layerFactory = new LeafletLayerFactory(
      this.gisService,
      this,
      this.service
    );
    this.layerHandler = new LeafletLayerHandler(this);
    this.onRouteClick = (clickData) => {
      this.gisService.setRouteClickData(clickData);
    };
    this.inputHandler = new MapInputHandler(this);
    this.elementHandler = new LeafletElementHandler(this);

    const mouseClick = (event: LeafletMouseEvent): void => {
      this.selected = false;
      this.dragged = false;
      this.currentTarget = [];

      this.leafletMap.eachLayer((layer) => {
        if (layer instanceof MilitarySymbologyContainer) {
          //if on select mode then deselect all entities when you click the cursit screen
          if (
            this.currentController.controllerType ===
            CursitControllerType.BulkSelect
          ) {
            this.leafletMap.dragging.disable();
            this.startPosition = event.latlng;
            let deselectedElements = false;
            layer.getSymbolsArray().forEach((symbol) => {
              if (symbol instanceof LeafletEntity && symbol.selected) {
                symbol.selected = false;
                deselectedElements = true;
              }
            });

            layer.leafletGraphicsList.forEach((graphic) => {
              if (graphic instanceof LeafletGraphics && graphic.selected) {
                graphic.selected = false;
                deselectedElements = true;
              }
            });
            //if any elements were deselected then remove all entitiy details boxes and redraw
            if (deselectedElements) {
              this.store.dispatch(
                mapActions.mapElementSelection({
                  elementId: undefined,
                  screenCoordinates: undefined,
                  coordinates: undefined
                })
              );
            }
            layer.redraw();
          }
        }
      });

      //if in select mode on cursit then select all the entities within the selection bounds drawn
      if (
        this.currentController.controllerType ===
          CursitControllerType.BulkSelect &&
        this.startPosition
      ) {
        const selectionBounds = new LatLngBounds(
          this.startPosition,
          event.latlng
        );

        this.leafletMap.eachLayer((layer) => {
          if (layer instanceof MilitarySymbologyContainer) {
            layer.getSymbolsArray().forEach((symbol) => {
              if (symbol instanceof LeafletEntity) {
                if (selectionBounds.contains(symbol.location)) {
                  symbol.selected = true;
                }
              }
            });

            layer.leafletGraphicsList.forEach((graphic) => {
              if (
                graphic instanceof LeafletGraphics &&
                selectionBounds.contains(graphic.locations[0])
              ) {
                graphic.selected = true;
              }
            });

            layer.redraw();
            this.startPosition = null;
          }
        });
      }

      //get all the elements that have been hit on each layer
      this.leafletMap.eachLayer((layer) => {
        if (layer instanceof MilitarySymbologyContainer) {
          const targetInfo = layer.getHitElement(event);
          if (targetInfo) {
            this.currentTarget.push(targetInfo);
          }
        }
      });

      if (
        this.getCurrentController().controllerType === CursitControllerType.Pan
      ) {
        //if an element was hit by the pointer event
        //if cursit is in pan mode then select the first element in the list of targets
        if (this.currentTarget.length !== 0) {
          const { target, layer } = this.currentTarget[0];
          this.selectedElement = target;

          if (target instanceof LeafletEntity) {
            target.selected = true;

            this.selected = true;
            const containerPoint = this.leafletMap.projectFromLatLonToPoint(
              target.location
            );

            this.selectionMenuElement.visible = true;
            this.selectionMenuElement.position.set(
              containerPoint.x,
              containerPoint.y
            );

            this.store.dispatch(
              mapActions.mapElementSelection({
                elementId: target.id,
                screenCoordinates: {
                  x: containerPoint.x,
                  y: containerPoint.y
                },
                coordinates: [[target.location[0], target.location[1]]]
              })
            );

            target.bringToFront();
            layer.redraw();
          } else if (target instanceof LeafletTacticalGraphics) {
            this.store.dispatch(
              mapActions.mapElementSelection({
                elementId: target.id,
                screenCoordinates: {
                  x: event.containerPoint.x,
                  y: event.containerPoint.y
                },
                coordinates: target.projectedLocations.map((location) => [
                  location[0],
                  location[1]
                ])
              })
            );
          } else if (target instanceof LeafletGraphics) {
            target.selected = true;

            this.selected = true;
            const containerPoint = this.leafletMap.projectFromLatLonToPoint(
              target.locations[target.locations.length - 1]
            );

            this.selectionMenuElement.visible = true;
            this.selectionMenuElement.position.set(
              containerPoint.x,
              containerPoint.y
            );

            this.store.dispatch(
              mapActions.mapElementSelection({
                elementId: target.id,
                screenCoordinates: {
                  x: event.containerPoint.x,
                  y: event.containerPoint.y
                },
                coordinates: target.projectedLocations.map((location) => [
                  location[0],
                  location[1]
                ])
              })
            );

            target.bringToFront();
            layer.redraw();
          }

          // After successfully selecting an entity, show the menu
          // provide a position value to the menu
          //
          this.store.dispatch(mapActions.showEntitySelectionMenu());
        } else if (!this.dragged) {
          this.selected = false;
          if (this.currentTarget.length === 0) {
            this.selectionMenuElement.visible = false;

            this.store.dispatch(
              mapActions.mapElementSelection({
                elementId: undefined,
                screenCoordinates: undefined,
                coordinates: undefined
              })
            );
          }

          // Hide menu when clicking on empty area (only when not dragging)
          this.store.dispatch(mapActions.hideEntitySelectionMenu());
        } else if (this.dragged) {
          if (this.currentTarget && this.currentTarget.length !== 0) {
            const { target } = this.currentTarget[0];

            if (
              target instanceof LeafletEntity ||
              target instanceof LeafletGraphics
            ) {
              this.entitySelected(target);
            }
          }
          // If dragged and finished dragging, show entity selection menu
          this.store.dispatch(mapActions.showEntitySelectionMenu());
        }
      }

      if (this.mouseDown && this.selectedElement) {
        this.mouseDown = false;
        this.selectedElement.alpha = 1.0;
        this.currentTarget[0].layer.redraw();
        this.currentTarget[0].layer.redrawing = false;
      }

      // Always reset dragged state at the end
      this.dragged = false;
    };

    const mouseDrag = (_: LeafletMouseEvent): void => {
      // for experimental use
      if (!this.dragged) {
        this.dragged = true;

        // Hide entity selection menu when dragging starts
        this.store.dispatch(mapActions.hideEntitySelectionMenu());
      }
    };

    this.leafletMap.on('click', mouseClick);

    this.leafletMap.on('drag', mouseDrag);

    this.mouseCoordinateLocator = new LeafletMouseCoordinateLocator(this);
    this.mouseCoordinateLocator.startMouseCoordinateLocator();
    this.mouseCoordinateLocator.setCoordinateType(MouseCoordinateStd.MGRS);

    this.gridType = 'MGRS';
    const rootLayer = new LeafletLayerGroup(
      this,
      this.leafletMap.getRootLayer()
    );
    rootLayer.layerName = 'rootLayer';
    rootLayer.layerId = '-11';
    rootLayer.layerType = 'Parent';
    rootLayer.setVisible(true);
    rootLayer.parentId = '-111111';
    this.layerHandler.addLayer(rootLayer);
    this.layerHandler.rootLayer = rootLayer;

    // this.leafletMap.on('layeradd', this.onLayerAdd);

    // const drawingButtonsDiv = document.querySelector('.leaflet-pm-draw');
    //
    // if (drawingButtonsDiv?.children) {
    //   const buttonContainers = Array.from(drawingButtonsDiv.children) as HTMLDivElement[];
    //   for (const buttonContainer of buttonContainers) {
    //     buttonContainer.firstChild?.addEventListener('mousedown', (e) => {
    //       this.militarySymbolLayer.bringToFront();
    //       this.militarySymbolLayer.setOptions({pane: 'markerPane'});
    //       this.militarySymbolLayer.redraw();
    //     });
    //   }
    // }
    //
    // const editButtonsDiv = document.querySelector('.leaflet-pm-edit');
    //
    // if (editButtonsDiv?.children) {
    //   const buttonContainers = Array.from(editButtonsDiv.children) as HTMLDivElement[];
    //   for (const buttonContainer of buttonContainers) {
    //     buttonContainer.firstChild?.addEventListener('mousedown', (e) => {
    //       if (buttonContainer.title === 'Drag Layers'
    //       || buttonContainer.title === 'Remove Layers') {
    //         let pixiOverlayDiv = this.militarySymbolLayer.
    //         getPane()?.querySelector('.leaflet-pixi-layer');
    //         if ((pixiOverlayDiv as HTMLDivElement).style) {
    //           (pixiOverlayDiv as HTMLDivElement).style.zIndex = '101';
    //         }
    //       }
    //     });
    //   }
    // }
  }

  setMouseCoordinateType(mouseCoordinateType: MouseCoordinateType): void {
    if (this.mouseCoordinateLocator) {
      this.mouseCoordinateLocator.setCoordinateType(mouseCoordinateType);
    }
  }

  override addSymbolOnMapClick(): void {
    // const idCounter = 0;

    const layer: MapLayer<Layer> | undefined =
      this.layerFactory.getLayersHash()['Military Symbol Layer'];
    if (layer) {
      // const getRandomCode = (): string => {
      //   const infantry = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCI---*****`;
      //   const engineer = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUCE---*****`;
      //   const medic = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSM---*****`;
      //   const maintenance = `S${['F', 'H', 'U', 'N'][Math.floor(Math.random() * 4)]}GPUSX---*****`;
      //   return [infantry, engineer, medic, maintenance][
      //     Math.floor(Math.random() * 4)
      //   ];
      // };
      // const mouseClick = (event: LeafletMouseEvent): void => {
      //   // console.log(event.originalEvent);
      //   console.log(`map display'den${this.selected}`);
      //   if (!this.selected) {
      //     idCounter += 1;
      //     const element = this.elementFactory.createMilitaryElement(
      //       getRandomCode(),
      //       idCounter.toString(),
      //       idCounter.toString(),
      //       SHAPE_TYPE.POINT,
      //       [event.latlng.lng,
      //         event.latlng.lat]
      //     );
      //     if (element) {
      //       layer?.addElement(element);
      //     }
      //   }
      //   // display.getMap().off('click', mouseClick);
      // };
      //
      // this.map.on('preclick', mouseClick);
    }
  }

  setMilitaryElementIconSize(iconSize: number): void {
    let iconSizeNew = iconSize;
    if (iconSizeNew === 0) {
      iconSizeNew = 0.01;
    }
    this.iconSize = 2 * (iconSizeNew / 100);
    this.layerHandler.redrawAllLayers();
    this.map.redraw();
  }

  centerMapOnSelectedElement(): void {
    if (this.selectedElement instanceof LeafletEntity) {
      this.leafletMap.panTo(this.selectedElement.location);
    } else if (
      this.selectedElement instanceof LeafletTacticalGraphics ||
      this.selectedElement instanceof LeafletGraphics
    ) {
      this.leafletMap.panTo(this.selectedElement.getLocation(0));
    }
  }

  private checkMeasuring(): void {
    if (
      this.getCurrentController().controllerType !==
      CursitControllerType.Measuring
    ) {
      this.disableRuler();
    }
  }

  get map(): LeafletMap {
    return this.leafletMap;
  }

  set map(map: LeafletMap) {
    this.leafletMap = map;
  }

  setMapOptions(mapOptions: MapOptions): void {
    this.leafletMap.options = mapOptions;
  }

  override syncController(): void {
    // throw new Error('Method not implemented.', { cause: this });
    // To be implemented layer
  }

  // onLayerAdd = (event: any) => {
  //   // console.log(event.layer);
  //   //   console.log('layer added')
  //   //This is important, do ont miss it!!!!!!
  //   let pixiOverlayDiv = this.militarySymbolLayer.getPane()?.querySelector(
  //   '.leaflet-pixi-layer');
  //   if ((pixiOverlayDiv as HTMLDivElement).style) {
  //     (pixiOverlayDiv as HTMLDivElement).style.zIndex = '300';
  //   }
  //
  // }

  override getGISElements(): GISElement<any>[] {
    return this.layerHandler
      .getLayerList()
      .flatMap((l) => Object.values(l?.getElements() ?? []));
  }

  // private getChildLayers(layer: LeafletLayerGroup | LeafletLayer) {
  //   if (layer instanceof LeafletLayerGroup) {
  //
  //   } else {
  //     layer.
  //   }
  // }

  override enableRuler() {
    super.enableRuler();
    if (this.rulerControl) {
      return;
    }
    // Create the ruler control
    this.rulerControl = (L as any).control.ruler();
    this.map.addControl(this.rulerControl);
    this.rulerControl._toggleMeasure();
  }

  override disableRuler() {
    super.disableRuler();
    if (!this.rulerControl) {
      return;
    }
    this.rulerControl._closePath();
    this.rulerControl._toggleMeasure();
    this.map.removeControl(this.rulerControl);
    this.rulerControl = null;
  }

  override setMouseLocation(): void {
    throw new Error('Method not implemented.', { cause: this });
  }

  override updateScaleIndicator(): void {
    const scaleIndicator = this.getScaleIndicator(
      this.leafletMap,
      this.scaleIndicatorMaxWidth
    );
    // Compare saved and updated scale indicators, as MapChange can occur frequently/repeatedly
    if (
      scaleIndicator &&
      JSON.stringify(this.scaleIndicator) !== JSON.stringify(scaleIndicator)
    ) {
      // Dispatch update to scale indicator if there's actually a difference in parameters
      this.store.dispatch(mapActions.scaleIndicator(scaleIndicator));
    }
  }

  update(distanceUnit: Unit | null): ScaleIndicator | null {
    const bounds = this.map.getBounds();
    const centerLat = bounds.getCenter().lat;
    const equatorialRadius =
      6378137 * Math.PI * Math.cos(centerLat * (Math.PI / 180));
    const horizontalDistance =
      (equatorialRadius *
        (bounds.getNorthEast().lng - bounds.getSouthWest().lng)) /
      180;
    const mapSize = this.map.getSize();
    let scaleRatio = 0;
    if (mapSize.x > 0) {
      scaleRatio =
        horizontalDistance * (this.scaleIndicatorMaxWidth / mapSize.x);
    }
    return this.updateScales(distanceUnit, scaleRatio);
  }

  updateScales(distanceUnit: Unit | null, scaleRatio): ScaleIndicator | null {
    if (distanceUnit.uomName === 'Metre' && scaleRatio) {
      return this.updateMetric(scaleRatio);
    }
    if (distanceUnit.uomName === 'MileUS' && scaleRatio) {
      return this.updateImperial(scaleRatio);
    }

    return null;
  }

  updateMetric(scaleMeters): ScaleIndicator | null {
    let scaleValue;
    let secondNumber;
    if (scaleMeters > 500) {
      scaleValue = scaleMeters / 1000;
      secondNumber = LeafletMapDisplay.getRoundNum(scaleValue);
      const barWidthInPixels = this.getScaleWidth(secondNumber / scaleValue);
      return {
        text: `${secondNumber} km`,
        style: {
          width: `${barWidthInPixels}px`,
          left: `${(this.scaleIndicatorMaxWidth - barWidthInPixels) / 2}px`
        }
      };
    }
    scaleValue = LeafletMapDisplay.getRoundNum(scaleMeters);
    const barWidthInPixels = this.getScaleWidth(scaleValue / scaleMeters);
    return {
      text: `${scaleValue} m`,
      style: {
        width: `${barWidthInPixels}px`,
        left: `${(this.scaleIndicatorMaxWidth - barWidthInPixels) / 2}px`
      }
    };
  }

  updateImperial(scaleRatio): ScaleIndicator | null {
    let scaleValue;
    let secondNumber;
    const scaleFeet = 3.2808399 * scaleRatio;
    if (scaleFeet > 2640) {
      scaleValue = scaleFeet / 5280;
      secondNumber = LeafletMapDisplay.getRoundNum(scaleValue);
      const barWidthInPixels = this.getScaleWidth(secondNumber / scaleValue);
      return {
        text: `${secondNumber} mi`,
        style: {
          width: `${barWidthInPixels}px`,
          left: `${(this.scaleIndicatorMaxWidth - barWidthInPixels) / 2}px`
        }
      };
    }
    secondNumber = LeafletMapDisplay.getRoundNum(scaleFeet);
    const barWidthInPixels = this.getScaleWidth(secondNumber / scaleFeet);
    return {
      text: `${secondNumber} ft`,
      style: {
        width: `${barWidthInPixels}px`,
        left: `${(this.scaleIndicatorMaxWidth - barWidthInPixels) / 2}px`
      }
    };
  }

  getScaleWidth(ratio): number {
    return Math.round(this.scaleIndicatorMaxWidth * ratio) - 10;
  }

  static getRoundNum(value): number {
    if (value >= 2) {
      const exponent = Math.floor(value).toString().length - 1;
      const divisor = 10 ** exponent;
      const roundedValue = value / divisor;
      if (roundedValue >= 10) {
        return 10 * divisor;
      }
      if (roundedValue >= 5) {
        return 5 * divisor;
      }
      if (roundedValue >= 3) {
        return 3 * divisor;
      }
      if (roundedValue >= 2) {
        return 2 * divisor;
      }
      return divisor;
    }
    return Math.round(100 * value) / 100;
  }

  getScaleIndicator(
    map: LeafletMap,
    maxWidthPixels: number,
    distanceUnit = this.distanceUnits.METRE
  ): ScaleIndicator | null {
    return this.update(distanceUnit);
  }

  override redraw(): void {
    this.map.redraw();
  }

  zoomIn(): void {
    this.leafletMap.zoomIn();
  }

  zoomOut(): void {
    this.leafletMap.zoomOut();
  }

  fitBounds(bounds: BoundingBox): void {
    this.leafletMap.fitBounds([
      [bounds.south, bounds.west],
      [bounds.north, bounds.east]
    ]);
  }

  containerPointToLatLng(
    containerX: number,
    containerY: number
  ): { lat: number; lng: number } {
    const latLng = this.leafletMap.containerPointToLatLng([
      containerX,
      containerY
    ]);
    return { lat: latLng.lat, lng: latLng.lng };
  }

  getContainerDOM(): HTMLElement {
    return this.leafletMap.getContainer();
  }

  // override getMapCoordinateFromMouseEvent(me: MouseEvent): string {
  //   const mapNodePosition = this.map.getContainer().getBoundingClientRect();
  //   const latLon: LatLng = this.map.containerPointToLatLng(
  //     [me.clientX - mapNodePosition.left, me.clientY - mapNodePosition.top]
  //   );
  //   if (this.coordinateType === 'LatLon') {
  //     return latLonFormatter([latLon.lat, latLon.lng]);
  //   }
  //   return mgrs.forward([latLon.lng, latLon.lat]);
  // }

  unselect() {
    this.selectedElement = null;
    this.selected = false;
    this.leafletMap.eachLayer((layer) => {
      if (layer instanceof MilitarySymbologyContainer) {
        layer.getSymbolsArray().forEach((entity) => {
          if (entity instanceof LeafletEntity) {
            entity.selected = false;
            if (entity.broughtToFront) {
              const markerParent = entity.parent;
              markerParent.removeChild(entity);
              markerParent.addChildAt(entity, entity.originalParentIndex);
              entity.broughtToFront = false;
            }
            entity.removeSelectionBox();
          }
        });
        layer.leafletGraphicsList.forEach((graphic) => {
          if (graphic instanceof LeafletGraphics) {
            graphic.selected = false;
            if (graphic.broughtToFront) {
              const markerParent = graphic.parent;
              markerParent.removeChild(graphic);
              markerParent.addChildAt(graphic, graphic.originalParentIndex);
              graphic.broughtToFront = false;
            }
          }
        });
        layer.redraw();
      }
    });
  }

  entitySelected(entity: LeafletEntity | LeafletGraphics) {
    this.selectedElement = entity;

    if (entity instanceof LeafletEntity) {
      const containerPoint = this.leafletMap.projectFromLatLonToPoint(
        entity.location
      );
      this.selectionMenuElement.position.set(
        containerPoint.x,
        containerPoint.y
      );
    } else if (entity instanceof LeafletGraphics) {
      const containerPoint = this.leafletMap.projectFromLatLonToPoint(
        entity.locations[entity.locations.length - 1]
      );
      this.selectionMenuElement.position.set(
        containerPoint.x,
        containerPoint.y
      );
    }

    entity.select();
    this.mouseDown = false;
    this.dragged = false;
    this.centerMapOnSelectedElement();
  }
}
