/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/21/2023, 1:40 PM
 */

import { LeafletEntity } from '../LeafletEntity';

declare class QuadTree {
  constructor(
    x: number,
    y: number,
    width: number,
    height: number,
    maxObjects: number,
    maxLevels: number
  );

  insert(entity: LeafletEntity): void;

  retrieve(entity: LeafletEntity): LeafletEntity[];
}
