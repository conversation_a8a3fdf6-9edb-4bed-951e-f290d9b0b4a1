import * as milstd from './milstd-2525.model';

export const FunctionIdMappings = {
  // Air-related Function IDs
  'AA': 'Airborne Unit',
  'FW': 'Fixed-Wing Aircraft',
  'RW': 'Rotary-Wing Aircraft',

  // Ground-related Function IDs
  'AR': 'Armor',
  'IN': 'Infantry',
  'AT': 'Anti-Tank',

  // Sea-related Function IDs
  'SS': 'Submarine',
  'CG': 'Cruiser',
  'DD': 'Destroyer',

  // Add more as necessary for different Battle Dimensions or Symbol Sets
} as const;

export type FunctionId = keyof typeof FunctionIdMappings;

export type FunctionDescription = typeof FunctionIdMappings[FunctionId] | 'Unknown';

export interface MILSTD2525C extends milstd.MILSTD2525 {
  description: FunctionDescription;
}

export const getFunctionDescription = (code: string): FunctionDescription => {
  const functionId = code.slice(3, 5);
  return FunctionIdMappings[functionId as FunctionId] || 'Unknown';
}

export const getSymbolInfo = (code: string): MILSTD2525C => {
  const symbolInfo = milstd.getSymbolInfo(code);
  return {
    ...symbolInfo,
    description: getFunctionDescription(code)
  };
};

