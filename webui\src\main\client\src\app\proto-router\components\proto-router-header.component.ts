import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatSelectChange } from '@angular/material/select';

@Component({
    selector: 'vcci-proto-router-header',
    template: `
    <mat-icon style="margin-left: 5px;" [svgIcon]="icon"></mat-icon>
    <span style="margin: 0 5px">{{ title }}</span>
    <ng-container *ngIf="type !== 'router'">
      <flex-block style="min-width: 20px;"/>
      <mat-form-field>
        <mat-label>Services</mat-label>
        <mat-select [multiple]="true" [value]="selectedNodes" (selectionChange)="onSelectionChanged($event)">
          <mat-select-trigger>
            {{includeAllNodes ? 'ALL' : selectedNodes.join(',')}}
          </mat-select-trigger>
          <mat-option [value]="'ALL'">ALL</mat-option>
          <mat-option *ngFor="let node of nodes" [value]="node">{{node}}</mat-option>
        </mat-select>
      </mat-form-field>
    </ng-container>
    <div style="width: 20px"></div>
  `,
    host: {
        style: `
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 13px;
      font-weight: 700;
      height: 80px;
      padding: 10px 0;
    `,
        class: 'custom-font'
    },
    standalone: false
})
export class ProtoRouterHeaderComponent implements OnChanges {
  // TODO: Use a FormControl instead.
  @Input() nodes: string[] = [];
  @Input({ required: true }) type: 'inputs' | 'outputs' | 'router' = 'inputs';

  @Output() onFilterNodes = new EventEmitter<string[]>();

  selectedNodes: string[] = ['ALL'];
  includeAllNodes: boolean = true;

  ngOnChanges(changes: SimpleChanges) {
    if (changes['nodes'] && this.includeAllNodes) {
      this.selectedNodes = ['ALL', ...this.nodes];
      this.onFilterNodes.emit(this.selectedNodes.filter(n => n !== 'ALL'));
    }
  }

  onSelectionChanged(change: MatSelectChange): void {
    const includeAll = change.value.includes('ALL');
    if ((this.includeAllNodes && !includeAll)  || (!this.includeAllNodes && includeAll)) {
      this._toggleAll(includeAll);
    } else if (!includeAll && change.value.length === this.nodes.length) {
      this._toggleAll(true);
    } else if (change.value.length <= this.nodes.length) {
      console.log("NODES", this.selectedNodes);
      this.includeAllNodes = false;
      this.selectedNodes = change.value.filter((n: string) => n !== 'ALL');
    }
    this.onFilterNodes.emit(this.selectedNodes.filter(n => n !== 'ALL'));
  }

  get title() {
    return this.type === 'router' ? 'ROUTER NODES' : this.type.toUpperCase();
  }

  get icon() {
    return this.type === 'router' ? 'node' : this.type;
  }

  private _toggleAll(on: boolean): void {
    this.includeAllNodes = on;
    this.selectedNodes = this.includeAllNodes ? ['ALL', ...this.nodes] : [];
  }
}
