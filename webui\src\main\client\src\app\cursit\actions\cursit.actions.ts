import { createActionGroup, emptyProps } from '@ngrx/store';
import { payload } from '@simfront/common-libs';

import { CursitController } from '../models/cursit.model';
import { CursitFilter } from '../models/cursit-filter.model';
import { GeoFilterPoints } from '../reducers/cursit.reducer';

// Another way of creation of actions as a group.
export const cursitActions = createActionGroup({
  source: 'Cursit',
  events: {
    'Suspend Map Refresh': payload<boolean>(),
    'Dismiss Dialog': payload<unknown | null | undefined>(),
    'Update Cursit Controller': payload<CursitController>(),
    'Open Filter Dialog': emptyProps(),
    'Search Query': payload<string>(),
    'Close Filter Dialog': emptyProps(),
    'Update Cursit Filters': payload<CursitFilter>(),
    'Toggle Geo Draw': emptyProps(),
    'Geo Filter Drawn': payload<GeoFilterPoints>(),
    'Active Geo Filter Changed': payload<GeoFilterPoints>(),
    'Remove Geo Filters': emptyProps(),
    'Navigate To Cursit': emptyProps(),
    'Trigger MapResize': emptyProps(),
    'Show Hide LayerManager': payload<boolean>(),

    //     'Update Scale Indicator': props<{ payload: ScaleIndicator }>(),
    // 'Open Map Layer Dialog': props<{ payload: OpenMapLayerDialog }>(),
    // 'Initialize Map Success': props<{ payload: InitMapSuccess }>(),
    // 'Initialize Map': props<{ payload: InitMap }>(),
    // 'MouseMoved': props<{ payload: string }>()
  }
});
