import { CommonModule, NgOptimizedImage, NgSwitch } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { LetDirective } from '@ngrx/component';

import { MaterialModule } from '../material/material.module';
import { FilePayloadDirective } from '../shared/directives/file-payload.directive';
import { IconButtonSizeDirective } from '../shared/directives/icon-button-size.directive';
import { SvgIconSizeDirective } from '../shared/directives/svg-icon-size.directive';

import { ConnectionItemComponent } from './connection/connection-item.component';
import { ConnectionPanelComponent } from './connection/connection-panel.component';
import { EndpointItemComponent } from './endpoint/endpoint-item.component';
import { BSDialogComponent } from './node/bs-dialog.component';
import { BSFormComponent } from './node/bs-form.component';
import { NodeRoutingModule } from './node/node-routing.module';
import { NodeComponent } from './node/node.component';
import { MatStepper } from '@angular/material/stepper';
import { DataParameterComponent } from '../data/bs-params/components/data-param.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { PageComponent } from '../core/components/page.component';
import { CoreModule } from '@simfront/common-libs';
import { DataParamFormComponent } from './node/bs-form/data-param-form.component';

@NgModule({
  declarations: [
    BSDialogComponent,
    BSFormComponent,
    ConnectionPanelComponent,
    ConnectionItemComponent,
    EndpointItemComponent,
    NodeComponent
  ],
  imports: [
    NodeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    CoreModule,
    LetDirective,
    IconButtonSizeDirective,
    SvgIconSizeDirective,
    FilePayloadDirective,
    NgOptimizedImage,
    NgSwitch,
    MatStepper,
    DataParameterComponent,
    MatProgressSpinner,
    PageComponent,
    DataParamFormComponent
  ],
  exports: [BSFormComponent, ConnectionPanelComponent, BSDialogComponent]
})
export class BsnModule {}
