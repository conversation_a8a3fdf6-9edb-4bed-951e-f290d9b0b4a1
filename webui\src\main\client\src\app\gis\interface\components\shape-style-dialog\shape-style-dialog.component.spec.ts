import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ShapeStyleDialogComponent } from './shape-style-dialog.component';

describe('ShapeStyleDialogComponent', () => {
  let component: ShapeStyleDialogComponent;
  let fixture: ComponentFixture<ShapeStyleDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ShapeStyleDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ShapeStyleDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
