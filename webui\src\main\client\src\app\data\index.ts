import { State } from '@simfront/common-libs';

import { Connection } from './connection/connection.model';
import { DataPath } from './data-path/data-path.model';
import {
  Candidate,
  OutboundObject
} from './endpoint/association/association.model';
import { Endpoint } from './endpoint/endpoint.model';
import { Entity } from './entity/entity.model';
import { LogEntry } from './log/log.model';
import { Node } from './node/node.model';
import { Settings } from './settings/settings.model';
import { LayerInfo } from './layer-manager/layer-manager.model';
import { Route } from './route/route.model';
import { Movement } from './movement/movement.model';

// TODO: Move this to main index file.
export interface RootState {
  dataPath: State<DataPath, string>;
  entity: State<Entity, string>;
  node: State<Node, string>;
  endpoint: {
    adapter: State<Endpoint, string>;
    association: {
      candidates: Candidate[];
      associations: OutboundObject[];
    };
  };
  log: State<LogEntry, string>;
  connection: State<Connection, string>;
  settings: State<Settings, string>;
  layer: State<LayerInfo, string>;
  route: State<Route, string>;
  movement: State<Movement, string>;
}
