import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBarModule, MatSnackBarRef } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
    template: `
      <div class="container">
        <h2>{{ title }}</h2>
        <div class="message" matSnackBarLabel>
          <p class="url">{{ url }}</p>
          <p>{{ message }}</p>
        </div>
        <div class="button-container">
          <button mat-button (click)="snackBarRef.dismiss()">Close</button>
          <button mat-button color="warn" (click)="onViewLogsClick()">View Logs</button>
        </div>
      </div>
    `,
    styles: [`
      .message {
        word-break: break-all;
      }

      .url {
        font-size: 12px;
        /*font-weight: bold;*/
        font-style: italic;
      }

      .container {
        display: flex;
        flex-direction: column;
        margin: auto;
        padding: 10px;
        width: calc(100% - 10px);
      }

      .container > h2 {
        margin: 0 0 0 10px;
        color: var(--text-description);
      }

      .container > div {
        font-size: 14px;
        white-space: pre-wrap;
      }

      .button-container {
        width: fit-content;
        align-self: end;
        display: flex;
        flex-direction: row;
        justify-content: end;
      }
    `],
    imports: [
        MatButtonModule,
        MatSnackBarModule
    ]
})
export class HttpErrorComponent {

  title: string;
  url: string;
  message: string;

  snackBarRef = inject(MatSnackBarRef);
  router = inject(Router);

  constructor() {
    const { data } = this.snackBarRef.containerInstance.snackBarConfig;
    this.url = `${data.method} : ${data.her.url}`;
    const her = data.her;
    if (her instanceof HttpErrorResponse) {
      if (her.status === 0) {
        this.title = her.statusText;
        this.message = `This error can occur for the following reasons:\n •  Client cannot connect to the server.\n •  Client cannot receive response within the timeout period.\n •  Request was "stopped" by the client.`;
      } else {
        this.title = her.statusText;
        this.message = typeof her.error === 'string' ? her.error : her.message;
      }
    } else {
      this.title = 'An error occurred';
      this.message = her?.error?.message || 'An unknown error occurred.';
    }
  }

  // TODO: Implement custom errors that could point towards the correct logs origin.
  onViewLogsClick(): void {
    this.snackBarRef.dismiss();
    this.router.navigate(['/logs']);
  }
}
