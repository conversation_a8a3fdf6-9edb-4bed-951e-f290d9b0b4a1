import { Component, Input } from '@angular/core';
import { defaultExpandCollapse } from '../../../../shared/util/animation';

@Component({
    selector: 'vcci-filter-details',
    templateUrl: 'endpoint-filter-details.component.html',
    styleUrls: ['endpoint-filter-details.component.css'],
    animations: [defaultExpandCollapse],
    standalone: false
})
export class EndpointFilterDetailsComponent {
  @Input() filterDetails = 'Not implemented yet';
  expanded = false;
}
