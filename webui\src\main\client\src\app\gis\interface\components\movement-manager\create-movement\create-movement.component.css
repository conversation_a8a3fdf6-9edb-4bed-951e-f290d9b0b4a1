.create-movement-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  max-height: 45vh;
  overflow-y: auto;
}

/* Movement Settings Section */
.movement-settings {
  padding-bottom: 6px;
}

.movement-settings h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
}

.settings-row {
  display: flex;
  gap: 16px;
}

.settings-row h3 {
  margin: 0;
  padding-top: 6px;
  font-size: 16px;
  font-weight: 500;
}

.settings-group {
  display: flex;
  flex-direction: column;
}

.radio-group-inline {
  display: flex;
  align-items: center;
}

.radio-group-inline mat-radio-button {
  margin-right: 16px;
}

.time-inputs {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.time-inputs mat-form-field {
  flex: 1;
  min-width: 80px;
}

/* Entities Involved Section */
.entities-involved {
  border-radius: 8px;
}

.entities-involved h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.entities-controls {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
}

.entity-type-toggles {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

/* Entity Lists */
.entity-lists {
  display: flex;
  gap: 16px;
  height: 250px;
  width: 100%;
}

.entity-actions {
  display: flex;
  padding-top: 10px;
}

.entity-source,
.entity-selection {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.entity-source h4,
.entity-selection h4 {
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  font-weight: 500;
}

.entity-items {
  flex: 1;
  padding-right: 10px;
  overflow-y: auto;
}

.entity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.entity-item:hover {
  border-color: #ddd;
}

.entity-item.selected {
  background-color: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.entity-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.entity-item.selected .entity-icon {
  color: #1976d2;
}

.entity-name {
  flex: 1;
  font-size: 12px;
  font-weight: 500;
}

.entity-count {
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #eee;
  font-size: 11px;
  text-align: center;
}

/* Selected Entities */
.selected-entities {
  flex: 1;
  overflow-y: auto;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  font-style: italic;
  font-size: 12px;
}

/* Route Controls */
.route-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.route-controls h4 {
  font-size: 16px;
  font-weight: 500;
}

.route-controls h5 {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.route-type-selection-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.route-type-selection {
  display: flex;
  align-items: center;
}

.route-type-selection h4 {
  margin-bottom: 8px;
}

.radio-group-vertical {
  display: flex;
  gap: 8px;
}

.radio-group-vertical mat-radio-button {
  margin-bottom: 8px;
}

.route-info-section {
  padding: 16px;
  border-radius: 4px;
}

.route-info-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.route-info-row mat-form-field {
  flex: 1;
  min-width: 150px;
}

.existing-route-section,
.create-route-section,
.move-to-point-section {
  border-radius: 4px;
  flex: 1;
}

.existing-route-section h5,
.create-route-section h5,
.move-to-point-section h5 {
  color: var(--text);
}

.route-placeholder {
  font-style: italic;
  color: #666;
  margin: 8px 0;
}

/* Route Creation Controls */
.route-creation-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: nowrap;
  align-items: center;
}

.route-creation-controls mat-form-field {
  flex: 0 1 200px;
  min-width: 150px;
}

.route-creation-controls button {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Route Points Container */
.route-points-container {
  border-radius: 4px;
  overflow: hidden;
}

.route-points-header {
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
}

.route-points-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0;
}

.route-point-item {
  transition: background-color 0.2s ease;
}

.route-point-item-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
  background: var(--calian-gradient);
}

.route-point-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.route-point-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 12px;
  align-items: flex-start;
}

.route-point-row mat-form-field {
  flex: 1;
  min-width: 120px;
}

.checkbox-row {
  align-items: center;
}

.bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-row mat-checkbox {
  margin-right: 16px;
}

.remove-point-btn {
  color: var(--error);
}

.route-actions,
.route-info-actions {
  display: flex;
  justify-content: space-between;
  padding-top: 16px;
}

/* Form Field Adjustments */
mat-form-field {
  width: 100%;
}

mat-checkbox {
  font-size: 12px;
}

mat-stepper {
  border-radius: 8px;
}

.route-points-list mat-expansion-panel {
  margin-bottom: 8px;
}

.route-points-list mat-expansion-panel-header {
  padding: 12px;
}

.route-points-list mat-panel-title {
  flex: 1;
  max-width: none;
  margin: 0;
  cursor: grab;
}
