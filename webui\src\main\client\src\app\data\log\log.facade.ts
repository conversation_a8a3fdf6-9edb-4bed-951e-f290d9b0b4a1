import { Injectable } from '@angular/core';
import { PayloadTypedAction } from '@simfront/common-libs';
import { LogEntry, LogFacadeBase } from './log.model';

@Injectable({ providedIn: 'root' })
export class LogFacade extends LogFacadeBase {
  override inboundMapOne<P, Type extends string>(t: LogEntry, pta: PayloadTypedAction<P, Type>): LogEntry {
    return t.originator === 'VCCIApp' ? { ...t, originator: 'VCCI' } : t;
  }
}
