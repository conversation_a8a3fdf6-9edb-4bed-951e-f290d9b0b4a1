import { Component } from '@angular/core';
import { DataService } from '../../data.service';
import { DeepPartial } from '@simfront/common-libs';
import { Settings } from '../settings.model';
import { resetAllSettings } from '../actions/settings.action';
import { Store } from '@ngrx/store';

@Component({
  selector: 'vcci-settings-page',
  templateUrl: 'settings-page.component.html',
  standalone: false
})
export class SettingsPageComponent {
  settings$ = this.ds.settings.get$('user');

  constructor(
    private ds: DataService,
    private store: Store
  ) {}

  saveChange(changes: DeepPartial<Settings>) {
    this.ds.settings.updateOne({
      id: 'user',
      changes: changes
    });
  }

  resetAllSettings() {
    this.store.dispatch(resetAllSettings());
  }
}
