const {app, BrowserWindow} = require('electron');
const childProcess = require('child_process');
const http = require('http')

const APP_LAYER_PATH = 'C:\\SimFront\\Repos\\bola-test\\micronuat\\ms-vcci\\vcciapplayer\\micro.bat';
if (require('electron-squirrel-startup')) app.quit();

async function createWindow() {

  win = new BrowserWindow({width: 800, height: 800, frame: true, webPreferences: {nodeIntegration: true, contextIsolation: false}},);

  // If the app layer is not started, then start it otherwise move on
  const appLayerStatus = await isAppLayerRunning();
  if (appLayerStatus === 'NOT_RUNNING') {

    const child = childProcess.spawn(APP_LAYER_PATH);
    child.stdout.on('data', function (data) {
      console.log('stdout: ' + data);
      if (data.toString().includes('Startup completed')) {
        console.log('NEXUS APP LAYER STARTED SUCCESSFULLY')
      }
    });

    child.stderr.on('data', function (data) {
      console.log('stderr: ' + data);
    });

    child.on('exit', function (code) {
      console.log('child process exited with code ' + code);
      child.kill();
    });

    win.on('close', () => {
      console.log('Killing process')
      child.kill('SIGINT')
    })
  } else {
    console.log("NEXUS APP LAYER IS ALREADY RUNNING, JOINING SESSION")
  }


  win.loadFile('dist/client/index.html');


}

app.whenReady().then(() => {
  createWindow()
})


/***
 * Check the status of the Application Layer, if there is an error
 * then it is not running, if the request succeeds then it is running
 * @returns {Promise<unknown>}
 */
function isAppLayerRunning() {

  return new Promise((resolve, reject) => {
    let req = http.get('http://127.0.0.1:8086/bsn');
    req.on('response', res => {
      resolve('RUNNING');
    })
    req.on('error', err => {
      resolve('NOT_RUNNING');
    })


  })
}

