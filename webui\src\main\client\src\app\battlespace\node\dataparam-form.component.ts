import { SelectionModel } from '@angular/cdk/collections';
import {
  Component,
  DestroyRef,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  signal,
  WritableSignal
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

import { SFMap } from '@simfront/common-libs';

import { Subscription } from 'rxjs';

import {
  DataParameter,
  DataParameterGroup,
  GetNext,
  GROUP_CONTAINER,
  Instruction,
  normalizeParamVal,
  ParameterGroupValidator,
  Parameters
} from '../../data/bs-params/bs-params.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'vcci-dataparam-form',
  styleUrls: ['dataparam-form.component.css'],
  templateUrl: 'dataparam-form.component.html',
  standalone: false,
  providers: [
    {
      provide: GROUP_CONTAINER,
      useFactory: () => signal<SFMap<SelectionModel<string>>>({})
    }
  ]
})
export class DataParamFormComponent implements OnChanges, OnDestroy {
  @Input() parameters: DataParameter[];
  @Input() usingDefault = false;
  @Input() formType: 'Connection' | 'Endpoint' = 'Connection';
  @Output() getNext = new EventEmitter<GetNext>();
  @Output() identifierChanged = new EventEmitter<{
    type: 'Connection' | 'Endpoint';
    value: string;
  }>();
  @Output() networkInterfaceChanges = new EventEmitter<string>();
  @Output() onCreateConnection = new EventEmitter();
  @Output() onUpdateConnection = new EventEmitter();
  @Output() onUpdateEndpoint = new EventEmitter();

  form: FormGroup = new FormGroup({});
  orGroups: WritableSignal<SFMap<SelectionModel<string>>> =
    inject(GROUP_CONTAINER);
  private _orGroups: SFMap<DataParameterGroup> = {};
  private _connSub: Subscription | undefined = undefined;
  private _endpointSub: Subscription | undefined = undefined;
  private _destroyRef: DestroyRef = inject(DestroyRef);

  ngOnChanges() {
    this._updateForm();
  }

  private _updateForm(): void {
    const paramNames = new Set(this.parameters.map((p) => p.name));
    // Add missing controls and update existing ones
    this.parameters.forEach((p) => {
      if (!!p.group) {
        this.updateOrGroup(p);
      }
      const control = this.form.get(p.name);
      if (!control) {
        this.form.addControl(p.name, new FormControl(null));
      } else if (p.type === 'IDENTIFIER' && this.formType === 'Endpoint') {
        control.patchValue(normalizeParamVal(p));
      }
    });

    // Remove controls that no longer exist
    Object.keys(this.form.controls).forEach((controlName) => {
      if (!paramNames.has(controlName)) {
        this.form.removeControl(controlName);
      }
    });
    this.form.addValidators(
      ParameterGroupValidator(this._orGroups, this.orGroups())
    );
    const connFc = this.form.controls['CONNECTION_NAME'];
    if (connFc) {
      this._connSub?.unsubscribe();
      this._connSub = connFc.valueChanges
        .pipe(takeUntilDestroyed(this._destroyRef))
        .subscribe((value) =>
          this.identifierChanged.emit({
            type: 'Connection',
            value
          })
        );
    }
  }

  updateOrGroup(param: DataParameter): void {
    this._orGroups[param.group.name] = param.group;
    this.orGroups.update((orGroups) =>
      !orGroups[param.group.name]
        ? {
            ...orGroups,
            [param.group.name]: new SelectionModel<string>(
              param.group.groupType !== 'SINGLE_SELECT',
              param.group.defaultValues
            )
          }
        : orGroups
    );
  }

  onNext(dp: DataParameter): void {
    if (dp.hasNext) {
      this.getNext.emit({
        requestParameter: dp.name,
        instruction: Instruction.GetNextParam,
        parameters: this.getDataParameterValues()
      });
    }
  }

  getParameterValues(): Parameters {
    return this.parameters
      .filter((f) => {
        const form = this.form.controls[f.name];
        return (
          !form.invalid &&
          form.enabled &&
          f.editable &&
          (!f.group || this.orGroups()[f.group?.name]?.isSelected(f.name))
        );
      })
      .reduce<Parameters>(
        (p, c) => {
          const value = this.form.value[c.name];
          if (c.valueList) {
            p.listParameters[c.name] = {
              id: 0,
              key: c.name,
              singleParameters: value.map((v) => ({
                id: 0,
                key: c.name,
                value: v
              }))
            };
            return p;
          } else {
            p.singleParameters[c.name] = { id: 0, key: c.name, value };
            return p;
          }
        },
        { listParameters: {}, singleParameters: {} }
      );
  }

  getDataParameterValues(): SFMap<DataParameter> {
    return this.parameters.reduce((p, c) => {
      p[c.name] = { ...c, value: this.form.get(c.name)?.value };
      return p;
    }, {} as SFMap<DataParameter>);
  }

  onNetworkInterfaceChanges(value: string): void {
    this.networkInterfaceChanges.emit(value);
  }

  ngOnDestroy() {
    this._connSub?.unsubscribe();
    this._endpointSub?.unsubscribe();
  }
}
