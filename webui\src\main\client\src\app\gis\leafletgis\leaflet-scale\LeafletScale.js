import * as L from 'leaflet';

export const LeafletScale = L.Control.extend({
    options: {
        position: "bottomleft",
        maxWidth: 150,
        metric: true,
        imperial: false,
        updateWhenIdle: false
    },

    onAdd: function (map) {
        this._map = map;
        var className = "leaflet-scale-bar";
        var container = L.DomUtil.create("div", className);
        var ruler = L.DomUtil.create("div", className + "-ruler", container);
        L.DomUtil.create("div", className + "-ruler-block " + className + "-upper-first-piece", ruler);
        <PERSON>.<PERSON>.create("div", className + "-ruler-block " + className + "-upper-second-piece", ruler);
        L.DomUtil.create("div", className + "-ruler-block " + className + "-lower-first-piece", ruler);
        L.DomUtil.create("div", className + "-ruler-block " + className + "-lower-second-piece", ruler);
        this._addScales(this.options, className, container);
        this.ScaleContainer = container;
        map.on(this.options.updateWhenIdle ? "moveend" : "move", this._update, this);
        map.whenReady(this._update, this);
        return container;
    },

    onRemove: function (map) {
        map.off(this.options.updateWhenIdle ? "moveend" : "move", this._update, this);
    },

    _addScales: function (options, className, container) {
        this._iScale = L.DomUtil.create("div", className + "-label-div", container);
        this._iScaleLabel = L.DomUtil.create("div", className + "-label", this._iScale);
        this._iScaleFirstNumber = L.DomUtil.create("div", className + "-label " + className + "-first-number", this._iScale);
        this._iScaleSecondNumber = L.DomUtil.create("div", className + "-label " + className + "-second-number", this._iScale);
    },

    _update: function () {
        var bounds = this._map.getBounds();
        var centerLat = bounds.getCenter().lat;
        var equatorialRadius = 6378137 * Math.PI * Math.cos(centerLat * Math.PI / 180);
        var horizontalDistance = equatorialRadius * (bounds.getNorthEast().lng - bounds.getSouthWest().lng) / 180;
        var mapSize = this._map.getSize();
      if (mapSize.x > 0) {
        const scaleRatio = horizontalDistance * (this.options.maxWidth / mapSize.x);
        this._updateScales(this.options, scaleRatio);
      }
    },

    _updateScales: function (options, scaleRatio) {
        if (options.metric && scaleRatio) {
            this._updateMetric(scaleRatio);
        } else if (options.imperial && scaleRatio) {
            this._updateImperial(scaleRatio);
        }
    },

    _updateMetric: function (scaleMeters) {
        var scaleValue;
        var firstNumber;
        var secondNumber;
        this._iScaleLabel.innerHTML = "0";
        if (scaleMeters > 500) {
            scaleValue = scaleMeters / 1000;
            secondNumber = this._getRoundNum(scaleValue);
            firstNumber = this._getRoundNum(scaleValue / 2);
            this._iScale.style.width = this._getScaleWidth(secondNumber / scaleValue) + "px";
            this._iScaleFirstNumber.innerHTML = firstNumber;
            this._iScaleSecondNumber.innerHTML = secondNumber + "km";
        } else {
            scaleValue = this._getRoundNum(scaleMeters);
            firstNumber = this._getRoundNum(scaleMeters / 2);
            this._iScale.style.width = this._getScaleWidth(scaleValue / scaleMeters) + "px";
            this._iScaleFirstNumber.innerHTML = firstNumber;
            this._iScaleSecondNumber.innerHTML = scaleValue + "m";
        }
    },

    _updateImperial: function (scaleRatio) {
        var scaleValue;
        var secondNumber;
        var fistNumber;
        var scaleFeet = 3.2808399 * scaleRatio;
        this._iScaleLabel.innerHTML = "0";
        if (scaleFeet > 2640) {
            scaleValue = scaleFeet / 5280;
            secondNumber = this._getRoundNum(scaleValue);
            fistNumber = this._getRoundNum(scaleValue / 2);
            this._iScale.style.width = this._getScaleWidth(secondNumber / scaleValue) + "px";
            this._iScaleFirstNumber.innerHTML = fistNumber;
            this._iScaleSecondNumber.innerHTML = secondNumber + "mi";
        } else {
            secondNumber = this._getRoundNum(scaleFeet);
            fistNumber = this._getRoundNum(scaleFeet / 2);
            this._iScale.style.width = this._getScaleWidth(secondNumber / scaleFeet) + "px";
            this._iScaleFirstNumber.innerHTML = fistNumber;
            this._iScaleSecondNumber.innerHTML = secondNumber + "ft";
        }
    },

    _getScaleWidth: function (ratio) {
        return Math.round(this.options.maxWidth * ratio) - 10;
    },

    _getRoundNum: function (value) {
        if (value >= 2) {
            var exponent = Math.floor(value).toString().length - 1;
            var divisor = Math.pow(10, exponent);
            var roundedValue = value / divisor;
            if (roundedValue >= 10) {
                return 10 * divisor;
            } else if (roundedValue >= 5) {
                return 5 * divisor;
            } else if (roundedValue >= 3) {
                return 3 * divisor;
            } else if (roundedValue >= 2) {
                return 2 * divisor;
            } else {
                return divisor;
            }
        }
        return (Math.round(100 * value) / 100).toFixed(1);
    }
});

