<form [formGroup]="fg$()">
  <vcci-bs-data-params
    [parameters]="parameters"
    [usingDefault]="usingDefault"
    (getNext)="onNext($event)"
    (networkInterfaceChanges)="onNetworkInterfaceChange($event)"/>
  <vcci-bs-data-sections
    [dataSections]="sections"
    [usingDefault]="usingDefault"
    (getNext)="onNext($event)"
    (networkInterfaceChanges)="onNetworkInterfaceChange($event)"/>
  <vcci-bs-data-pages
    [dataPages]="pages"
    [usingDefault]="usingDefault"
    (getNext)="onNext($event)"
    (networkInterfaceChanges)="onNetworkInterfaceChange($event)"/>
</form>
