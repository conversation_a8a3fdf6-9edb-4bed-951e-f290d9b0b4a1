import { SFMap } from '@simfront/common-libs';

import { MapLayer } from './MapLayer';
import { GridType, LayerType } from '../../data/layer-manager/layer-manager.model';

export interface LayerFactory<L> {

  createGridLayer: (gridType: GridType, colour: string) => MapLayer<L>;
  createTiledLayer: (url: string, layers?: string[]) => Promise<MapLayer<L>> | null;
  createMilitarySymbolLayer: (name: string, layerId?: string) => MapLayer<L>;
  getLayersHash: () => SFMap<MapLayer<L>>;
  createFeatureLayer: (filePath: string, layerType: LayerType) =>
  MapLayer<L> | Promise<MapLayer<L>>;
  createShapeDrawingLayer: (layerName: string, layerId?: string) => MapLayer<L>;
}

export abstract class MapLayerFactory<L> implements LayerFactory<L> {
  protected layersHash: SFMap<MapLayer<L>> = {};

  abstract createGridLayer(gridType: GridType, colour: string): MapLayer<L>;

  getLayersHash(): SFMap<MapLayer<L>> {
    return this.layersHash;
  }

  createTiledLayer(url: string, layers?: string[]): Promise<MapLayer<L>> | null {
    if (url.toLowerCase().includes('tile')) {
      return this.gisCreateTiledLayer(url);
    } if (url.toLowerCase().includes('wms')) {
      return this.gisCreateWMSLayer(url, layers);
    } if (url.toLowerCase().includes('wmts')) {
      return this.gisCreateWMTSLayer(url, layers);
    }
    return null;
  }

  createMilitarySymbolLayer(name: string, layerId?: string): MapLayer<L> {
    return this.gisCreateMilitarySymbolLayer(name, layerId);
  }

  abstract createShapeDrawingLayer(layerName: string, layerId?: string): MapLayer<L>;

  abstract createFeatureLayer(
    filePath: string,
    layerType: LayerType
  ): MapLayer<L> | Promise<MapLayer<L>>;

  protected abstract gisCreateTiledLayer(url: string): Promise<MapLayer<L>> | null;

  protected abstract gisCreateWMSLayer(url: string, layer?: string[]): Promise<MapLayer<L>>;

  protected abstract gisCreateWMTSLayer(url: string, layers?: string[]): Promise<MapLayer<L>>;

  protected abstract gisCreateMilitarySymbolLayer(name: string, layerId: string): MapLayer<L>;
}
