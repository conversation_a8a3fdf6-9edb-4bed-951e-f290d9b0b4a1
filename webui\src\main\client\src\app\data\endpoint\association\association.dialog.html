<h2 mat-dialog-title>Endpoint Association Manager</h2>
<mat-dialog-content>
  <div class="associated-table">
    <sf-core-table
      identifier="assoc"
      includePaginator
      includeSearch
      includeSort
      [data]="assocEntities$ | async"
      [columns]="assocCols"
      [trackBy]="assocTrackBy"
      [selectionMode]="selectionMode"
      (tableSelectionChange)="onAssocSelected($event)"
    >
    </sf-core-table>
    <mat-dialog-actions align="end">
      <button
        mat-raised-button
        cdkFocusInitial
        [disabled]="disDisabled"
        (click)="onDissoc(selectedAssoc)"
      >
        Dissociate
      </button>
    </mat-dialog-actions>
  </div>
  <div class="assoc-table-group">
    <sf-core-table
      identifier="src"
      includePaginator
      includeSearch
      includeSort
      [data]="src$ | async"
      [columns]="srcCols"
      [trackBy]="srcTrackBy"
      [selectionMode]="selectionMode"
      (tableSelectionChange)="onSrcSelected($event)"
    >
    </sf-core-table>
    <sf-core-table
      identifier="dst"
      includePaginator
      includeSearch
      includeSort
      [data]="dst$ | async"
      [columns]="dstCols"
      [trackBy]="dstTrackBy"
      [selectionMode]="selectionMode"
      [busy]="busy$ | async"
      (tableSelectionChange)="onDstSelected($event)"
    >
    </sf-core-table>
  </div>
  <mat-dialog-actions align="end">
    <button
      mat-raised-button
      cdkFocusInitial
      [disabled]="assDisabled"
      (click)="onAssoc(selectedSrc, selectedDst)"
    >
      Associate
    </button>
  </mat-dialog-actions>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>Cancel</button>
  <button mat-button [mat-dialog-close]="true" (click)="onApply()">
    Apply
  </button>
</mat-dialog-actions>
<ng-template #serviceTemplate let-e>
  <mat-icon [matTooltip]="e.assoc?.dstName">check</mat-icon>
</ng-template>
