import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, switchMap } from 'rxjs';
import { GisService } from '../../interface/service/gis.service';

import { processTacticalGraphicsAction, updateTacticalGraphicsAction } from '../actions/leaflet.actions';

@Injectable()
export class LeafletEffects {
  updateTacticalGraphics$ = createEffect(() => this.actions$.pipe(
    ofType(updateTacticalGraphicsAction),
    map(action => action.payload),
    switchMap(list => this.gisService.requestTacticalGraphics(list).pipe(
      map(result => processTacticalGraphicsAction(result))
    ))
  ));

  processTacticalGraphics$ = createEffect(() => this.actions$.pipe(
    ofType(processTacticalGraphicsAction),
    map(action => {
      this.gisService.processTacticalGraphics(action.payload);
    })
    // ,
    // catchError(error => {
    //   console.error('Error occurred:', error);
    //   return of({ type: 'ErrorAction', payload: error });
    // })
  ), { dispatch: false });

  constructor(private actions$: Actions, private gisService: GisService) {

  }
}
