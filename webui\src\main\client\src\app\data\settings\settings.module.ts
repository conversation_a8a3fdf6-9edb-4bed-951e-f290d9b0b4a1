import { CommonModule, NgOptimizedImage } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { CoreModule } from '@simfront/common-libs';

import { MaterialModule } from '../../material/material.module';

import { SettingsPageComponent } from './container/settings-page.component';
import { CursitSettingsComponent } from './components/cursit-settings.component';
import { ConnectionPanelSettingsComponent } from './components/conn-panel-settings.component';
import { SettingsRoutingModule } from './settings-routing.module';
import { HoverOverlayDirective } from '../../shared/directives/hover-overlay.directive';
import { ConfigDialogSettingsComponent } from './components/config-dialog-settings.component';
import { FlexBlockDirective } from 'src/app/shared/directives/flex-block.directive';
import { PageComponent } from 'src/app/core/components/page.component';
import { LayoutSettingsComponent } from './components/layout-settings.component';

export const COMPONENTS = [
  SettingsPageComponent,
  CursitSettingsComponent,
  ConnectionPanelSettingsComponent,
  ConfigDialogSettingsComponent,
  LayoutSettingsComponent
];

@NgModule({
  declarations: COMPONENTS,
  exports: COMPONENTS,
  imports: [
    SettingsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    CoreModule,
    MaterialModule,
    NgOptimizedImage,
    HoverOverlayDirective,
    FlexBlockDirective,
    PageComponent
  ]
})
export class SettingsModule {}
