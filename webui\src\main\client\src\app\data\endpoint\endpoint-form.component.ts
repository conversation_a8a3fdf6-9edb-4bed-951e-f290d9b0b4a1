import { Component } from '@angular/core';
import {
  DialogOutParams,
  FacadeDialogContentComponent,
} from '@simfront/common-libs';
import { Endpoint } from './endpoint.model';

@Component({
  selector: 'vcci-endpoint-form',
  template: `
    <div
      class="m-container"
      style="padding: 0 20px"
      *ngIf="
        data.inParams.crud === 'Delete' && data.inParams.quantity === 'One'
      "
    >
      <p>Are you sure you want to delete the endpoint:</p>
      <ul style="padding: 0 30px;">
        <li>{{ id }}</li>
      </ul>
    </div>
  `,
  styles: [
    `
      .m-container {
        margin: 5px;
      }

      ::ng-deep .mat-mdc-dialog-content:has(vcci-endpoint-form) {
        padding-bottom: 0 !important;
      }

      ::ng-deep
        .mat-mdc-dialog-content:has(vcci-endpoint-form)
        ~ .mat-mdc-dialog-actions {
        justify-content: flex-end !important;
        padding: 20px !important;
      }

      ::ng-deep vcci-endpoint-form > vcci-bs-dialog > .mat-mdc-dialog-content {
        padding: 0 !important;
        width: 100%;
        height: 100%;
      }
    `,
  ],
  standalone: false,
})
export class EndpointForm extends FacadeDialogContentComponent<
  Endpoint,
  'id',
  string
> {
  title = '';
  endpoint: Endpoint | undefined;
  id = '';

  constructor() {
    super();
    this.title = `${this.data.inParams.crud} Endpoint`;

    this.initDialog();
  }

  initDialog(): void {
    if (
      this.data.inParams.crud === 'Delete' &&
      this.data.inParams.quantity === 'One'
    ) {
      this.id = this.data.inParams.id;
    }
  }

  override outData(): DialogOutParams<Endpoint, 'id', string> {
    if (
      this.data.inParams.crud === 'Delete' &&
      this.data.inParams.quantity === 'One'
    ) {
      return { ...this.data.inParams, id: this.data.inParams.id };
    }

    throw new Error(
      `Crud Operation '${this.data.inParams.crud}' is not supported`,
    );
  }
}
