/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/1/2024, 1:54 PM
 */

import { Component, EventEmitter, Input, Output } from '@angular/core';
import { GisService } from '../service/gis.service';
import { ButtonInfo, ExpandedToolbars } from './toolbar-container.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { EntitySearchComponent } from './entity-search.component/entity-search.component';
import { DataService } from '../../../data/data.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'vcci-entity-toolbar',
  styleUrls: ['./entity-toolbar.component.scss'],
  templateUrl: './entity-toolbar.component.html',
  standalone: false
})
export class EntityToolbarComponent {
  @Input() expanded: boolean = false;
  @Input() all: boolean = false;
  @Input() simmoveOn: boolean = false;
  @Output() entityToolbarExpanded: EventEmitter<ExpandedToolbars> =
    new EventEmitter<ExpandedToolbars>();
  buttonInfoList: ButtonInfo[] = [];
  readonly env = environment;

  constructor(
    private bs: MatBottomSheet,
    private gisService: GisService,
    private data: DataService
  ) {
    // this.buttonInfoList.push({
    //   svgIcon: 'create-route-etb',
    //   label: 'CREATE ROUTE',
    //   action: 'create-route'
    // });
    this.buttonInfoList.push({
      svgIcon: 'find-in-orbat-etb',
      label: 'Find in Orbat',
      action: 'find-in-orbat',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'find-entity-etb',
      label: 'Find Entity',
      action: 'find-entity'
    });
    // this.buttonInfoList.push({
    //   svgIcon: 'hide-labels-etb',
    //   label: 'HIDE LABELS',
    //   action: 'hilde-labels'
    // });
    this.buttonInfoList.push({
      svgIcon: 'hide-all-units-etb',
      label: 'Hide All Units',
      action: 'hide-all-units',
      selected: false
    });
    this.buttonInfoList.push({
      svgIcon: 'hide-all-materiels-etb',
      label: 'Hide All Materiels',
      action: 'hide-all-materiels',
      selected: false
    });
    this.buttonInfoList.push({
      svgIcon: 'create-radar-etb',
      label: 'Create Radar',
      action: 'create-radar',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'create-control-feature-etb',
      label: 'Create Control Feature',
      action: 'create-control-feature',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'create-route-etb',
      label: 'Create Route',
      action: 'create-route',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'create-unit-etb',
      label: 'Create Unit',
      action: 'create-unit',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'create-materiel-etb',
      label: 'Create Materiel',
      action: 'create-materiel',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'create-entity-etb',
      label: 'Create Entity',
      action: 'create-entity',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'disaggregation-etb',
      label: 'Disaggregation',
      action: 'disaggregation',
      hidden: this.env.production
    });
    this.buttonInfoList.push({
      svgIcon: 'add-same-as-last-etb',
      label: 'Add Last',
      action: 'add-same-as-last'
    });
    // this.buttonInfoList.push({
    //   svgIcon: 'cluster-etb',
    //   label: 'ENTITY CLUSTER',
    //   action: 'cluster'
    // });
  }

  /**
   console.log(action);
   * Perform the action for a selected button. Some buttons do not want to
   * toggle styles based on selected state which is why the selected is an optional
   * property
   * @param buttonInfo details about the button
   */
  buttonAction(buttonInfo: ButtonInfo): void {
    const action = buttonInfo.action;
    if (buttonInfo.selected !== undefined) {
      buttonInfo.selected = !buttonInfo.selected;
    }

    switch (action) {
      case 'create-route':
        this.startRouteCreation();
        break;
      case 'hide-all-materiels':
        this.gisService.hideShowElementType('MA', !buttonInfo.selected);
        break;
      case 'find-entity':
        this.openBottomSheet();
        break;
      case 'hide-all-units':
        this.gisService.hideShowElementType('OR', !buttonInfo.selected);
        break;
      case 'create-entity':
        this.data.entity.openCreateEntityDialog();
        break;
    }
  }

  openBottomSheet(): void {
    this.bs.open(EntitySearchComponent);
  }

  startRouteCreation(): void {
    this.gisService.startRouteCreation();
  }

  toggleEntityToolbar(): void {
    if (this.all) {
      this.expanded = true;
    } else {
      this.expanded = !this.expanded;
    }
    this.entityToolbarExpanded.emit({
      entities: this.expanded,
      drawing: false,
      movement: false,
      styling: false,
      all: false
    });
  }
}
