import {
  InjectionToken,
  Injector,
  Optional,
  SkipSelf,
  WritableSignal
} from '@angular/core';

import {
  BaseDialogComponent,
  DialogInParams,
  SFMap
} from '@simfront/common-libs';

import { nullishOrEmpty } from '../../shared/util/util.model';

import { Connection, isConnection } from '../connection/connection.model';
import { Endpoint, isInputEndpoint } from '../endpoint/endpoint.model';

import { BSParamsState, BSParamsStore } from './bs-params.store';
import {
  AbstractControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn
} from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';

export interface SingleParameter {
  id: number;
  key: string;
  value: string;
}

export interface ListParameter {
  id: number;
  key: string;
  singleParameters: SingleParameter[];
}

export interface Parameters {
  singleParameters: SFMap<SingleParameter>;
  listParameters: SFMap<ListParameter>;
}

export interface BSErrorMessage {
  battleSpaceNode?: string;
  errorMessage: string;
  errorRequest?: string;
  messageTrackerId?: string;
}

export enum Instruction {
  GetParamList,
  GetParamDefault,
  GetNextParam,
  GetParam,
  GetConfiguredParamList
}

export type ParameterType =
  | 'STRING'
  | 'INT'
  | 'DOUBLE'
  | 'LONG'
  | 'BOOLEAN'
  | 'IPADDRESS'
  | 'NETWORK_INTERFACE'
  | 'FILE'
  | 'IDENTIFIER'
  | 'IPPORT'
  | 'PASSWORD';

export type GroupType = 'NONE' | 'SINGLE_SELECT' | 'MULTIPLE_SELECT' | 'LIST';

export interface DataParameterGroup {
  name: string;
  groupType: GroupType;
  mandatory: boolean;
  defaultValues: string[];
}

export interface BaseDataParameter {
  name: string;
  editable: boolean;
  visible: boolean;
  type: ParameterType;
  mandatory: boolean;
  hasNext: boolean;
  hasPrev: boolean;
  validatorRegEx: string;
  defaultValue: string;
  list: boolean;
  valueList: boolean;
  group?: DataParameterGroup;
  value: string;
}

export type DataParameter = BaseDataParameter &
  ({ list: true; listOptions: string[] } | { list: false }) &
  ({ valueList: false } | { valueList: true; listValues: string[] }) &
  ({ hasNext: true; nextParamNames: string[] } | { hasNext: false }) &
  ({ hasPrev: true; prevParamName: string } | { hasPrev: false });

export interface DataSection {
  sectionName: string;
  mandatory: boolean;
  lastSection: boolean;
  parameters: SFMap<DataParameter>;
}

export interface DataPage {
  pageName: string;
  mandatory: boolean;
  lastPage: boolean;
  sections: SFMap<DataSection>;
  parameters: SFMap<DataParameter>;
}

export const isDataPage = (
  i: DataPage | DataSection | DataParameter
): i is DataPage => !!i['pageName'];

export const isDataSection = (
  i: DataPage | DataSection | DataParameter
): i is DataSection => !!i['sectionName'];

export function getIdentifierPath(
  items: DataPage[] | DataSection[] | DataParameter[],
  path: string = ''
): string | null {
  for (const item of items) {
    if (isDataPage(item)) {
      const myP = `pages.${item.pageName}.`;
      let p = getIdentifierPath(Object.values(item.parameters), myP);
      if (p) return p;
      p = getIdentifierPath(Object.values(item.sections), myP);
      if (p) return p;
    } else if (isDataSection(item)) {
      const myP = `${path}sections.${item.sectionName}.`;
      return getIdentifierPath(Object.values(item.parameters), myP);
    } else if (item.type === 'IDENTIFIER') {
      return `${path}parameters.${item.name}`;
    }
  }
  return null;
}

export const PARENT_FORM = new InjectionToken<WritableSignal<FormGroup>>(
  'PARENT_FORM'
);
export const PAGE_FORM = new InjectionToken<WritableSignal<FormGroup>>(
  'PAGE_FORM'
);
export const SECTION_FORM = new InjectionToken<WritableSignal<FormGroup>>(
  'SECTION_FORM'
);
export const PARAMS_FORM = new InjectionToken<WritableSignal<FormGroup>>(
  'PARAMS_FORM'
);
export const GROUP_CONTAINER = new InjectionToken<
  WritableSignal<SFMap<SelectionModel<string>>>
>('GROUP_CONTAINER');
export const DEFAULT_NETWORK_INTERFACE = new InjectionToken<
  WritableSignal<string | undefined>
>('DEFAULT_NETWORK_INTERFACE');

export interface GetNext {
  requestParameter: string;
  parameters: SFMap<DataParameter>;
  instruction?: Instruction;
}

export type Message = 'connectionMessage' | 'endpointMessage';

export interface DataParameters {
  singleParameters: SFMap<string>;
  listParameters: SFMap<string[]>;
}

export interface BSConfigData {
  type: 'bsc' | 'bse';
  errorMessage?: string;
}

export interface ParameterRequest {
  isInput?: boolean;
  parentName?: string;
  name?: string;
  instruction: Instruction;
  requestParameter: string;
  parameters: SFMap<DataParameter>;
  sections?: SFMap<DataSection>;
  pages?: SFMap<DataPage>;
}

interface BSCBaseConfigData extends BSConfigData {
  type: 'bsc';
  connectionConfig: ParameterRequest;
  endpointName: string;
}
export type BSCConfigData = BSCBaseConfigData &
  (
    | {
        includeEndpoint: true;
        endpointConfig: ParameterRequest;
      }
    | {
        includeEndpoint: false;
      }
  );

export interface BSEConfigData extends BSConfigData {
  type: 'bse';
  connectionName: string;
  endpointConfig: ParameterRequest;
}

export interface CreateBSC {
  battleSpaceConnection: Connection;
  battleSpaceEndpoint?: Endpoint;
}

export const isBSCConfigData = (
  val: BSEConfigData | BSCConfigData
): val is BSCConfigData => val.type === 'bsc';
export const isBSErrorMessage = (val: any): val is BSErrorMessage => {
  if (typeof val === 'string') {
    val = JSON.parse(val);
  }
  return !!val.errorMessage;
};

export interface PortalData<T extends Connection | Endpoint> {
  provider: BaseDialogComponent<T, 'id', string>;
  params: DialogInParams<T, string>;
}

export const BS_STORE_PROVIDER = {
  provide: BSParamsStore,
  useFactory: (parentInjector: Injector, store?: BSParamsStore) => {
    if (!store) {
      const injector = Injector.create({
        providers: [{ provide: BSParamsStore }],
        parent: parentInjector
      });
      store = injector.get(BSParamsStore);
    }
    return store;
  },
  deps: [Injector, [new Optional(), new SkipSelf(), BSParamsStore]]
};

export const assembleStoreData = <T extends Endpoint | Connection, I>(
  inParams: DialogInParams<T, I>
): Partial<BSParamsState> => {
  if (inParams.crud === 'Update' && inParams.quantity === 'One') {
    const { t } = inParams;
    const con = isConnection(t);
    return {
      ...inParams,
      model: con ? 'Connection' : 'Endpoint',
      selectedNode: t.node,
      selectedConnection: con ? t.name : t.connection,
      direction: con ? t.direction : isInputEndpoint(t) ? 'IN' : 'OUT',
      currentParameters: t.parameters
    };
  }
  return null;
};

export const parameterToBoolean = (
  value: string | number | boolean
): boolean | null => {
  if (typeof value === 'boolean') {
    return value;
  } else if (typeof value === 'number') {
    return value > 0;
  } else if (nullishOrEmpty(value)) {
    return false;
  }
  const lowerCase = value.toLowerCase();
  return lowerCase === 'true' || lowerCase === 'on' || lowerCase === 'yes';
};

export const normalizeParamVal = (
  parameter: DataParameter,
  useDefault = false
): string[] | string | boolean => {
  if (parameter.valueList) {
    return parameter.listValues;
  }
  if (
    parameter.list &&
    parameter.listOptions &&
    parameter.listOptions.length === 1 &&
    parameter.mandatory
  ) {
    return parameter.listOptions[0];
  }
  const value = (useDefault ? parameter.defaultValue : parameter.value) || '';
  return parameter.type === 'BOOLEAN' ? parameterToBoolean(value) : value;
};

export const compareValue = <P extends string[] | string | boolean>(
  a: P,
  b: P
): boolean => {
  if (Array.isArray(a) && Array.isArray(b)) {
    return a.length !== b.length ? false : a.every((v) => b.indexOf(v) > -1);
  } else {
    return a === b;
  }
};

const lengthOrSize = (value: unknown): number | null =>
  value == null
    ? null
    : Array.isArray(value) || typeof value === 'string'
      ? value.length
      : value instanceof Set
        ? value.size
        : null;

const isEmptyInputValue = (value: unknown): boolean =>
  value === undefined || value === null || lengthOrSize(value) === 0;

const patternValidation = (
  pattern: RegExp | string | null,
  value: unknown
): boolean => {
  if (!pattern || isEmptyInputValue(value)) return true;
  let regex: RegExp;
  let regexStr: string;
  if (typeof pattern === 'string') {
    regexStr = '';
    if (pattern.charAt(0) !== '^') regexStr += '^';
    regexStr += pattern;
    if (pattern.charAt(pattern.length - 1) !== '$') regexStr += '$';
    regex = new RegExp(regexStr);
  } else {
    regex = pattern;
  }
  const validate = (v: unknown) =>
    isEmptyInputValue(v) || regex.test(String(v));
  if (Array.isArray(value)) {
    return value.reduce<boolean>((acc, v) => {
      if (!acc) {
        return acc;
      } else {
        return validate(v);
      }
    }, true);
  } else {
    return validate(value);
  }
};

export const ParameterValidator = (parameter: DataParameter): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    const errors: ValidationErrors = {};
    if (
      parameter.mandatory &&
      parameter.editable &&
      isEmptyInputValue(control.value)
    ) {
      errors['required'] = true;
    }
    if (
      !!parameter.validatorRegEx &&
      !patternValidation(parameter.validatorRegEx, control.value)
    ) {
      errors['pattern'] = true;
    }
    return Object.keys(errors).length > 0 ? errors : null;
  };
};

export const ParameterGroupValidator = (
  groups: SFMap<DataParameterGroup>,
  orGroups: SFMap<SelectionModel<string>>
): ValidatorFn => {
  return (_: AbstractControl): ValidationErrors | null =>
    Object.values(groups).reduce<ValidationErrors | null>((acc, grp) => {
      if (grp.mandatory && orGroups[grp.name]?.isEmpty()) {
        return { ['group']: true };
      }
      return acc;
    }, null);
};
