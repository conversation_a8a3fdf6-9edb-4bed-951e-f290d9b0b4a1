import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON>vent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';


@Injectable()
export class WithCredentialsInterceptor implements HttpInterceptor {

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    const clonedRequest = req.clone({
      withCredentials: true,
      headers: req.headers.set('X-Session-ID', sessionStorage.getItem('SESSION_ID'))
    });
    return next.handle(clonedRequest);
  }

}
