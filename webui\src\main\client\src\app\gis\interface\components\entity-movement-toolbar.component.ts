/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/1/2024, 1:57 PM
 */

import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ButtonInfo, ExpandedToolbars } from './toolbar-container.component';

@Component({
    selector: 'vcci-entity-movement-toolbar',
    styleUrls: ['./entity-movement-toolbar.component.scss'],
    templateUrl: './entity-movement-toolbar.component.html',
    standalone: false
})

export class EntityMovementToolbarComponent {
  @Input() expanded: boolean = true;
  @Input() all: boolean = false;
  @Output() entityMovementToolbarExpanded: EventEmitter<ExpandedToolbars> = new EventEmitter<ExpandedToolbars>();

  buttonInfoList: ButtonInfo[] = [];

  constructor() {
    this.buttonInfoList.push({
      svgIcon: 'clear-analysis-emtb',
      label: 'Clear Analysis',
      action: 'clear-analysis'
    });
    this.buttonInfoList.push({
      svgIcon: 'route-emtb',
      label: 'Route',
      action: 'route'
    });
    this.buttonInfoList.push({
      svgIcon: 'mnvr-manager-emtb',
      label: 'MNVR. Manager',
      action: 'mnvr-manager'
    });
  }

  buttonAction(action: string): void {
    switch (action) {
      case 'clear-analysis':
        this.clearAnalysis();
        break;
    }
  }

  clearAnalysis(): void {

  }

  toggleEntityMovementToolbar(): void {
    if (this.all) {
      this.expanded = true;
    } else {
      this.expanded = !this.expanded;
    }
    this.entityMovementToolbarExpanded.emit({
      entities:  false,
      drawing: false,
      movement: this.expanded,
      styling: false,
      all: false
    });
  }
}
