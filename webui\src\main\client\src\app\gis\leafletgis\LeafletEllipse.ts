import { Coordinate } from '../../cursit/models/cursit.model';
import { ShapeType } from '../interface/models/map-element.model';
import { LeafletMap } from './LeafletMap';
import { LeafletGraphics } from './LeafletGraphics';
import { LeafletMapDisplay } from './LeafletMapDisplay';
/**
 * LeafletEllipse class for rendering ellipses, circles, and arcs on a Leaflet map.
 * It uses geodesic calculations so that the rendered ellipse
 * respects the curvature of the Earth.
 */
export class LeafletEllipse extends LeafletGraphics {
  /**
   * Creates a new LeafletEllipse instance
   *
   * @param id Unique identifier for the ellipse
   * @param map LeafletMap instance to render on
   * @param type ShapeType of the ellipse
   * @param catCode Category code for styling
   * @param coordinates Array of coordinates forming the ellipse
   * @param label Label for the ellipse
   */
  constructor(
    id: string,
    map: LeafletMapDisplay,
    type: ShapeType,
    catCode: string,
    coordinates: Coordinate[],
    label: string
  ) {
    super(id, map, type, catCode, coordinates, label);
    this.drawEllipse();
  }

  /**
   * Draws the ellipse (or circle) on the map using geodesic calculations.
   */
  drawEllipse(): void {
    this._graphicsElement.clear();

    const centerLon = this._locations[0][1];
    const centerLat = this._locations[0][0];

    // Diameter points
    const p1Lon = this._locations[1][1];
    const p1Lat = this._locations[1][0];
    const p2Lon = this._locations[2][1];
    const p2Lat = this._locations[2][0];

    // Compute radii in meters using the haversine formula
    const r1 = this.computeDistance(centerLat, centerLon, p1Lat, p1Lon);
    const r2 = this.computeDistance(centerLat, centerLon, p2Lat, p2Lon);

    // Compute initial bearing from center to first diameter point.
    const orientation =
      (this.computeBearing(centerLat, centerLon, p1Lat, p1Lon) + 360) % 360;

    // Number of points to approximate the shape.
    const steps = 128;
    const ellipsePoints: { x: number; y: number }[] = [];

    // Loop over the parametric angle range.
    for (let i = 0; i < steps; i++) {
      // Compute theta in degrees from the adjusted range.
      const theta = 0 + (i * 360) / steps;
      const thetaRad = (theta * Math.PI) / 180;

      // Compute the ellipse's local coordinates:
      // x = r1 * cos(theta), y = r2 * sin(theta)
      const x = r1 * Math.cos(thetaRad);
      const y = r2 * Math.sin(thetaRad);

      // Rotate the point by the chosen orientation.
      const orientationRad = (orientation * Math.PI) / 180;
      const xRot = x * Math.cos(orientationRad) - y * Math.sin(orientationRad);
      const yRot = x * Math.sin(orientationRad) + y * Math.cos(orientationRad);

      // Calculate distance from the center.
      const distance = Math.sqrt(xRot * xRot + yRot * yRot);

      // Get the bearing (adjust for inverted y-axis).
      let bearing = (Math.atan2(xRot, -yRot) * 180) / Math.PI;

      // Shift bearing by 90° and normalize to 0-359°.
      bearing = (bearing - 90 + 360) % 360;

      // Compute the destination point from the center using the geodesic helper.
      const dest = this.computeDestinationPoint(
        centerLat,
        centerLon,
        distance,
        bearing
      );
      // Project the destination point to map coordinates.
      const projected = this._map.map.projectFromLatLonToPoint([
        dest.lat,
        dest.lon
      ]);
      ellipsePoints.push({ x: projected.x, y: projected.y });
    }

    const scale = this.getZoomScale(this._map.map.getZoom());
    const adjustedStrokeWidth = this._strokeWidth / scale;

    // Draw the shape as a polygon.
    this._graphicsElement.poly(ellipsePoints, true);

    // Need fill to handle element selection
    this._graphicsElement.fill({
      color: this.getFillColorFromCatCode(this._catCode),
      alpha: 0.01
    });

    this._graphicsElement.stroke({
      width: adjustedStrokeWidth,
      color: this.selected
        ? 'red'
        : this.getFillColorFromCatCode(this._catCode),
      alignment: 0.5
    });
  }

  /**
   * Helper to compute the distance between two geographic points using the Haversine formula.
   */
  private computeDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6378137;
    const toRad = (val: number) => (val * Math.PI) / 180;
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRad(lat1)) *
        Math.cos(toRad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Computes the initial bearing (in degrees) from one geographic point to another.
   */
  private computeBearing(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const toRad = (val: number) => (val * Math.PI) / 180;
    const toDeg = (val: number) => (val * 180) / Math.PI;
    const dLon = toRad(lon2 - lon1);
    const y = Math.sin(dLon) * Math.cos(toRad(lat2));
    const x =
      Math.cos(toRad(lat1)) * Math.sin(toRad(lat2)) -
      Math.sin(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.cos(dLon);
    let brng = Math.atan2(y, x);
    return (toDeg(brng) + 360) % 360;
  }

  redraw(): void {
    this.drawEllipse();
    this.addLabel();
  }

  setSpeedAndBearing(speed: number, bearing: number): void {}

  setSymbolCode(symbolCode: string): void {}
}
