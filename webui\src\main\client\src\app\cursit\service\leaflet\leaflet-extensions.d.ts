declare namespace L {
  namespace TileLayer {
    class WMTS extends Tile<PERSON><PERSON><PERSON> {
      constructor(baseUrl: string, options: any);
      setParams(params: any, noRedraw?: boolean): this;
      wmtsParams: any;
      options: any;
    }
  }

  namespace tileLayer {
    function wmts(baseUrl: string, options?: any): TileLayer.WMTS;
    function getWMTSCapabilities(baseUrl: string): Promise<{ layer: string, tilematrixset: string, style: string, format: string }>
  }
}
