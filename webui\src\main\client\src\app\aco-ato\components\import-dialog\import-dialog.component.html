<h2 mat-dialog-title>RAP (ATO/ACO) Importer</h2>
<form [formGroup]="form" mat-dialog-content>
  @for (control of fileControls; track control.key) {
    <div class='file-row'>
      <mat-form-field appearance="fill" class="file-input">
        <mat-label> {{ control.label }}</mat-label>
        <input matInput
               #fileInput
               [formControlName]="control.key"
               [accept]="control.accept"
               vcciFilePayload
        />
        @if (form.get(control.key)?.hasError('invalidExtension')) {
          <mat-error>Expected file type is {{ control.accept }}</mat-error>
        }
      </mat-form-field>
      <button mat-raised-button type="button" (click)="fileInput.click()">Select</button>
    </div>
  }
</form>
<mat-dialog-actions align="end">
  <button
    [disabled]="form.invalid"
    [mat-dialog-close]="form.getRawValue()"
    color="primary"
    mat-raised-button>Execute
  </button>
  <button mat-button mat-dialog-close>Cancel</button>
</mat-dialog-actions>
