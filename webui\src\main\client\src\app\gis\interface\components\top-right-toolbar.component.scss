@import 'toolbar-mixins';


.top-right-toolbar {
  z-index: 450;
  display: flex;
  position: absolute;
  top: 15px;
  left: 160px;
  width: calc(100% - 160px);
  // Allow pass through of pointer events to the map, wrapper elements should set pointer-events: all;
  pointer-events: none;
}

.button-wrapper {
  @include button-wrapper-styling;
  // Add pointer events after disabling from parent
  pointer-events: all;
}

button {
  @include toolbar-button-styling;
}

button:hover {
  @include button-hover-styling;
}

.expanded {
  @include expanded-button-styling;
}

:host ::ng-deep .expanded .button-icon .foregroundColor {
  @include expanded-button-styling;
}

button:active {
  @include button-active-styling;
}

::ng-deep .button-icon {
  @include toolbar-button-icon-styling;
}

.slider-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 15px;
  margin-left: 5px;
  // Add pointer events after disabling from parent
  pointer-events: all;
}

.slider-background-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //background: rgb(204, 204, 204, .5);
}

.slider-background-world {
  width: 80px;
  height: auto;
}

.slider-background-scale {
  margin-top: -30px;
  width: 140px;
  height: auto;
}

:host ::ng-deep .slider-background-world .foregroundColor,
:host ::ng-deep .slider-background-scale .foregroundColor,
:host ::ng-deep .element-resize-scale .foregroundColor {
  fill: $icon-color;
}

.map-opacity-slider {
  margin-top: -15px;
  margin-left: 8px;
  width: 128px;
  height: 20px;
}

::ng-deep .map-opacity-slider .mdc-slider__thumb-knob {
  margin-top: -15px;
  width: 15px !important;
  height: 15px !important;
  border: unset !important;
}

.map-opacity-label {
  margin-top: 3px;
  max-width: unset;
}

.slider-background-wrapper {
  max-height: 55px;
}

.element-resize-slider-button {
  margin-top: -15px;
  background-color: transparent;
  border: unset;
  width: 20px;
  height: 20px;
}

.element-resize-scale {
  margin-top: 10px;
  width: 140px;
  height: auto;
}


::ng-deep .map-opacity-slider.mat-mdc-slider.mat-primary {
  --mdc-slider-handle-color: #0f2648;
  --mdc-slider-focus-handle-color: #0f2648;
  --mdc-slider-hover-handle-color: #7b1fa200;
  --mdc-slider-active-track-color: #1f34a200;
  --mdc-slider-inactive-track-color: #1f23a200;
  --mdc-slider-with-tick-marks-active-container-color: #fff;
  --mdc-slider-with-tick-marks-inactive-container-color: #7b1fa200;
  --mat-mdc-slider-ripple-color: #7b1fa200;
  --mat-mdc-slider-hover-ripple-color: rgba(123, 31, 162, 0.00);
  --mat-mdc-slider-focus-ripple-color: rgba(123, 31, 162, 0.0);
}
