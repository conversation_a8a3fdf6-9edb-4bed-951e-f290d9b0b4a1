<div class="top-toolbar">
  <div
    class="toolbar-container"
    [ngClass]="toolbarExpanded() ? 'expanded' : 'collapsed'"
  >
    <div class="menu-item">
      <button
        mat-button
        matTooltip="Center"
        (click)="centerSelected()"
        class="menu-button"
      >
        Center
        <mat-icon>center_focus_strong</mat-icon>
      </button>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="menu-item">
      <button
        mat-button
        matTooltip="Filter"
        (click)="filterAction()"
        class="menu-button"
      >
        Filter
        <mat-icon>filter_list</mat-icon>
      </button>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="menu-item">
      <button
        mat-button
        matTooltip="Layers"
        (click)="showHideLayerList()"
        class="menu-button"
      >
        Layers
        <mat-icon>layers</mat-icon>
      </button>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="menu-item">
      <nexus-entity-menu></nexus-entity-menu>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="menu-item">
      <nexus-movement-menu></nexus-movement-menu>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="menu-item">
      <nexus-drawing-menu></nexus-drawing-menu>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="menu-item">
      <button
        matTooltip="Settings"
        mat-button
        class="menu-button"
        [matMenuTriggerFor]="settingsMenu"
      >
        <mat-icon>settings</mat-icon>
      </button>
      <mat-menu #settingsMenu="matMenu">
        <nexus-settings-slider></nexus-settings-slider>
      </mat-menu>
    </div>
  </div>

  <div
    class="toggle-button"
    [ngClass]="toolbarExpanded() ? 'expandedToggle' : 'collapsedToggle'"
  >
    <mat-icon
      (click)="toggleExpand()"
      [@rotate]="toolbarExpanded()"
      matTooltip="{{
        toolbarExpanded() ? 'Collapse toolbar' : 'Expand toolbar'
      }}"
      >keyboard_arrow_down</mat-icon
    >
  </div>
</div>
