import { Coordinate } from '../../cursit/models/cursit.model';

const reverseCoordinate = (coord: Coordinate): Coordinate => {
  if (coord.length === 2) {
    return [coord[1], coord[0]];
  }
  if (coord.length === 3) {
    return [coord[1], coord[0], coord[2]];
  }
  throw new Error('Invalid coordinate format');
};

export const reverseCoordinates = (coordinate: Coordinate | Coordinate[]):
  Coordinate | Coordinate[] => {
  if (coordinate.flat(1).length > 3) {
    return coordinate.map(coord => reverseCoordinate(coord as Coordinate));
  }

  return reverseCoordinate(coordinate as Coordinate);
};
