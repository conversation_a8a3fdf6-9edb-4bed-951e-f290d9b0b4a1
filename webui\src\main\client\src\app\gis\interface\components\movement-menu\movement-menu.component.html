<button
  matTooltip="Movement"
  mat-button
  class="menu-button"
  [matMenuTriggerFor]="movementMenu"
>
  Movement
  <mat-icon
    svgIcon="entity-movement-tb"
    style="width: 24px; height: 24px"
  ></mat-icon>
</button>

<mat-menu #movementMenu="matMenu">
  <button mat-menu-item (click)="openMovementDialog()">MNVR. Manager</button>
  <button mat-menu-item>Route</button>
  <button mat-menu-item>Clear Analysis</button>
</mat-menu>
