.entity-selection-menu {
  display: flex;
  position: absolute;
  z-index: 400;
  transform: translateY(-72px);
  transition:
    left 0.1s ease-out,
    top 0.1s ease-out;
  pointer-events: auto;
  will-change: left, top;
}

.entity-selection-menu.hidden {
  display: none;
}

button {
  display: flex;
  align-items: center;
  margin-right: 5px;
  background-color: rgba(var(--background-rgb), var(--opacity-high));
  border: 1px solid rgb(255, 255, 0);
  width: 2.5rem;
  height: 2.5rem;
  cursor: pointer;
}

button:hover {
  cursor: pointer;
  background-color: rgba(var(--secondary-background-dark-rgb), var(--opacity-high));
}

button:active {
  background-color: rgba(var(--secondary-background-light-rgb), var(--opacity-high));
}

::ng-deep .element-menu .foregroundColor {
  fill: rgb(255, 255, 0);
}
