import { Component, Input } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { DataSection } from '../../../../data/bs-params/bs-params.model';
import {
  MatStep,
  Mat<PERSON>tep<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>per<PERSON><PERSON><PERSON>,
  MatStepperPrevious
} from '@angular/material/stepper';
import { MatButton } from '@angular/material/button';
import { BsDataSectionComponent } from '../bs-data-section/bs-data-section.component';
import { BsDataParamBase } from '../bs-data-param/bs-data-param.base';

@Component({
  selector: 'vcci-bs-data-sections',
  templateUrl: 'bs-data-sections.component.html',
  imports: [
    MatStepper,
    MatStep,
    ReactiveFormsModule,
    MatStepLabel,
    MatButton,
    MatStepperPrevious,
    MatStepperNext,
    BsDataSectionComponent
  ],
  styleUrl: 'bs-data-sections.component.css'
})
export class BsDataSectionsComponent extends BsDataParamBase {
  @Input({ required: true }) dataSections: DataSection[] = [];
}
