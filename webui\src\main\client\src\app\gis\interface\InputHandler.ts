import { MouseListener } from './EventListener';
import { MapDisplay } from './MapDisplay';
import { CreateRoute } from './RouteCreator';
import { RouteClickData } from './service/gis.service';

export interface InputHandler {
  addMissionPlanningMouseListeners(mouseListener: MouseListener): void;

  removeMissionPlanningMouseListeners(mouseListener: MouseListener): void;

  addMouseCoordinatesListeners(mouseListener: MouseListener): void;

  removeMouseCoordinatesListeners(mouseListener: MouseListener): void;

  startRouteCreation(): void;

  cancelRouteCreation(): void;
}

export class MapInputHandler implements InputHandler {
  mapElement: HTMLElement;
  mapDisplay: MapDisplay<object, object>;
  routeCreator: CreateRoute;

  constructor(mapDisplay: MapDisplay<object, object>) {
    this.mapDisplay = mapDisplay;
    this.mapElement = this.mapDisplay.getContainerDOM();
    this.routeCreator = new CreateRoute(this.mapDisplay);
  }

  startRouteCreation(): void {
    this.routeCreator.startCreateRoute();
  }

  cancelRouteCreation(): void {
    this.routeCreator.endCreateRoute();
  }

  addMissionPlanningMouseListeners(mouseListener: MouseListener): void {
    this.mapElement.addEventListener('mousedown', mouseListener.mouseDown);
    this.mapElement.addEventListener('mouseup', mouseListener.mouseUp);
    this.mapElement.addEventListener('mousemove', mouseListener.mouseMove);
    this.mapElement.addEventListener('mouseleave', mouseListener.mouseLeave);
    this.mapElement.addEventListener('mouseenter', mouseListener.mouseEntered);
    this.mapElement.addEventListener('click', mouseListener.mouseClick);
    this.mapElement.addEventListener(
      'dblclick',
      mouseListener.mouseDoubleClick
    );
  }

  removeMissionPlanningMouseListeners(mouseListener: MouseListener): void {
    this.mapElement.removeEventListener('mousedown', mouseListener.mouseDown);
    this.mapElement.removeEventListener('mouseup', mouseListener.mouseUp);
    this.mapElement.removeEventListener('mousemove', mouseListener.mouseMove);
    this.mapElement.removeEventListener('mouseleave', mouseListener.mouseLeave);
    this.mapElement.removeEventListener(
      'mouseenter',
      mouseListener.mouseEntered
    );
    this.mapElement.removeEventListener('click', mouseListener.mouseClick);
    this.mapElement.removeEventListener(
      'dblclick',
      mouseListener.mouseDoubleClick
    );
  }

  addMouseCoordinatesListeners(mouseListener: MouseListener): void {
    this.mapElement.addEventListener('mousemove', mouseListener.mouseMove);
  }

  removeMouseCoordinatesListeners(mouseListener: MouseListener): void {
    this.mapElement.removeEventListener('mousemove', mouseListener.mouseMove);
  }
}
