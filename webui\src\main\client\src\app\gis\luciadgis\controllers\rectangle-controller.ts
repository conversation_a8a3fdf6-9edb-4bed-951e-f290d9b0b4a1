// Typescript implementation of RectangleController, provided by Luciad support
// As-is JavaScript file provided by support: ./RectangleController

import { LineType } from '@luciad/ria/geodesy/LineType';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { Bounds } from '@luciad/ria/shape/Bounds';
import { Point } from '@luciad/ria/shape/Point';
import { createBounds } from '@luciad/ria/shape/ShapeFactory';
import { Transformation } from '@luciad/ria/transformation/Transformation';
import { createTransformation } from '@luciad/ria/transformation/TransformationFactory';
import { Controller } from '@luciad/ria/view/controller/Controller';
import { EVENT_HANDLED, EVENT_IGNORED, HandleEventResult } from '@luciad/ria/view/controller/HandleEventResult';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { GestureEventType } from '@luciad/ria/view/input/GestureEventType';
import { ModifierType } from '@luciad/ria/view/input/ModifierType';
import { Map } from '@luciad/ria/view/Map';
import { GeoCanvas } from '@luciad/ria/view/style/GeoCanvas';

/**
 * Base class for rectangle-based controllers (select-by-rectangle, zoom-by-rectangle,...)
 **/
export abstract class RectangleController extends Controller {
  _rectangleStyle = {
    stroke: { color: 'rgb(6,47,141)', width: 3 },
    // stroke: { color: 'rgb(96, 134, 238)', width: 3 },
    // fill: { color: 'rgba(96, 134, 238, 0.5)' },
    lineType: LineType.CONSTANT_BEARING
  };

  _isDragging = false;
  _fromPointModel: Point | null = null;
  _fromPointView: Point | null = null;
  _toPointModel: Point | null = null;
  _toPointView: Point | null = null;
  _mapToModel: Transformation | null = null;

  override onActivate(map: Map): void {
    super.onActivate(map);
    this._mapToModel = createTransformation(map.reference, getReference('CRS:84'));
  }

  override onDeactivate(map: Map): void {
    this._mapToModel = null;
    return super.onDeactivate(map);
  }

  override onDraw(geoCanvas: GeoCanvas): void {
    if (this._fromPointModel && this._toPointModel) {
      const bounds = RectangleController._calculateDrawnRectangleBounds(
        this._fromPointModel,
        this._toPointModel
      );
      if (bounds) {
        geoCanvas.drawShape(bounds, this._rectangleStyle);
      }
    }
  }

  override onGestureEvent(event: GestureEvent): HandleEventResult {
    switch (event.type) {
      case GestureEventType.DRAG:
        return this._onDrag(event);
      case GestureEventType.DRAG_END:
        if (this._isDragging) return this._onDragEnd(event);
        return EVENT_IGNORED;
      default:
        return EVENT_IGNORED;
    }
  }

  _modelPointUnderMouse(event: GestureEvent): Point | null {
    if (this.map && this._mapToModel) {
      try {
        const mapPoint = this.map.viewToMapTransformation.transform(event.viewPoint);
        return this._mapToModel.transform(mapPoint);
      } catch (ex) {
        // console.error(ex);
      }
    }
    return null;
  }

  _onDrag(event: GestureEvent): HandleEventResult {
    // If Control key is down, pan control is activated
    if (event.modifier === ModifierType.CTRL) {
      return HandleEventResult.EVENT_IGNORED;
    }
    if (!this._isDragging) {
      this._fromPointView = event.viewPoint;
      this._fromPointModel = this._modelPointUnderMouse(event);
      this._isDragging = true;
    }
    this.map.clearSelection();
    this._toPointView = event.viewPoint;
    this._toPointModel = this._modelPointUnderMouse(event);
    this.invalidate(); // trigger re-draw
    return EVENT_HANDLED;
  }

  _onDragEnd(event: GestureEvent): HandleEventResult {
    // If Control key is down, pan control is activated
    if (event.modifier === ModifierType.CTRL) {
      return HandleEventResult.EVENT_IGNORED;
    }
    this._toPointModel = this._modelPointUnderMouse(event);
    const modelBounds = RectangleController._calculateDrawnRectangleBounds(
      this._fromPointModel,
      this._toPointModel
    );
    const viewBounds = RectangleController._calculateDrawnRectangleBounds(
      this._fromPointView,
      this._toPointView
    );
    this.rectangleDragged(modelBounds, viewBounds);
    this._isDragging = false;
    this.invalidate();
    return EVENT_HANDLED;
  }

  static _calculateDrawnRectangleBounds(p1: Point | null, p2: Point | null): Bounds | null {
    if (p1 && p2) {
      const minX = Math.min(p1.x, p2.x);
      const maxX = Math.max(p1.x, p2.x);
      const minY = Math.min(p1.y, p2.y);
      const maxY = Math.max(p1.y, p2.y);

      const width = maxX - minX;
      const height = maxY - minY;

      return createBounds(p1.reference, [minX, width, minY, height]);
    }
    return null;
  }

  abstract rectangleDragged(modelBounds: Bounds | null, viewBounds: Bounds | null): void;

  reset(): void {
    this._isDragging = false;
    this._fromPointModel = null;
    this._toPointModel = null;
    this.invalidate();
  }
}
