import {
  LatLngTuple,
  Layer,
  LayerGroup,
  Map,
  MapOptions,
  Point
} from 'leaflet';
import { MilitarySymbologyContainer } from './leaflet-symbology-layer/MilitarySymbologyContainer';
import { leafletLayer } from './utility';

export class LeafletMap extends Map {
  midZoom: number;
  _pixiContainer: MilitarySymbologyContainer;
  rootLayer = new LayerGroup<Layer | LayerGroup>();

  constructor(element: string | HTMLElement, options?: MapOptions) {
    super(element, options);
    this.midZoom = (this.getMaxZoom() + this.getMinZoom()) / 2;

    const root = leafletLayer(this.rootLayer);
    root.visible(true);
    root.layerId = '-11';
    root.layerName = 'rootLayer';
  }

  getRootLayer(): LayerGroup {
    return leafletLayer(this.rootLayer);
  }

  projectFromLatLonToPoint(latLon: LatLngTuple): Point {
    return this.project(latLon, this.midZoom);
  }

  projectFromLatLonToLatLngExpression(latLon: LatLngTuple): LatLngTuple {
    const point: Point = this.project(latLon, this.midZoom);
    return [point.x, point.y];
  }

  get scaleForProjection(): number {
    return this.getZoomScale(this.getZoom(), this.midZoom);
  }

  override addLayer(layer: Layer): this {
    if (layer instanceof MilitarySymbologyContainer) {
      this.pixiContainer = layer;
    }
    return super.addLayer(layer);
  }

  set pixiContainer(container: MilitarySymbologyContainer) {
    this._pixiContainer = container;
  }

  get pixiContainer(): MilitarySymbologyContainer {
    return this._pixiContainer;
  }

  redraw(): void {
    // This only redraws the first layer i.e. if CLOCS and TAK are connected, only TAK will be redrawn.
    this.pixiContainer.redrawAll();

    // TODO replace with proper redraw structure
    // this.fire('moveend');
  }
}
