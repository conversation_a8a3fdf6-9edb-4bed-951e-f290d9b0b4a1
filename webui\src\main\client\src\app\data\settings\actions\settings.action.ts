import { createAction, props } from '@ngrx/store';
import { LayoutState } from '../../../core/reducers/layout.reducer';
import { DEFAULT_SETTINGS, Settings } from '../settings.model';

export const resetAllSettings = createAction('[Settings] Reset All Settings');

export const saveSettings = createAction(
  '[Settings] Save Settings',
  props<{ settings: Settings }>()
);
export const showSnackBar = createAction(
  '[UI] Show snack bar',
  props<{ message: string }>()
);
export const setLayoutState = createAction(
  '[Layout] Set Layout Defaults',
  props<{ connPannelSettings: any }>()
);
