/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/15/2024, 2:27 PM
 */
import { Coordinate } from '../../../cursit/models/cursit.model';

export const Reference = {
  Equidistant: 'EPSG:4326',
  GeoCentric: 'EPSG:4978',
  WebMercator: 'EPSG:3857'
} as const;

export type GISCoordinateReference = typeof Reference[keyof typeof Reference];

export type ScreenCoordinates = { x: number, y: number };

export interface MapElementSelectionInfo {
  elementId: string | number | undefined;
  coordinates: Coordinate[];
  screenCoordinates: ScreenCoordinates;
}

export type StrokeType = undefined | 'solid' | 'dashed' | 'dotted';
export type FillType = undefined | 'plain' | 'hatched';
export type StrokeWidth = number;

export interface StrokeInfo {
  strokeColor?: string;
  strokeType?: StrokeType;
  strokeWidth?: StrokeWidth;
}

export interface FillInfo {
  fillColor?: string;
  fillType?: FillType;
}

export type ShapeStyle = {
  strokeInfo: StrokeInfo,
  fillInfo: FillInfo
};

export type ShapeStyleInfo = {
  normal: ShapeStyle,
  selected: ShapeStyle
};

export interface SelectedShapeStyleInfo {
  shapeId?: string | number;
  shapeStyleInfo?: ShapeStyleInfo;
}
