/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/21/2023, 1:10 PM
 */
import { LeafletEntity } from '../LeafletEntity';

declare class QuadtreeNode {
  constructor(
    x: number,
    y: number,
    width: number,
    height: number,
    maxObjects: number,
    maxLevels: number,
    level: number
  );

  split(): void;

  getIndex(entity: LeafletEntity): number;

  insert(entity: LeafletEntity): void;

  retrieve(entity: LeafletEntity): LeafletEntity[];
}
