import { createFeature, createReducer, on } from '@ngrx/store';

import { LayoutActions } from '../actions/layout.actions';
import { DEFAULT_SETTINGS } from 'src/app/data/settings/settings.model';

export type SidePanelState = 'Consolidated' | 'Routing' | 'Hidden';
export type ConnectionPanelDisplay = 'ALL' | 'ENDPOINTS';
export type ConnectionPanelPosition = 'left' | 'right';

export interface LayoutState {
  showLeftSideNav: boolean;
  showRightSideNav: boolean;
  sidePanel: SidePanelState;
  connectionPanelDisplay: ConnectionPanelDisplay;
  connectionsExpanded: boolean;
  panelPosition: ConnectionPanelPosition;
  hideDataman: boolean;
  hideArcs: boolean;
}

const initialState: LayoutState = {
  showLeftSideNav: DEFAULT_SETTINGS.connPanel.expanded,
  showRightSideNav: DEFAULT_SETTINGS.connPanel.expanded,
  sidePanel: DEFAULT_SETTINGS.connPanel.mode,
  connectionPanelDisplay: DEFAULT_SETTINGS.connPanel.connectionDisplay,
  connectionsExpanded: DEFAULT_SETTINGS.connPanel.connectionsExpanded,
  panelPosition: DEFAULT_SETTINGS.connPanel.position,
  hideDataman: DEFAULT_SETTINGS.layout.hideDataman,
  hideArcs: DEFAULT_SETTINGS.layout.hideArcs
};

export const layoutFeature = createFeature({
  name: 'layout',
  reducer: createReducer(
    initialState,
    on(LayoutActions.setInitialLayoutState, (state, action) => ({ ...state, ...action.payload })),
    on(LayoutActions.toggleLeftSidenav, (state, action) => ({ ...state, showLeftSideNav: action.payload ?? !state.showLeftSideNav })),
    on(LayoutActions.toggleRightSidenav, (state, action) => ({ ...state, showRightSideNav: action.payload ?? !state.showRightSideNav })),
    on(LayoutActions.toggleConnectionsExpanded, (state, action) => ({ ...state, connectionsExpanded: action.payload ?? !state.connectionsExpanded })),
    on(LayoutActions.toggleDatamanStatus, (state, action) => ({ ...state, hideDataman: action.payload ?? !state.hideDataman })),
    on(LayoutActions.toggleBackgroundArcEffect, (state, action) => ({ ...state, hideArcs: action.payload ?? !state.hideArcs })),
    on(LayoutActions.changeSidePanel, (state, action) => ({ ...state, sidePanel: action.payload })),
    on(LayoutActions.changeConnectionPanelDisplay, (state, action) => ({ ...state, connectionPanelDisplay: action.payload })),
    on(LayoutActions.changeConnectionPanelPosition, (state, action) => ({ ...state, panelPosition: action.payload })),
  )
});

export const {
  name,
  reducer,
  selectLayoutState,
  selectShowLeftSideNav,
  selectShowRightSideNav,
  selectConnectionsExpanded,
  selectSidePanel,
  selectConnectionPanelDisplay,
  selectPanelPosition,
  selectHideDataman,
  selectHideArcs
} = layoutFeature;
