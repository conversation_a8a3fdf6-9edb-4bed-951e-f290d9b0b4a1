import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Point } from '../../../shared/util/svg/line-svg.model';

@Component({
    selector: '[routeSvgButton]',
    template: `
    <svg:circle
      [ngClass]="myCircleClasses"
      [class.svg-clickable]="isSvgClickable"
      [class.svg-normal]="!isSvgClickable"
      [attr.fill]="fillColor"
      [attr.stroke-width]="strokeWidth"
      [attr.r]="radius"
      [attr.cx]="circleX"
      [attr.cy]="circleY"
      [attr.stroke]="selected | routeColor: connectedTo"
      (click)="svgClick.emit()"
    />
    <svg:use
      class="svg-img"
      [class.svg-clickable]="isSvgClickable"
      [class.svg-normal]="!isSvgClickable"
      [ngClass]="myIconClasses"
      [attr.x]="iconX"
      [attr.y]="iconY"
      [attr.height]="height"
      [attr.width]="width"
      [attr.stroke-width]="strokeWidth"
      [attr.href]="svgIconHref"
      [matTooltip]="buttonTooltip"
      (click)="svgClick.emit()"
    />
  `,
    styles: [`
    .svg-img {
      fill: rgb(66, 66, 66);
    }

    .svg-clickable {
      pointer-events: auto;
      cursor: pointer;
    }

    .svg-normal {
      pointer-events: auto;
      cursor: auto;
    }
  `],
    standalone: false
})
export class RouteSvgButtonComponent implements OnChanges {
  private _height = 24;
  private _width = 24;
  private _iconClasses: Record<string, boolean> = {};
  private _circleClasses: Record<string, boolean> = {};

  @Input({ required: true }) iconPoint: Point | undefined = undefined;
  @Input() connectedTo: string[] = [];
  @Input() isInput: boolean = true;
  @Input() initialOffset = 30;
  @Input() selected: string[] = [];
  @Input() lockSizeRatio = true;
  @Input() svgIconHref: string | undefined = undefined;
  @Input() radius = 14;
  @Input() strokeWidth = 3;
  @Input() fillColor = 'grey';
  @Input() tooltip: string | undefined = undefined;
  @Input() iconXOffset = 0;
  @Input() iconYOffset = 0;
  @Input() buttonTooltip: string | undefined = undefined;

  @Input() set iconClasses(classes: string) {
    this._iconClasses = classes.split(' ').reduce<Record<string, boolean>>(
      (acc, c) => ({ ...acc, [c]: true }),
      {}
    );
  }

  @Input() set circleClasses(classes: string) {
    this._circleClasses = classes.split(' ').reduce<Record<string, boolean>>(
      (acc, c, i) => ({ ...acc, [i + 1]: c }),
      {}
    );
  }

  @Input() set height(height: number) {
    if (this.lockSizeRatio) {
      const aspectRatio = this.getAspectRatio();
      this._width = height * aspectRatio;
    }
    this._height = height;
  }

  @Input() set width(width: number) {
    if (this.lockSizeRatio) {
      const aspectRatio = this.getAspectRatio();
      this._height = width / aspectRatio;
    }
    this._width = width;
  }

  @Output() svgClick = new EventEmitter<unknown>();

  circleX = 0;
  circleY = 0;
  iconX = 0;
  iconY = 0;

  get isSvgClickable() {
    return this.svgClick.observed;
  }

  get height(): number {
    return this._height;
  }

  get width(): number {
    return this._width;
  }

  get myIconClasses(): Record<string, boolean> {
    return this._iconClasses;
  }

  get myCircleClasses(): Record<string, boolean> {
    return this._circleClasses;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['iconPoint'] || changes['initialOffset'] || changes['height'] || changes['width']
      || changes['iconXOffset'] || changes['iconYOffset'] || changes['isInput']) {
      this.calculatePositions();
    }
  }

  private calculatePositions(): void {
    if (this.iconPoint === undefined) {
      return;
    }
    // Calculate the position of the outline for the button.
    this.circleX = this.iconPoint.x + (this.isInput ? this.initialOffset : -this.initialOffset);
    this.circleY = this.iconPoint.y;
    // Calculate the position of the image for the button.
    const halfWidth = this.width / 2;
    const halfHeight = this.height / 2;
    this.iconX = this.iconPoint.x + (this.isInput ? (this.initialOffset - halfWidth) : -(this.initialOffset + halfWidth)) + this.iconXOffset;
    this.iconY = this.iconPoint.y - halfHeight + this.iconYOffset;
  }

  getAspectRatio(): number {
    return this._width / this._height;
  }
}
