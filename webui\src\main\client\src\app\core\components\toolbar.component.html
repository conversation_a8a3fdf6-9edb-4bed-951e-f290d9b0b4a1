<ng-container *ngrxLet="sidePanelState$ as state">
  <div class="toolbar">
    <mat-icon style="transform: scale(2); margin-right: 10px" svgIcon="nexus-brand" />

    <button class="system-text" mat-button [matMenuTriggerFor]="fileMenu">File</button>
    <mat-menu #fileMenu="matMenu">
      <!-- <button mat-menu-item>Create Connection</button> -->
      <!-- <button mat-menu-item>Create Entity</button> -->
      <!-- <mat-divider /> -->
      <button mat-menu-item (click)="toggleAutoBackup()">
        <mat-icon>{{ dbAutoBackUpEnabled ? 'database_off' : 'database'}}</mat-icon>
        {{ dbAutoBackUpEnabled ? 'Disable' : 'Enable' }} Auto Backup
      </button>
      <button mat-menu-item (click)="backupDatabase()">
        <mat-icon>backup</mat-icon>
        Backup Database
      </button>
    </mat-menu>

    <!--TODO: Implement Edit menu -->
    <button class="system-text" mat-button>Edit</button>

    <button class="system-text" mat-button [matMenuTriggerFor]="viewMenu">View</button>
    <mat-menu #viewMenu="matMenu">
      <h2 class="menu-group-title">Main Panel</h2>
      <a mat-menu-item routerLink="/cursit" *ngIf="environment.cursit">
        <mat-icon inline class="cursit-icon" svgIcon="world-map" />
        CURSIT
      </a>
      <a mat-menu-item routerLink="/router">
        <mat-icon inline svgIcon="router" />
        ROUTER
      </a>
      <a mat-menu-item routerLink="/services">
        <mat-icon inline svgIcon="node-protocol" />
        SERVICES
      </a>
      <a mat-menu-item routerLink="/logs">
        <mat-icon inline>summarize</mat-icon>
        LOGS
      </a>
      <a mat-menu-item routerLink="/settings">
        <mat-icon inline>settings</mat-icon>
        SETTINGS
      </a>
      <a mat-menu-item routerLink="/aco-ato">
        <mat-icon inline>flight</mat-icon>
        ACO/ATO
      </a>
      <mat-divider />
      <h2 class="menu-group-title">Side Panel</h2>
      <button mat-menu-item (click)="changeSidePanel('Consolidated')">
        <mat-icon *ngIf="state === 'Consolidated'">check</mat-icon>
        Consolidated
      </button>
      <button mat-menu-item (click)="changeSidePanel('Routing')">
        <mat-icon *ngIf="state === 'Routing'">check</mat-icon>
        Routing
      </button>
      <button mat-menu-item (click)="changeSidePanel('Hidden')">
        <mat-icon *ngIf="state === 'Hidden'">check</mat-icon>
        Hidden
      </button>
    </mat-menu>

    <!--TODO: Implement Servers menu -->
    <button class="system-text" mat-button>Servers</button>
  </div>

</ng-container>

<vcci-navigation />
