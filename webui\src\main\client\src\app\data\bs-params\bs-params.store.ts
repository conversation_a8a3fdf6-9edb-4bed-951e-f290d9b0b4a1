import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

import { ComponentStore } from '@ngrx/component-store';
import { tapResponse } from '@ngrx/operators';

import { Crud, SFMap } from '@simfront/common-libs';

import {
  catchError,
  combineLatest,
  concat,
  delay,
  filter,
  interval,
  map,
  Observable,
  of,
  pipe,
  switchMap,
  take,
  takeUntil,
  tap,
  throwError,
  withLatestFrom
} from 'rxjs';

import { environment } from '../../../environments/environment';

import { BSDialogComponent } from '../../battlespace/node/bs-dialog.component';
import { BSFormComponent } from '../../battlespace/node/bs-form.component';
import { nullishOrEmpty } from '../../shared/util/util.model';
import {
  BSCConfigData,
  BSEConfigData,
  BSErrorMessage,
  DataPage,
  DataParameter,
  DataSection,
  GetNext,
  Instruction,
  isBSCConfigData,
  isBSErrorMessage,
  Message,
  ParameterRequest,
  Parameters
} from './bs-params.model';

import {
  canCreateInput,
  canCreateOutput,
  Connection,
  Direction,
  getConnectionId
} from '../connection/connection.model';
import { ConnWithEndpoint, DataService } from '../data.service';
import { Node, NodeSupport } from '../node/node.model';
import { Endpoint, getEndpointId } from '../endpoint/endpoint.model';
import { Store } from '@ngrx/store';
import {
  linkToGlobalState,
  unlinkFromGlobalState
} from '../../reducers/component-store.reducer';

export type BSParamDialogPayload = Partial<BSParamsState> &
  Required<Pick<BSParamsState, 'crud' | 'model'>>;

type Model = 'Connection' | 'Endpoint';

interface BSMessageEvent {
  me: MessageEvent;
  crud: Crud;
  model: Model;
  direction: Direction;
}

export interface BSParamsState {
  crud: Crud;
  model: Model;
  nodes: SFMap<Node>;
  selectedNode: string | undefined;
  direction: Direction | undefined;
  selectedConnection: string | true | undefined;
  connParams: DataParameter[];
  connPages: DataPage[];
  connSections: DataSection[];
  endParams: DataParameter[];
  endPages: DataPage[];
  endSections: DataSection[];
  busy: boolean;
  webSocketState: 'OPENING' | 'CONNECTED' | 'CLOSING' | 'CLOSED';
  endpointName?: string;
  currentParameters?: Parameters;
  error?: string;
  showConnect: boolean;
  invalid: boolean;
}

const initialState: BSParamsState = {
  crud: 'Create',
  model: 'Connection',
  nodes: {},
  selectedNode: undefined,
  direction: undefined,
  selectedConnection: true,
  connParams: [],
  connPages: [],
  connSections: [],
  endParams: [],
  endPages: [],
  endSections: [],
  busy: false,
  webSocketState: 'CLOSED',
  showConnect: false,
  invalid: true
};

interface GetConfigData {
  node: Node;
  direction: Direction;
  directions?: string[];
  connectionName: string | true | undefined;
  connections?: string[];
  instruction?: Instruction;
  parameters?: SFMap<DataParameter>;
  requestParameter?: string;
  endpointName: string;
  crud: Crud;
  model: Model;
}

@Injectable()
export class BSParamsStore
  extends ComponentStore<BSParamsState>
  implements OnDestroy
{
  private ws: WebSocket;
  private form: BSFormComponent | undefined = undefined;

  public setBSFormComponent(form: BSFormComponent): void {
    this.form = form;
  }

  // <editor-fold desc="Selectors">
  readonly crud$ = this.select((state) => state.crud);
  readonly model$ = this.select((state) => state.model);
  readonly title$ = this.select((state) => `${state.crud} ${state.model}`);
  readonly nodes$ = this.select((state) => Object.values(state.nodes));
  readonly webSocketState$ = this.select((state) => state.webSocketState);
  readonly webSocketReady$ = this.webSocketState$.pipe(
    map((wsState) => wsState === 'CONNECTED'),
    tap((isReady) => this.patchState(isReady ? {} : { busy: true })),
    filter((isReady) => isReady)
  );

  readonly selectedNode$ = this.select((state) => state.selectedNode);
  readonly showDirections$ = this.select(
    (state) => state.selectedNode !== undefined
  );
  private readonly _selectedNodeItem$ = this.select(
    (state) => state.nodes[state.selectedNode]
  );
  readonly direction$ = this.select((state) => state.direction);
  readonly selectedConnection$ = this.select(
    (state) => state.selectedConnection
  );
  readonly showConnections$ = this.select(
    (state) => state.selectedNode !== undefined && state.direction !== undefined
  );

  readonly currentParameters$ = this.select((state) => state.currentParameters);
  readonly connParams$ = this.select((state) => state.connParams);
  readonly connPages$ = this.select((state) => state.connPages);
  readonly connSections$ = this.select((state) => state.connSections);

  readonly connParamsWithValue$ = combineLatest([
    this.currentParameters$,
    this.connParams$
  ]).pipe(
    map(([current, connParams]) => this.paramsWithValues(connParams, current))
  );
  readonly connPagesWithValue$ = combineLatest([
    this.currentParameters$,
    this.connPages$
  ]).pipe(
    map(([current, pages]) =>
      pages.map((page) => ({
        ...page,
        parameters: this.mapParamWithValues(page.parameters, current),
        sections: this.sectionsWithValues(page.sections, current)
      }))
    )
  );
  readonly connSectionsWithValue$ = combineLatest([
    this.currentParameters$,
    this.connSections$
  ]).pipe(
    map(([current, sections]) =>
      sections.map((s) => ({
        ...s,
        parameters: this.mapParamWithValues(s.parameters, current)
      }))
    )
  );

  readonly endParams$ = this.select((state) => state.endParams);
  readonly endPages$ = this.select((state) => state.endPages);
  readonly endSections$ = this.select((state) => state.endSections);

  readonly endParamsWithValue$ = combineLatest([
    this.currentParameters$,
    this.endParams$
  ]).pipe(
    map(([current, endParams]) => this.paramsWithValues(endParams, current))
  );
  readonly endPagesWithValue$ = combineLatest([
    this.currentParameters$,
    this.endPages$
  ]).pipe(
    map(([current, pages]) =>
      pages.map((page) => ({
        ...page,
        parameters: this.mapParamWithValues(page.parameters, current),
        sections: this.sectionsWithValues(page.sections, current)
      }))
    )
  );
  readonly endSectionsWithValue$ = combineLatest([
    this.currentParameters$,
    this.endSections$
  ]).pipe(
    map(([current, sections]) =>
      sections.map((s) => ({
        ...s,
        parameters: this.mapParamWithValues(s.parameters, current)
      }))
    )
  );

  private paramsWithValues(
    params: DataParameter[],
    values: Parameters
  ): DataParameter[] {
    return params.map((c) =>
      c.valueList
        ? {
            ...c,
            listValues: values?.listParameters[c.name]?.singleParameters.map(
              (v) => v.value
            )
          }
        : {
            ...c,
            value: values?.singleParameters[c.name]?.value ?? c?.value
          }
    );
  }

  private mapParamWithValues(
    params: SFMap<DataParameter>,
    values: Parameters
  ): SFMap<DataParameter> {
    return Object.entries(params).reduce<SFMap<DataParameter>>(
      (acc, [key, value]) => {
        acc[key] = value.valueList
          ? {
              ...value,
              listValues: values?.listParameters[key]?.singleParameters.map(
                (v) => v.value
              )
            }
          : {
              ...value,
              value: values?.singleParameters[key]?.value ?? value?.value
            };
        return acc;
      },
      {}
    );
  }

  private sectionsWithValues(
    sections: SFMap<DataSection>,
    values: Parameters
  ): SFMap<DataSection> {
    return Object.entries(sections).reduce<SFMap<DataSection>>(
      (acc, [key, section]) => {
        acc[key] = {
          ...section,
          parameters: this.mapParamWithValues(section.parameters, values)
        };
        return acc;
      },
      {}
    );
  }

  private readonly connectionItem$ = this.selectedConnection$.pipe(
    map((connectionName) => connectionName as string),
    withLatestFrom(this.data.nonExchangeWithEndpoint$),
    map(([connectionItem, connections]) =>
      connections.find((c) => c.name === connectionItem)
    )
  );
  readonly endpointName$ = this.select((state) => state.endpointName ?? '');
  readonly busy$ = this.select((state) => state.busy).pipe(delay(0));
  readonly error$ = this.select((state) => state.error);
  readonly lastParameterHasNext$ = this.select((state) =>
    state.crud === 'Create'
      ? state.endParams.length > 0
        ? state.endParams[state.endParams.length - 1]?.hasNext
        : state.connParams.length > 0
          ? state.connParams[state.connParams.length - 1]?.hasNext
          : false
      : false
  );
  readonly hasSingleParameter$ = combineLatest([
    this.connParams$,
    this.endParams$
  ]).pipe(
    map(([connParams, endParams]) => {
      return (
        connParams.some((c) => c.editable) || endParams.some((e) => e.editable)
      );
    })
  );

  readonly invalid$ = concat(
    of(true), // Initial emission
    interval(100).pipe(
      filter(() => this.form !== undefined),
      take(1),
      switchMap(() =>
        combineLatest([
          this.form.isFormInvalid$(),
          this.form.isFormDirty$(),
          this.crud$,
          this.lastParameterHasNext$,
          this.hasSingleParameter$
        ]).pipe(
          map(
            ([invalid, dirty, crud, hasNext, hasSingle]) =>
              invalid ||
              hasNext ||
              !hasSingle ||
              (crud === 'Update' ? !dirty : false)
          )
        )
      )
    )
  ).pipe(delay(0));
  private readonly _nodeSupport$ = this._selectedNodeItem$.pipe(
    map((node) => node?.support)
  );
  readonly availableDirection$ = this._nodeSupport$.pipe(
    withLatestFrom(this.connectionItem$),
    map(([n, conn]) => this._getAvailableDirections(n, conn))
  );

  readonly availableConnections$ = combineLatest([
    this.direction$,
    this._selectedNodeItem$,
    this.data.nonExchangeWithEndpoint$,
    this.crud$,
    this.selectedConnection$
  ]).pipe(
    map(([direction, node, connections, crud, current]) =>
      this._getAvailableConnections(node, direction, connections, crud, current)
    )
  );

  readonly showConnectionButton$ = combineLatest([
    this.endParams$,
    this.connParams$,
    this.crud$
  ]).pipe(
    map(
      ([endParams, connParams, crud]) =>
        connParams.length > 0 &&
        !connParams[connParams.length - 1].hasNext &&
        endParams.length === 0 &&
        crud === 'Create'
    )
  );

  private readonly _configData$ = combineLatest([
    this._selectedNodeItem$,
    this.direction$,
    this.selectedConnection$,
    this.availableDirection$,
    this.availableConnections$,
    this.crud$,
    this.model$,
    this.endpointName$
  ]).pipe(
    map(
      ([
        node,
        direction,
        connectionName,
        directions,
        connections,
        crud,
        model,
        endpointName
      ]) => ({
        node,
        direction,
        connectionName,
        directions,
        connections,
        crud,
        model,
        endpointName
      })
    )
  );
  // </editor-fold>

  //<editor-fold desc="Handlers">
  private _messageHandler = (data: BSMessageEvent): void => {
    const { me, crud, model, direction } = data;
    const config: BSCConfigData | BSEConfigData | BSErrorMessage = JSON.parse(
      me.data
    );
    if (isBSErrorMessage(config)) {
      this.patchState({
        busy: false,
        error: config.errorMessage
      });
    } else if (isBSCConfigData(config)) {
      let message: Partial<BSParamsState> = {
        busy: false
      };
      if (crud === 'Update' && model === 'Endpoint') {
        message['connParams'] = [];
        message['connPages'] = [];
        message['connSections'] = [];
      } else {
        message['connParams'] = Object.values(
          config.connectionConfig?.parameters ?? {}
        );
        message['connPages'] = Object.values(
          config.connectionConfig?.pages ?? {}
        );
        message['connSections'] = Object.values(
          config.connectionConfig?.sections ?? {}
        );
      }
      if (
        (crud === 'Update' && model === 'Connection') ||
        !config.includeEndpoint
      ) {
        message['endParams'] = [];
        message['endPages'] = [];
        message['endSections'] = [];
      } else {
        message['endParams'] = Object.values(
          config.endpointConfig?.parameters ?? {}
        );
        message['endPages'] = Object.values(config.endpointConfig?.pages ?? {});
        message['endSections'] = Object.values(
          config.endpointConfig?.sections ?? {}
        );
      }
      this.patchState(message);
    } else {
      this.patchState({
        busy: false,
        endParams: Object.values(config.endpointConfig?.parameters ?? {}).map(
          (p) =>
            p.type === 'IDENTIFIER'
              ? {
                  ...p,
                  defaultValue: `${config.connectionName}_${direction}_FEED`
                }
              : p
        ),
        endPages: Object.values(config.endpointConfig?.pages ?? {}),
        endSections: Object.values(config.endpointConfig?.sections ?? {})
      });
    }
  };

  private _errorHandler = (error: string | undefined): void => {
    if (!!error) {
      console.error(error);
    }
    this.patchState({
      error,
      busy: false
    });
  };
  //</editor-fold>

  // <editor-fold desc="Actions and Effects">
  public changeConnectionName(name: string): void {
    this._changeConnectionName(name);
  }

  private readonly _changeConnectionName = this.effect<string>(
    pipe(
      withLatestFrom(this.endParams$, this.direction$),
      map(([name, endParams, direction]) => ({ name, endParams, direction })),
      filter(({ name, endParams }) => {
        const identifier = endParams.find((e) => e.type === 'IDENTIFIER');
        return (
          !!name &&
          identifier !== undefined &&
          (nullishOrEmpty(identifier.value) ||
            identifier.value.endsWith('_FEED'))
        );
      }),
      map(({ name, endParams, direction }) =>
        endParams.map((e) =>
          e.type === 'IDENTIFIER'
            ? {
                ...e,
                value: `${name}_${direction}_FEED`
              }
            : e
        )
      ),
      tap((endParams: DataParameter[]) => this.patchState({ endParams }))
    )
  );

  public changeEndpointName(name: string): void {
    this._changeEndpointName(name);
  }

  private readonly _changeEndpointName = this.effect<string>(
    pipe(
      withLatestFrom(this.endParams$),
      map(([value, endParams]) => ({
        value,
        endParams,
        identifier: endParams.findIndex((e) => e.type === 'IDENTIFIER')
      })),
      filter(({ identifier }) => identifier !== -1),
      map(({ value, endParams, identifier }) => {
        let updates = [...endParams];
        updates[identifier] = { ...updates[identifier], value };
        this.patchState({ endParams: updates });
        // endParams[identifier] = { ...endParams[identifier], value };
        return updates;
      })
    )
  );

  private readonly _updateNodes = this.updater((state, nodes: Node[]) => ({
    ...state,
    nodes: nodes.reduce<SFMap<Node>>((acc, n) => ({ ...acc, [n.name]: n }), {})
  }));

  private readonly _updateSelection = this.effect<Partial<BSParamsState>>(
    pipe(
      tap((state) => {
        this.patchState(state);
      }),
      switchMap(() =>
        this.webSocketReady$.pipe(
          withLatestFrom(this._configData$, this.crud$),
          switchMap(([_, data, crud]) =>
            this._send(data, crud).pipe(
              tapResponse(this._messageHandler, this._errorHandler)
            )
          )
        )
      )
    )
  );

  private readonly _getNext = this.effect<{ getNext: GetNext; m: Message }>(
    pipe(
      withLatestFrom(this._configData$, this.crud$),
      switchMap(([{ getNext, m }, data, crud]) =>
        this._send({ ...data, ...getNext }, crud, m).pipe(
          tapResponse(this._messageHandler, this._errorHandler)
        )
      )
    )
  );

  public updateSelection(state: Partial<BSParamsState>): void {
    this._updateSelection(state);
  }

  public getNext(getNext: GetNext, m: Message): void {
    this._getNext({ getNext, m });
  }

  public createConnection(connParams: Parameters): void {
    this._createConnection(connParams);
  }

  private readonly _createConnection = this.effect<Parameters>(
    pipe(
      withLatestFrom(this._configData$, this.selectedConnection$),
      tap(() => this.patchState({ busy: true })),
      map(([connParams, data, connName]) => ({
        connParams,
        data,
        alreadyCreated: typeof connName === 'string'
      })),
      switchMap(({ data, connParams, alreadyCreated }) =>
        this._createConnection$(data, connParams, alreadyCreated).pipe(
          tap((connection) =>
            this.patchState({ currentParameters: connection.parameters })
          ),
          tap(() => this._hideConnectionParameters()),
          tap((connection) =>
            this.updateSelection({
              selectedConnection: connection.name,
              model: 'Endpoint'
            })
          )
        )
      )
    )
  );

  private readonly _hideConnectionParameters = this.effect<void>(
    pipe(
      withLatestFrom(this.connParamsWithValue$),
      map(([_, connParams]) => connParams),
      tap((connParams: DataParameter[]) =>
        this.patchState({
          connParams: connParams.map((c) => ({ ...c, visible: false }))
        })
      )
    )
  );

  private readonly _disableConnectionParameters = this.effect<void>(
    pipe(
      withLatestFrom(this.connParamsWithValue$),
      map(([_, connParams]) => connParams),
      tap((connParams: DataParameter[]) =>
        this.patchState({
          connParams: connParams.map((c) => ({
            ...c,
            editable: false
          }))
        })
      )
    )
  );

  private _createConnection$(
    data: GetConfigData,
    connParams: Parameters,
    alreadyCreated: boolean
  ): Observable<Connection> {
    return new Observable<Connection>((subscriber) => {
      if (
        !this._isValidSelection(data) ||
        (Object.values(connParams.singleParameters).length === 0 &&
          Object.values(connParams.listParameters).length === 0 &&
          !alreadyCreated)
      ) {
        subscriber.error(
          'Could not create the connection, invalid configurations!'
        );
        subscriber.complete();
        return;
      }
      subscriber.next({
        name:
          data.connectionName === true
            ? connParams.singleParameters['CONNECTION_NAME']?.value
            : data.connectionName,
        node: data.node.name,
        status: 'DISCONNECTED',
        parameters: connParams,
        exchangeConnection: false,
        direction: data.direction
      });
      subscriber.complete();
    }).pipe(
      switchMap((connection) =>
        alreadyCreated
          ? of(connection)
          : this.data.connection.api.createOne(connection).pipe(
              tap((connection) =>
                this.data.connection.createOneReceived(connection)
              ),
              catchError((her) =>
                throwError(
                  () => `Failed to create the connection: ${her.error}`
                )
              )
            )
      ),
      tapResponse(
        (connection) =>
          this.patchState({
            busy: false,
            selectedConnection: connection.name
          }),
        this._errorHandler
      )
    );
  }

  private _createEndpoint$(
    connName: string,
    data: GetConfigData,
    endParams: Parameters
  ): Observable<Endpoint> {
    return new Observable<Endpoint>((subscriber) => {
      if (
        !connName ||
        (Object.values(endParams.singleParameters).length === 0 &&
          Object.values(endParams.listParameters).length === 0) ||
        endParams.singleParameters['ENDPOINT_NAME']?.value === ''
      ) {
        subscriber.error(
          'Could not create the endpoint, invalid configurations!'
        );
        subscriber.complete();
        return;
      }
      subscriber.next({
        name: endParams.singleParameters['ENDPOINT_NAME']?.value,
        node: data.node.name,
        capability: data.direction === 'IN' ? 'DATA_RECEIVER' : 'DATA_SENDER',
        connection: connName,
        state: 'PAUSED',
        parameters: endParams
      });
      subscriber.complete();
    }).pipe(
      switchMap((endpoint) =>
        this.data.endpoint.api.createOne(endpoint).pipe(
          tap((endpoint) => this.data.endpoint.createOneReceived(endpoint)),
          catchError((her) =>
            throwError(() => `Failed to create the endpoint: ${her.error}`)
          )
        )
      ),
      tapResponse(() => this.patchState({ busy: false }), this._errorHandler)
    );
  }

  /* Update Connection and Endpoint */

  public updateConnection(connParams: Parameters): void {
    this._updateConnection(connParams);
  }

  private readonly _updateConnection = this.effect<Parameters>(
    pipe(
      withLatestFrom(this._configData$, this.selectedConnection$),
      tap(() => this.patchState({ busy: true })),
      map(([connParams, data]) => ({
        connParams,
        data
      })),
      switchMap(({ data, connParams }) =>
        this._updateConnection$(data, connParams).pipe(
          tap((connection) => {
            this.patchState({ currentParameters: connection.parameters });
            this._disableConnectionParameters();
            this.updateSelection({
              selectedConnection: connection.name,
              model: 'Endpoint'
            });
          }),
          catchError((error) => {
            console.error('Connection update failed:', error);
            this.patchState({ busy: false });
            return throwError(() => error);
          })
        )
      )
    )
  );

  private _updateConnection$(
    data: GetConfigData,
    connParams: Parameters
  ): Observable<Connection> {
    return new Observable<Connection>((subscriber) => {
      if (
        !this._isValidSelection(data, 'Update') ||
        (Object.values(connParams.singleParameters).length === 0 &&
          Object.values(connParams.listParameters).length === 0)
      ) {
        subscriber.error(
          new Error('Could not update the connection, invalid configurations!')
        );
        subscriber.complete();
        return;
      }

      const connection: Connection = {
        name:
          data.connectionName === true
            ? connParams.singleParameters['CONNECTION_NAME']?.value
            : data.connectionName,
        node: data.node.name,
        status: 'DISCONNECTED',
        parameters: connParams,
        exchangeConnection: false,
        direction: data.direction
      };

      subscriber.next(connection);
      subscriber.complete();
    }).pipe(
      switchMap((connection) =>
        this.data.connection.api
          .updateOne(
            { id: getConnectionId(connection), changes: connection },
            false
          )
          .pipe(
            tap((connection) =>
              this.data.connection.updateOneReceived(connection)
            ),
            catchError((her) =>
              throwError(() => `Failed to update the connection: ${her.error}`)
            )
          )
      ),
      tapResponse((connection) => {
        this.patchState({
          busy: false,
          selectedConnection: connection.name
        });
      }, this._errorHandler)
    );
  }

  public updateEndpoint(endParams: Parameters): void {
    this._updateEndpoint({ endParams });
  }
  private readonly _updateEndpoint = this.effect<{
    endParams: Parameters;
  }>(
    pipe(
      withLatestFrom(this._configData$),
      tap(() => this.patchState({ busy: true })),
      map(([{ endParams }, data]) => ({
        endParams,
        data
      })),
      switchMap(({ endParams, data }) => this._updateEndpoint$(data, endParams))
    )
  );

  private _updateEndpoint$(
    data: GetConfigData,
    endParams: Parameters
  ): Observable<Endpoint> {
    return new Observable<Endpoint>((subscriber) => {
      if (
        (Object.values(endParams.singleParameters).length === 0 &&
          Object.values(endParams.listParameters).length === 0) ||
        !data.endpointName
      ) {
        subscriber.error(
          'Could not update the endpoint, invalid configurations!'
        );
        subscriber.complete();
        return;
      }
      subscriber.next({
        name: data.endpointName,
        node: data.node.name,
        capability: data.direction === 'IN' ? 'DATA_RECEIVER' : 'DATA_SENDER',
        connection: data.connectionName as string,
        state: 'PAUSED',
        parameters: endParams
      });
      subscriber.complete();
    }).pipe(
      switchMap((endpoint) => {
        const id = getEndpointId(endpoint);
        return this.data.endpoint.api
          .updateOne({ id, changes: endpoint }, false)
          .pipe(
            tap((endpoint) => this.data.endpoint.updateOneReceived(endpoint)),
            catchError((her) =>
              throwError(() => `Failed to update the endpoint: ${her.error}`)
            )
          );
      }),
      tapResponse(() => this.patchState({ busy: false }), this._errorHandler)
    );
  }

  public submit(
    connParams: Parameters,
    endParams: Parameters,
    crudType: Crud = 'Create',
    model: Model = 'Connection'
  ): void {
    if (crudType === 'Create') {
      this._submit({ connParams, endParams });
    } else {
      if (model === 'Endpoint') {
        this._submitUpdateEndpoint({ endParams });
      } else {
        this._submitUpdateConnection({ connParams });
      }
    }
  }

  private readonly _submit = this.effect<{
    connParams: Parameters;
    endParams: Parameters;
  }>(
    pipe(
      withLatestFrom(this._configData$, this.selectedConnection$),
      map(([{ connParams, endParams }, data, connName]) => ({
        connParams,
        endParams,
        data,
        alreadyCreated: typeof connName === 'string'
      })),
      tap(() => this.patchState({ busy: true, error: undefined })),
      switchMap(({ data, connParams, endParams, alreadyCreated }) =>
        this._createConnection$(data, connParams, alreadyCreated).pipe(
          switchMap((connection) =>
            this._createEndpoint$(connection.name, data, endParams)
          )
        )
      ),
      tapResponse(() => this.matDialog.close(), this._errorHandler)
    )
  );

  private readonly _submitUpdateConnection = this.effect<{
    connParams: Parameters;
  }>(
    pipe(
      withLatestFrom(this._configData$, this.selectedConnection$),
      map(([{ connParams }, data]) => ({
        connParams,
        data
      })),
      tap(() => this.patchState({ busy: true, error: undefined })),
      switchMap(({ data, connParams }) =>
        this._updateConnection$(data, connParams)
      ),
      tapResponse(() => this.matDialog.close(), this._errorHandler)
    )
  );

  private readonly _submitUpdateEndpoint = this.effect<{
    endParams: Parameters;
  }>(
    pipe(
      withLatestFrom(this._configData$, this.selectedConnection$),
      map(([{ endParams }, data]) => ({
        endParams,
        data
      })),
      tap(() => this.patchState({ busy: true, error: undefined })),
      switchMap(({ data, endParams }) =>
        this._updateEndpoint$(data, endParams)
      ),
      tapResponse(() => this.matDialog.close(), this._errorHandler)
    )
  );
  // </editor-fold>

  // <editor-fold desc="Default">
  constructor(
    private matDialog: MatDialogRef<BSDialogComponent>,
    private data: DataService,
    private store: Store
  ) {
    super(initialState);
    linkToGlobalState(this.state$, 'BSParamsStore', this.store);
    this.data.node.on$
      .pipe(takeUntil(this.data.onRefresh$))
      .subscribe((nodes) => this._updateNodes(nodes));
    this._initWebSocket();
    this.data.onRefresh$.subscribe(this.ngOnDestroy.bind(this));
  }

  override ngOnDestroy() {
    super.ngOnDestroy();
    this.patchState({ busy: true, webSocketState: 'CLOSING' });
    this.ws.close();
    unlinkFromGlobalState('BSParamsStore', this.store);
  }

  // </editor-fold>

  // <editor-fold desc="Web Socket">
  private _initWebSocket(): void {
    this.ws = new WebSocket(`ws://${environment.host}/configparam/webclient`);
    this.ws.onopen = () => this.patchState({ webSocketState: 'CONNECTED' });
    this.ws.onclose = () => this.patchState({ webSocketState: 'CLOSED' });
    this.startHeartbeat();
  }

  private _send = (
    data: GetConfigData,
    crud: Crud,
    m?: Message
  ): Observable<BSMessageEvent> => {
    this.patchState({ busy: true, error: undefined });
    return new Observable<BSMessageEvent>((subscriber) => {
      this.ws.onmessage = (me: MessageEvent): void => {
        // Ignore heartbeat messages
        if (me.data === '__PING__') {
          return;
        }
        subscriber.next({
          me,
          crud: data.crud,
          model: data.model,
          direction: data.direction
        });
        subscriber.complete();
      };
      const invalid = [
        {
          condition: data.node === undefined,
          message: 'Invalid configuration data, please select a node'
        },
        {
          condition: data.direction === undefined,
          message: 'Invalid configuration data, please select a direction'
        },
        {
          condition: data.connectionName === undefined,
          message:
            'Invalid configuration data, please select an existing connection or new connection'
        },
        {
          condition: !this._isValidSelection(data, crud),
          message: 'Invalid configuration data!'
        }
      ].find((v) => v.condition);

      if (invalid) {
        // TODO: Implement a better way to handle ignored errors.
        subscriber.error(undefined);
        subscriber.complete();
        return;
      }

      if (data.instruction === undefined && data.crud === 'Update') {
        data = { ...data, instruction: Instruction.GetConfiguredParamList };
      }
      const config = this._getConfig(data, m);
      this.ws.send(JSON.stringify(config));
    });
  };
  // </editor-fold>

  // <editor-fold desc="Helper Functions">
  private _getAvailableDirections(
    support: NodeSupport | undefined,
    conn: ConnWithEndpoint | undefined
  ): Direction[] {
    if (!support) {
      return [];
    }

    const endpoints = conn?.endpoints ?? [];
    const canCreateOut =
      conn?.direction !== 'IN' && canCreateOutput(support, endpoints);
    const canCreateIn =
      conn?.direction !== 'OUT' && canCreateInput(support, endpoints);
    return [
      ...(canCreateIn ? ['IN'] : []),
      ...(canCreateOut ? ['OUT'] : [])
    ] as Direction[];
  }

  private _getAvailableConnections(
    node: Node | undefined,
    direction: Direction | undefined,
    nonExchanges: ConnWithEndpoint[],
    crud: Crud,
    currentChoice: string | boolean
  ): string[] {
    if (
      node === undefined ||
      node.support === undefined ||
      direction === undefined
    ) {
      return [];
    }
    if (crud !== 'Create' && typeof currentChoice === 'string') {
      return [currentChoice];
    }
    if (direction === 'IN') {
      return nonExchanges
        .filter(
          (c) =>
            c.node === node.name && canCreateInput(node.support, c.endpoints)
        )
        .map((c) => c.name);
    }
    if (direction === 'OUT') {
      return nonExchanges
        .filter(
          (c) =>
            c.node === node.name && canCreateOutput(node.support, c.endpoints)
        )
        .map((c) => c.name);
    }
    return [];
  }

  private _getParameterRequest(
    node: Node,
    isInput: boolean,
    instruction: Instruction = Instruction.GetParamList,
    parameters: SFMap<DataParameter> = {},
    requestParameter: string = '',
    model: Model = 'Connection',
    connName: string,
    endName: string
  ): ParameterRequest {
    return {
      name: model === 'Connection' ? connName : endName,
      parentName: node.name,
      instruction,
      isInput,
      parameters,
      requestParameter
    };
  }

  private _isValidSelection(
    data: GetConfigData,
    crudType: Crud = 'Create'
  ): boolean {
    if (
      data.node === undefined ||
      data.direction === undefined ||
      data.connectionName === undefined ||
      (!data.directions?.includes(data.direction) && crudType !== 'Update')
    ) {
      return false;
    }
    return (
      data.connectionName === true ||
      data.connections.includes(data.connectionName)
    );
  }

  private _getConfig(
    data: GetConfigData,
    m?: Message
  ): BSCConfigData | BSEConfigData {
    const isInput = data.direction === 'IN';
    const pr = this._getParameterRequest(
      data.node,
      isInput,
      data.instruction,
      data.parameters,
      data.requestParameter,
      data.model,
      data.connectionName.toString(),
      data.endpointName
    );
    const endpointDataParams =
      this.form?.endForm?.getDataParameterValues() ?? {};
    const includeEndpoint = isInput
      ? (data.node.support?.createInputFeedWithConnection ?? true)
      : (data.node.support?.createOutputFeedWithConnection ?? true);
    if (data.model === 'Endpoint') {
      return {
        type: 'bse',
        connectionName: `${data.connectionName}`,
        endpointConfig: pr
      };
    }
    return {
      type: 'bsc',
      connectionConfig: {
        ...pr,
        name: data.connectionName === true ? '' : data.connectionName
      },
      endpointConfig:
        m === undefined || m === 'endpointMessage'
          ? { ...pr, name: data.endpointName }
          : {
              parentName: pr.parentName,
              name: data.endpointName,
              parameters: endpointDataParams,
              requestParameter: '',
              instruction: Instruction.GetParamList
            },
      includeEndpoint,
      endpointName: data.endpointName
    };
  }

  // </editor-fold>
  heartbeatTask: number | null = null;

  startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTask = setInterval(() => {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send('__PONG__');
      }
    }, 30000);
  }

  stopHeartbeat(): void {
    if (this.heartbeatTask) {
      clearInterval(this.heartbeatTask);
      this.heartbeatTask = null;
    }
  }
}
