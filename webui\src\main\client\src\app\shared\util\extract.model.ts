type Suffix<S extends string> = `${string}${S}`;

type ExtractKeysByNameCondition<T, C> = {
  [K in keyof T]: K extends C ? K : never;
}[keyof T];

interface Person {
  firstName:  string;
  numberName: number;
  email:      string | null;
}

type ExtractSuffixedProps<T, S extends string> = ExtractKeysByNameCondition<T, Suffix<S>>;
type ExtractSuffixPrefixes<P extends string, Suffix extends string>
  = P extends `${infer Prefix}${Suffix}` ? Prefix : never;
type SimpleProp<T, S extends string> = ExtractSuffixPrefixes<ExtractSuffixedProps<T, S>, S>;

type example = SimpleProp<Person, 'Name'>; // 'number' | 'first'

const createSuffixedGetter = <S extends string>(suffix: S) => <T extends Record<`${SimpleProp<T, S>}${S}`, any>>
  (prop: SimpleProp<T, S>, t: T) => t[`${prop}${suffix}`];

const nameGetter = createSuffixedGetter('Name');

const josh: Person = {
  firstName: 'josh',
  numberName: 1,
  email: null
};

const foo = nameGetter('first', josh);

interface BattleSpaceNode {
  displayName:    string;
  bindingKeyName: string;
}

const abacus: BattleSpaceNode = {
  displayName: 'Abacus',
  bindingKeyName: 'Ab1'
};

const bar = nameGetter('display', abacus);
