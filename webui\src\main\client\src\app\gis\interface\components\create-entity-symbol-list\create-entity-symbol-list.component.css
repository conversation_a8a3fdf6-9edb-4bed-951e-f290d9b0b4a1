mat-dialog-content {
  min-width: 400px;
  padding: 20px;
  overflow-y: auto;
}

p {
  margin-bottom: 16px;
}

.symbol-dropdown {
  width: 100%;
  margin-top: 10px;
}

.symbol-autocomplete {
  width: 100%;
}

::ng-deep .symbol-option {
  padding: 8px 16px;
  min-height: 56px;
  border-bottom: 1px solid var(--divider-light);
}

.symbol-option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.symbol-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.symbol-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.symbol-description {
  font-size: 14px;
  color: var(--text);
  line-height: 1.4;
  font-weight: 500;
}

.symbol-code {
  font-size: 12px;
  color: var(--text-description);
  font-family: monospace;
}

.selected-symbol-preview {
  padding: 16px;
  border: 1px solid var(--divider-light);
  border-radius: 4px;
  margin-top: 16px;
}

.selected-symbol-preview h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
}

.selected-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.selected-details {
  flex: 1;
}

.selected-details p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--text-description);
}

.selected-details strong {
  color: var(--text);
}

::ng-deep .mat-mdc-autocomplete-panel {
  max-height: 400px;
}

::ng-deep .mat-mdc-option .mdc-list-item__primary-text {
  display: flex;
  align-items: center;
  width: 100%;
}

mat-dialog-actions {
  padding: 16px 24px;
  margin: 0;
  gap: 8px;
}

mat-dialog-actions button {
  min-width: 80px;
}
