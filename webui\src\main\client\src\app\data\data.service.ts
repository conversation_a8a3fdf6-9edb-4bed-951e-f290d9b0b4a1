import {
  inject,
  Injectable,
  <PERSON><PERSON><PERSON><PERSON>,
  Renderer2,
  RendererFactory2
} from '@angular/core';

import { createEffect, EffectSources, ofType } from '@ngrx/effects';
import { Action, combineReducers, Store } from '@ngrx/store';

import {
  catchError,
  combineLatestWith,
  map,
  Observable,
  of,
  scan,
  Subject,
  switchMap,
  tap,
  withLatestFrom
} from 'rxjs';
import { ConnectionFacade } from './connection/connection.facade';
import { Connection, connectionReducer } from './connection/connection.model';
import { DataPathFacade } from './data-path/data-path.facade';
import { dataPathReducer } from './data-path/data-path.model';
import { EndpointFacade } from './endpoint/endpoint.facade';
import {
  Endpoint,
  endpointReducer,
  getEndpointId,
  extractEndpointInfo
} from './endpoint/endpoint.model';
import { EntityFacade } from './entity/entity.facade';
import { entityReducer } from './entity/entity.model';
import { LogFacade } from './log/log.facade';
import { logReducer } from './log/log.model';
import { NodeFacade } from './node/node.facade';
import { nodeReducer } from './node/node.model';
import { SettingsFacade } from './settings/settings.facade';
import { settingsReducer } from './settings/settings.model';
import { EventsService } from './events/events.service';
import { SFMap, uuidv4 } from '@simfront/common-libs';
import { environment } from '../../environments/environment';
import { NavigationEnd, Router } from '@angular/router';
import { LayerManagerFacade } from './layer-manager/layer-manager.facade';
import { LayerReducer } from './layer-manager/layer-manager.model';
import { RouteFacade } from './route/route.facade';
import { routeReducer } from './route/route.model';
import { MovementFacade } from './movement/movement.facade';
import { movementReducer } from './movement/movement.model';

export type ConnWithEndpoint = Connection & Record<'endpoints', Endpoint[]>;
export type EndpointWithConn = Endpoint &
  Record<'connectionObj', Connection | undefined>;

@Injectable({ providedIn: 'root' })
export class DataService implements OnDestroy {
  router = inject(Router);

  // <editor-fold desc="Selectors">
  nonExchangeWithEndpoint$ = this.connection.nonExchange$.pipe(
    combineLatestWith(this.endpoint.endpointByConnection$, this.node.entities$),
    map(([connections, endpoints, nodes]) =>
      connections.reduce<ConnWithEndpoint[]>((acc, conn) => {
        if (nodes[conn.node]?.launchable.status !== 'ON') {
          return acc;
        }
        const c = { ...conn, endpoints: endpoints[conn.name] || [] };
        acc.push(c);
        return acc;
      }, [])
    )
  );

  selectedEndpointsByConn$ = this.endpoint.selectedEndpoints$.pipe(
    map((selected) =>
      selected.reduce<SFMap<string[]>>((acc, ep) => {
        acc[ep.connection] = [...(acc[ep.connection] || []), getEndpointId(ep)];
        return acc;
      }, {})
    )
  );

  isProtoRouterActive$: Observable<boolean> = this.router.events.pipe(
    switchMap((event) => {
      let res = this.isProtoRouterActive$ ?? of(false);
      if (event instanceof NavigationEnd) {
        res = of(this.router.url.includes('/router'));
      }

      return res;
    })
  );

  datamanStatus$ = this.node.entities$.pipe(
    scan((datamanStatus, entities) => {
      const datamanLaunchable = entities['DATAMAN']?.launchable || undefined;
      if (!datamanLaunchable) {
        return 'OFF';
      }
      datamanStatus =
        datamanLaunchable.transientStatus === 'NONE'
          ? datamanLaunchable.status
          : datamanLaunchable.transientStatus;
      return datamanStatus;
    }, 'OFF')
  );

  nonExchangeByDirectionWithEndpoint$ = this.nonExchangeWithEndpoint$.pipe(
    map((connections) =>
      connections.reduce<{
        inputs: ConnWithEndpoint[];
        outputs: ConnWithEndpoint[];
      }>(
        (acc, conn) => {
          const input: ConnWithEndpoint = {
            ...conn,
            endpoints: conn.endpoints.filter(
              (e) => e.capability === 'DATA_RECEIVER'
            )
          };
          const output: ConnWithEndpoint = {
            ...conn,
            endpoints: conn.endpoints.filter(
              (e) => e.capability === 'DATA_SENDER'
            )
          };
          return {
            inputs: [
              ...acc.inputs,
              ...(input.endpoints.length > 0 ? [input] : [])
            ],
            outputs: [
              ...acc.outputs,
              ...(output.endpoints.length > 0 ? [output] : [])
            ]
          };
        },
        { inputs: [], outputs: [] }
      )
    )
  );

  nonExchangeInputWithEndpoints$ =
    this.nonExchangeByDirectionWithEndpoint$.pipe(map((data) => data.inputs));

  nonExchangeOutputWithEndpoints$ =
    this.nonExchangeByDirectionWithEndpoint$.pipe(map((data) => data.outputs));

  endpointsWithConnection$ = this.endpoint.join(
    this.connection.entities$,
    (e) => `${e.node}:${e.connection}`,
    'connectionObj'
  );

  nonExchangeEndpoints$ = this.endpointsWithConnection$.pipe(
    map((endpoints) =>
      endpoints.filter(
        (e) => !!e.connectionObj && !e.connectionObj.exchangeConnection
      )
    )
  );

  inputEndpointWithConn$ = this.nonExchangeEndpoints$.pipe(
    combineLatestWith(this.node.entities$),
    map(([endpoints, nodes]) =>
      endpoints.filter(
        (e) =>
          e.capability === 'DATA_RECEIVER' &&
          nodes[e.node]?.launchable.status === 'ON'
      )
    )
  );

  outputEndpointWithConn$ = this.nonExchangeEndpoints$.pipe(
    combineLatestWith(this.node.entities$),
    map(([endpoints, nodes]) =>
      endpoints.filter(
        (e) =>
          e.capability === 'DATA_SENDER' &&
          nodes[e.node]?.launchable.status === 'ON'
      )
    )
  );

  // supportedFilters$ = (endpoint: Endpoint) => this.node.filterInfos$(endpoint.node).pipe(
  //   map(infos => infos.filter(info => checkFilterLocation(info, endpoint.capability)))
  // );

  // Only show entities that have associations
  entitiesWithAssocs$ = this.entity.all$.pipe(
    combineLatestWith(this.endpoint.associations$),
    map(([entities, assocs]) =>
      entities
        .filter((e) => assocs.some((a) => a.srcId === e.uniqueId))
        .map((e) => ({
          ...e,
          assoc: assocs.find((a) => a.srcId === e.uniqueId)
        }))
    )
  );

  // Only show entities that do not have associations
  entitiesWithoutAssocs$ = this.entity.all$.pipe(
    combineLatestWith(this.endpoint.associations$),
    map(([entities, assocs]) =>
      entities.filter((e) => !assocs.some((a) => a.srcId === e.uniqueId))
    )
  );

  // </editor-fold>

  onRefresh$ = new Subject<void>();

  private _eventSource: EventSource;
  private _unListen: () => void;

  constructor(
    private rendererFactory: RendererFactory2,
    private es: EffectSources,
    private store: Store,
    public node: NodeFacade,
    public endpoint: EndpointFacade,
    public connection: ConnectionFacade,
    public entity: EntityFacade,
    public movement: MovementFacade,
    public dataPath: DataPathFacade,
    public log: LogFacade,
    public settings: SettingsFacade,
    public events: EventsService,
    public layer: LayerManagerFacade,
    public route: RouteFacade
  ) {
    let sessionId = sessionStorage.getItem('SESSION_ID');
    if (!sessionId) {
      sessionId = uuidv4();
      sessionStorage.setItem('SESSION_ID', sessionId);
    }
    this.store.addReducer('endpoint', endpointReducer);
    this.es.addEffects(this.endpoint);

    this.store.addReducer('node', nodeReducer);
    this.es.addEffects(this.node);

    this.store.addReducer('connection', connectionReducer);
    this.es.addEffects(this.connection);

    this.store.addReducer('entity', entityReducer);
    this.es.addEffects(this.entity);

    this.store.addReducer('dataPath', dataPathReducer);
    this.es.addEffects(this.dataPath);

    this.store.addReducer('log', logReducer);
    this.es.addEffects(this.log);

    this.store.addReducer('settings', settingsReducer);
    this.es.addEffects(this.settings);

    this.store.addReducer('layer', LayerReducer);
    this.es.addEffects(this.layer);

    this.store.addReducer('route', routeReducer);
    this.es.addEffects(this.route);
    this.store.addReducer('movement', movementReducer);
    this.es.addEffects(this.movement);

    this.entity.open(`${environment.origin}/events/entities/${sessionId}`);
    this.events.open(`${environment.origin}/events/${sessionId}`);
    this._beforeUnloadListener(this.rendererFactory.createRenderer(null, null));
  }

  // <editor-fold desc="Unload Event">
  private _beforeUnloadListener(renderer: Renderer2): void {
    this._unListen = renderer.listen('window', 'beforeunload', () => {
      this.onRefresh$.next();
      this.ngOnDestroy();
    });
  }

  ngOnDestroy() {
    this._eventSource.close();
    this.onRefresh$.complete();
    this._unListen();
  }

  // </editor-fold>
}
