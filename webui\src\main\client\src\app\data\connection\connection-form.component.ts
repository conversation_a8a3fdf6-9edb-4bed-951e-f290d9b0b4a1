import { Component } from '@angular/core';
import {
  DialogOutParams,
  FacadeDialogContentComponent,
} from '@simfront/common-libs';
import { Connection } from './connection.model';

@Component({
  selector: 'vcci-connection-form',
  template: `
    <div
      class="m-container"
      style="padding: 0 20px"
      *ngIf="
        data.inParams.crud === 'Delete' && data.inParams.quantity === 'One'
      "
    >
      <p>Are you sure you want to delete the connection:</p>
      <ul style="padding: 0 30px;">
        <li>{{ id }}</li>
      </ul>
    </div>
  `,
  styles: [
    `
      .m-container {
        margin: 5px;
      }

      ::ng-deep .mat-mdc-dialog-content:has(vcci-connection-form) {
        padding-bottom: 0 !important;
      }

      ::ng-deep
        .mat-mdc-dialog-content:has(vcci-connection-form)
        ~ .mat-mdc-dialog-actions {
        justify-content: flex-end !important;
        padding: 20px !important;
      }

      ::ng-deep
        vcci-connection-form
        > vcci-bs-dialog
        > .mat-mdc-dialog-content {
        padding: 0 !important;
      }
    `,
  ],
  standalone: false,
})
export class ConnectionForm extends FacadeDialogContentComponent<
  Connection,
  'id',
  string
> {
  title = '';
  connection: Connection | undefined;
  id = '';

  // TODO: Fix the error with updating title after view checked.
  constructor() {
    super();
    this.title = `${this.data.inParams.crud} Connection`;

    this.initDialog();
  }

  initDialog(): void {
    if (
      this.data.inParams.crud === 'Delete' &&
      this.data.inParams.quantity === 'One'
    ) {
      this.id = this.data.inParams.id;
    }
  }

  override outData(): DialogOutParams<Connection, 'id', string> {
    if (
      this.data.inParams.crud === 'Delete' &&
      this.data.inParams.quantity === 'One'
    ) {
      return { ...this.data.inParams, id: this.data.inParams.id };
    }
    throw new Error(
      `Crud Operation '${this.data.inParams.crud}' is not supported`,
    );
  }
}
