import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { Component, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CursitControllerType } from 'src/app/cursit/models/cursit.model';
import { ShapeType } from 'src/app/cursit/service/gis.model';
import { GisService } from '../../service/gis.service';
import { cursitActions } from 'src/app/cursit/actions/cursit.actions';
import { MatDialog } from '@angular/material/dialog';
import { ShapeStyleDialogComponent } from '../shape-style-dialog/shape-style-dialog.component';

@Component({
  selector: 'nexus-drawing-menu',
  imports: [
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatMenuModule,
    CommonModule,
    FormsModule
  ],
  templateUrl: './drawing-menu.component.html',
  styleUrl: './drawing-menu.component.css'
})
export class DrawingMenuComponent {
  gisService: GisService = inject(GisService);
  dialog: MatDialog = inject(MatDialog);
  shapeStyleDialogOpen = signal<boolean>(false);
  createTheShape(shapeType: ShapeType): void {
    this.gisService.store.dispatch(
      cursitActions.updateCursitController({
        controllerType: CursitControllerType.Create,
        controllerPayload: { shapeType }
      })
    );
  }

  openShapeStyleDialog(): void {
    this.shapeStyleDialogOpen.set(true);
    const dialogRef = this.dialog.open(ShapeStyleDialogComponent, {
      position: {
        top: '150px',
        right: '150px'
      },
      width: '450px',
      hasBackdrop: false
    });

    dialogRef.afterClosed().subscribe((result) => {
      this.shapeStyleDialogOpen.set(false);
    });
  }
}
