import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { acoAtoActions } from '../actions/aco-ato.actions';
import { catchError, exhaustMap, filter, map, of, switchMap, tap } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { SetRtDialogComponent } from '../components/set-rt-dialog/set-rt-dialog.component';
import { AcoAtoService } from '../service/aco-ato.service';
import { ImportDialogComponent } from '../components/import-dialog/import-dialog.component';
import { FilePayload } from '../../shared/directives/file-payload.directive';

@Injectable()
export class AcoAtoEffects {
  openImportDialog = createEffect(() =>
    this.action$.pipe(
      ofType(acoAtoActions.openImportDialog),
      exhaustMap(() =>
        this.dialog
          .open(ImportDialogComponent, {
            disableClose: true
          })
          .afterClosed()
      ),
      filter((formData: any) => !!formData),
      map((filePayload: Record<string, FilePayload>) =>
        acoAtoActions.executeRapFiles({ filePayload })
      )
    )
  );
  executeRapFiles = createEffect(() =>
    this.action$.pipe(
      ofType(acoAtoActions.executeRapFiles),
      switchMap(({ filePayload }) =>
        this.acoAtoService
          .uploadFiles(filePayload)
          .pipe(map(() => acoAtoActions.saveFileSuccess()))
      ),
      catchError((error) => of(acoAtoActions.saveFileFail(error)))
    )
  );
  openRapTimeDialog$ = createEffect(() =>
    this.action$.pipe(
      ofType(acoAtoActions.openRapTimesDialog),
      exhaustMap(() =>
        this.dialog
          .open<SetRtDialogComponent, undefined, number | undefined>(
            SetRtDialogComponent,
            {
              disableClose: true
            }
          )
          .afterClosed()
      ),
      filter((dateTime) => !!dateTime),
      map((dateTime) => acoAtoActions.saveRapTime(dateTime))
    )
  );
  saveRapTime$ = createEffect(
    () => () =>
      this.action$.pipe(
        ofType(acoAtoActions.saveRapTime),
        switchMap(({ time }) =>
          this.acoAtoService.saveRapTime(time).pipe(
            map(() => {
              return acoAtoActions.saveRapTimeSuccess();
            })
          )
        ),
        catchError((error) => {
          return of(acoAtoActions.saveRapTimeFail(error));
        })
      )
  );

  constructor(
    private action$: Actions,
    private dialog: MatDialog,
    private acoAtoService: AcoAtoService
  ) {}
}
