import { Pipe, PipeTransform } from '@angular/core';

export type ObjectTransformType = 'keys' | 'values';

@Pipe({
  name: 'object',
  pure: true,
  standalone: true
})
export class ObjectPipe implements PipeTransform {
  transform(value: any, type: ObjectTransformType): string[] {
    switch (type) {
      case 'keys':
        return Object.keys(value) as string[];
      case 'values':
        return Object.values(value);
    }
  }
}
