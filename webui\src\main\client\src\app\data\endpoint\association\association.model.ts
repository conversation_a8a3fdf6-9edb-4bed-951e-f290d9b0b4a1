import { DataParameters } from '../../bs-params/bs-params.model';
import { Entity, IconCodeTypeMap, Shapes } from '../../entity/entity.model';
import { UniqueId } from '../../entity/id.model';

export type RequestType = 'SENDER' | 'ALL_LISTENERS';

export interface Candidate {
  objectName: string;
  objectId: UniqueId;
  objectType: 'UNIT' | 'MATERIEL';
  iconCodeTypeMap: IconCodeTypeMap;
}

export interface OutboundObject {
  idx: number;
  srcId: UniqueId;
  srcName: string;

  dstId: UniqueId;
  dstName: string;
  dstParams: DataParameters;
}

export const candidateToEntity = (obj: Candidate): Entity => ({
  name: obj.objectName,
  uniqueId: obj.objectId,
  layers: [],
  defaultLayerIcon: {
    iconCodeTypeMap: obj.iconCodeTypeMap,
    catCode: '',
    location: {
      '@class': Shapes.PointLocation,
      bearing: 0,
      altitude: 0,
      speed: 0,
      longitude: 0,
      latitude: 0,
      battleSpaceObjectId: 0,
      layerId: 0,
      time: 0
    }
  },
  layerIcons: []
});
