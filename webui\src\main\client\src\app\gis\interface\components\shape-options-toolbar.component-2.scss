@import 'toolbar-mixins';

//line stroke pattern combobox drop down menu items
.stroke-pattern-option.solid {
  &::before {
    @include stroke-option-styling(3px, solid, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(3px, solid, $active-icon-color);
  }
}

.stroke-pattern-option.dashed {
  &::before {
    @include stroke-option-styling(3px, dashed, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(3px, dashed, $active-icon-color);
  }
}

.stroke-pattern-option.dot {
  &::before {
    @include stroke-option-styling(3px, dotted, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(3px, dotted, $active-icon-color);
  }
}

//line stroke pattern combobox drop down manu items hover
.stroke-pattern-option.solid:hover::before {
  @include stroke-option-styling(3px, solid, $active-icon-color);
}

.stroke-pattern-option.dashed:hover::before {
  @include stroke-option-styling(3px, dashed, $active-icon-color);
}

.stroke-pattern-option.dot:hover::before {
  @include stroke-option-styling(3px, dotted, $active-icon-color);
}

//line stroke widths
::ng-deep .stroke1 .mat-mdc-select-value::before {
  @include stroke-option-styling(1px, solid, $icon-color);
}

::ng-deep .stroke2 .mat-mdc-select-value::before {
  @include stroke-option-styling(2px, solid, $icon-color);
}

::ng-deep .stroke3 .mat-mdc-select-value::before {
  @include stroke-option-styling(3px, solid, $icon-color);
}

::ng-deep .stroke4 .mat-mdc-select-value::before {
  @include stroke-option-styling(4px, solid, $icon-color);
}

::ng-deep .stroke5 .mat-mdc-select-value::before {
  @include stroke-option-styling(5px, solid, $icon-color);
}

//line stroke combobox drop down items
.line-stroke-option.stroke1 {
  &::before {
    @include stroke-option-styling(1px, solid, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(1px, solid, $active-icon-color);
  }
}

.line-stroke-option.stroke2 {
  &::before {
    @include stroke-option-styling(2px, solid, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(2px, solid, $active-icon-color);
  }
}

.line-stroke-option.stroke3 {
  &::before {
    @include stroke-option-styling(3px, solid, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(3px, solid, $active-icon-color);
  }
}

.line-stroke-option.stroke4 {
  &::before {
    @include stroke-option-styling(4px, solid, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(4px, solid, $active-icon-color);
  }
}

.line-stroke-option.stroke5 {
  &::before {
    @include stroke-option-styling(5px, solid, $icon-color);
  }

  &[aria-selected="true"]::before {
    @include stroke-option-styling(5px, solid, $active-icon-color);
  }
}

//hover
.line-stroke-option.stroke1:hover::before {
  @include stroke-option-styling(1px, solid, $active-icon-color);
}

.line-stroke-option.stroke2:hover::before {
  @include stroke-option-styling(2px, solid, $active-icon-color);
}

.line-stroke-option.stroke3:hover::before {
  @include stroke-option-styling(3px, solid, $active-icon-color);
}

.line-stroke-option.stroke4:hover::before {
  @include stroke-option-styling(4px, solid, $active-icon-color);
}

.line-stroke-option.stroke5:hover::before {
  @include stroke-option-styling(5px, solid, $active-icon-color);
}
