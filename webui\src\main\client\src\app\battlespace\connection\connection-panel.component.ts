import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  ConnectionPanelDisplay,
  ConnectionPanelPosition
} from '../../core/reducers/layout.reducer';
import { ConnWithEndpoint, DataService } from '../../data/data.service';
import { PopoutWindowConfig } from '../../shared/popout-window/popout-window.model';
import { defaultRotate } from '../../shared/util/animation';
import { LayoutActions } from 'src/app/core/actions/layout.actions';

@Component({
  selector: 'vcci-connection',
  styleUrls: ['connection-panel.component.css'],
  templateUrl: 'connection-panel.component.html',
  animations: [defaultRotate('rotate', 0, 180)],
  standalone: false
})
export class ConnectionPanelComponent {
  _connections: ConnWithEndpoint[] = [];
  @Input() set connections(connections: ConnWithEndpoint[]) {
    this._connections = connections;
    this.connectionExpandedMap = connections.reduce((acc, conn) => {
      acc[conn.name] =
        this.connectionExpandedMap[conn.name] ?? this.connectionsExpanded;
      return acc;
    }, {});
  }
  get connections(): ConnWithEndpoint[] {
    return this._connections;
  }
  @Input() title = 'CONNECTIONS';
  @Input() isLeft = true;
  @Input() expanded = true;
  @Input() connectionsExpanded = true;
  @Input() isPopout = false;
  @Input() position: ConnectionPanelPosition = 'left';
  @Input() displayType: ConnectionPanelDisplay = 'ALL';
  @Input() selectedEndpointsByConn: { [key: string]: string[] } = {};

  @Output() onExpanded = new EventEmitter<boolean>();
  @Output() popout = new EventEmitter();
  @Output() changeDisplayType = new EventEmitter<ConnectionPanelDisplay>();

  connectionExpandedMap: { [key: string]: boolean } = {};
  get allConnectionsExpanded(): boolean {
    return this.connections.every(
      (conn) => this.connectionExpandedMap[conn.name]
    );
  }

  trackBy = (_: number, conn: ConnWithEndpoint): string =>
    `${conn.node}:${conn.name}`;

  constructor(public data: DataService) {}

  toggleExpand(value?: boolean): void {
    this.expanded = value ?? !this.expanded;
    this.onExpanded.emit(this.expanded);
  }

  getPopoutWindowConfig(): PopoutWindowConfig {
    return {
      title: this.title,
      windowStyle: `mat-card {
        width: 100% !important;
        border-radius: 0 !important;
      }
      .name {
        margin: auto !important;
      }`
    };
  }

  toggleConnectionDisplayType(isOn: boolean): void {
    this.changeDisplayType.emit(isOn ? 'ENDPOINTS' : 'ALL');
  }

  onConnectionExpanded(connName: string, expanded: boolean): void {
    if (!this.expanded) {
      this.toggleExpand(true);
    }
    this.connectionExpandedMap[connName] = expanded;
  }

  toggleExpandConnections(): void {
    const expanded = !this.allConnectionsExpanded;
    this.connections.forEach(
      (conn) => (this.connectionExpandedMap[conn.name] = expanded)
    );
  }

  dockSidebar() {
    this.data.settings.store.dispatch(
      LayoutActions.changeConnectionPanelPosition(
        this.position === 'left' ? 'right' : 'left'
      )
    );
  }
}
