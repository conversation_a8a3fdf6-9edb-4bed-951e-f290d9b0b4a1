/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/14/2023, 9:28 AM
 */
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { interval, map, switchMap, tap } from 'rxjs';
import { EndpointActions } from '../../../data/endpoint/endpoint.model';
import { GisService } from '../../interface/service/gis.service';
import { getRedrawInterval } from '../reducers/interval.reducer';
import { cursitActions } from '../../../cursit/actions/cursit.actions';

@Injectable()
export class GisEffects {
  constructor(
    private actions$: Actions,
    private gisService: GisService
  ) {
  }

  redraw$ = createEffect(() => this.gisService.store.select(getRedrawInterval).pipe(
    switchMap(
      time => interval(time).pipe(
        tap(_ => {
          this.gisService.updateLayers();
        })
      )
    )
  ), { dispatch: false });

  instruct$ = createEffect(() => this.actions$.pipe(
    ofType(EndpointActions.instructSuccess),
    map(action => action.payload),
    tap(({ id, instruction }) => {
      this.gisService.setLayerRedrawState(id, instruction);
    })
  ), { dispatch: false });

  onFilterUpdate$ = createEffect(() => this.actions$.pipe(
    ofType(cursitActions.updateCursitFilters),
    map(action => action.payload),
    map(filters => this.gisService.updateFilters(filters))
  ), { dispatch: false });
}
