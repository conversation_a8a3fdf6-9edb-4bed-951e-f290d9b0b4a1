import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSliderModule } from '@angular/material/slider';

import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { CoreModule } from '@simfront/common-libs';
import { GisEffects } from '../gis/common/effects/gis.effects';
import { layerRedrawIntervalFeature } from '../gis/common/reducers/interval.reducer';
import { AdditionalActionsToolbarComponent } from '../gis/interface/components/additional-actions-toolbar.component';
import { EntityMovementToolbarComponent } from '../gis/interface/components/entity-movement-toolbar.component';
import { EntitySelectionMenuComponent } from '../gis/interface/components/entity-selection-menu.component';
import { EntityToolbarComponent } from '../gis/interface/components/entity-toolbar.component';
import { LayerManagerComponent } from '../gis/interface/components/layer-manager.component';
import { MapControlComponent } from '../gis/interface/components/map-control.component';
import { MouseCoordinateComponent } from '../gis/interface/components/mouse-coordinate.component';
import { RightToolbarOptionsComponent } from '../gis/interface/components/right-toolbar-options.component';
import { ScaleIndicatorComponent } from '../gis/interface/components/scale-indicator.component';
import { ShapeOptionsToolbarComponent } from '../gis/interface/components/shape-options-toolbar.component';
import { ShapeToolbarComponent } from '../gis/interface/components/shape-toolbar.component';
import { ToolbarContainerComponent } from '../gis/interface/components/toolbar-container.component';
import { TopRightToolbarComponent } from '../gis/interface/components/top-right-toolbar.component';
import { ElementSelectionEffect } from '../gis/interface/effects/element-selection-effect';
import { elementSelectionFeature } from '../gis/interface/reducers/element-selection.reducer';
import { shapeSelectionFeature } from '../gis/interface/reducers/shape-selection.reducer';
import { GIS_ENGINE, GISENGINE } from '../gis/interface/service/gis.service';
import { LeafletEffects } from '../gis/leafletgis/effects/leaflet.effects';
import { MaterialModule } from '../material/material.module';
import { AddEntitiesDialogComponent } from './components/add-entities-dialog.component';
import { CursitToolbarComponent } from './components/cursit-toolbar.component';
import { CursitComponent } from './components/cursit.component';
import { CursitContainerComponent } from './containers/cursit-container.component';
import { CursitRoutingModule } from './cursit-routing.module';
import { CursitEffects } from './effects/cursit.effects';
import { LeafletModule } from '@bluehalo/ngx-leaflet';
import { LeafletMarkerClusterModule } from '@bluehalo/ngx-leaflet-markercluster';
import { LeafletCursitComponent } from './components/leaflet-cursit.component';
import { PopoutWindowModule } from '../shared/popout-window/popout-window.module';
import { EntityDetailsComponent } from '../gis/interface/components/entity-details.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { CreateEntityDialogComponent } from '../gis/interface/components/create-entity/create-entity-dialog.component';
import { TopToolbarComponent } from '../gis/interface/components/top-toolbar/top-toolbar.component';

export const COMPONENTS = [
  CursitComponent,
  ScaleIndicatorComponent,
  MouseCoordinateComponent,
  CursitContainerComponent,
  // TODO will this component be used? seems to be replaced with MapControlComponent
  CursitToolbarComponent,
  LayerManagerComponent,
  MapControlComponent,
  EntitySelectionMenuComponent,
  AddEntitiesDialogComponent,
  TopRightToolbarComponent,
  EntityToolbarComponent,
  EntityMovementToolbarComponent,
  ShapeToolbarComponent,
  ToolbarContainerComponent,
  RightToolbarOptionsComponent,
  AdditionalActionsToolbarComponent,
  EntityDetailsComponent
];

@NgModule({
  declarations: COMPONENTS,
  exports: COMPONENTS,
  imports: [
    LeafletModule,
    LeafletMarkerClusterModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    MaterialModule,
    CreateEntityDialogComponent,
    TopToolbarComponent,
    EffectsModule.forFeature([
      CursitEffects,
      LeafletEffects,
      GisEffects,
      ElementSelectionEffect
    ]),
    CursitRoutingModule,
    StoreModule.forFeature(layerRedrawIntervalFeature),
    StoreModule.forFeature(elementSelectionFeature),
    StoreModule.forFeature(shapeSelectionFeature),
    CoreModule,
    MatSliderModule,
    LeafletCursitComponent,
    PopoutWindowModule,
    MatMenuModule,
    MatBottomSheetModule,
    ShapeOptionsToolbarComponent
  ],
  providers: [{ provide: GIS_ENGINE, useValue: GISENGINE.LEAFLET }]
})
export class CursitModule {}
