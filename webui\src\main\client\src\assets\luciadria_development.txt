# #
# # Development license.
# #
version                   = 2022.1
productName               = LuciadRIA
copyright                 = Powered by Hexagon.
message                   = Powered by Hexagon.
licensee                  = Canadian Department of National Defence
licenseType               = development
optionalModules           = Geometry,MilitarySymbology,Panoramic
expiryDate                = 2025/3/31
serialNumber              = feca6e00
key                       = 4a86f9d25a645d9cd0b1b0dfb587a03f73d2394a792cb402c4e21b7de0d3ff3423ae2824a2d15818c86b700eaed32abb33070513c664a0f004fa07d3bc0e0750
#
ADMIN/1-FileReference         = 150518
ADMIN/2-MaintenanceInfo       = 
ADMIN/3-CustomerAccount       = Canadian Department of National Defence
ADMIN/4-CustomerNumber        = C00030
ADMIN/5-ProjectName           = VCCI (Virtual C2 Interface)
ADMIN/6-ProjectNumber         = P100345
ADMIN/7-DispatcherName        = DND Canadian Forces Development
ADMIN/8-GenerationInstruction = L1710325037645
ADMIN/9-LicenceDefinition     = LuciadRIA DEV_Development license DND_2022.1
ENTITLES/0-DISCLAIMER-0        = The entitlement information in this file is for information only
ENTITLES/0-DISCLAIMER-1        = Entitlements are determined by the agreements between <PERSON><PERSON> and the Licensee in those agreements
ENTITLES/1-Product and Tier    = LuciadRIA Pro
ENTITLES/2-License             = 8 DEVELOPERS
ENTITLES/3-EndUserSystem       = VCCI (Virtual C2 Interface) for Organization  in Territory Canada and abroad
