/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/6/2024, 9:54 AM
 */
import { Feature } from '@luciad/ria/model/feature/Feature';
import { Point } from '@luciad/ria/shape/Point';
import { Shape } from '@luciad/ria/shape/Shape';
import { ShapeType } from '@luciad/ria/shape/ShapeType';
import { BasicFeaturePainter } from '@luciad/ria/view/feature/BasicFeaturePainter';
import { PaintState } from '@luciad/ria/view/feature/FeaturePainter';
import { Layer } from '@luciad/ria/view/Layer';
import { Map } from '@luciad/ria/view/Map';
import { GeoCanvas } from '@luciad/ria/view/style/GeoCanvas';
import { IconStyle } from '@luciad/ria/view/style/IconStyle';
import { LuciadShape } from './LuciadShape';

export class LuciadShapePainter extends BasicFeaturePainter {
  override paintBody(
    geoCanvas: GeoCanvas,
    feature: Feature,
    shape: Shape,
    layer: Layer,
    map: Map,
    paintState: PaintState
  ): void {
    if (feature instanceof LuciadShape) {
      if (paintState.selected) {
        const style = feature.selectedStyle;
        if (style) {
          if (shape instanceof Point) {
            geoCanvas.drawIcon(shape, feature.selectedStyle as IconStyle);
          } else {
            geoCanvas.drawShape(shape, feature.selectedStyle);
          }
        } else {
          super.paintBody(geoCanvas, feature, shape, layer, map, paintState);
        }
      } else {
        const style = feature.normalStyle;
        if (style) {
          if (shape instanceof Point) {
            geoCanvas.drawIcon(shape, feature.normalStyle as IconStyle);
          } else {
            geoCanvas.drawShape(shape, feature.normalStyle);
          }
        } else {
          if (shape instanceof Point) {
            const styling = super.getStyle(ShapeType.POINT, { selected: false });
            console.log(styling);
          }
          super.paintBody(geoCanvas, feature, shape, layer, map, paintState);
        }
      }
    }
  }
}
