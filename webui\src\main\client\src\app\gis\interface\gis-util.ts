import { MapLayer } from './MapLayer';
import {AbstractMapLayerGroup, MapLayerGroup} from './MapLayerGroup';
import { LayerInfo, LayerVisibility } from '../../data/layer-manager/layer-manager.model';
import {Layer, LayerGroup} from "leaflet";

export function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

export function calculateXAndY(bearing: number, hypotenuse: number): { x: number, y: number } {
  const x = (Math.sin(toRadians(bearing)) * hypotenuse);
  const y = (Math.cos(toRadians(bearing)) * hypotenuse);
  return { x, y };
}

export function calculateArrowhead(
  originX: number,
  originY: number,
  endX: number,
  endY: number,
  arrowLength: number,
  arrowAngleDegrees: number
): { x: number, y: number } [] {
  // Calculate the angle of the line
  const angleRadians = Math.atan2(endY - originY, endX - originX);
  const arrowAngleRadians = toRadians(arrowAngleDegrees);

  // Calculate the arrowhead points
  const deltaRadians = angleRadians - arrowAngleRadians;
  const arrowX1 = endX - arrowLength * Math.cos(deltaRadians);
  const arrowY1 = endY - arrowLength * Math.sin(deltaRadians);

  const totalRadians = angleRadians + arrowAngleRadians;
  const arrowX2 = endX - arrowLength * Math.cos(totalRadians);
  const arrowY2 = endY - arrowLength * Math.sin(totalRadians);

  return [
    { x: arrowX1, y: arrowY1 },
    { x: arrowX2, y: arrowY2 }
  ];
}

export function rotatePoint(
  x: number,
  y: number,
  originX: number,
  originY: number,
  angleDegrees: number
): { x: number, y: number } {
  const angleRadians = toRadians(angleDegrees);
  const cosAngle = Math.cos(angleRadians);
  const sinAngle = Math.sin(angleRadians);

  // Translate the point back to the origin
  const translatedX = x - originX;
  const translatedY = y - originY;

  // Rotate the point around the origin
  const rotatedX = translatedX * cosAngle - translatedY * sinAngle;
  const rotatedY = translatedX * sinAngle + translatedY * cosAngle;

  // Translate the point back to its original position
  const finalX = rotatedX + originX;
  const finalY = rotatedY + originY;

  return { x: finalX, y: finalY };
}

export type LayerVisitor<L, G> = (
  parent: MapLayerGroup<L, G> | null, currentLayer: MapLayer<L>) => boolean;

export function iterateChildModels<L, G>(
  parent: MapLayerGroup<L, G> | null,
  layer: MapLayer<L>,
  visitor: LayerVisitor<L, G>
): boolean {
  if (layer !== null) {
    if (!visitor(parent, layer)) {
      return false;
    }
    if ((layer as any).childLayers !== undefined) {
      const container = layer as unknown as MapLayerGroup<L, G>;
      for (const l of Object.values(container.childLayers)) {
        if (!iterateChildModels(container, l, visitor)) {
          return false;
        }
      }
    }
  }
  return true;
}

export function iterateAllLayers <L, G>(
  visitor: LayerVisitor<L, G>,
  layerList: MapLayerGroup<L, G>
): void {
  Object.values(layerList.childLayers).forEach(layer => {
    iterateChildModels<L, G>(null, layer, visitor);
  });
}

export function getParentLayer<L, G>(
  layer: MapLayer<L>,
  rootLayer: MapLayer<L>
): MapLayerGroup<L, G> | null {
  const layerList = rootLayer as unknown as MapLayerGroup<L, G>;
  let parentLayer: MapLayerGroup<L, G> = null;
  iterateAllLayers((parent: MapLayerGroup<L, G> | null, currentLayer: MapLayer<L>): boolean => {
    if ((currentLayer as any).childLayers !== undefined) {
      const currentTreeLayer = currentLayer as unknown as MapLayerGroup<L, G>;
      if (currentTreeLayer.childLayers[layer.layerId] !== undefined) {
        parentLayer = currentTreeLayer;
        return false;
      }
    }
    return true;
  }, layerList);

  return parentLayer;
}

export type LayerAndLevel<L> = { level: number, mapLayer: MapLayer<L> | undefined };

export function findLayerAndGroupLevel<L, G>(
  id: string,
  layer: MapLayer<L>,
  currentLevel = 0
): LayerAndLevel<L> {
  if (layer.layerId === id) {
    return {
      level: currentLevel,
      mapLayer: layer
    };
  }

  if ((layer as any).childLayers !== undefined) {
    const layerGroup = layer as unknown as MapLayerGroup<L, G>;
    for (const childLayer of Object.values(layerGroup.childLayers)) {
      const {  level,  mapLayer } = findLayerAndGroupLevel(id, childLayer, currentLevel + 1);
      if (level !== -1) {
        return {
          mapLayer,
          level
        };
      }
    }
  }

  return {
    mapLayer: undefined,
    level: -1
  };
}

export function getLayerGroupVisibility<L, G>(
  layerGroup: MapLayerGroup<L, G>
): LayerVisibility {
  let hasVisibleChild = false;
  let hasHiddenChild = false;

  Object.values(layerGroup.childLayers).forEach(child => {
    if ((child as any).childLayers !== undefined) {
      const group = child as unknown as MapLayerGroup<L, G>;
      const groupVisibility = getLayerGroupVisibility(group);

      if (groupVisibility === 'VISIBLE') {
        hasVisibleChild = true;
      } else if (groupVisibility === 'HIDDEN') {
        hasHiddenChild = true;
      }
    } else if (child.isVisible()) {
      hasVisibleChild = true;
    } else {
      hasHiddenChild = true;
    }
  });

  if (hasVisibleChild && hasHiddenChild) {
    return 'PARTIAL';
  }
  if (hasVisibleChild) {
    return 'VISIBLE';
  }
  return 'HIDDEN';
}

export function getLayerInfo<L>(mapLayer: MapLayer<L>): LayerInfo {
  return  {
    layerId: mapLayer.layerId,
    layerName: mapLayer.layerName,
    visibility: mapLayer.isVisible() ? 'VISIBLE' : 'HIDDEN',
    layerType: mapLayer.layerType,
    layerSource: mapLayer.layerSource,
    locked: 'unlocked',
    opacity: mapLayer.layerType === 'Base' || mapLayer.layerId === '-888' ? 1 : undefined,
    children: (mapLayer instanceof AbstractMapLayerGroup) ?Object.keys((mapLayer as MapLayerGroup<Layer, LayerGroup>).childLayers): [],
    parentId: mapLayer.parentId
  };
}

export type ParentLayerInfo<L, G> = { parentLayer: MapLayerGroup<L, G>, layerInfo: LayerInfo };

