:host {
  display: grid;
  height: 100%;
  width: 100%;
  grid-template-columns: min-content auto min-content;
  grid-template-rows: 80px 2px auto;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}

.svg-container {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  overflow: visible;
}

.endpoint-container {
  display: inline-grid;
  grid-template-rows: min-content;
  grid-auto-rows: min-content;
  padding: 10px;
  z-index: 1;
  width: fit-content;
}

/*.endpoint-container > vcci-proto-router-endpoint:nth-child(even) {*/
/*  margin-top: 50px;*/
/*  margin-right: 40px;*/
/*}*/

.endpoint-container > vcci-proto-router-endpoint {
  margin-bottom: 10px;
  height: min-content;
}

.table-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  margin-left: 16px
}

.table-header:first-of-type {
  margin-right: 16px;
}

.table-header > h2 {
  margin-top: 16px;
}

.border-right {
  border-right: var(--divider-light) solid 1px;
}

.border-left {
  border-left: var(--divider-light) solid 1px;
}

.proto-router-svg-container {
  min-width: 200px;
}
