import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  Movement,
  MovementActions,
  MovementFacadeBase
} from './movement.model';
import { NodeFacade } from '../node/node.facade';
import { createEffect, ofType } from '@ngrx/effects';
import {
  catchError,
  concatMap,
  map,
  mergeMap,
  Observable,
  of,
  switchMap,
  withLatestFrom
} from 'rxjs';
import { GenericDialog } from 'src/app/shared/generic-dialog/generic-dialog.dialog';
import { MovementManagerComponent } from '../../gis/interface/components/movement-manager/movement-manager.component';

@Injectable({ providedIn: 'root' })
export class MovementFacade extends MovementFacadeBase {
  nodeFacade = inject(NodeFacade);
  http = inject(HttpClient);

  constructor() {
    super();
  }

  openMovementManagerDialog(): void {
    this.store.dispatch(MovementActions.openMovementManagerDialog());
  }

  saveMovement(movement: Movement): void {
    this.store.dispatch(MovementActions.saveMovement(movement));
  }

  openMovementManagerDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MovementActions.openMovementManagerDialog),
      withLatestFrom(this.nodeFacade.simMovementNode$),
      switchMap(([action, simMove]) =>
        simMove?.launchable.status === 'ON'
          ? this.dialog
              .open(MovementManagerComponent, {
                width: '1000px',
                hasBackdrop: false
              })
              .afterClosed()
          : this.dialog
              .open(GenericDialog, {
                data: {
                  title: 'Cannot Create Routes without SIM_MOVE',
                  message:
                    'SIM_MOVE is currently turned OFF. Turn on the node to create routes.'
                }
              })
              .afterClosed()
      ),
      map(() => MovementActions.closeMovementManagerDialog())
    )
  );

  saveMovement$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MovementActions.saveMovement),
      map((action) => action.payload),
      mergeMap((movement) =>
        this.saveMovementAPI(movement).pipe(
          concatMap((movement) => [
            this.actions.createOneReceived(movement),
            MovementActions.saveMovementSuccess()
          ]),
          catchError((_) => of(this.actions.createOneFailed()))
        )
      )
    )
  );

  saveMovementAPI(movement: Movement): Observable<Movement> {
    return this.http.post<Movement>(this.endpoint, movement);
  }
}
