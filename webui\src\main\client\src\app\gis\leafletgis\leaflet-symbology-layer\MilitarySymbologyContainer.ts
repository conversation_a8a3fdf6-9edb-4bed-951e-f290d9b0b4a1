import { SFMap } from '@simfront/common-libs';

import { RendererSettings } from '../../../app.component';

import { LeafletEntity } from '../LeafletEntity';
import { LeafletMapDisplay } from '../LeafletMapDisplay';
import { LeafletTacticalGraphics } from '../LeafletTacticalGraphics';

import { LeafletPixiContainer } from './LeafletPixiContainer';
import { MilitarySymbologyLayer } from './MilitarySymbologyLayer';
import { Container, EventBoundary, Point } from 'pixi.js';
import { LeafletGraphics } from '../LeafletGraphics';

export class MilitarySymbologyContainer extends LeafletPixiContainer {
  tacticalGraphicsList: LeafletTacticalGraphics[] = [];
  leafletGraphicsList: LeafletGraphics[] = [];
  leafletMapDisplay: LeafletMapDisplay;
  layerHash: SFMap<MilitarySymbologyLayer>;
  iconSize: number = 1.0;

  constructor(leafletMapDisplay: LeafletMapDisplay) {
    super();
    this.leafletMapDisplay = leafletMapDisplay;
    RendererSettings.setSymbologyStandard(RendererSettings.Symbology_2525B);
    RendererSettings.setDefaultPixelSize(42);
    this.on('RendererReady', () => this.redrawAll());
  }

  addLayer(layer: MilitarySymbologyLayer): void {
    if (!(layer.name in this.layerHash)) {
      this.layerHash[layer.name] = layer;
      layer.container = this.getContainer();
    }
  }

  removeLayer(layerName: string): void {
    if (layerName in this.layerHash) {
      delete this.layerHash[layerName];
    }
  }

  drawWithJs(): void {
    this.drawSymbols();
    const container = this.getContainer();
    const renderer = this.getRenderer();
    if (renderer && container) {
      renderer.render(container);
    }
  }

  drawSymbols(): void {
    const zoom = this.getZoom();

    const scale = this.getScale(zoom);
    const newScale = (1 / scale) * this.leafletMapDisplay.iconSize;
    const symbolsArray = this.getSymbolsArray();
    const firstRenderingArray = this.getFirstRenderingArray();
    symbolsArray.forEach((symbol) => {
      if (symbol instanceof LeafletEntity) {
        if (symbol.selected) {
          symbol.select();
        }
        if (symbol.speed > 0) {
          symbol.drawDirectionArrow();
        }
        if (this.getContainer().children.length > 100000) {
          symbol.removeLabel();
        }
      }
    });

    while (firstRenderingArray.length > 0) {
      const entity = firstRenderingArray.pop();
      if (entity instanceof LeafletEntity) {
        const coords = this.project(entity.location);
        entity.x = coords.x;
        entity.y = coords.y;
        entity.scale.set(newScale);
        symbolsArray.push(entity);
      }
    }

    this.tacticalGraphicsList.forEach((tg) => tg.redraw());
    this.leafletGraphicsList.forEach((lg) => {
      lg.redraw();
      lg.boundsArea = this.getContainer().toLocal(lg.getBounds());
      if (lg.selected) {
        lg.select();
        lg.addSelectionMenu();
      }
    });

    if (
      this.prevZoom !== zoom ||
      this.iconSize !== this.leafletMapDisplay.iconSize
    ) {
      this.iconSize = this.leafletMapDisplay.iconSize;
      this.setMarkerScale(newScale);
      this.prevZoom = zoom;
    }
  }

  // override async redraw(data?: any): Promise<void> {
  //   const container = this.getContainer();
  //   const renderer = this.getRenderer();
  //   if (renderer && container) {
  //     renderer.render(container);
  //   }
  // }

  override redrawAll(): void {
    this.drawWithJs();
  }

  //get the element in the layer that is hit by the pointer event, or return null if no such element exists
  getHitElement(event: any): {
    target: LeafletEntity | LeafletGraphics;
    layer: MilitarySymbologyContainer;
  } | null {
    const interaction = this.getRenderer().events;
    const pointerEvent = event.originalEvent;
    const pixiPoint: Point = new Point();
    interaction.mapPositionToPoint(
      pixiPoint,
      pointerEvent.clientX,
      pointerEvent.clientY
    );
    const boundary = new EventBoundary(this.getContainer());
    const target = boundary.hitTest(pixiPoint.x, pixiPoint.y);

    if (target instanceof LeafletEntity) {
      return { target, layer: this };
    } else if (target instanceof Container) {
      let current = target;
      while (current) {
        if (current instanceof LeafletGraphics) {
          return { target: current, layer: this };
        }
        current = current.parent;
      }
    }
    return null;
  }

  addTacticalGraphics(tg: LeafletTacticalGraphics): void {
    this.getContainer().addChild(tg);
    this.tacticalGraphicsList.push(tg);
  }

  addLeafletGraphics(lg: LeafletGraphics): void {
    lg.eventMode = 'dynamic';
    lg.interactive = true;
    this.getContainer().addChild(lg);
    this.leafletGraphicsList.push(lg);
  }

  removeTacticalGraphics(tg: LeafletTacticalGraphics): void {
    this.tacticalGraphicsList = this.tacticalGraphicsList.filter(
      (tacg) => tacg !== tg
    );
    this.getContainer().removeChild(tg);
  }

  removeLeafletGraphics(lg: LeafletGraphics): void {
    this.leafletGraphicsList = this.leafletGraphicsList.filter(
      (graphic) => graphic !== lg
    );
    this.getContainer().removeChild(lg);
  }

  // this.tg1 = new LeafletTacticalGraphics(
  //   'GHMPOADC--****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[42, -81], [40, -82]],
  //   {}
  // );
  //
  // this.tg2 = new LeafletTacticalGraphics(
  //   'GFG*GLF---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[41, -81], [45, -85]],
  //   {}
  // );
  //
  // //
  // this.tg3 = new LeafletTacticalGraphics(
  //   'GFG*PF----****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.8, -80.05], [40.5, -80.5]],
  //   {}
  // );
  //
  // // AXIS OF ADVANCE
  // this.tg4 = new LeafletTacticalGraphics(
  //   'GFG*OLAV--****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[41, -80], [41.4, -80.3], [41.5, -80.5], [41.6, -80.5], [41.04, -80.2]],
  //   {}
  // );
  //
  // // Counter ATTACK BY FIRE
  // this.tg5 = new LeafletTacticalGraphics(
  //   'GHT*KF----****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[41, -80.5], [41.4, -80.8], [41.5, -81], [41.6, -81], [41.04, -80.7]],
  //   {}
  // );
  //
  // // RANGE FAN
  // this.tg6 = new LeafletTacticalGraphics(
  //   'GFFPAXS---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.9, -80.2]],
  //   { AM: [0, 1000, 2000], AN: [315, 45, 330, 30], X: [100, 200] }
  // );
  //
  // // Main Supply Route
  // this.tg7 = new LeafletTacticalGraphics(
  //   'GFSPLRT---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[41, -81.5], [41.4, -81.8], [41.5, -82], [41.6, -82], [41.04, -81.7]],
  //   {}
  // );
  //
  // this.tg8 = new LeafletTacticalGraphics(
  //   'GHMPSP----****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[41.2, -81.0], [41.6, -81.3], [41.7, -81.5], [41.8, -81.5], [41.24, -81.2]],
  //   {}
  // );
  //
  // this.tg9 = new LeafletTacticalGraphics(
  //   'GHMPSL----****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[41.2, -81.3], [41.6, -81.6], [41.7, -81.8], [41.8, -81.8], [41.24, -81.5]],
  //   {}
  // );
  //
  // this.tg10 = new LeafletTacticalGraphics(
  //   'GHMPBCD---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.5, -81.3], [40.6, -81.4], [40.6, -81.3]],
  //   {}
  // );
  //
  // this.tg11 = new LeafletTacticalGraphics(
  //   'GHMPOHO---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.5, -81.5], [40.55, -81.55], [40.60, -81.60]],
  //   {}
  // );
  //
  // this.tg12 = new LeafletTacticalGraphics(
  //   'GUMPOWCD--****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.52, -81.57], [40.57, -81.62], [40.62, -81.65]],
  //   {}
  // );
  //
  // this.tg13 = new LeafletTacticalGraphics(
  //   'GUMPOWH---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.52, -81.47], [40.57, -81.52], [40.62, -81.55]],
  //   {}
  // );
  //
  // this.tg14 = new LeafletTacticalGraphics(
  //   'GUMPOEF---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.42, -81.47], [40.47, -81.52]],
  //   {}
  // );
  //
  // this.tg15 = new LeafletTacticalGraphics(
  //   'GUMPOMC---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.35, -81.47], [40.42, -81.52]],
  //   {}
  // );
  //
  // this.tg16 = new LeafletTacticalGraphics(
  //   'GUMPOADC--****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.345, -81.42], [40.415, -81.47]],
  //   {}
  // );
  //
  // this.tg17 = new LeafletTacticalGraphics(
  //   'GUMPOGR---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.345, -81.025], [40.415, -81.075], [40.745, -80.825], [40.515, -81.275]],
  //   {}
  // );
  //
  // this.tg18 = new LeafletTacticalGraphics(
  //   'GUGPSAE---****X',
  //   gisService.mapDisplay.map as LeafletMap,
  //   MilitaryElementType.TACTICAL_LINE,
  //   [[40.045, -81.025], [40.115, -81.075], [40.445, -80.825], [40.215, -81.275]],
  //   {}
  // );
}
