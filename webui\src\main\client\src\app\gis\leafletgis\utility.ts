/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/4/2024, 3:43 PM
 */
import { Layer } from 'leaflet';

export interface LeafletLayer extends Layer {
  layerName: string;
  layerId: string;
  _visible: boolean;
  visible(visible: boolean): void;
}

export function isCustomLayer(layer: Layer): layer is LeafletLayer {
  return (
    '_visible' in layer
    && 'visible' in layer
    && 'layerName' in layer
    && 'layerId' in layer
  );
}

export function leafletLayer<T extends Layer>(layer: T): T & LeafletLayer {
  if ('_visible' in layer && 'visible' in layer && 'layerName' in layer && 'layerId' in layer) {
    return layer as T & LeafletLayer;
  }
  throw new Error('The provided layer is not a LeafletLayer');
}
