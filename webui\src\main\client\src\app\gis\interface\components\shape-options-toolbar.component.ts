/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 2/1/2024, 2:54 PM
 */

import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Observable } from 'rxjs';
import {
  FillType,
  SelectedShapeStyleInfo,
  ShapeStyle,
  StrokeType,
  StrokeWidth
} from '../models/gis.model';
import { selectSelectedShapeStyleInfo } from '../reducers/shape-selection.reducer';
import { GisService } from '../service/gis.service';
import { ExpandedToolbars } from './toolbar-container.component';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'vcci-shape-options-toolbar',
  styleUrls: [
    './shape-options-toolbar.component-1.scss',
    './shape-options-toolbar.component-2.scss'
  ],
  templateUrl: './shape-options-toolbar.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatSelectModule,
    MatButtonModule,
    MatTooltipModule,
    FormsModule
  ]
})
export class ShapeOptionsToolbarComponent implements OnInit {
  @Input() expanded: boolean = false;
  @Input() all: boolean = false;
  @Output() shapeOptionsToolbarExpanded: EventEmitter<ExpandedToolbars> =
    new EventEmitter<ExpandedToolbars>();

  // normalShapeStyle: ShapeStyle = {
  //   strokeInfo: {
  //     strokeColor: 'rgb(0,0,255)',
  //     strokeWidth: 1,
  //     strokeType: 'solid'
  //   },
  //   fillInfo: {
  //     fillColor: 'rgb(0,0,0, 0.2)',
  //     fillType: 'plain'
  //   }
  // };

  normalStrokeWidth: StrokeWidth = 1;
  normalStrokeType: StrokeType = 'solid';
  normalStrokeColor: string = 'rgb(0,0,255)';
  normalFillColor: string = 'rgb(0,0,0, 0.2)';
  normalFillType: FillType = 'plain';

  // selectedShapeStyle: ShapeStyle = {
  //   strokeInfo: {
  //     strokeColor: 'rgb(255,213,0)',
  //     strokeWidth: 1,
  //     strokeType: 'solid'
  //   },
  //   fillInfo: {
  //     fillColor: 'rgb(0,0,0, 0.2)',
  //     fillType: 'plain'
  //   }
  // };

  selectedStrokeWidth: StrokeWidth = 1;
  selectedStrokeType: StrokeType = 'solid';
  selectedStrokeColor: string = 'rgb(255,213,0)';
  selectedFillColor: string = 'rgb(0,0,0, 0.2)';
  selectedFillType: FillType = 'plain';

  normalPlainFillActive: boolean = true;
  selectedPlainFillActive: boolean = true;

  outlineNormalColorPicker: HTMLElement;
  outlineSelectedColorPicker: HTMLElement;
  fillColorPicker: HTMLElement;

  selected: boolean = false;
  selectedShapeId: number | string | undefined = undefined;

  selectedShapeStyle$: Observable<SelectedShapeStyleInfo> =
    this.gisService.store.select(selectSelectedShapeStyleInfo);

  constructor(private gisService: GisService) {
    this.selectedShapeStyle$.subscribe((selectedShapeStyle) => {
      this.selectedShapeId = selectedShapeStyle.shapeId;
      if (selectedShapeStyle.shapeStyleInfo) {
        this.setNormalStyleInfo(selectedShapeStyle.shapeStyleInfo.normal);
        this.setSelectedStyleInfo(selectedShapeStyle.shapeStyleInfo.selected);
      }
    });
  }

  private setNormalStyleInfo(shapeStyle: ShapeStyle): void {
    const { strokeInfo, fillInfo } = shapeStyle;
    const { strokeColor, strokeType, strokeWidth } = strokeInfo;

    const { fillColor, fillType } = fillInfo;

    this.normalStrokeColor = strokeColor;
    this.normalStrokeType = strokeType;
    this.normalStrokeWidth = strokeWidth;

    this.normalFillColor = fillColor;
    this.normalFillType = fillType;
  }

  private setSelectedStyleInfo(shapeStyle: ShapeStyle): void {
    const { strokeInfo, fillInfo } = shapeStyle;
    const { strokeColor, strokeType, strokeWidth } = strokeInfo;

    const { fillColor, fillType } = fillInfo;

    this.selectedStrokeColor = strokeColor;
    this.selectedStrokeType = strokeType;
    this.selectedStrokeWidth = strokeWidth;

    this.selectedFillColor = fillColor;
    this.selectedFillType = fillType;
  }

  ngOnInit(): void {
    this.outlineNormalColorPicker = document.getElementById(
      'custom-normal-outline-color-input'
    );
    this.outlineSelectedColorPicker = document.getElementById(
      'custom-selected-outline-color-input'
    );
    this.fillColorPicker = document.getElementById('custom-fill-color-input');
  }

  changeColor(): void {
    // Perform any additional logic if needed
    console.log('Selected Color:', this.normalStrokeColor);
  }

  toggleShapeOptionsToolbar(): void {
    if (this.all) {
      this.expanded = true;
    } else {
      this.expanded = !this.expanded;
    }
    this.shapeOptionsToolbarExpanded.emit({
      entities: false,
      drawing: false,
      movement: false,
      styling: this.expanded,
      all: false
    });
  }

  plainFill(): void {
    if (this.selected) {
      this.selectedPlainFillActive = true;
    } else {
      this.normalPlainFillActive = true;
    }
  }

  hatchedFill(): void {
    if (this.selected) {
      this.selectedPlainFillActive = false;
    } else {
      this.normalPlainFillActive = false;
    }
  }

  updateFilType(selected: boolean, fillType: FillType): void {
    this.gisService.elementHandler.shapeStyleInfo = selected
      ? {
          ...this.gisService.elementHandler.shapeStyleInfo,
          selected: {
            ...this.gisService.elementHandler.shapeStyleInfo.selected,
            fillInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.selected
                .fillInfo,
              fillType
            }
          }
        }
      : {
          ...this.gisService.elementHandler.shapeStyleInfo,
          normal: {
            ...this.gisService.elementHandler.shapeStyleInfo.normal,
            fillInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.normal.fillInfo,
              fillType
            }
          }
        };
  }

  updateStrokeColor(selected: boolean, strokeColor: string): void {
    this.gisService.elementHandler.shapeStyleInfo = selected
      ? {
          ...this.gisService.elementHandler.shapeStyleInfo,
          selected: {
            ...this.gisService.elementHandler.shapeStyleInfo.selected,
            strokeInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.selected
                .strokeInfo,
              strokeColor
            }
          }
        }
      : {
          ...this.gisService.elementHandler.shapeStyleInfo,
          normal: {
            ...this.gisService.elementHandler.shapeStyleInfo.normal,
            strokeInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.normal
                .strokeInfo,
              strokeColor
            }
          }
        };
  }

  normalStrokeColorSelect(): void {
    this.outlineNormalColorPicker.click();
  }

  setNormalStrokeColor(color: string): void {
    this.normalStrokeColor = color;
    this.updateStrokeColor(false, color);
  }

  selectedStrokeColorSelect(): void {
    this.outlineSelectedColorPicker.click();
  }

  setSelectedStrokeColor(color: string): void {
    this.selectedStrokeColor = color;
    this.updateStrokeColor(true, color);
  }

  fillColor(): void {
    this.fillColorPicker.click();
  }

  updateFillColor(selected: boolean, fillColor: string): void {
    console.log(`this is fill color: ${fillColor}`);
    this.gisService.elementHandler.shapeStyleInfo = selected
      ? {
          ...this.gisService.elementHandler.shapeStyleInfo,
          selected: {
            ...this.gisService.elementHandler.shapeStyleInfo.selected,
            fillInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.selected
                .fillInfo,
              fillColor: this.hexToRgba(fillColor)
            }
          }
        }
      : {
          ...this.gisService.elementHandler.shapeStyleInfo,
          normal: {
            ...this.gisService.elementHandler.shapeStyleInfo.normal,
            fillInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.normal.fillInfo,
              fillColor: this.hexToRgba(fillColor)
            }
          }
        };

    this.gisService.elementHandler.updateSelectedElementStyle();
  }

  hexToRgba(hex: string): string {
    const newHex = hex.replace(/^#/, '');
    const bigint = parseInt(newHex, 16);
    const r = Math.floor(bigint / 65536) % 256;
    const g = Math.floor(bigint / 256) % 256;
    const b = bigint % 256;

    return `rgba(${r}, ${g}, ${b}, 0.2)`;
  }

  setFillColor(): void {
    this.updateFillColor(
      this.selected,
      this.selected ? this.selectedFillColor : this.normalFillColor
    );
  }

  activateSelection(): void {
    this.selected = true;
  }

  activateNormal(): void {
    this.selected = false;
  }

  updateStrokePattern(selected: boolean, strokeType: StrokeType): void {
    this.gisService.elementHandler.shapeStyleInfo = selected
      ? {
          ...this.gisService.elementHandler.shapeStyleInfo,
          selected: {
            ...this.gisService.elementHandler.shapeStyleInfo.selected,
            strokeInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.selected
                .strokeInfo,
              strokeType
            }
          }
        }
      : {
          ...this.gisService.elementHandler.shapeStyleInfo,
          normal: {
            ...this.gisService.elementHandler.shapeStyleInfo.normal,
            strokeInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.normal
                .strokeInfo,
              strokeType
            }
          }
        };
  }

  selectStrokePattern(value: StrokeType): void {
    if (this.selected) {
      this.selectedStrokeType = value;
    } else {
      this.normalStrokeType = value;
    }

    this.updateStrokePattern(this.selected, value);
  }

  updateStrokeWidth(selected: boolean, strokeWidth: StrokeWidth): void {
    this.gisService.elementHandler.shapeStyleInfo = selected
      ? {
          ...this.gisService.elementHandler.shapeStyleInfo,
          selected: {
            ...this.gisService.elementHandler.shapeStyleInfo.selected,
            strokeInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.selected
                .strokeInfo,
              strokeWidth
            }
          }
        }
      : {
          ...this.gisService.elementHandler.shapeStyleInfo,
          normal: {
            ...this.gisService.elementHandler.shapeStyleInfo.normal,
            strokeInfo: {
              ...this.gisService.elementHandler.shapeStyleInfo.normal
                .strokeInfo,
              strokeWidth
            }
          }
        };
  }

  selectStroke(value: string): void {
    const integerStrokeValue = parseInt(value.substring(6, value.length), 10);
    if (this.selected) {
      this.selectedStrokeWidth = integerStrokeValue;
    } else {
      this.normalStrokeWidth = integerStrokeValue;
    }

    this.updateStrokeWidth(this.selected, integerStrokeValue);
  }
}
