import { Injectable } from '@angular/core';
import { Actions } from '@ngrx/effects';
import { GisService } from '../service/gis.service';

@Injectable()
export class ElementSelectionEffect {
  // $selectMapElement = createEffect(() => this.actions$.pipe(
  //   ofType(mapActions.mapElementSelection),
  //   tap(value => { console.log(value.payload); })
  // ), { dispatch: false });

  constructor(private actions$: Actions, private gisService: GisService) {

  }
}
