import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { MatDialog } from '@angular/material/dialog';
import { cursitActions } from '../actions/cursit.actions';
import {
  exhaustMap,
  map,
  withLatestFrom,
  filter,
  tap,
  delay,
  debounceTime
} from 'rxjs';
import { CursitFilterComponent } from '../components/cursit-filter/cursit-filter.component';
import { Store } from '@ngrx/store';
import { selectFilters } from '../reducers/cursit.reducer';
import { CursitFilter } from '../models/cursit-filter.model';
import { NodeActions } from '../../data/node/node.model';
import { GisService } from '../../gis/interface/service/gis.service';

@Injectable()
export class CursitEffects {
  constructor(
    private actions$: Actions,
    private dialog: MatDialog,
    private store: Store,
    private router: Router,
    private gisService: GisService
  ) {}

  openFilterDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(cursitActions.openFilterDialog),
      withLatestFrom(this.store.select(selectFilters)),
      map(([_, filters]) => filters),
      exhaustMap((filters) =>
        this.dialog
          .open<
            CursitFilterComponent,
            CursitFilter,
            CursitFilter | undefined | null
          >(CursitFilterComponent, {
            data: filters,
            minWidth: '500px',
            maxHeight: '500px'
          })
          .afterClosed()
          .pipe(
            map((filters) =>
              !!filters
                ? cursitActions.updateCursitFilters(filters)
                : cursitActions.closeFilterDialog()
            )
          )
      )
    )
  );

  closeFilterDialog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(NodeActions.dismissFilterDialog),
      map((_) => cursitActions.removeGeoFilters())
    )
  );

  navigateToCursit$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(cursitActions.navigateToCursit),
        tap(() => this.router.navigate(['/cursit']))
      ),
    { dispatch: false }
  );

  triggerResize$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(cursitActions.triggerMapResize),
        debounceTime(200),
        tap(() => this.gisService.resizeTrigger())
      ),
    { dispatch: false }
  );
}
