.route-creation-section {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

mat-dialog-content {
  min-width: 450px;
  max-width: 450px;
}

::ng-deep .mat-mdc-table > tbody {
  max-height: 100px;
  overflow-y: auto;
  display: block;
}

::ng-deep .mat-mdc-table thead,
::ng-deep .mat-mdc-table tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

::ng-deep .mat-mdc-table td,
::ng-deep .mat-mdc-table th {
  display: table-cell;
}

::ng-deep .mat-mdc-table .mat-column-actions {
  width: 60px;
  max-width: 60px;
}
