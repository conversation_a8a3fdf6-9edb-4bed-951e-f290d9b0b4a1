import { Feature } from '@luciad/ria/model/feature/Feature';
import { getReference } from '@luciad/ria/reference/ReferenceProvider';
import { Point } from '@luciad/ria/shape/Point';
import { Polygon } from '@luciad/ria/shape/Polygon';
import { Polyline } from '@luciad/ria/shape/Polyline';
import { Shape } from '@luciad/ria/shape/Shape';
import { createPoint } from '@luciad/ria/shape/ShapeFactory';
import { createTransformation } from '@luciad/ria/transformation/TransformationFactory';
import { Classifier } from '@luciad/ria/view/feature/transformation/Classifier';
import { Map } from '@luciad/ria/view/Map';
import { LineStyle } from '@luciad/ria/view/style/LineStyle';
import { ShapeStyle as LuciadShapeStyle } from '@luciad/ria/view/style/ShapeStyle';
import { Coordinate } from '../../cursit/models/cursit.model';
import { FillType, Reference, ShapeStyle, StrokeType } from '../interface/models/gis.model';
import { HatchedImageBuilder } from '../interface/shape-util/HatchedImageBuilder';

export function GetMapPointFromViewPoint(map: Map | undefined, viewPoint: Point): Point | null {
  let mapPointReference;
  if (map) {
    if (map.reference.identifier.toLowerCase().includes('4978')) {
      mapPointReference = map.reference;
    } else {
      mapPointReference = getReference(Reference.Equidistant);
    }

    const transformation = createTransformation(map.reference, mapPointReference);

    const tempMapPoint = createPoint(map.reference, [0, 0, 0]);
    const tempModelPoint = createPoint(map.reference, [0, 0, 0]);

    try {
      map.viewToMapTransformation.transform(viewPoint, tempMapPoint);
      transformation.transform(tempMapPoint, tempModelPoint);
    } catch (exception) {
      // TODO: implement catch
    }

    return createPoint(mapPointReference, [tempModelPoint.x, tempModelPoint.y, tempModelPoint.z]);
  }

  return null;
}

export function getPointFromViewPoint(map: Map, viewPoint: Point): Point | null {
  const tempMapPoint = createPoint(map.reference, []);
  const targetReference = getReference('CRS:84');
  const tempModelPoint = createPoint(targetReference, []);
  const map2Model = createTransformation(map.reference, targetReference);
  map.viewToMapTransformation.transform(viewPoint, tempMapPoint);
  map2Model.transform(tempMapPoint, tempModelPoint);
  return createPoint(targetReference, [tempModelPoint.x, tempModelPoint.y, tempModelPoint.z]);
}

export function getScreenXYFromMapPoint(map: Map, mapPoint: Point): Coordinate {
  const model2Map = createTransformation(getReference('CRS:84'), map.reference);
  const viewPoint = map.mapToViewTransformation.transform(model2Map.transform(mapPoint));
  return [viewPoint.x, viewPoint.y];
}

export function isShapePolyType(shape: Shape): boolean {
  return shape instanceof Polyline || shape instanceof Polygon;
}

export function dashConverter(strokeType: StrokeType): [number, number] | undefined {
  if (strokeType !== undefined) {
    switch (strokeType) {
      case 'dashed':
        return [10, 5];
      case 'dotted':
        return [1, 3];
      default:
        return undefined;
    }
  }

  return undefined;
}

export function fillTypeConverter(
  fillType: FillType,
  fillColor: string
): HTMLImageElement | undefined {
  if (fillType !== undefined) {
    switch (fillType) {
      case 'hatched':
        return  new HatchedImageBuilder()
          .patterns([HatchedImageBuilder.Pattern.SLASH, HatchedImageBuilder.Pattern.BACKGROUND])
          .lineColor(fillColor)
          .lineWidth(1)
          .patternSize(10, 10)
          .backgroundColor(undefined)
          .build();
      default:
        return undefined;
    }
  }

  return undefined;
}

function convertDashToStrokeType(dash: number[]): StrokeType {
  if (dash.length === 2) {
    if (dash[0] === 10 && dash[1] === 5) {
      return 'dashed';
    } if (dash[0] === 1 && dash[1] === 3) {
      return 'dotted';
    }
  }
  return 'solid';
}

export function luciadStyleToGeneralStyle(luciadShapeStyle: LuciadShapeStyle): ShapeStyle {
  const { fill, stroke } = luciadShapeStyle;
  const { color: fillColor, image } = fill || undefined;
  const {  color: strokeColor, dash, width: strokeWidth } = stroke as LineStyle || undefined;
  const hatchedOrPlain: FillType = image ? 'hatched' : 'plain';
  return {
    fillInfo: {
      fillColor,
      fillType: fillColor ? hatchedOrPlain : undefined
    },
    strokeInfo: {
      strokeType: dash ? convertDashToStrokeType(dash) : 'solid',
      strokeColor,
      strokeWidth
    }
  };
}

export class AffiliationClassifier extends Classifier {
  static getClassification(f: Feature): string {
    return (f.properties as any).code.charAt(1);
  }
}

