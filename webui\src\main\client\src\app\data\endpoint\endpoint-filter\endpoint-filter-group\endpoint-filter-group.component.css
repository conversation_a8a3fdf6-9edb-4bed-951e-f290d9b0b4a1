.panel-title {
    margin-bottom: 10px;
    width: 150px;
}

.group-panel {
    display: flex;
    flex-direction: row;
    justify-content: stretch;
    align-items: center;
    gap: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.prefix-column {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
}

.prefix-item {
    width: 55px;
    height: 60px;
}

.filter-column {
    width: calc(100% - 120px);
    overflow: auto;
}

.filter-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    height: 60px;
}

.filter-button {
    min-width: 100px;
    flex: 0 0 auto;
    text-wrap: none;
}

.action-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.action-row > button {
    margin: 5px;
}
