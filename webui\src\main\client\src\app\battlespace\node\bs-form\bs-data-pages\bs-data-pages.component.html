@if (dataPages.length === 1) {
  <vcci-bs-data-page [dataPage]="dataPages[0]" />
} @else if (dataPages.length > 1) {
  <mat-tab-group>
    @for (dataPage of dataPages; track dataPage.pageName) {
      <mat-tab [label]="dataPage.pageName">
        <vcci-bs-data-page
          [dataPage]="dataPage"
          [usingDefault]="usingDefault"
          (networkInterfaceChanges)="onNetworkInterfaceChange($event)"
          (getNext)="onNext($event)"/>
      </mat-tab>
    }
  </mat-tab-group>
}
