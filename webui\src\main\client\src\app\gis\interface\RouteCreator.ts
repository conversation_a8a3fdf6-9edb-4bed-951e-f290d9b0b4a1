import { MouseListener } from './EventListener';
import { GISElement } from './GISElement';
import { MapDisplay } from './MapDisplay';
import { RouteClickData } from './service/gis.service';

export class CreateRoute implements MouseListener {
  mapDisplay: MapDisplay<object, object>;
  mapNodePosition: DOMRect;
  firstClick: boolean = true;
  route: GISElement<object>;

  constructor(mapDisplay: MapDisplay<object, object>) {
    this.mapDisplay = mapDisplay;

    new ResizeObserver((): void => {
      this.mapNodePosition = this.mapDisplay
        .getContainerDOM()
        .getBoundingClientRect();
    }).observe(this.mapDisplay.getContainerDOM());

    this.bindAllListeners();
  }

  private getGeographicCoordinates(event: MouseEvent): {
    lat: number;
    lng: number;
    containerX: number;
    containerY: number;
  } {
    const containerX = event.clientX - this.mapNodePosition.left;
    const containerY = event.clientY - this.mapNodePosition.top;
    const geoCoords = this.mapDisplay.containerPointToLatLng(
      containerX,
      containerY
    );

    return {
      lat: geoCoords.lat,
      lng: geoCoords.lng,
      containerX,
      containerY
    };
  }

  private bindAllListeners(): void {
    this.mouseClick = this.mouseClick.bind(this);
    this.mouseUp = this.mouseUp.bind(this);
    this.mouseDown = this.mouseDown.bind(this);
    this.mouseEntered = this.mouseEntered.bind(this);
    this.mouseLeave = this.mouseLeave.bind(this);
    this.mouseMove = this.mouseMove.bind(this);
    this.mouseDoubleClick = this.mouseDoubleClick.bind(this);
  }

  startCreateRoute(): void {
    this.mapDisplay.inputHandler.addMissionPlanningMouseListeners(this);
  }

  endCreateRoute(): void {
    this.mapDisplay.inputHandler.removeMissionPlanningMouseListeners(this);
  }

  mouseClick(event: MouseEvent): void {
    if (this.firstClick) {
      this.firstClick = false;
      // this.route: GISElement<object> = this.mapDisplay.elementFactory.createRoute();
    }

    const coords = this.getGeographicCoordinates(event);

    if (this.mapDisplay.onRouteClick) {
      const clickData: RouteClickData = {
        latitude: coords.lat,
        longitude: coords.lng,
        containerX: coords.containerX,
        containerY: coords.containerY
      };
      this.mapDisplay.onRouteClick(clickData);
    }

    // this.route.addLocation();
    console.log(
      `Current Mouse Location on Map from Mouse Click: Lat: ${coords.lat}, Lng: ${coords.lng}, Container Point: ${coords.containerX}, ${coords.containerY}, Map: ${this.mapDisplay.map}`
    );
  }

  mouseMove(event: MouseEvent): void {
    const coords = this.getGeographicCoordinates(event);
    console.log(
      `Current Mouse Location on Map from Create Route Mouse Move: Lat: ${coords.lat}, Lng: ${coords.lng}, Container Point: ${coords.containerX}, ${coords.containerY}, Map: ${this.mapDisplay.map}`
    );
  }

  mouseEntered(event: MouseEvent): void {
    const coords = this.getGeographicCoordinates(event);
    console.log(
      `Current Mouse Location on Map from Mouse Entered: Lat: ${coords.lat}, Lng: ${coords.lng}, Container Point: ${coords.containerX}, ${coords.containerY}, Map: ${this.mapDisplay.map}`
    );
  }

  mouseUp(event: MouseEvent): void {
    const coords = this.getGeographicCoordinates(event);
    console.log(
      `Current Mouse Location on Map from Mouse Up: Lat: ${coords.lat}, Lng: ${coords.lng}, Container Point: ${coords.containerX}, ${coords.containerY}, Map: ${this.mapDisplay.map}`
    );
  }

  mouseDown(event: MouseEvent): void {
    const coords = this.getGeographicCoordinates(event);
    console.log(
      `Current Mouse Location on Map from Mouse Down: Lat: ${coords.lat}, Lng: ${coords.lng}, Container Point: ${coords.containerX}, ${coords.containerY}, Map: ${this.mapDisplay.map}`
    );
  }

  mouseLeave(event: MouseEvent): void {
    const coords = this.getGeographicCoordinates(event);
    console.log(
      `Current Mouse Location on Map from Mouse Leave: Lat: ${coords.lat}, Lng: ${coords.lng}, Container Point: ${coords.containerX}, ${coords.containerY}, Map: ${this.mapDisplay.map}`
    );
  }

  mouseDoubleClick(event: MouseEvent): void {
    if (!this.firstClick) {
      this.firstClick = true;
      // this.route.removeLocation(this.route.getLocationCount() - 1);
      this.endCreateRoute();
    }
  }
}
