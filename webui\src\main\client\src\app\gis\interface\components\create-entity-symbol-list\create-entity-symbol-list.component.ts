import { Component, OnInit, signal, inject } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SymbologyComponent } from '@simfront/common-libs';
import { map, startWith } from 'rxjs/operators';
import {
  SymbolSetList,
  generateAllDimensionFunctionCombinations
} from 'src/app/milstd/models/milstd-2525b.model';
import { Observable } from 'rxjs';

@Component({
  selector: 'nexus-create-entity-symbol-list',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    SymbologyComponent
  ],
  templateUrl: './create-entity-symbol-list.component.html',
  styleUrl: './create-entity-symbol-list.component.css'
})
export class CreateEntitySymbolListComponent implements OnInit {
  private dialogRef = inject(MatDialogRef<CreateEntitySymbolListComponent>);

  setList = signal<SymbolSetList[]>([]);
  selectedSymbol = signal<SymbolSetList | null>(null);

  symbolControl = new FormControl<string | SymbolSetList>('');
  filteredOptions: Observable<SymbolSetList[]>;

  ngOnInit() {
    this.testCombinationGeneration();

    this.filteredOptions = this.symbolControl.valueChanges.pipe(
      startWith(''),
      map((value) => {
        const description =
          typeof value === 'string' ? value : value?.description;
        return description
          ? this.setList().filter((symbol) =>
              symbol.description
                .toLowerCase()
                .includes(description.toLowerCase())
            )
          : this.setList();
      })
    );
  }

  private testCombinationGeneration() {
    const allCombinations = generateAllDimensionFunctionCombinations();
    this.setList.set(allCombinations);
  }

  onSymbolSelected(symbol: SymbolSetList) {
    this.selectedSymbol.set(symbol);
  }

  displaySymbol(symbol?: SymbolSetList): string {
    return symbol ? symbol.description : '';
  }

  selectSymbol() {
    const selected = this.selectedSymbol();
    if (selected) {
      this.dialogRef.close(selected);
    }
  }

  cancel() {
    this.dialogRef.close();
  }
}
