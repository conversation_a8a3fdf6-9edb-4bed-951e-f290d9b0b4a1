<div
  id="layerManager"
  class="layerManager"
  [ngClass]="{ 'small-size': isSmallSize, hide: !show }"
>
  <div class="wrapper">
    <div class="layer-manager-header">
      <h1>Layer List :</h1>
      <div class="collapse-button">
        <button
          mat-icon-button
          [matTooltip]="isCollapsed ? 'Expand' : 'Collapse'"
          (click)="toggleCollapse()"
          (mousedown)="removeResizeListener()"
          (mouseup)="toggleButtonReleased()"
        >
          <mat-icon>{{ isCollapsed ? 'expand_less' : 'expand_more' }}</mat-icon>
        </button>
        <button
          mat-icon-button
          matTooltip="Close"
          (click)="close()"
          (mousedown)="removeResizeListener()"
          (mouseup)="toggleButtonReleased()"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>
    <div class="main-tree-div">
      <div [hidden]="isCollapsed">
        <div class="layer-list-div">
          <sf-core-nested-tree
            class="first-tree"
            #tree
            [data]="layerListData"
          >
          </sf-core-nested-tree>
        </div>
        <mat-divider class="divider"></mat-divider>
        <div class="grid-list-div">
          <mat-card-subtitle class="card">
            <mat-slide-toggle
              color="primary"
              class="grid-toggle"
              (change)="toggleAllGridLayers($event.checked)"
              [(ngModel)]="isSlideToggleChecked"
            >
              <span class="grid-parent">Grid Layers</span>
            </mat-slide-toggle>
          </mat-card-subtitle>
          <sf-core-nested-tree class="grid-tree" [data]="gridLayerListData">
          </sf-core-nested-tree>
        </div>
        <mat-divider class="divider"></mat-divider>
        <div class="base-layers-list-div">
          <sf-core-nested-tree
            class="world-borders"
            [data]="shapeLayerListData"
          >
          </sf-core-nested-tree>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #custom let-node>
  <div class="layer-row-wrapper">
    @if (node.value.layerType !== 'Grid') {
      <div class="layer-row">
        <button
          *ngIf="
            node.value.layerType !== 'Parent' && node.value.layerType !== 'Grid'
          "
          [matTooltip]="
            node.value.locked === 'locked'
              ? 'Unlock layer'
              : 'Lock layer'
          "
          class="layer-lock-button"
          (click)="lockLayer(node.value)"
        >
          <mat-icon
            *ngIf="node.value.locked === 'unlocked'"
            class="lock-unlock-icon"
            svgIcon="unlock-layer"
          >
          </mat-icon>
          <mat-icon
            *ngIf="node.value.locked === 'locked'"
            class="lock-unlock-icon"
            svgIcon="lock-layer"
          ></mat-icon>
        </button>
        <mat-checkbox
          class="check-box"
          [disabled]="node.value.locked === 'locked'"
          [checked]="node.value.visibility === 'VISIBLE'"
          [indeterminate]="
            node.value.visibility === 'PARTIAL'
          "
          (change)="toggleLayerVisibility(node, $event.checked)"
        >
          <span>
            {{ node.value.layerName }}
          </span>
        </mat-checkbox>
        @if (node.value.opacity !== undefined) {
          <button mat-flat-button class="layer-settings-button">
            <mat-icon
              (click)="toggleOpacity(node.value.layerId)"
              class="layer-settings-icon"
              >page_info</mat-icon
            >
          </button>
        }
      </div>
    } @else {
      <div class="layer-row">
        <mat-radio-button
          class="radio-button"
          [checked]="node.value.visibility === 'VISIBLE'"
          [disabled]="!isSlideToggleChecked"
          (change)="toggleGridLayers(node)"
        >
          <span>
            {{ node.value.layerName }}
          </span>
        </mat-radio-button>
      </div>
    }

    @if (node.value.opacity !== undefined) {
      <div
        class="slider-wrapper"
        [ngClass]="{ hidden: !opacitySlider[node.value.layerId] }"
      >
        <div class="slider-background-wrapper" matTooltip="Set Map Opacity">
          <mat-icon
            class="slider-background-scale"
            svgIcon="map-opacity-scale-tr-tb"
          ></mat-icon>
          <mat-slider class="map-opacity-slider" min="0" max="1" step="0.1">
            <input
              matSliderThumb
              [(ngModel)]="node.value.opacity"
              (valueChange)="setLayerOpacity(node.value, $event)"
            />
          </mat-slider>
        </div>
      </div>
    }
  </div>
</ng-template>
