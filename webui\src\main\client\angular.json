{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"client": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "nexus", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"allowedCommonJsDependencies": ["bowser", "eventemitter3", "earcut", "milsymbol", "leaflet", "konva"], "outputPath": {"base": "dist/client"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "stylePreprocessorOptions": {"includePaths": ["src/global-styles"]}, "styles": ["./node_modules/@angular/material/prebuilt-themes/purple-green.css", "src/styles.scss", "src/theme-colors.scss", "node_modules/leaflet/dist/leaflet.css", "node_modules/leaflet.markercluster/dist/MarkerCluster.css", "node_modules/leaflet-draw/dist/leaflet.draw.css", "node_modules/leaflet.markercluster/dist/MarkerCluster.Default.css", "node_modules/leaflet-ruler/src/leaflet-ruler.css", "src/assets/mil-sym-js/renderer.css"], "scripts": ["src/assets/mil-sym-js/savm-bc.js", "node_modules/leaflet/dist/leaflet.js", "node_modules/leaflet-draw/dist/leaflet.draw.js", "node_modules/leaflet.markercluster/dist/leaflet.markercluster.js", "node_modules/leaflet-ruler/src/leaflet-ruler.js"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "35mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "8kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "client:build:production"}, "development": {"buildTarget": "client:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "client:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/purple-green.css", "src/styles.scss"], "scripts": ["node_modules/leader-line/leader-line.min.js", "src/assets/mil-sym-js/savm-bc.js"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"]}}