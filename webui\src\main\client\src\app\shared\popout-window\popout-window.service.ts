import { ComponentType } from '@angular/cdk/overlay';
import { ComponentPortal, DomPortalOutlet } from '@angular/cdk/portal';
import {
  ApplicationRef,
  ComponentFactoryResolver,
  ComponentRef,
  Injectable,
  InjectionToken,
  Injector,
  ViewContainerRef
} from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  IPopoutWindowModal,
  POPOUT_WINDOW_DATA,
  PopoutWindow,
  PopoutWindowConfig,
  PopoutWindowRect
} from './popout-window.model';

@Injectable({ providedIn: 'root' })
export class PopoutWindowService {
  private static _darkMode: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public static set darkMode(darkMode: boolean) {
    PopoutWindowService._darkMode.next(darkMode);
  }

  public static getDarkMode(): Observable<boolean> {
    return PopoutWindowService._darkMode.asObservable();
  }

  private static _viewContainerRef: ViewContainerRef | undefined;
  public static set viewContainerRef(viewContainerRef: ViewContainerRef | undefined) {
    PopoutWindowService._viewContainerRef = viewContainerRef;
  }

  public static get viewContainerRef(): ViewContainerRef | undefined {
    return PopoutWindowService._viewContainerRef;
  }

  constructor(private injector: Injector, private componentFactoryResolver: ComponentFactoryResolver, private applicationRef: ApplicationRef) {
  }

  open<T>(popoutWindow: PopoutWindow, config?: PopoutWindowConfig<T>): void;
  open<T, C extends IPopoutWindowModal>(component: ComponentType<C>, config?: PopoutWindowConfig<T>): void;
  open<T, C extends IPopoutWindowModal>(component: ComponentType<C> | PopoutWindow, config?: PopoutWindowConfig<T>): void {
    if (component instanceof PopoutWindow) {
      component.open(undefined, config);
    } else {
      const _popoutWindow = PopoutWindowService.createPopoutWindow(config);
      if (_popoutWindow && _popoutWindow.window) {
        _popoutWindow.config = config;
        const outlet = this.createCDKPortal(_popoutWindow.window);
        const injector = PopoutWindowService.createDefaultInjector(config?.injectorToken, config?.data);
        const componentRef = PopoutWindowService.attachTemplateContainer(outlet, component, injector);
        componentRef.instance.onClose.subscribe(() => _popoutWindow.close());
        componentRef.instance.isPopoutWindow = true;
      }
    }
  }

  public static createPopoutWindow(config: PopoutWindowConfig | undefined): PopoutWindow | undefined {
    const _window = PopoutWindowService.createWindow(config);
    const _popoutWindow = new PopoutWindow();
    _popoutWindow.window = _window;
    if (_window && _popoutWindow) {
      PopoutWindowService.assignStyle(_popoutWindow, config);
      window.addEventListener('unload', () => _popoutWindow.close());
    }
    return _popoutWindow;
  }

  static createWindow(config: PopoutWindowConfig | undefined): Window | null {
    const rect = PopoutWindowService._calculateRect(config?.rect);
    return window.open(
      '',
      `popoutWindow${Date.now()}`,
      `
        width=${rect.width},
        height=${rect.height},
        left=${rect.x},
        top=${rect.y}`
    );
  }

  static createStyleObserver(window: Window | null): MutationObserver | undefined {
    if (window) {
      const popout: Window = window;
      const docHead = document.querySelector('head');
      const observer =  new MutationObserver(mutations => mutations.forEach(
        mutation => mutation.addedNodes.forEach(node => {
          if (node.nodeName === 'STYLE') {
            popout.document.head.appendChild(node.cloneNode(true));
          }
        })
      ));
      if (docHead !== null) {
        observer.observe(docHead, { childList: true });
      }
      return observer;
    }
    return undefined;
  }

  static assignStyle<T>(popoutWindow: PopoutWindow, config: PopoutWindowConfig | undefined): void {
    if (popoutWindow.window && popoutWindow.document) {
      const documentRef: Document = popoutWindow.document;
      // Assign general style and title.
      documentRef.title = config?.title || 'Popout Window';
      documentRef.body.style.margin = '0';

      // Suppress clone styles.
      if (config === undefined || config.suppressClonedStyles === undefined || !config.suppressClonedStyles) {
        popoutWindow.toggleDarkMode(this._darkMode.value);
        document.head.querySelectorAll('style').forEach(node => documentRef.head.appendChild(node.cloneNode(true)));
        popoutWindow.observeStyleChanges();
        document.head.querySelectorAll('link[rel="stylesheet"]').forEach(node => documentRef.head.insertAdjacentHTML(
          'beforeend',
          `<link rel="stylesheet" type="${(node as HTMLLinkElement).type}" href="${(node as HTMLLinkElement).href}">`
        ));
        document.fonts.forEach(node => (popoutWindow.document as any).fonts.add(node));
      }

      // Add window style url.
      if (config?.windowStyleUrl) {
        popoutWindow.document.head.insertAdjacentHTML(
          'beforeend',
          `<link rel="stylesheet" type="text/css" href="${window.location.origin}/${config.windowStyleUrl}">`
        );
      }

      // Add window style.
      if (config?.windowStyle) {
        popoutWindow.document.head.insertAdjacentHTML('beforeend', `<style>${config.windowStyle}</style>`);
      }
    }
  }

  assignStyleToWindow(window: Window, config: PopoutWindowConfig | undefined): void {
    if (window && window.document) {
      const documentRef: Document = window.document;
      // Assign general style and title.
      documentRef.title = config?.title || 'Popout Window';
      documentRef.body.style.margin = '0';

      // Suppress clone styles.
      if (config === undefined || config.suppressClonedStyles === undefined || !config.suppressClonedStyles) {
        // popoutWindow.toggleDarkMode(this._darkMode.value);
        document.head.querySelectorAll('style').forEach(node => documentRef.head.appendChild(node.cloneNode(true)));
        // popoutWindow.observeStyleChanges();
        document.head.querySelectorAll('link[rel="stylesheet"]').forEach(node => documentRef.head.insertAdjacentHTML(
          'beforeend',
          `<link rel="stylesheet" type="${(node as HTMLLinkElement).type}" href="${(node as HTMLLinkElement).href}">`
        ));
        document.fonts.forEach(node => (window.document as any).fonts.add(node));
      }

      // Add window style url.
      if (config?.windowStyleUrl) {
        window.document.head.insertAdjacentHTML(
          'beforeend',
          `<link rel="stylesheet" type="text/css" href="${window.location.origin}/${config.windowStyleUrl}">`
        );
      }

      // Add window style.
      if (config?.windowStyle) {
        window.document.head.insertAdjacentHTML('beforeend', `<style>${config.windowStyle}</style>`);
      }
    }
  }

  public createCDKPortal(window: Window): DomPortalOutlet {
    // Create DOM Portal.
    const outlet = new DomPortalOutlet(window.document.body, this.componentFactoryResolver, this.applicationRef, this.injector);
    // Copy styles from parent window
    document.querySelectorAll('style').forEach(htmlElement => window.document.head.appendChild(htmlElement.cloneNode(true)));
    return outlet;
  }

  public static attachTemplateContainer<T>(outlet: DomPortalOutlet, component: ComponentType<T>, injector: Injector): ComponentRef<T> {
    const containerPortal = new ComponentPortal(component, PopoutWindowService.viewContainerRef, injector);
    return outlet.attach(containerPortal);
  }

  public static createDefaultInjector<T>(token: InjectionToken<T> | undefined, data: T | undefined): Injector {
    return Injector.create({ providers: [{ provide: token || POPOUT_WINDOW_DATA, useValue: data || {} }] });
  }

  private static _calculateRect(rect: PopoutWindowRect | undefined): PopoutWindowRect {
    const navHeight = window.outerHeight - window.innerHeight;
    const navWidth = window.outerWidth - window.innerWidth;
    const width = rect && rect.width > 99 ? rect.width : 500;
    const height = rect && rect.height > 99 ? rect.height : 500;
    const winLeft = rect?.x || width;
    const winTop = rect?.y || height;
    const x = window.screenX + navWidth + winLeft;
    const y = window.screenY + navHeight + winTop - 60;
    return { width, height, x, y };
  }
}
