import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { RouterLegendItemComponent } from './router-legend-item.component';

@Component({
    selector: 'vcci-router-legends',
    template: `
    <span mat-dialog-title class="legend-title">LEGENDS</span>
    <!-- TODO: Make the items more responsive to resize -->
    <mat-dialog-content class="legends-container">
      <vcci-router-legend-item
        *ngFor="let item of legendItems"
        [icon]="item.icon"
        [scaleIconDown]="!!item.scaleIconDown"
      >
        <span [innerHTML]="item.span"></span>
      </vcci-router-legend-item>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Close</button>
    </mat-dialog-actions>
  `,
    styles: [`
      .legends-container {
          padding-top: 10px;
          display: grid;
          max-width: 100%;
          grid-template-rows: repeat(3, auto);
          grid-template-columns: repeat(5, minmax(min-content, max-content));
      }

      .legend-title {
          grid-column: 1/-1;
          font-weight: 700;
          font-size: 21px;
      }
  `],
    host: { class: 'custom-font' },
    imports: [
        CommonModule,
        RouterLegendItemComponent,
        MatDialogModule,
        MatButtonModule
    ]
})
export class RouterLegendsComponent {
  legendItems: { icon: string, span: string, scaleIconDown?: boolean }[] = [
    { icon: 'connection', span: 'CONNECTION LINK', scaleIconDown: true },
    { icon: 'connect-all', span: 'CONNECTED' },
    { icon: 'running', span: 'RUNNING' },
    { icon: 'filtered-full', span: 'FULL SELECTION' },
    { icon: 'endpoint-flow-direction-out', span: 'END POINT FLOW OUT' },
    { icon: 'vcci-connection', span: '<b>NEXUS CONNECTION</b>', scaleIconDown: true },
    { icon: 'unlinked', span: 'NOT CONNECTED' },
    { icon: 'disrupted', span: 'DISRUPTED' },
    { icon: 'filtered-partial', span: 'PARTIAL SELECTION' },
    { icon: 'endpoint-flow-direction-in', span: 'END POINT FLOW IN' },
    { icon: 'nexus-brand', span: '<b>NEXUS LANDMARK</b>' },
    { icon: 'detail', span: 'DETAILS' },
    { icon: 'stop-all', span: 'NOT RUNNING' },
    { icon: 'filtered-partial', span: 'NO SELECTION' }
  ];
}
