import { Component, Input } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { EntityFormField } from 'src/app/milstd/models/milstd-2525.model';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'nexus-create-entity-code',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    CommonModule
  ],
  templateUrl: './create-entity-code.component.html',
  styleUrl: './create-entity-code.component.css'
})
export class CreateEntityCodeComponent {
  @Input() parentForm: FormGroup;
  @Input() entityType: string = 'Unit';
  @Input() milsymCodeFormFields: EntityFormField<string>[] = [];

  symbolCode: string = '';
  private subscription: Subscription;

  constructor() {}
}
