import { FederatedPointerEvent, Graphics } from 'pixi.js';
import { Store } from '@ngrx/store';
import { mapActions } from '../interface/actions/map.actions';

/**
 * Represents a selection menu for map elements
 * This component is displayed when an element is selected on the map
 */
export class LeafletSelectionMenu extends Graphics {
  private svgDefinition = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" height="100%" width="100%">
    <rect x="0" y="0" width="40" height="40" fill="gray"/>
    <path stroke-width="1" stroke="yellow" fill="black" d="M13.21,10.54H17.5a.56.56,0,0,0,0-1.11H13.21a.56.56,0,0,0,0,1.11Z"/>
    <path stroke-width="1" stroke="yellow" fill="black" d="M13.21,7.73h1.43a.56.56,0,1,0,0-1.11H13.21a.56.56,0,0,0,0,1.11Z"/>
    <path stroke-width="1" stroke="yellow" fill="black" d="M14.64,15.14H13.21a.56.56,0,0,0,0,1.11h1.43a.56.56,0,1,0,0-1.11Z"/>
    <path stroke-width="1" stroke="yellow" fill="black" d="M19.79,12.29H13.21a.55.55,0,0,0-.55.55.55.55,0,0,0,.55.55h6.58a.55.55,0,0,0,.55-.55A.55.55,0,0,0,19.79,12.29Z"/>
    <rect stroke-width="1" stroke="yellow" fill="black" x="9.5" y="4" width="1" height="16"/>
    <rect stroke="yellow" stroke-width="1" x="0" y="0" width="40" height="40"/>
  </svg>`;

  constructor(private store: Store) {
    super();

    this.svg(this.svgDefinition);

    // Set interactive properties
    this.visible = false;
    this.eventMode = 'dynamic';
    this.cursor = 'pointer';
    this.interactive = true;
    this.zIndex = 1000;

    this.on('pointerdown', this.onClick.bind(this));
  }

  /**
   * Handles click events on the selection menu
   */
  private onClick(event: FederatedPointerEvent): void {
    event.stopPropagation();

    this.store.dispatch(mapActions.openEntitySelectionDialog());
  }

  // TODO: Add methods to add and remove the selection menu to entity or shape
}
