<h2 mat-dialog-title>Symbol Set List</h2>
<mat-dialog-content>
  <mat-form-field appearance="outline" class="symbol-dropdown">
    <mat-label>Search and Select Symbol</mat-label>
    <input
      type="text"
      matInput
      [formControl]="symbolControl"
      [matAutocomplete]="symbolAutocomplete"
      placeholder="Type to search symbols..."
    />
    <mat-autocomplete
      #symbolAutocomplete="matAutocomplete"
      [displayWith]="displaySymbol"
      (optionSelected)="onSymbolSelected($event.option.value)"
      class="symbol-autocomplete"
    >
      @for (symbol of filteredOptions | async; track symbol.code) {
        <mat-option [value]="symbol" class="symbol-option">
          <div class="symbol-option-content">
            <div class="symbol-preview">
              <sf-symbology [milCode]="symbol.code" [size]="24"> </sf-symbology>
            </div>
            <div class="symbol-info">
              <span class="symbol-description">{{ symbol.description }}</span>
              <span class="symbol-code">{{ symbol.code }}</span>
            </div>
          </div>
        </mat-option>
      }
    </mat-autocomplete>
  </mat-form-field>

  @if (selectedSymbol()) {
    <div class="selected-symbol-preview">
      <h3>Selected Symbol:</h3>
      <div class="selected-content">
        <sf-symbology [milCode]="selectedSymbol()!.code" [size]="48">
        </sf-symbology>
        <div class="selected-details">
          <p>
            <strong>Description:</strong> {{ selectedSymbol()!.description }}
          </p>
          <p><strong>Code:</strong> {{ selectedSymbol()!.code }}</p>
          <p><strong>Dimension:</strong> {{ selectedSymbol()!.dimension }}</p>
          <p>
            <strong>Function ID:</strong> {{ selectedSymbol()!.functionId }}
          </p>
        </div>
      </div>
    </div>
  }
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button
    mat-raised-button
    color="primary"
    (click)="selectSymbol()"
    [disabled]="!selectedSymbol()"
  >
    Select
  </button>
  <button mat-button (click)="cancel()">Cancel</button>
</mat-dialog-actions>
