import { ActionCreator, createAction } from '@ngrx/store';

export type SSE = 'EventSource' | 'WebSocket';

export abstract class SseService<T, S extends SSE> {
  protected abstract url: string;
  protected abstract sse: S;
  protected abstract get actions(): {
    open: ActionCreator<`[${S}/${string}] Open`>
  } & S extends 'WebSocket' ? {
    message: ActionCreator<`[${S}/${string}] Message`>
  } : {};

  // store = inject(Store);
  // actions$ = inject(Actions);
  // effectSources = inject(EffectSources);

  constructor() {
    // this.effectSources.addEffects(this);
  }

  protected createSSEAction<A extends string>(type: A): ActionCreator<`[${S}/${string}] ${A}`> {
    return createAction(`[${this.sse}/${this.constructor.name}}] ${type}`);
  }
}

export abstract class WebSocketSSE<T> extends SseService<T, 'WebSocket'> {
  sse: 'WebSocket';
  protected override get actions() {
    return {
      open: this.createSSEAction('Open'),
      message: this.createSSEAction('Message')
    }
  }
}

export abstract class EventSourceSSE<T> extends SseService<T, 'EventSource'> {
  sse: 'EventSource';
  protected override get actions() {
    return {
      open: this.createSSEAction('Open')
    }
  }
}

export class Test extends EventSourceSSE<string> {
  url: 'go/here';
}


// class BaseClass {
//   getClassName(): string {
//     return this.constructor.name;
//   }
// }
//
// export class ChildClass extends BaseClass {}
//
// const child = new ChildClass();
// console.log(child.getClassName()); // Output: "ChildClass"


// export abstract class SseService<T, S extends SSE> {
//   protected abstract get actions(): {
//     open: ActionCreator;
//   };
//
//   constructor() {
//     this.actions = {
//       open: createAction(`[${this.getActionPrefix()}/Test] Open`)
//     };
//   }
//
//   private getActionPrefix(): string {
//     switch (this.constructor) {
//       case WebSocketService:
//         return 'WebSocket';
//       case EventSourceService:
//         return 'EventSource';
//       default:
//         throw new Error('Invalid service type');
//     }
//   }
// }


  // protected abstract get actions(): {
  //   open: ActionCreator;
  // } & (S extends 'WebSocket' ? { message: ActionCreator } : {});
// }

// export class Test extends SseService<any, 'EventSource'> {
  // protected get actions() {
  //   return {
  //     open: createAction(`[WebSocket/Test}] Open`)
  //   };
  // }
// }
