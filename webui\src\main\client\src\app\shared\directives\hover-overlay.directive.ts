import { Directive, ElementRef, HostListener, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';

@Directive({
  selector: '[vcciHoverOverlay]',
  standalone: true
})
export class HoverOverlayDirective<C> {

  @Input('vcciHoverOverlay') templateRef: TemplateRef<C>;
  @Input() context: C;

  @HostListener('mouseenter') onMouseEnter() {
    this.openOverlay();
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.closeOverlay();
  }

  private _overlayRef: OverlayRef;

  constructor(private _overlay: Overlay, private _er: ElementRef, private _vc: ViewContainerRef) {
  }

  private openOverlay() {
    const positionStrategy = this._overlay.position()
      .flexibleConnectedTo(this._er)
      .withPositions([
        {
          originX: 'center',
          originY: 'bottom',
          overlayX: 'center',
          overlayY: 'top',
        },
      ]);

    this._overlayRef = this._overlay.create({ positionStrategy });
    const portal = new TemplatePortal(this.templateRef, this._vc, this.context);
    this._overlayRef.attach(portal);
  }

  private closeOverlay() {
    if (this._overlayRef) {
      this._overlayRef.detach();
      this._overlayRef.dispose();
    }
  }

}
