<div class="panel-title"><b>GROUPS</b></div>
<!-- TODO: Try and make this one loop but keep the overflow on the button group -->
<div class="group-panel">
  <div class="prefix-column">
    <!--suppress JSUnusedLocalSymbols -->
    <div class="prefix-item" *ngFor="let filter of filters; let i=index" style="text-align: end">
      <div style="margin-top: 14px">{{i === 0 ? '' : 'OR'}}</div>
    </div>
  </div>
  <div class="prefix-column filter-column">
    <div class="filter-row" *ngFor="let filter of filters; let i=index">
      <ng-container *ngIf="i === 0; else nthGroup">
        <button
          class="filter-button"
          mat-raised-button
          [color]="filter[info.name] | filterHasValue"
          *ngFor="let info of filterInfos"
          (click)="selectFilter({key: i, info: info.name})">
          {{info.name}}
        </button>
      </ng-container>
      <ng-template #nthGroup>
        <button
          class="filter-button"
          mat-raised-button
          [color]="filter[info.name] | filterHasValue"
          *ngFor="let info of allowedMultipleGroups"
          (click)="selectFilter({key: i, info: info.name})">
          {{info.name}}
        </button>
      </ng-template>
    </div>
  </div>
  <div class="prefix-column">
    <!--suppress JSUnusedLocalSymbols -->
    <ng-container *ngFor="let filter of filters; let i=index">
      <div class="prefix-item">
        <button mat-icon-button *ngIf="i !== 0; else emptySpace" (click)="removeGroup(i)">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </ng-container>
  </div>
</div>

<div class="action-row">
  <button mat-button (click)="resetAll()">RESET ALL</button>
  <button mat-button (click)="addGroup()" [disabled]="allowedMultipleGroups.length === 0">ADD GROUP</button>
</div>

<ng-template #emptySpace>
  <div style="width: 50px"></div>
</ng-template>
