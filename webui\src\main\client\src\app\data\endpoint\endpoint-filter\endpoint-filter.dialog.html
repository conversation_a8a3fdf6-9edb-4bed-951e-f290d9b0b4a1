<div mat-dialog-title class="title" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
  <h2>ENDPOINT FILTERS</h2>
  <mat-form-field class="source-select">
    <mat-select
      [disabled]="true"
      [value]="endpoint$ | async"
      [compareWith]="compareEndpoint"
      (selectionChange)="setEndpoint($event.value)">
      <mat-option *ngFor="let endpoint of (data.nonExchangeEndpoints$ | async)" [value]="endpoint">
        {{endpoint.name}}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <button mat-icon-button mat-dialog-close class="title-close">
    <mat-icon>close</mat-icon>
  </button>
</div>
<mat-dialog-content>
  <vcci-filter-group [filterInfos]="filterInfos$ | async" [filters]="filters$ | async" />
  <vcci-filter-editor [info]="selectedInfo$ | async" [filter]="selectedFilter$ | async" />
</mat-dialog-content>
<mat-progress-bar mode="indeterminate" *ngIf="busy$ | async" />
<mat-dialog-actions align="end">
  <button mat-button (click)="submit()">APPLY</button>
  <button mat-button mat-dialog-close>CLOSE</button>
</mat-dialog-actions>
