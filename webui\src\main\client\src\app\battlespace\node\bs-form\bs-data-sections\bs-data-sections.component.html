@if (dataSections.length === 1) {
  <vcci-bs-data-section [dataSection]="dataSections[0]"/>
} @else if (dataSections.length > 1) {
  <mat-stepper>
    @for (dataSection of dataSections; track dataSection.sectionName; let first = $first; let last = $last) {
      @let sectionName = dataSection.sectionName;
      <mat-step>
        <ng-template matStepLabel>{{ sectionName }}</ng-template>
        <vcci-bs-data-section
          [dataSection]="dataSection"
          [usingDefault]="usingDefault"
          (networkInterfaceChanges)="onNetworkInterfaceChange($event)"
          (getNext)="onNext($event)"/>
        <div>
          @if (!first) {
            <button mat-button matStepperPrevious>Back</button>
          }
          @if (!last) {
            <button mat-button matStepperNext>Next</button>
          }
        </div>
      </mat-step>
    }
  </mat-stepper>
}
