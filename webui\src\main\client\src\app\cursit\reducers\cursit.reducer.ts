import { createFeature, createReducer, on } from '@ngrx/store';

import { cursitActions } from '../actions/cursit.actions';
import { CursitController, CursitControllerType } from '../models/cursit.model';
import { CursitFilter, INITIAL_FILTER } from '../models/cursit-filter.model';
import { LatLng } from '../../data/entity/entity.model';

export interface GeoFilterPoints {
  p1: LatLng;
  p2: LatLng;
}

export interface State {
  suspendMapRefresh: boolean;
  // filters: Filter[];
  cursitController: CursitController;
  isFilterBusy: boolean;
  searchQuery: string;
  isGeoDraw: boolean;
  activeGeoArea: GeoFilterPoints | undefined; // this is the current geo area displayed on the map
  newGeoAreaDrawn: GeoFilterPoints | undefined; // used from leaflet map when new area is drawn
  filters: CursitFilter;
  layerManagerVisibility: boolean;
}

export const initialState: State = {
  suspendMapRefresh: false,
  // filters: [],
  cursitController: { controllerType: CursitControllerType.Pan },
  isFilterBusy: true,
  isGeoDraw: false,
  activeGeoArea: {
    p1: {
      latitude: 0,
      longitude: 0
    },
    p2: {
      latitude: 0,
      longitude: 0
    }
  },
  newGeoAreaDrawn: undefined,
  searchQuery: '',

  filters: INITIAL_FILTER,
  layerManagerVisibility: false
};

const cursitFeature = createFeature<'cursit', State>({
  name: 'cursit',
  reducer: createReducer(
    initialState,
    on(cursitActions.updateCursitController, (state, action) => ({
      ...state,
      cursitController: action.payload
    })),
    on(cursitActions.suspendMapRefresh, (state, action) => ({
      ...state,
      suspendMapRefresh: action.payload
    })),
    on(cursitActions.dismissDialog, (state, action) => ({
      ...state,
      dismissDialog: action.payload
    })),
    on(cursitActions.searchQuery, (state, action) => ({
      ...state,
      searchQuery: action.payload
    })),
    on(cursitActions.updateCursitFilters, (state, action) => ({
      ...state,
      filters: action.payload
    })),
    on(cursitActions.toggleGeoDraw, (state, action) => ({
      ...state,
      isGeoDraw: !state.isGeoDraw
    })),
    on(cursitActions.geoFilterDrawn, (state, action) => ({
      ...state,
      newGeoAreaDrawn: action.payload
    })),
    on(cursitActions.activeGeoFilterChanged, (state, action) => ({
      ...state,
      activeGeoArea: action.payload
    })),
    on(cursitActions.removeGeoFilters, (state, action) => ({
      ...state,
      activeGeoArea: null,
      newGeoAreaDrawn: null
    })),
    on(cursitActions.showHideLayerManager, (state, action) => {
      const newBool = action.payload ?? !state.layerManagerVisibility;
      return {...state, layerManagerVisibility: newBool};
    }),

    // on(cursitActions.initializeMap, (state, action) => (
    //   { ...state, initializeMap: action.payload }
    // ))
  )
});

export const {
  name,
  reducer,
  selectCursitState,
  selectIsFilterBusy,
  selectCursitController,
  selectSuspendMapRefresh,
  selectSearchQuery,
  selectFilters,
  selectIsGeoDraw,
  selectActiveGeoArea,
  selectNewGeoAreaDrawn,
  selectLayerManagerVisibility
} = cursitFeature;
