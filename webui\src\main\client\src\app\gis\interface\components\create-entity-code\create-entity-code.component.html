<div class="tab-content" [formGroup]="parentForm">
  <div class="flex-container">
    <div class="input-section">
      @for (field of milsymCodeFormFields; track field.label) {
        <mat-form-field>
          <mat-label>{{ field.label }}</mat-label>
          <input
            matInput
            [formControlName]="field.name"
            type="text"
            placeholder="Enter 15-character code"
          />
          @if (
            parentForm.get(field.name)?.invalid &&
            (parentForm.get(field.name)?.dirty ||
              parentForm.get(field.name)?.touched)
          ) {
            <mat-error>
              @if (parentForm.get(field.name)?.errors?.['required']) {
                {{ field.label }} is required
              }
              @if (parentForm.get(field.name)?.errors?.['minlength']) {
                {{ field.label }} must be at least 15 characters long
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>
  </div>
</div>
