import {
  AfterViewInit,
  Component,
  DestroyRef,
  EventEmitter,
  inject,
  Input,
  Output,
  signal,
  ViewChild
} from '@angular/core';

import { Crud } from '@simfront/common-libs';

import {
  DEFAULT_NETWORK_INTERFACE,
  GetNext,
  Message
} from '../../data/bs-params/bs-params.model';
import { BSParamsStore } from '../../data/bs-params/bs-params.store';
import { Node } from '../../data/node/node.model';
import { Settings } from '../../data/settings/settings.model';

import { Direction } from '../../data/connection/connection.model';
import {
  combineLatest,
  combineLatestWith,
  debounceTime,
  map,
  Observable,
  startWith
} from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DataParamFormComponent } from './bs-form/data-param-form.component';

type Model = 'Connection' | 'Endpoint';

@Component({
  selector: 'vcci-bs-form',
  styleUrls: ['bs-form.component.css'],
  templateUrl: 'bs-form.component.html',
  standalone: false,
  providers: [
    {
      provide: DEFAULT_NETWORK_INTERFACE,
      useValue: signal(undefined)
    }
  ]
})
export class BSFormComponent implements AfterViewInit {
  private _destroyRef: DestroyRef = inject(DestroyRef);
  private _defaultNetworkInterface = inject(DEFAULT_NETWORK_INTERFACE);

  @Input({ required: true }) nodes: Node[];
  @Input() crud: Crud = 'Create';
  private _settings: Settings;
  @Input() set settings(settings: Settings) {
    this._settings = settings;
    this._defaultNetworkInterface.set(
      this.settings.configDialog.rememberNetworkInterface
        ? this.settings.configDialog.networkInterface
        : undefined
    );
  }
  get settings(): Settings {
    return this._settings;
  }
  @Input() model: Model;

  @Output() networkInterfaceChanges = new EventEmitter<string>();

  @ViewChild('conForm') conForm: DataParamFormComponent;
  @ViewChild('endForm') endForm: DataParamFormComponent;

  selectedNode$ = this.store.selectedNode$;

  direction$ = this.store.direction$;
  showDirections$ = this.store.showDirections$;
  availableDirections$ = this.store.availableDirection$;

  showConnections$ = this.store.showConnections$;
  selectedConnection$ = this.store.selectedConnection$;
  availableConnections$ = this.store.availableConnections$;

  connParams$ = this.store.connParamsWithValue$;
  connPages$ = this.store.connPagesWithValue$;
  connSections$ = this.store.connSectionsWithValue$;

  endParams$ = this.store.endParamsWithValue$;
  endPages$ = this.store.endPagesWithValue$;
  endSections$ = this.store.endSectionsWithValue$;

  constructor(private readonly store: BSParamsStore) {}

  ngAfterViewInit(): void {
    this.store.setBSFormComponent(this);
  }

  onNodeChange(node: string): void {
    this.store.updateSelection({
      selectedNode: node,
      endParams: [],
      connParams: []
    });
  }

  directionSelectionChange(direction: Direction | undefined): void {
    this.store.updateSelection({ direction, endParams: [], connParams: [] });
  }

  connectionSelectionChange(selectedConnection: string | true): void {
    this.store.updateSelection({
      selectedConnection,
      endParams: [],
      connParams: []
    });
  }

  onGetNext(getNext: GetNext, m: Message): void {
    this.store.getNext(getNext, m);
  }

  onCreateConnection(): void {
    this.store.createConnection(this.conForm.getParameterValues());
  }

  isFormDirty$(): Observable<boolean> {
    return combineLatest([
      this.isConnectionFormDirty$(),
      this.isEndpointFormDirty$()
    ]).pipe(map(([conn, end]) => conn || end));
  }

  isFormInvalid$(): Observable<boolean> {
    return this.isConnectionFormInvalid$().pipe(
      combineLatestWith(this.isEndpointFormInvalid$()),
      map(([isConnInvalid, isEndInvalid]) => isConnInvalid || isEndInvalid)
    );
  }

  isConnectionFormInvalid$(): Observable<boolean> {
    return this.conForm.form.statusChanges.pipe(
      takeUntilDestroyed(this._destroyRef),
      startWith(false),
      map(() => this.conForm.form.invalid)
    );
  }

  isEndpointFormInvalid$(): Observable<boolean> {
    return this.endForm.form.statusChanges.pipe(
      takeUntilDestroyed(this._destroyRef),
      startWith(false),
      map(() => this.endForm.form.invalid)
    );
  }

  isConnectionFormDirty$(): Observable<boolean> {
    return this.conForm.form.statusChanges.pipe(
      takeUntilDestroyed(this._destroyRef),
      debounceTime(250),
      startWith(false),
      map(() => this.conForm.form.dirty)
    );
  }

  isEndpointFormDirty$(): Observable<boolean> {
    return this.endForm.form.statusChanges.pipe(
      takeUntilDestroyed(this._destroyRef),
      debounceTime(250),
      startWith(false),
      map(() => this.endForm.form.dirty)
    );
  }

  onUpdateConnection(): void {
    this.store.updateConnection(this.conForm.getParameterValues());
  }

  onUpdateEndpoint(): void {
    this.store.updateEndpoint(this.endForm.getParameterValues());
  }

  onNetworkInterfaceChanges(value: string): void {
    this.networkInterfaceChanges.emit(value);
  }

  onSubmit(): void {
    const conParams = this.conForm.getParameterValues();
    const endParams = this.endForm.getParameterValues();
    this.store.submit(conParams, endParams, this.crud, this.model);
  }

  onIdentifierChanged({
    type,
    value
  }: {
    type: 'Connection' | 'Endpoint';
    value: string;
  }) {
    if (type === 'Connection') {
      this.store.changeConnectionName(value);
    } else {
      this.store.changeEndpointName(value);
    }
  }
}
