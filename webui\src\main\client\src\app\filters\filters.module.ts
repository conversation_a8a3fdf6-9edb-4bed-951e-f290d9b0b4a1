import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CommonModule,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON><PERSON><PERSON>,
  NgS<PERSON>Default
} from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MaterialModule } from '../material/material.module';
import { EntityFilterComponent } from './components/filter-types/entity-filter.component';
import { FollowEntityFilterComponent } from './components/filter-types/follow-entity-filter.component';
import { GeoFilterComponent } from './components/filter-types/geo-filter.component';
import {
  CdkFixedSizeVirtualScroll,
  CdkVirtualForOf,
  CdkVirtualScrollViewport
} from '@angular/cdk/scrolling';
import {
  CoreModule,
  SFTableTreeCellDirective,
  SFTableTreeDef,
  SFTreeNodeDef,
  SFTreeNodeDirective,
  StickyDirective,
  VirtualTableTreeComponent
} from '@simfront/common-libs';
import { MatTableModule } from '@angular/material/table';
import {
  MatMenu,
  MatMenuContent,
  MatMenuItem,
  MatMenuTrigger
} from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';

@NgModule({
  imports: [
    CommonModule,
    MaterialModule,
    CoreModule,
    AsyncPipe,
    NgForOf,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    MatButtonModule,
    MatDialogModule,
    MatDividerModule,
    MatFormFieldModule,
    MatIconModule,
    MatOptionModule,
    MatSelectModule,
    NgForOf,
    MaterialModule,
    ReactiveFormsModule,
    FormsModule,
    CdkVirtualScrollViewport,
    CdkFixedSizeVirtualScroll,
    CdkVirtualForOf,
    SFTreeNodeDirective,
    SFTreeNodeDef,
    VirtualTableTreeComponent,
    SFTableTreeCellDirective,
    SFTableTreeDef,
    MatTableModule,
    StickyDirective,
    MatMenu,
    MatMenuTrigger,
    MatMenuItem,
    MatMenuContent,
    MatFormFieldModule,
    MatInputModule
  ],
  exports: [
    EntityFilterComponent,
    FollowEntityFilterComponent,
    GeoFilterComponent
  ],
  declarations: [
    EntityFilterComponent,
    FollowEntityFilterComponent,
    GeoFilterComponent
  ]
})
export class FiltersModule {}
