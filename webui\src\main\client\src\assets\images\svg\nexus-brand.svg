<svg xmlns="http://www.w3.org/2000/svg" viewBox="18.842 1.158 54.549 55.83" width="200px" height="200px" xmlns:bx="https://boxy-svg.com" preserveAspectRatio="none">
  <defs>
    <linearGradient id="Gradient" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -25.802112, 73.301141)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-2" x1="41.99" y1="44.42" x2="85.92" y2="0.49" href="#Gradient" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -11.27658, 77.926557)"></linearGradient>
    <linearGradient id="Gradient-3" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#Gradient" gradientTransform="matrix(1, 0, 0, -1, -52.686444, 15.542414)"></linearGradient>
    <linearGradient id="Gradient-4" x1="32.81" y1="35.23" x2="76.74" y2="-8.7" href="#Gradient" gradientTransform="matrix(1, 0, 0, -1, -43.494416, 35.103535)"></linearGradient>
    <linearGradient id="Gradient-5" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#Gradient" gradientTransform="matrix(1, 0, 0, -1, -3.315534, 41.70135)"></linearGradient>
    <bx:export>
      <bx:file format="svg"></bx:file>
    </bx:export>
    <linearGradient id="Gradient-1" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#gradient-1" gradientTransform="matrix(1, 0, 0, -1, 41.473568, 36.834431)"></linearGradient>
    <linearGradient id="gradient-1" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-6" x1="41.99" y1="44.42" x2="85.92" y2="0.49" href="#gradient-2" gradientTransform="matrix(0.999999, 0, 0, -0.999999, 39.897229, 39.281343)"></linearGradient>
    <linearGradient id="gradient-2" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-3" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -3.786105, 62.767331)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-4" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, 42.98086, 37.497405)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-7" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#gradient-5" gradientTransform="matrix(1, 0, 0, -1, 38.339499, 28.119017)"></linearGradient>
    <linearGradient id="gradient-5" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-8" x1="29.24" y1="31.66" x2="73.17" y2="-12.27" href="#gradient-6" gradientTransform="matrix(1, 0, 0, -1, 0.224132, 44.118649)"></linearGradient>
    <linearGradient id="gradient-6" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-9" x1="32.81" y1="35.23" x2="76.74" y2="-8.7" href="#gradient-7" gradientTransform="matrix(1, 0, 0, -1, -4.024496, 30.589434)"></linearGradient>
    <linearGradient id="gradient-7" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-10" x1="32.81" y1="35.23" x2="76.74" y2="-8.7" href="#gradient-8" gradientTransform="matrix(1, 0, 0, -1, 29.699694, 20.316634)"></linearGradient>
    <linearGradient id="gradient-8" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="Gradient-11" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#gradient-9" gradientTransform="matrix(1, 0, 0, -1, -7.995584, 19.149491)"></linearGradient>
    <linearGradient id="gradient-9" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-9-1" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#gradient-10" gradientTransform="matrix(1, 0, 0, -1, -1.708419, 15.235164)"></linearGradient>
    <linearGradient id="gradient-10" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
    <linearGradient id="gradient-9-2" x1="36.09" y1="38.51" x2="80.02" y2="-5.42" href="#gradient-11" gradientTransform="matrix(1, 0, 0, -1, -0.015558, 17.978205)"></linearGradient>
    <linearGradient id="gradient-11" x1="35.35" y1="37.77" x2="79.28" y2="-6.16" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0.999999, 0, 0, -0.999999, -8.83099, 64.402856)">
      <stop offset="0" stop-color="#cccccc"></stop>
      <stop offset="1" stop-color="#ffffff"></stop>
    </linearGradient>
  </defs>
  <g transform="matrix(-1.03185, 0, 0, 1.03185, 87.343, -0.631567)" id="object-0" style="">
    <path d="M 26.941 42.405 C 26.941 42.405 25.418 39.191 24.726 39.446 C 24.726 39.446 24.007 39.805 23.508 40.044 L 22.371 36.928 C 26.371 34.498 23.809 35.281 28.469 33.101 L 34.005 40.754 C 32.35 40.619 30.285 40.853 26.941 42.405 Z" style="fill: url(#Gradient); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.938332, -0.345736, 0.345736, -0.938332, 0.000001, 0)"></path>
    <path d="M 27.216 14.945 C 27.216 14.945 26.51 12.278 25.948 12.383 C 25.948 12.383 20.964 12.235 16.205 12.552 L 15.293 7.656 C 17.391 7.374 19.914 7.054 21.774 6.853 C 27.014 6.283 28.518 6.54 31.636 7.535 L 35.591 16.619 C 35.591 16.619 31.72 14.125 27.216 14.945 Z" style="fill: url(#Gradient-3);" stroke="black" stroke-width="0.2" transform="matrix(-0.983083, -0.183162, 0.183162, -0.983083, 48.178831339119, 25.986510289072)"></path>
    <path d="M 26.875 23.443 C 26.875 23.443 25.908 20.102 25.168 20.29 C 25.168 20.29 23.135 20.628 18.368 21.671 L 17.538 17.863 C 19.488 17.351 18.462 17.609 19.13 17.447 C 24.613 16.114 24.907 16.134 30.535 15.715 L 34.677 23.476 C 29.464 22.307 26.875 23.443 26.875 23.443 Z" style="fill: url(#Gradient-4); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.975232, -0.221184, 0.221184, -0.975232, -0.000008, -0.000004)"></path>
    <path d="M 58.395 20.605 C 58.395 20.605 57.529 18.195 56.993 18.35 C 56.993 18.35 54.504 19.734 52.904 20.205 L 51.645 15.946 C 53.211 15.315 54.191 14.274 55.784 13.56 C 57.295 12.883 58.648 12.36 61.103 11.806 L 66.083 19.833 C 63.402 19.24 61.193 19.699 58.395 20.605 Z" style="fill: url(#Gradient-5);" stroke="black" stroke-width="0.2" transform="matrix(-0.960479, -0.278351, 0.278351, -0.960479, 78.97568593435, 60.586503518302)"></path>
    <path d="M 34.915 37.29 L 35.197 38.31 L 32.386 36.291 C 32.386 36.291 36.408 44.486 31.291 53.002 L 28.629 47.211 C 32.125 42.794 26.929 37.498 30.186 33.944 C 30.186 33.944 35.566 29.556 36.113 29.499 L 41.933 34.379 C 41.933 34.379 41.393 33.423 36.678 38.067 C 36.53 38.212 35.051 37.147 34.915 37.29 Z" style="fill: url(#Gradient-2); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.826347, -0.563162, 0.563162, -0.826347, 0.681368, -0.261844)"></path>
    <path d="M 64.617 28.351 L 64.958 29.066 C 57.884 33.298 55.525 37.706 55.525 37.706 C 55.525 37.706 57.458 33.417 64.617 28.351 Z" style="fill: url(#Gradient-1); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.966962, -0.254919, 0.254919, -0.966962, -0.477151, 0.062192)"></path>
    <path d="M 61.361 23.539 L 62.148 24.35 C 59.591 25.969 56.401 34.569 56.401 34.569 C 56.401 34.569 58.456 25.841 61.361 23.539 Z" style="fill: url(#Gradient-6); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.826347, -0.563162, 0.563162, -0.826347, -0.770894, 0.236268)"></path>
    <path d="M 44.955 26.818 L 44.729 30.504 C 39.881 32.977 43.373 37.47 37.514 41.102 L 37.262 38.5 C 43.205 34.457 40.393 29.532 44.955 26.818 Z" style="fill: url(#gradient-3);" stroke="black" stroke-width="0.2" transform="matrix(-0.965926, -0.258819, 0.258819, -0.965926, 72.440698285847, 77.289000184601)"></path>
    <path d="M 61.223 20.65 L 61.965 21.925 C 58.965 23.206 54.511 29.408 54.511 29.408 C 54.511 29.408 56.863 24.125 61.223 20.65 Z" style="fill: url(#gradient-4); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.938332, -0.345736, 0.345736, -0.938332, -0.991152, 0.177115)"></path>
    <path d="M 50.091 20.134 L 49.917 24.215 C 45.929 25.531 48.849 27.563 44.037 30.128 L 43.813 27.994 C 47.554 25.705 45.043 23.043 50.091 20.134 Z" style="fill: url(#Gradient-8);" stroke="black" stroke-width="0.2" transform="matrix(-0.960479, -0.278351, 0.278351, -0.960479, 81.763899875668, 63.198898810368)"></path>
    <path d="M 62.798 18.161 L 63.379 19.386 C 60.463 20.286 55.255 24.303 55.255 24.303 C 55.255 24.303 59.498 19.798 62.798 18.161 Z" style="fill: url(#Gradient-7); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.960479, -0.278351, 0.278351, -0.960479, -2.986787, 0.361965)"></path>
    <path d="M 48.71 16.933 L 48.4 19.612 C 46.08 20.111 45.701 21.267 43.218 22.213 L 43.161 21.058 C 45.879 19.871 46.463 18.065 48.71 16.933 Z" style="fill: url(#Gradient-9); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.975232, -0.221184, 0.221184, -0.975232, -0.586235, 0.064387)"></path>
    <path d="M 60.017 14.94 L 60.425 15.935 C 58.184 16.396 52.609 19.149 52.609 19.149 C 52.609 19.149 56.587 16.361 60.017 14.94 Z" style="fill: url(#Gradient-10);" stroke="black" stroke-width="0.2" transform="matrix(-0.975232, -0.221184, 0.221184, -0.975232, 106.521898022949, 46.322188386108)"></path>
    <path d="M 50.097 13.686 L 49.676 15.452 C 47.917 15.38 47.056 15.416 45.18 15.687 L 45.245 14.687 C 46.854 14.309 47.795 14.053 50.097 13.686 Z" style="fill: url(#Gradient-11); transform-box: fill-box; transform-origin: 50% 50%;" stroke="black" stroke-width="0.2" transform="matrix(-0.995616, -0.093531, 0.093531, -0.995616, -0.98221, 0.047008)"></path>
    <path d="M 56.674 9.655 L 56.374 10.844 C 54.007 11.547 45.439 11.828 45.439 11.828 L 52.192 10.525 L 52.423 11.035 L 52.56 10.478 C 52.56 10.478 55.859 9.817 56.674 9.655 Z" style="fill: url(#gradient-9-1);" stroke="black" stroke-width="0.2" transform="matrix(-0.995616, -0.093531, 0.093531, -0.995616, 101.032000041453, 26.294199519047)"></path>
    <path d="M 52.142 13.779 L 52.368 14.675 C 48.925 15.226 44.804 16.142 44.804 16.142 C 44.804 16.142 47.328 14.983 52.142 13.779 Z" style="fill: url(#gradient-9-2);" stroke="black" stroke-width="0.2" transform="matrix(-0.995616, -0.093531, 0.093531, -0.995616, 101.260121543514, 33.409877205059)"></path>
  </g>
</svg>