
/**
 * Used to display map scale
 */
export interface ScaleIndicator {
  text:  string;
  style:  ScaleIndicatorBackgroundStyle;
}

/**
 * Used for map scale bar CSS
 */
export interface ScaleIndicatorBackgroundStyle {
  width:  string;
  left:   string;
}

/**
 * Default max width of the scale indicator on the map
 */
export const ScaleIndicatorDefaultMaxWidth = 150;

/**
 * Represents a unit of measurement
 * Modification of LuciadRIA_2020.0.03\samples\common\DistanceUnit.js
 */
export interface Unit {
  uomName:        string;
  uomSymbol:      string;
  toMetreFactor:  number;
}

export function convertToStandard(aValue: number, toMetreFactor: number): number {
  return aValue * toMetreFactor;
}

export function convertFromStandard(aValue: number, toMetreFactor: number): number {
  return aValue / toMetreFactor;
}

/**
 * List of supported units of measurement
 * Modification of LuciadRIA_2020.0.03\samples\common\DistanceUnit.js
 */
export class DistanceUnits {
  METRE: Unit = { uomName: 'Metre', uomSymbol: 'm', toMetreFactor: 1 };
  KM: Unit = { uomName: 'Kilometre', uomSymbol: 'km', toMetreFactor: 1000 };
  NM: Unit = { uomName: 'NauticalMile', uomSymbol: 'NM', toMetreFactor: 1852.0 };
  MILE_US: Unit = { uomName: 'MileUS', uomSymbol: 'mi', toMetreFactor: 1609.3472186944375 };
  FT: Unit = { uomName: 'Feet', uomSymbol: 'ft', toMetreFactor: 0.30480060960121924 };
}

