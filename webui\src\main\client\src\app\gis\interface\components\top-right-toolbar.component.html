<div class="top-right-toolbar">
  <div class="button-wrapper">
    <button
      (click)="filterAction()"
      class="top-right-toolbar-button"
      matTooltip="Filter"
    >
      <mat-icon class="button-icon" svgIcon="filter-tr-tb"></mat-icon>
    </button>
  </div>
  @if (!env.production) {
    <div class="button-wrapper">
      <button
        class="top-right-toolbar-button"
        [disabled]="true"
        matTooltip="Edit Map"
        (click)="editMapAction()"
      >
        <mat-icon class="button-icon" svgIcon="edit-map-tr-tb"></mat-icon>
      </button>
    </div>
    <div class="button-wrapper">
      <button
        class="top-right-toolbar-button"
        [disabled]="true"
        matTooltip="Manage Map Servers"
        (click)="mapServersAction()"
      >
        <mat-icon class="button-icon" svgIcon="map-servers-tr-tb"></mat-icon>
      </button>
    </div>
    <div class="button-wrapper">
      <button
        (click)="centerSelected()"
        class="top-right-toolbar-button"
        matTooltip="Center Selected"
      >
        <mat-icon
          class="button-icon"
          svgIcon="center-selected-tr-tb"
        ></mat-icon>
      </button>
    </div>
  }
  <div class="button-wrapper">
    <button
      (click)="showHideLayerList()"
      [ngClass]="{ expanded: (layerManagerVisibility$ | async) || false }"
      class="top-right-toolbar-button"
      matTooltip="Layer Manager"
    >
      <mat-icon class="button-icon" svgIcon="layer-list-tr-tb"></mat-icon>
    </button>
  </div>

  <div style="flex-grow: 1"></div>

  <div class="slider-wrapper">
    <div class="slider-background-wrapper" matTooltip="Set Map Opacity">
      <mat-icon
        class="slider-background-world"
        svgIcon="map-opacity-tr-tb"
      ></mat-icon>
      <mat-icon
        class="slider-background-scale"
        svgIcon="map-opacity-scale-tr-tb"
      ></mat-icon>
      <mat-slider class="map-opacity-slider" max="1" min="0" step="0.1">
        <input
          (valueChange)="setBackgroundMapOpacity($event)"
          [(ngModel)]="mapOpacitySliderValue"
          matSliderThumb
        />
      </mat-slider>
    </div>
  </div>

  <!--  <div class="button-wrapper">-->
  <!--    <button class="top-right-toolbar-button"-->
  <!--            matTooltip="Reset Map Opacity"-->
  <!--            (click)="resetMapOpacity()">-->
  <!--      <mat-icon class="button-icon" svgIcon="reset-opacity-tr-tb"></mat-icon>-->
  <!--    </button>-->
  <!--  </div>-->

  <div class="slider-wrapper">
    <div class="slider-background-wrapper" matTooltip="Resize Icons">
      <mat-icon
        class="element-resize-scale"
        svgIcon="element-resize-tr-tb"
      ></mat-icon>
      <mat-slider class="map-opacity-slider" max="100" min="0" step="10">
        <input
          (valueChange)="setElementSize($event)"
          [(ngModel)]="elementSizeValue"
          matSliderThumb
        />
      </mat-slider>
    </div>
  </div>

  <!--  <div class="button-wrapper">-->
  <!--    <button class="top-right-toolbar-button"-->
  <!--            matTooltip="Element Actions"-->
  <!--            (click)="elementActions()">-->
  <!--      <mat-icon class="button-icon" svgIcon="element-actions-tr-tb"></mat-icon>-->
  <!--    </button>-->
  <!--  </div>-->
</div>
