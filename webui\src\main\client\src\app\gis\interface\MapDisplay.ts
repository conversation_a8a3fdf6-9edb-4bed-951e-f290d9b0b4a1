import { <PERSON><PERSON><PERSON> } from '@angular/core';
import { Store } from '@ngrx/store';

import domtoimage from 'dom-to-image-more';

import { Observable, Subject } from 'rxjs';
import { cursitActions } from '../../cursit/actions/cursit.actions';
import {
  CursitController,
  CursitControllerType
} from '../../cursit/models/cursit.model';
import { selectCursitController } from '../../cursit/reducers/cursit.reducer';

import { setLayerRedrawInterval } from '../common/actions/interval.actions';

import { ElementFactory } from './ElementFactory';
import { ElementHandler } from './ElementHandler';
import { InputHandler } from './InputHandler';
import { LayerFactory } from './LayerFactory';
import { LayerHandler } from './LayerHandler';
import {
  GridType,
  LayerManagerActions
} from '../../data/layer-manager/layer-manager.model';
import {
  DistanceUnits,
  ScaleIndicator,
  ScaleIndicatorDefaultMaxWidth,
  Unit
} from './models/scale-indicator.model';
import {
  MouseCoordinateLocator,
  MouseCoordinateType
} from './MouseCoordinateLocator';
import { GISElement } from './GISElement';
import { BoundingBox } from 'src/app/data/entity/entity.model';
import { mapActions } from './actions/map.actions';
import { RouteClickData } from './service/gis.service';

export interface MapDisplay<L, G> {
  layerHandler: LayerHandler<L, G>;

  elementFactory: ElementFactory<L>;

  layerFactory: LayerFactory<L>;

  inputHandler: InputHandler;

  elementHandler: ElementHandler;

  map: unknown;

  onRouteClick?: (clickData: RouteClickData) => void;

  getReference?(): any;

  addSymbolOnMapClick(): void;
  getGISElements(): GISElement<any>[];

  store: Store;

  ngZone: NgZone;

  initializeObservableController(store: Store): void;

  getObservableController(): Observable<CursitController>;

  updateCursitController(newCursitController: CursitController): void;

  setMouseLocation(): void;

  getScaleIndicator(
    map: any,
    maxWidthPixels: number,
    distanceUnit: Unit | null
  ): ScaleIndicator | null;

  updateScaleIndicator(): void;

  redraw(): void;

  setMouseCoordinateType(mouseCoordinateType: MouseCoordinateType): void;

  gridType: GridType;

  toggleLayerManagerVisibility(visible?: boolean): void;

  zoomIn(): void;

  zoomOut(): void;

  fitBounds(bounds: BoundingBox): void;

  centerMapOnSelectedElement(): void;

  setMilitaryElementIconSize(iconSize: number): void;

  // getMapCoordinateFromMouseEvent(mouseEvent: MouseEvent): string;
  printMap(): void;
  enableRuler(): void;
  disableRuler(): void;
  get isMeasuring(): boolean;

  containerPointToLatLng(
    containerX: number,
    containerY: number
  ): { lat: number; lng: number };

  getContainerDOM(): HTMLElement;
}

export abstract class AbstractMapDisplay<L, G> implements MapDisplay<L, G> {
  protected _layerHandler: LayerHandler<L, G>;
  protected _elementFactory: ElementFactory<L>;
  protected _layerFactory: LayerFactory<L>;
  protected _inputHandler: InputHandler;
  protected _elementHandler: ElementHandler;
  protected currentController: CursitController = {
    controllerType: CursitControllerType.Pan
  };
  protected _store: Store;
  protected cursitContoller$: Observable<CursitController>;
  protected distanceUnits: DistanceUnits = new DistanceUnits();
  protected scaleIndicator: ScaleIndicator | null = null;
  scaleIndicatorMaxWidth: number = ScaleIndicatorDefaultMaxWidth;
  protected scaleIndicator$: Observable<ScaleIndicator>;
  protected mouseCoordinateLocator: MouseCoordinateLocator;
  protected _gridType: GridType = 'MGRS';
  private _ngZone: NgZone;
  public iconSize = 1;
  private panActive: HTMLElement;
  protected measuring: boolean = false;
  onRouteClick?: (clickData: RouteClickData) => void;
  // protected mapScale$: Subject<number[] | undefined> = new Subject();

  // elementHash: Map<string, GISElement> = new Map<string, GISElement>();

  protected constructor(store: Store, ngZone: NgZone) {
    this.store = store;
    this.ngZone = ngZone;
    setTimeout(() => this.store.dispatch(setLayerRedrawInterval(1000)), 1000);

    this.cursitContoller$ = this.store.select(selectCursitController);
    this.cursitContoller$.subscribe((value) => {
      this.setCurrentController(value);
    });

    const mapContainer = document.querySelector('.map');

    let isCtrlPressed = false;

    function updateCursor(): void {
      if (mapContainer) {
        mapContainer.classList.toggle('hand-pan', isCtrlPressed);
      }
    }

    document.addEventListener('keydown', (event) => {
      if (event.key === 'Control') {
        isCtrlPressed = true;
        updateCursor();
      }
    });

    document.addEventListener('keyup', (event) => {
      const panActive = document.querySelector('.pan-active');
      if (event.key === 'Control' && !panActive) {
        mapContainer.classList.remove('hand-pan');
        mapContainer.classList.remove('hand-pan-dragging');
        isCtrlPressed = false;
      }
    });

    document.addEventListener('pointerdown', () => {
      this.panActive = document.querySelector('.pan-active');
      if (isCtrlPressed || this.panActive) {
        mapContainer?.classList.add('hand-pan-dragging');
      }
    });

    document.addEventListener('pointerup', () => {
      if (isCtrlPressed || this.panActive) {
        isCtrlPressed = false;
        this.panActive = null;
        mapContainer?.classList.remove('hand-pan-dragging');
      }
    });
  }

  abstract setMilitaryElementIconSize(iconSize: number): void;

  abstract centerMapOnSelectedElement(): void;

  set ngZone(zone: NgZone) {
    this._ngZone = zone;
  }

  get ngZone(): NgZone {
    return this._ngZone;
  }

  set gridType(type: GridType) {
    this._gridType = type;
  }

  get gridType(): GridType {
    return this._gridType;
  }

  get elementFactory(): ElementFactory<L> {
    return this._elementFactory;
  }

  set elementFactory(elementFactory: ElementFactory<L>) {
    this._elementFactory = elementFactory;
  }

  get layerFactory(): LayerFactory<L> {
    return this._layerFactory;
  }

  set layerFactory(layerFactory: LayerFactory<L>) {
    this._layerFactory = layerFactory;
  }

  get layerHandler(): LayerHandler<L, G> {
    return this._layerHandler;
  }

  set layerHandler(layerHandler: LayerHandler<L, G>) {
    this._layerHandler = layerHandler;
  }

  get elementHandler(): ElementHandler {
    return this._elementHandler;
  }

  set elementHandler(elementHandler: ElementHandler) {
    this._elementHandler = elementHandler;
  }

  public abstract get map(): any;

  public abstract set map(map: any);

  abstract addSymbolOnMapClick(): void;

  abstract syncController(): void;

  abstract setMouseCoordinateType(
    mouseCoordinateType: MouseCoordinateType
  ): void;

  get isMeasuring(): boolean {
    return this.measuring;
  }

  getGISElements(): GISElement<any>[] {
    return [];
  }

  enableRuler() {
    this.measuring = true;
  }

  disableRuler() {
    this.measuring = false;
  }

  getCurrentController(): CursitController {
    return this.currentController;
  }

  private setCurrentController(controller: CursitController): void {
    this.currentController = controller;
    this.syncController();
  }

  set store(store: Store) {
    this._store = store;
  }

  get store(): Store {
    return this._store;
  }

  initializeObservableController(store: Store): void {
    this.store = store;
  }

  getObservableController(): Observable<CursitController> {
    return this.cursitContoller$;
  }

  updateCursitController(newCursitController: CursitController): void {
    this.store.dispatch(
      cursitActions.updateCursitController(newCursitController)
    );
  }

  toggleLayerManagerVisibility(visible?: boolean) {
    this.store.dispatch(cursitActions.showHideLayerManager(visible));
  }

  abstract setMouseLocation(): void;

  get inputHandler(): InputHandler {
    return this._inputHandler;
  }

  set inputHandler(inputHandler: InputHandler) {
    this._inputHandler = inputHandler;
  }

  abstract getScaleIndicator(
    map: unknown,
    maxWidthPixels: number,
    distanceUnit?: Unit
  ): ScaleIndicator | null;

  abstract updateScaleIndicator(): void;

  abstract redraw(): void;

  abstract zoomIn(): void;

  abstract zoomOut(): void;

  abstract fitBounds(bounds: BoundingBox): void;

  abstract containerPointToLatLng(
    containerX: number,
    containerY: number
  ): { lat: number; lng: number };

  abstract getContainerDOM(): HTMLElement;

  printMap(): void {
    domtoimage
      .toPng(this.getContainerDOM())
      .then((dataUrl: string) => {
        // Open a new window and display the captured screenshot for printing
        const printWindow = window.open('', '_blank');
        printWindow.document.open();
        printWindow.document.write(`
          <html lang="en">
            <head>
            <link rel="icon" type="image/x-icon" href="../../../assets/images/svg/nexus-brand.svg" />
              <title>Print CURSIT Map</title>
              <style>
               body {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 100%;
                  overflow: hidden;
                }
                img {
                  width: 100%;
                }
              </style>
            </head>
            <body>
              <img src="${dataUrl}" alt="data url" />
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      })
      .catch((error) => {
        console.error('Error capturing screenshot:', error);
      });
  }

  // abstract getMapCoordinateFromMouseEvent(mouseEvent: MouseEvent): string;
}
