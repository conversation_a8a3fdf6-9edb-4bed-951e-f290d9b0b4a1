<h2 mat-dialog-title>Create Entity</h2>

@if (settingLocation()) {
  <p mat-dialog-content class="unscrollable">Select a location on the map</p>
  <mat-dialog-content #selectedLocation>
    @if (locationFormat() === 'Lat Lon') {
      <p>Longitude: {{ locationSelected().longitude }}</p>
      <p>Latitude: {{ locationSelected().latitude }}</p>
    } @else {
      <p>MGRS: {{ locationSelected().mgrs }}</p>
    }
    <mat-dialog-actions align="end">
      <button
        mat-button
        [disabled]="!locationSelected().selected"
        (click)="selectLocation()"
      >
        Select
      </button>
      <button mat-button (click)="restoreForm()">Cancel</button>
    </mat-dialog-actions>
  </mat-dialog-content>
} @else {
  <mat-dialog-content>
    <form [formGroup]="createEntityForm">
      <!-- Top section with Symbol Preview, Name, Suffix -->
      <div class="top-section">
        <!-- Symbol Preview Section -->
        <button
          mat-button
          type="button"
          (click)="openSymbolSetDialog()"
          class="symbol-container"
          matTooltip="Choose Symbol"
        >
          <sf-symbology [milCode]="symbolCode" [size]="50"></sf-symbology>
        </button>

        <!-- Name, Suffix, and Location -->
        <div class="entity-details">
          <div class="entity-name-suffix">
            <mat-form-field class="name-field">
              <mat-label>Name</mat-label>
              <input formControlName="name" matInput type="text" />
            </mat-form-field>

            <mat-form-field class="suffix-field">
              <mat-label>Entity Suffix</mat-label>
              <input formControlName="entitySuffix" matInput type="text" />
            </mat-form-field>
          </div>

          <!-- Location section -->
          <div
            [ngClass]="
              locationFormat() === 'Lat Lon'
                ? 'location-fields'
                : 'location-fields-mgrs'
            "
          >
            @if (locationFormat() === 'Lat Lon') {
              <mat-form-field class="longitude-field">
                <mat-label>Longitude</mat-label>
                <input formControlName="longitude" matInput type="number" />
              </mat-form-field>

              <mat-form-field class="latitude-field">
                <mat-label>Latitude</mat-label>
                <input formControlName="latitude" matInput type="number" />
              </mat-form-field>
            } @else {
              <mat-form-field class="mgrs-field">
                <mat-label>MGRS</mat-label>
                <input formControlName="mgrs" matInput type="text" />
              </mat-form-field>
            }
            <button mat-button type="button" (click)="openSelectLocation()">
              Select Location
            </button>
          </div>
        </div>
      </div>

      <nexus-create-entity-code
        [parentForm]="createEntityForm"
        [entityType]="entityType()"
        [milsymCodeFormFields]="milsymCodeFormFields"
      >
      </nexus-create-entity-code>

      <!-- Tabs section -->
      <div class="tabs-section">
        <nexus-create-entity-tab
          [entityType]="entityType()"
          [parentForm]="createEntityForm"
          [milsymFormFields]="milsymFormFields"
          [milsymCodeFormFields]="milsymCodeFormFields"
        ></nexus-create-entity-tab>
      </div>
    </form>
  </mat-dialog-content>
  <mat-progress-bar />
  <mat-dialog-actions align="end">
    <button
      mat-button
      type="submit"
      [disabled]="createEntityForm.invalid"
      (click)="submitCreateEntityForm()"
    >
      Create
    </button>
    <button mat-button mat-dialog-close>Cancel</button>
  </mat-dialog-actions>
}
