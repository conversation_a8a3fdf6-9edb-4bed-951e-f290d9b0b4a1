import {
  animate,
  state,
  style,
  transition,
  trigger
} from '@angular/animations';
import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import {
  getConnectionHost,
  getConnectionPort
} from '../../../data/connection/connection.model';
import { DataPath } from '../../../data/data-path/data-path.model';
import { DataService, EndpointWithConn } from '../../../data/data.service';
import { Endpoint, getEndpointId } from '../../../data/endpoint/endpoint.model';

@Component({
  selector: 'vcci-proto-router-endpoint',
  templateUrl: 'proto-router-endpoint.component.html',
  styleUrls: ['proto-router-endpoint.component.scss'],
  animations: [
    trigger('visible', [
      state('true', style({ opacity: 1, visibility: 'visible' })),
      state('false', style({ opacity: 0, visibility: 'hidden' })),
      transition('0 <=> 1', [
        style({ visibility: 'visible' }),
        animate('200ms ease')
      ])
    ])
  ],
  standalone: false
})
export class ProtoRouterEndpointComponent implements OnChanges {
  @ViewChild('containerElement', { static: true, read: ElementRef })
  containerElement: ElementRef;

  @Input({ required: true }) endpoint: EndpointWithConn;
  @Input() selected = false;

  @Output() onEndpointClicked = new EventEmitter<string>();

  protected readonly getEndpointId = getEndpointId;
  isAccepted = (a: Endpoint, b: Endpoint) => b.capability !== a.capability;
  host = '';
  port = '';
  isInput = false;
  endpointId = '';

  constructor(public data: DataService) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['endpoint'] && this.endpoint.connectionObj) {
      this.isInput = this.endpoint.capability === 'DATA_RECEIVER';
      this.host = getConnectionHost(this.endpoint.connectionObj);
      this.port = getConnectionPort(this.endpoint.connectionObj);
      this.endpointId = getEndpointId(this.endpoint);
    }
  }

  endpointClicked(): void {
    this.onEndpointClicked.emit(this.endpointId);
  }

  getContainer(): ElementRef {
    return this.containerElement;
  }

  handleOnDrop({ data, dropped }: { data: Endpoint; dropped: Endpoint }): void {
    const input = data.capability === 'DATA_RECEIVER' ? data : dropped;
    const output = data.capability !== 'DATA_RECEIVER' ? data : dropped;
    const dataPath: DataPath = {
      id: `${input.node}:${input.name}-${output.node}:${output.name}`,
      name: `${input.node}:${input.name}-${output.node}:${output.name}`,
      pathStart: { node: input.node, endpoint: input.name },
      pathEnd: { node: output.node, endpoint: output.name },
      isTransferAllDataRoute: true
    };
    this.data.dataPath.createOne(dataPath);
  }

  openUpdateEndpointDialog(): void {
    this.data.endpoint.openCreateEndpointDialog({
      crud: 'Update',
      model: 'Endpoint',
      selectedNode: this.endpoint.node,
      direction: this.endpoint.capability === 'DATA_RECEIVER' ? 'IN' : 'OUT',
      selectedConnection: this.endpoint.connection,
      endpointName: this.endpoint.name,
      currentParameters: this.endpoint.parameters,
      invalid: false
    });
  }

  restartEndpoint(): void {
    this.data.endpoint.restart(this.endpointId);
  }
}
