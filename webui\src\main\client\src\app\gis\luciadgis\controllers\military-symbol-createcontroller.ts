import { Feature, FeatureProperties } from '@luciad/ria/model/feature/Feature';
import { BasicCreateController } from '@luciad/ria/view/controller/BasicCreateController';
import { CreateControllerConstructorOptions } from '@luciad/ria/view/controller/CreateController';
import { HandleEventResult } from '@luciad/ria/view/controller/HandleEventResult';
import { FeatureLayer } from '@luciad/ria/view/feature/FeatureLayer';
import { GestureEvent } from '@luciad/ria/view/input/GestureEvent';
import { GestureEventType } from '@luciad/ria/view/input/GestureEventType';
import { Map } from '@luciad/ria/view/Map';
import { ShapeType } from '../../interface/models/map-element.model';
import { dashConverter, fillTypeConverter } from '../luciad.utils';
import { IconFactory } from '../LuciadIconFactory';
import { LuciadMapDisplay } from '../LuciadMapDisplay';
import { LuciadShape } from '../LuciadShape';
import { MS2525Symbol } from '../MS2525Symbol';

export class MilitarySymbolCreateController extends BasicCreateController {
  luciadMapDisplay: LuciadMapDisplay;
  milSymbolCode: string;
  shapeType: ShapeType;
  creationLayer: FeatureLayer;
  startCreating;

  constructor(
    luciadMapDisplay: LuciadMapDisplay,
    shapeType: ShapeType,
    defaultProperties?: FeatureProperties,
    options?: CreateControllerConstructorOptions
  ) {
    super(shapeType, defaultProperties, options);
    if (defaultProperties) {
      const { code } = defaultProperties;
      if (code !== undefined) {
        this.milSymbolCode = code;
      }
    }
    this.shapeType = shapeType;
    this.luciadMapDisplay = luciadMapDisplay;
  }

  setCreationLayer(creationLayer: FeatureLayer): void {
    this.creationLayer = creationLayer;
  }

  setMilSymbol(symbolCode: string): void {
    this.milSymbolCode = symbolCode;
  }

  override onChooseLayer(map: Map): FeatureLayer | null {
    if (this.milSymbolCode) {
      return this.luciadMapDisplay.layerHandler.getLayer('ManuallyCreatedObjects').getGISLayer() as FeatureLayer;
    }

    return this.luciadMapDisplay.layerHandler.getLayer('GeodeticShapeLayer').getGISLayer() as FeatureLayer;
  }

  override onCreateNewObject(map: Map, layer: FeatureLayer): LuciadShape | MS2525Symbol {
    if (this.milSymbolCode !== undefined) {
      const ms2525Symbol = new MS2525Symbol(
        this.luciadMapDisplay,
        this.milSymbolCode,
        '',
        this.luciadMapDisplay.getReference(),
        this.shapeType
      );

      this.luciadMapDisplay.selectedElement = ms2525Symbol;

      return ms2525Symbol;
    }

    const luciadShape = new LuciadShape(
      this.luciadMapDisplay,
      this.luciadMapDisplay.getReference(),
      this.shapeType
    );

    const normalStyle = this.luciadMapDisplay.elementHandler.shapeStyleInfo.normal;
    const selectedStyle = this.luciadMapDisplay.elementHandler.shapeStyleInfo.selected;
    if (normalStyle) {
      const { strokeInfo, fillInfo } = normalStyle;
      const { strokeColor, strokeType, strokeWidth } = strokeInfo;
      const { fillColor, fillType } = fillInfo;
      if (this.shapeType === ShapeType.POINT) {
        luciadShape.genericNormalIconStyle = this.luciadMapDisplay.elementHandler.shapeStyleInfo.normal;
        luciadShape.normalStyle = {
          image: IconFactory.rectangle(
            {
              width: 15,
              height: 15,
              stroke: strokeColor,
              fill: strokeColor
            }
          )
        };
      } else {
        luciadShape.normalStyle = {
          stroke: {
            color: strokeColor,
            width: strokeWidth,
            dash: dashConverter(strokeType)
          },
          fill: {
            color: fillColor,
            image: fillTypeConverter(fillType, fillColor)
          }
        };
      }
    }

    if (selectedStyle) {
      const { strokeInfo, fillInfo } = selectedStyle;
      const { strokeColor, strokeType, strokeWidth } = strokeInfo;
      const { fillColor, fillType } = fillInfo;
      if (this.shapeType === ShapeType.POINT) {
        luciadShape.genericSelectedIconStyle = this.luciadMapDisplay.elementHandler.shapeStyleInfo.selected;
        luciadShape.selectedStyle = {
          image: IconFactory.rectangle(
            {
              width: 15,
              height: 15,
              stroke: strokeColor,
              fill: strokeColor
            }
          )
        };
      } else {
        luciadShape.selectedStyle = {
          stroke: {
            color: strokeColor,
            width: strokeWidth,
            dash: dashConverter(strokeType)
          },
          fill: {
            color: fillColor,
            image: fillTypeConverter(fillType, fillColor)
          }
        };
      }
    }

    this.luciadMapDisplay.selectedElement = luciadShape;
    return luciadShape;
  }

  override onObjectCreated(map: Map, layer: FeatureLayer, feature: Feature): void | Promise<void> {
    return super.onObjectCreated(map, layer, feature);
  }

  override onGestureEvent(aEvent: GestureEvent): HandleEventResult {
    if (aEvent.type === GestureEventType.DOWN) {
      console.log('down');
    } else if (aEvent.type === GestureEventType.DOUBLE_CLICK) {
      console.log('double click');
    }
    return super.onGestureEvent(aEvent);
  }
}
