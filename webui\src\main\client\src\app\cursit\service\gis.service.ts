import { inject, Injectable } from '@angular/core';
import { Entity } from '../../data/entity/entity.model';
import { LayerOptions } from './gis.model';
import { EntityService } from '../../data/entity/entity.service';
import { OnBeforeUnload } from '@simfront/common-libs';
import { processArrayItemsIdleCallback } from '../../shared/util/util.model';

@Injectable()
export abstract class GisService {
  protected entityService: EntityService = inject(EntityService);
  protected _observer: ResizeObserver = new ResizeObserver(this.resize.bind(this));
  protected set container(element: Element) {
    this._observer.observe(element);
  }

  constructor() {
    this.entityService.open();
    this.entityService.entities$.subscribe(entities => processArrayItemsIdleCallback(entities, this.addEntity.bind(this)));
  }

  protected addLayer(layerName: string, url: string, options?: LayerOptions): void {
    const lowercase = url.toLowerCase();
    if (lowercase.includes('tile')) {
      this.addTileLayer(layerName, url, options);
    } else if (lowercase.includes('wms')) {
      this.addWMSLayer(layerName, url, options);
    } else if (lowercase.includes('wmts')) {
      this.addWMTSLayer(layerName, url, options);
    } else {
      console.error('Layer type not supported.');
    }
  }

  abstract initMap(): void;
  abstract addEntity(entity: Entity): void;
  abstract removeLayer(layerName: string): void;
  abstract addTileLayer(layerName: string, url: string, options?: LayerOptions): void;
  abstract addWMSLayer(layerName: string, url: string, options?: LayerOptions): void;
  abstract addWMTSLayer(layerName: string, url: string, options?: LayerOptions): void;
  abstract resize(): void;

  destroy(): void {
    this._observer.unobserve(this.resize.bind(this));
    this.entityService.stop();
  }

  @OnBeforeUnload
  private _destroy(): void {
    this.destroy();
  }

}


