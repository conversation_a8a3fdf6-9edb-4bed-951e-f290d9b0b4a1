import {
  Component,
  inject,
  Input,
  signal,
  Signal,
  WritableSignal
} from '@angular/core';
import {
  DataParameter,
  PAGE_FORM,
  PARAMS_FORM,
  PARENT_FORM,
  SECTION_FORM
} from '../../../../data/bs-params/bs-params.model';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { BsDataParamComponent } from '../bs-data-param/bs-data-param.component';
import { BsDataParamBase } from '../bs-data-param/bs-data-param.base';

@Component({
  selector: 'vcci-bs-data-params',
  templateUrl: './bs-data-params.component.html',
  imports: [ReactiveFormsModule, BsDataParamComponent],
  styleUrl: './bs-data-params.component.css',
  providers: [
    {
      provide: PARAMS_FORM,
      useFactory: () => signal(new FormGroup({}))
    }
  ]
})
export class BsDataParamsComponent extends BsDataParamBase {
  parentFormGroup$: Signal<FormGroup> = inject(PARENT_FORM, { skipSelf: true });
  pageFormGroup$?: Signal<FormGroup> = inject(PAGE_FORM, {
    optional: true,
    skipSelf: true
  });
  sectionFormGroup$?: Signal<FormGroup> = inject(SECTION_FORM, {
    optional: true,
    skipSelf: true
  });
  paramsFormGroup$: WritableSignal<FormGroup> = inject(PARAMS_FORM, {
    self: true
  });

  private _parameters: DataParameter[] = [];
  @Input({ required: true }) set parameters(parameters: DataParameter[]) {
    const paramsFormGroup =
      this.sectionFormGroup$?.().get('parameters') ||
      this.pageFormGroup$?.().get('parameters') ||
      this.parentFormGroup$().get('parameters');
    if (paramsFormGroup instanceof FormGroup) {
      this.paramsFormGroup$.set(paramsFormGroup);
    }
    this._parameters = parameters;
  }
  get parameters() {
    return this._parameters;
  }
}
