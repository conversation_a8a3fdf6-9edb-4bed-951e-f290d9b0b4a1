/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/26/2024, 10:49 AM
 */

// export function convertShape(s: Shape, reverse: boolean = false): ControlFeatureLocation | null {
//   switch (s.type) {
//     case ShapeType.POINT:
//       return s.focusPoint ? fromLcdPoint(s.focusPoint) : null;
//     case ShapeType.POLYGON:
//       return fromLcdPoly(s as Polygon);
//     case ShapeType.POLYLINE:
//       const pl = s as Polyline;
//       return reverse ? fromLcdPolyReverse(pl) : fromLcdPoly(pl);
//     case ShapeType.ELLIPSE:
//       return fromLcdEllipse(s as Ellipse);
//     case ShapeType.ARC_BAND:
//       return fromLcdArcBand(s as ArcBand);
//     default:
//       return null;
//   }
// }
