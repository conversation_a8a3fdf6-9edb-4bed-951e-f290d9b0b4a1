import {
  Component,
  inject,
  Renderer2,
  signal,
  computed,
  effect
} from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
  FormArray
} from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { CoreModule } from '@simfront/common-libs';
import { DataService } from 'src/app/data/data.service';
import { MovementTabsComponent } from './movement-tabs/movement-tabs.component';
import { toolbarExpanded } from '../top-toolbar/top-toolbar.component';
import { GisService, RouteClickData } from '../../service/gis.service';
import { LeafletMapDisplay } from 'src/app/gis/leafletgis/LeafletMapDisplay';
import { LeafletMouseEvent, Point } from 'leaflet';
import {
  Movement,
  routeNameRequiredValidator
} from 'src/app/data/movement/movement.model';
import { Entity, Shapes } from 'src/app/data/entity/entity.model';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'nexus-movement-manager',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    CoreModule,
    MovementTabsComponent
  ],
  templateUrl: './movement-manager.component.html',
  styleUrl: './movement-manager.component.css'
})
export class MovementManagerComponent {
  ds = inject(DataService);

  // Convert observables to signals
  private allNodes = toSignal(this.ds.node.all$, { initialValue: [] });
  private activeNodes = toSignal(this.ds.node.on$, { initialValue: [] });

  movementEngine = computed(() =>
    this.allNodes().filter((node) => node.creation)
  );

  selectedNode = computed(
    () => this.movementEngine().find((node) => node.name === 'SIM_MOVE') ?? null
  );

  creatingRoute = signal<boolean>(false);
  savedMovement = signal<any>(null); // Stores movement data from sessionStorage
  private dialogRef = inject(MatDialogRef<MovementManagerComponent>);
  private renderer = inject(Renderer2);
  private gisService = inject(GisService);
  createMovementForm = new FormGroup({
    name: new FormControl('', Validators.required),
    movementEngine: new FormControl('', Validators.required),
    selectMission: new FormControl(0),
    startMovements: new FormControl('IMMEDIATE', Validators.required),
    startHours: new FormControl(0),
    startMinutes: new FormControl(0),
    startSeconds: new FormControl(0),
    entityType: new FormControl('land', Validators.required),
    stepIndex: new FormControl(0),
    entitySelection: new FormGroup({
      selectedEntities: new FormControl<Entity[]>([], [Validators.required])
    }),
    routeSelection: new FormGroup(
      {
        routeType: new FormControl('NEW_ROUTE', [Validators.required]),
        routeName: new FormControl(''),
        routePointsInformation: new FormArray([])
      },
      { validators: routeNameRequiredValidator() }
    ),
    routeInformation: new FormGroup({
      callsign: new FormControl(''),
      speed: new FormControl(60, Validators.required),
      altitude: new FormControl(30),
      minHeight: new FormControl(30),
      maxHeight: new FormControl(30),
      moveToStart: new FormControl(false),
      returnToStart: new FormControl(false)
    })
  });

  readonly toolbarExpanded = toolbarExpanded;

  readonly env = environment;

  constructor() {
    effect(() => {
      if (!this.env.production) {
        console.log('Active nodes:', this.activeNodes());
      }
    });

    // Clear the stashed movement when the component is initialized
    sessionStorage.removeItem('movement');
    sessionStorage.removeItem('routePoints');
  }

  onNodeSelected(event: any) {
    if (!this.env.production) {
      console.log(event);
    }
  }

  openCreateRoute() {
    this.toolbarExpanded.update((v) => !v);
    this.creatingRoute.set(true);
    this.dialogRef.updateSize('300px');
    this.dialogRef.updatePosition({ top: '100px', right: '50px' });
    this.dialogRef.disableClose = true;
    const dialogContainer = document.querySelector('.mat-mdc-dialog-surface');
    if (dialogContainer) {
      this.renderer.setStyle(dialogContainer, 'overflow-y', 'hidden');
    }
    const mapContainer = (
      this.gisService.mapDisplay as LeafletMapDisplay
    ).map.getContainer();
    this.renderer.setStyle(mapContainer, 'cursor', 'crosshair');
    (this.gisService.mapDisplay as LeafletMapDisplay).map.on(
      'click',
      this.locationOnClick
    );

    this.startRouteCreation();
  }

  restoreCreateMovement() {
    this.toolbarExpanded.update((v) => !v);
    this.creatingRoute.set(false);
    this.dialogRef.updatePosition();
    this.dialogRef.updateSize('1000px');
    this.dialogRef.disableClose = false;
    const dialogContainer = document.querySelector('.mat-mdc-dialog-surface');
    if (dialogContainer) {
      this.renderer.setStyle(dialogContainer, 'overflow-y', 'auto');
    }
    const mapContainer = (
      this.gisService.mapDisplay as LeafletMapDisplay
    ).map.getContainer();
    this.renderer.setStyle(mapContainer, 'cursor', '');
    (this.gisService.mapDisplay as LeafletMapDisplay).map.off(
      'click',
      this.locationOnClick
    );

    this.cancelRouteCreation();
  }

  locationOnClick = (e: LeafletMouseEvent) => {
    if (!this.env.production) {
      console.log(e);
    }
  };

  startRouteCreation(): void {
    this.gisService.startRouteCreation();
  }

  cancelRouteCreation(): void {
    this.gisService.cancelRouteCreation();
  }

  clearRoutePoints(): void {
    this.gisService.clearRoutePoints();
  }

  cancelMovementCreation(): void {
    this.cancelRouteCreation();
    this.clearRoutePoints();

    const stashedMovement = sessionStorage.getItem('movement');

    if (stashedMovement) {
      this.deleteStashedMovement();
    }

    // Clear saved movement data
    this.savedMovement.set(null);
  }

  disableCreateMovement(): boolean {
    return (
      this.createMovementForm.invalid ||
      this.gisService.routePoints().length < 2
    );
  }

  stashMovement(): void {
    // save to local storage
    const movementStorage = JSON.parse(
      sessionStorage.getItem('movement') || '[]'
    );
    movementStorage.push(this.createMovementForm.getRawValue());
    sessionStorage.setItem('movement', JSON.stringify(movementStorage));
    const routePointStorage = JSON.parse(
      sessionStorage.getItem('routePoints') || '[]'
    );
    routePointStorage.push(this.gisService.routePoints());
    sessionStorage.setItem('routePoints', JSON.stringify(routePointStorage));

    // Clear the form by resetting the form group to the initial state and route points
    this.createMovementForm.reset({
      startMovements: 'IMMEDIATE',
      entityType: 'land',
      stepIndex: 0,
      routeSelection: {
        routeType: 'NEW_ROUTE'
      },
      routeInformation: {
        callsign: '',
        speed: 60,
        altitude: 30,
        minHeight: 30,
        maxHeight: 30
      }
    });
    this.gisService.clearRoutePoints();

    // Clear saved movement data so new route points use default values
    this.savedMovement.set(null);
  }

  restoreStashedMovement(): void {
    const movementStorage = JSON.parse(
      sessionStorage.getItem('movement') || '[]'
    );

    const routePointStorage = JSON.parse(
      sessionStorage.getItem('routePoints') || '[]'
    );

    const movement = movementStorage.pop();
    const routePoints = routePointStorage.pop();

    // Store the movement data in signal for passing to child components
    this.savedMovement.set(movement);

    // Clear existing route points first
    this.gisService.clearRoutePoints();

    // Reset form first to clear any existing state
    this.createMovementForm.reset();

    // Add each route point individually after form reset
    if (routePoints && Array.isArray(routePoints)) {
      routePoints.forEach((point: RouteClickData) => {
        this.gisService.setRouteClickData(point);
      });
    }

    // Add the movement data to the form
    this.createMovementForm.patchValue(movement);

    sessionStorage.setItem('movement', JSON.stringify(movementStorage));
    sessionStorage.setItem('routePoints', JSON.stringify(routePointStorage));
  }

  disableStashMovement(): boolean {
    return this.gisService.routePoints().length < 2;
  }

  disableRestoreMovement(): boolean {
    const movementStorage = JSON.parse(
      sessionStorage.getItem('movement') || '[]'
    );

    return movementStorage.length === 0;
  }

  deleteStashedMovement(): void {
    sessionStorage.removeItem('movement');
  }

  createMovement(): void {
    const formValues = this.createMovementForm.getRawValue();

    const formData: Movement = {
      movementEngine: formValues.movementEngine,
      name: formValues.name,
      // selectMission: formValues.selectMission,
      startType: formValues.startMovements,
      startTime: formValues.startHours,
      type: formValues.routeSelection.routeType,
      entities: formValues.entitySelection.selectedEntities,
      existingRoute: null, // TODO: Add the ID of the existing route when it is implemented
      routePoints: formValues.routeSelection.routePointsInformation.map(
        (point) => ({
          latitude: point.latitude,
          longitude: point.longitude,
          '@class': Shapes.Point
        })
      ),
      routeName: formValues.routeSelection.routeName,
      routePointInfo: formValues.routeSelection.routePointsInformation,
      callsign: formValues.routeInformation.callsign,
      speed: formValues.routeInformation.speed,
      altitude: formValues.routeInformation.altitude,
      minHeight: formValues.routeInformation.minHeight,
      maxHeight: formValues.routeInformation.maxHeight,
      moveToStart: formValues.routeInformation.moveToStart,
      returnToStart: formValues.routeInformation.returnToStart
    };

    console.log('Movement data to be sent to backend:', formData);
    this.ds.movement.saveMovement(formData);
  }
}
