import { geoJSON, Layer, LayerGroup, stamp, TileLayer } from 'leaflet';

import { getJSON } from 'ol/net';
import { EventBoundary, Point } from 'pixi.js';
import { MapLayerFactory } from '../interface/LayerFactory';
import { MapLayer } from '../interface/MapLayer';
import { GridType, LayerType } from '../../data/layer-manager/layer-manager.model';
import { GisService } from '../interface/service/gis.service';
import { GARSGraticuleLayer } from './gars-graticule/GARSGraticuleLayer';
import {
  GridOptions,
  LatLonGraticuleLayer
} from './latlon-graticule/LatLonGraticule';

import { MilitarySymbologyContainer } from './leaflet-symbology-layer/MilitarySymbologyContainer';
import { getWMTSCapabilities, WMTS } from './leaflet-wmts/Leaflet-WMTS';
import { LeafletEntity } from './LeafletEntity';
import { LeafletGraphics } from './LeafletGraphics';
import { LeafletMapDisplay } from './LeafletMapDisplay';
import { LeafletMapLayer } from './LeafletMapLayer';
import { MGRSGraticuleLayer } from './mgrs-graticule/MGRSGraticuleLayer';
import { LeafletTacticalGraphicsService } from './service/LeafletTacticalGraphicsService';
import WMS = TileLayer.WMS;

export class LeafletLayerFactory extends MapLayerFactory<Layer> {
  mapDisplay: LeafletMapDisplay;
  service: LeafletTacticalGraphicsService;

  constructor(
    private gisService: GisService,
    mapDisplay: LeafletMapDisplay,
    service: LeafletTacticalGraphicsService
  ) {
    super();
    this.mapDisplay = mapDisplay;
    this.service = service;
  }

  createGridLayer(gridType: GridType, color: string): MapLayer<Layer> | null {
    const gridMapLayer = new LeafletMapLayer();
    gridMapLayer.setLeafLetMap(this.mapDisplay.leafletMap);
    switch (gridType) {
      case 'MGRS': {
        const mgrsGraticuleLayer = new MGRSGraticuleLayer({
          color,
          weight: 1
        } as GridOptions);
        gridMapLayer.setGISLayer(mgrsGraticuleLayer);
        gridMapLayer.layerId = stamp(mgrsGraticuleLayer).toString();
        gridMapLayer.layerName = 'MGRS Grid Layer';
        gridMapLayer.layerType = 'Grid';
        gridMapLayer.parentId = '-777';
        this.layersHash[gridMapLayer.layerId] = gridMapLayer;
        break;
      }
      case 'LatLon': {
        const latlonGraticuleLayer = new LatLonGraticuleLayer({
          color,
          weight: 1
        });
        gridMapLayer.setGISLayer(latlonGraticuleLayer);
        gridMapLayer.layerId = stamp(latlonGraticuleLayer).toString();
        gridMapLayer.layerName = 'LatLon Grid Layer';
        gridMapLayer.layerType = 'Grid';
        gridMapLayer.parentId = '-777';
        this.layersHash[gridMapLayer.layerId] = gridMapLayer;
        break;
      }
      case 'GARS': {
        const garsGraticuleLayer = new GARSGraticuleLayer();
        gridMapLayer.setGISLayer(garsGraticuleLayer);
        gridMapLayer.layerId = stamp(garsGraticuleLayer).toString();
        gridMapLayer.layerName = 'GARS Grid Layer';
        gridMapLayer.layerType = 'Grid';
        gridMapLayer.parentId = '-777';
        this.layersHash[gridMapLayer.layerName] = gridMapLayer;
        break;
      }
      default:
        throw Error('Not a Supported Grid Layer Type');
    }
    return gridMapLayer;
  }

  createShapeDrawingLayer(
    layerName: string,
    layerId?: string
  ): MapLayer<Layer> {
    const shapeDrawingLayer = new MilitarySymbologyContainer(
      this.mapDisplay
      // {/* pane: 'markerPane', forceCanvas : true*/}
    );

    shapeDrawingLayer.getContainer().eventMode = 'dynamic';

    const mapLayer = new LeafletMapLayer(
      shapeDrawingLayer,
      layerId ?? stamp(shapeDrawingLayer).toString(),
      layerName,
      this.mapDisplay.map
    );
    mapLayer.layerName = layerName;
    this.layersHash[mapLayer.layerId] = mapLayer;
    mapLayer.layerType = 'Feature';
    return mapLayer;
  }

  protected gisCreateMilitarySymbolLayer(
    name: string,
    layerId: string
  ): MapLayer<Layer> {
    const militarySymbolLayer = new MilitarySymbologyContainer(
      this.mapDisplay
      // {/* pane: 'markerPane', forceCanvas : true*/}
    );

    militarySymbolLayer.getContainer().eventMode = 'dynamic';
    // militarySymbolLayer.getContainer().on('click', (event)=>{
    //   console.log(event.target);
    // });

    const boundary = new EventBoundary(militarySymbolLayer.getContainer());
    this.mapDisplay.map.on('click', (e) => {
      const interaction = militarySymbolLayer.getRenderer().events;
    });

    this.mapDisplay.leafletMap.on('moveend', () => {
      militarySymbolLayer.redrawing = false;
    });

    this.mapDisplay.leafletMap.on('mousemove', (e) => {
      if (this.mapDisplay.selectedElement && this.mapDisplay.mouseDown) {
        if (this.mapDisplay.selectedElement instanceof LeafletEntity) {
          this.mapDisplay.selectedElement.setLocation([
            e.latlng.lat,
            e.latlng.lng
          ]);
          militarySymbolLayer.redrawing = true;
          militarySymbolLayer.redraw();
        }
      }
    });

    this.mapDisplay.leafletMap.on('click', () => {
      this.mapDisplay.mouseDown = true;
      this.mapDisplay.selectedElement = null;
      if (!this.mapDisplay.selected) {
        militarySymbolLayer.getSymbolsArray().forEach((entity) => {
          if (entity instanceof LeafletEntity) {
            entity.selected = false;
            if (entity.broughtToFront) {
              const markerParent = entity.parent;
              markerParent.removeChild(entity);
              markerParent.addChildAt(entity, entity.originalParentIndex);
              entity.broughtToFront = false;
            }
            entity.removeSelectionBox();
          }
        });

        if (militarySymbolLayer.leafletGraphicsList) {
          militarySymbolLayer.leafletGraphicsList.forEach((graphic) => {
            if (graphic instanceof LeafletGraphics) {
              graphic.selected = false;
              if (graphic.broughtToFront) {
                const parent = graphic.parent;
                parent.removeChild(graphic);
                parent.addChildAt(graphic, graphic.originalParentIndex);
                graphic.broughtToFront = false;
              }
            }
          });
        }

        militarySymbolLayer.redraw();
      }
    });

    const mapLayer = new LeafletMapLayer(
      militarySymbolLayer,
      layerId ?? stamp(militarySymbolLayer).toString(),
      name,
      this.mapDisplay.map
    );
    mapLayer.layerName = name;
    mapLayer.setGISLayerVisibility(true);
    this.layersHash[mapLayer.layerId] = mapLayer;
    mapLayer.layerType = 'Feature';
    // this.mapDisplay.map.addLayer(mapLayer as unknown as Layer)
    return mapLayer;
  }

  protected gisCreateTiledLayer(url: string): Promise<MapLayer<Layer>> {
    // this.map.addLayer(tileLayer);
    const promise = new Promise<MapLayer<Layer>>((resolve, reject) => {
      const tileLayer: TileLayer = new TileLayer(url, { maxZoom: 21 });
      const leafletTileMapLayer = new LeafletMapLayer(
        tileLayer,
        stamp(tileLayer).toString(),
        url,
        this.mapDisplay.map
      );
      this.layersHash[leafletTileMapLayer.layerName] = leafletTileMapLayer;
      leafletTileMapLayer.layerType = 'Web Service';
      resolve(leafletTileMapLayer);
      reject(new Error('Could not create tiled layer1'));
    });

    return promise;
  }

  protected gisCreateWMSLayer(
    url: string,
    layers?: string[]
  ): Promise<MapLayer<Layer>> {
    const layerGroup: LayerGroup = new LayerGroup();

    const res = new Promise<MapLayer<Layer>>((resolve, reject) => {
      layers?.forEach((layer) => {
        const wmsLayer: WMS = new WMS(url, {
          maxZoom: 21,
          layers: layer,
          transparent: true,
          format: 'image/png'
        });
        wmsLayer.setParams({
          layers: layer,
          service: 'WMS',
          version: '1.3.0',
          request: 'GetMap',
          transparent: true
        });
        layerGroup.addLayer(wmsLayer);
      });

      const leafletWMSMapLayer = new LeafletMapLayer(
        layerGroup,
        stamp(layerGroup).toString(),
        undefined,
        this.mapDisplay.map
      );
      leafletWMSMapLayer.layerType = 'Web Service';
      resolve(leafletWMSMapLayer);
      reject(new Error('Problem with Leaflet WMS Layer creation!'));
    });

    return res;
  }

  protected async gisCreateWMTSLayer(url: string): Promise<MapLayer<Layer>> {
    const result = await getWMTSCapabilities(url);
    const layerParameters = result[0];
    const wmtsLayer: WMTS = new WMTS(url, {
      layer: layerParameters.layername,
      tilematrixset: layerParameters.tilematrixset,
      format: layerParameters.format,
      style: layerParameters.style
    });
    const leafletWMTSMapLayer = new LeafletMapLayer(
      wmtsLayer,
      stamp(wmtsLayer).toString(),
      layerParameters.layername,
      this.mapDisplay.map
    );
    leafletWMTSMapLayer.layerType = 'Web Service';
    return leafletWMTSMapLayer;
  }

  // onLayerAdd = (event: any) => {
  //   // console.log(event.layer);
  //   //   console.log('layer added')
  //   //This is important, do ont miss it!!!!!!
  //   let pixiOverlayDiv = this.militarySymbolLayer.getPane()?.querySelector(
  //   '.leaflet-pixi-layer');
  //   if ((pixiOverlayDiv as HTMLDivElement).style) {
  //     (pixiOverlayDiv as HTMLDivElement).style.zIndex = '300';
  //   }
  //
  // }

  createFeatureLayer(
    filePath: string,
    layerType: LayerType
  ): Promise<LeafletMapLayer> {
    if (layerType === 'Base') {
      return new Promise<LeafletMapLayer>((resolve, reject) => {
        getJSON(filePath)
          .then((geojson) => {
            const layer = geoJSON(geojson, {
              style: {
                color: 'rgb(0, 0, 0)',
                fillColor: 'rgb(131, 149, 170)',
                fillOpacity: 0.3,
                weight: 1
              }
            });
            const leafletMapLayer = new LeafletMapLayer(
              layer,
              stamp(layer).toString(),
              'World'
            );
            leafletMapLayer.setLeafLetMap(this.mapDisplay.leafletMap);
            resolve(leafletMapLayer);
          })
          .catch((error) => {
            console.error('Error loading GeoJSON:', error);
            reject(error);
          });
      });
    }

    return undefined;
  }
}
