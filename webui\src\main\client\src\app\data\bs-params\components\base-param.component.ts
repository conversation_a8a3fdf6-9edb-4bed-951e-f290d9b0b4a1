import {
  AfterViewInit,
  DestroyRef,
  Directive,
  HostBinding,
  inject,
  signal
} from '@angular/core';
import { DataParameterComponent } from './data-param.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime } from 'rxjs';
import { parameterToBoolean } from '../bs-params.model';

@Directive()
export abstract class BaseParamComponent implements AfterViewInit {
  @HostBinding('class.data-param-grp') get isDataParamGrp(): boolean {
    return !!this.parameter.group;
  }

  @HostBinding('class.data-param-form-field') dataParamClass = true;

  private _container = inject(DataParameterComponent);
  private _destroyRef: DestroyRef = inject(DestroyRef);
  private _prevParamEnabled = true;

  errorMessage = signal('');

  get parameter() {
    return this._container.parameter;
  }

  get label() {
    return this._container.parameter.name;
  }

  get value() {
    return this._container.value;
  }

  get hasNext() {
    return this._container.parameter.hasNext;
  }

  get defaultValue() {
    return this._container.parameter.defaultValue;
  }

  get invalid() {
    return this._container.formControl.invalid;
  }

  get formControl() {
    return this._container.formControl;
  }

  get group() {
    return this._container.group;
  }

  get isSelectedInGroup() {
    return this.group?.isSelected(this.parameter.name) ?? true;
  }

  get isPreviousEnabled(): boolean {
    return this._prevParamEnabled;
  }

  get shouldEnableFormControl(): boolean {
    if (!this.parameter.editable || !this.isPreviousEnabled) {
      return false;
    } else if (this.parameter.type === 'BOOLEAN') {
      return true;
    } else {
      return this.isSelectedInGroup;
    }
  }

  setGroupValue(checked: boolean): void {
    this._container.setGroupValue(checked);
  }

  ngAfterViewInit(): void {
    this._registerOnChanges();
    this._registerPreviousParam();
    this._registerGroupChanges();
    this.updateFormControlState();
  }

  private _registerOnChanges(): void {
    combineLatest([
      this.formControl.statusChanges,
      this.formControl.valueChanges
    ])
      .pipe(debounceTime(250), takeUntilDestroyed(this._destroyRef))
      .subscribe(() => this.updateErrors());
  }

  private _registerPreviousParam(): void {
    if (!this.parameter.hasPrev) {
      return;
    }
    const prev = this._container.container.control.get(
      this.parameter.prevParamName
    );
    if (this.parameter.hasPrev && prev) {
      this._prevParamEnabled = parameterToBoolean(prev.value) && !prev.invalid;
      prev.valueChanges
        .pipe(takeUntilDestroyed(this._destroyRef))
        .subscribe((value) => {
          this._prevParamEnabled = !!value && !prev.invalid;
          this.updateFormControlState();
        });
    }
  }

  private _registerGroupChanges(): void {
    if (this.group) {
      this.group.changed
        .asObservable()
        .pipe(takeUntilDestroyed(this._destroyRef))
        .subscribe(() => {
          if (this.parameter.type === 'BOOLEAN') {
            this.formControl.patchValue(this.isSelectedInGroup);
          }
          this.updateFormControlState();
        });
    }
  }

  updateErrors(): void {
    if (this.formControl.hasError('required')) {
      this.errorMessage.set('You must enter a value');
    } else if (this.formControl.hasError('pattern')) {
      this.errorMessage.set(
        `${this.parameter.name} needs to be a valid ${this.parameter.type}.`
      );
    } else {
      this.errorMessage.set('');
    }
  }

  updateFormControlState(): void {
    if (this.shouldEnableFormControl) {
      this.formControl.enable();
    } else {
      this.formControl.disable();
    }
  }

  onGetNext(): void {
    this._container.onGetNext();
  }

  onValueChange(value: string[] | string | boolean): void {
    this._container.writeValue(value);
    if (this.parameter.type === 'NETWORK_INTERFACE') {
      this._container.networkInterfaceChanges.emit(`${value}`);
    } else if (this.parameter.type === 'BOOLEAN' && !!this.parameter.group) {
      this.setGroupValue(!!value);
    }
  }

  onUseDefault(): void {
    this._container.useDefault();
  }

  onInput(event: Event): void {
    const newValue = (event.target as HTMLInputElement).value;
    this.onValueChange(newValue);
  }
}
