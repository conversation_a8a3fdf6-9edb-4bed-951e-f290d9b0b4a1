import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { LetDirective } from '@ngrx/component';

import { StoreModule } from '@ngrx/store';
import { BsnModule } from '../battlespace/bsn.module';
import { CursitModule } from '../cursit/cursit.module';

import { MaterialModule } from '../material/material.module';
import { PopoutWindowModule } from '../shared/popout-window/popout-window.module';

import { NavigationComponent } from './components/navigation.component';
import { ToolbarComponent } from './components/toolbar.component';
import { App2Component } from './containers/app2.component';
import { layoutFeature } from './reducers/layout.reducer';
import { DataParamFormComponent } from '../battlespace/node/bs-form/data-param-form.component';
import { IconButtonSizeDirective } from '../shared/directives/icon-button-size.directive';
import { SvgIconSizeDirective } from '../shared/directives/svg-icon-size.directive';
import { ArcComponent } from './components/calian-arc.component';

export const COMPONENTS = [
  App2Component,
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    StoreModule.forFeature(layoutFeature),
    MatTooltipModule,
    BsnModule,
    CursitModule,
    MatMenuModule,
    LetDirective,
    PopoutWindowModule,
    ToolbarComponent,
    NavigationComponent,
    DataParamFormComponent,
    NavigationComponent,
    IconButtonSizeDirective,
    SvgIconSizeDirective,
    ArcComponent
  ],
  declarations: COMPONENTS,
  exports: COMPONENTS
})
export class VcciCoreModule {
}
