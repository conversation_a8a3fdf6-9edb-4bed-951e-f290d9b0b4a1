import { Component } from '@angular/core';
import {
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle
} from '@angular/material/dialog';
import { MatButton } from '@angular/material/button';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orm<PERSON>ield, <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { Store } from '@ngrx/store';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import {
  FilePayload,
  FilePayloadDirective
} from '../../../shared/directives/file-payload.directive';

interface FileControl {
  key: string;
  label: string;
  accept: string;
}

export function extValidator(allowedExt: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) return null;
    const parsed = JSON.parse(control.value);
    const filename = parsed?.fileName || '';
    const ext = filename.split('.').pop()?.toLowerCase();
    const exp = allowedExt.replace('.', '').toLowerCase();
    return ext === exp ? null : { invalidExtension: true };
  };
}

@Component({
  selector: 'import-dialog',
  templateUrl: './import-dialog.component.html',
  imports: [
    MatDialogTitle,
    MatButton,
    MatDialogActions,
    MatDialogContent,
    MatFormField,
    MatLabel,
    MatDialogClose,
    ReactiveFormsModule,
    FilePayloadDirective,
    MatInput,
    MatError
  ],
  styleUrls: ['./import-dialog.component.css']
})
export class ImportDialogComponent {
  fileControls: FileControl[] = [
    {
      key: 'acoFilePath',
      label: 'Air Control Order (ACO) File',
      accept: '.aco'
    },
    {
      key: 'atoFilePath',
      label: 'Air Traffic Order (ATO) File',
      accept: '.ato'
    },
    { key: 'airBaseFilePath', label: 'Airbases Specification', accept: '.exe' },
    {
      key: 'aircraftFilePath',
      label: 'Aircraft Specification',
      accept: '.exe'
    },
    { key: 'customFilePath', label: 'Custom Configurations', accept: '.txt' }
  ];
  form = new FormGroup(
    Object.fromEntries(
      this.fileControls.map((control) => [
        control.key,
        new FormControl<FilePayload | null>(null, [
          Validators.required,
          extValidator(control.accept)
        ])
      ])
    )
  );
}
