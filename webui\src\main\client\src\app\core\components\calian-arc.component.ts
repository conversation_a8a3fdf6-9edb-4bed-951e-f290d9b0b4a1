import { Component, ContentChild, Input, TemplateRef } from '@angular/core';

@Component({
  selector: 'nexus-arc',
  template: `
    <div class="arc-box">
      <svg
        class="arc arc-two"
        width="465"
        height="460"
        viewBox="0 0 465 460"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.0662 435.584C73.9736 321.826 154.71 220.786 252.667 138.231C350.623 55.676 463.88 -6.77669 585.973 -45.5613C708.065 -84.3459 836.601 -98.7029 964.242 -87.8125C1091.88 -76.9221 1216.13 -40.9976 1329.89 17.9098C1443.64 76.8172 1544.68 157.554 1627.24 255.51C1709.79 353.466 1772.25 466.724 1811.03 588.816C1849.81 710.908 1864.17 839.444 1853.28 967.085C1842.39 1094.73 1806.47 1218.97 1747.56 1332.73C1688.65 1446.49 1607.91 1547.53 1509.96 1630.08C1412 1712.64 1298.74 1775.09 1176.65 1813.87C1054.56 1852.66 926.024 1867.02 798.384 1856.12C670.743 1845.23 546.497 1809.31 432.74 1750.4C318.983 1691.5 217.942 1610.76 135.387 1512.8C52.8323 1414.85 -9.62033 1301.59 -48.4049 1179.5C-87.1895 1057.4 -101.546 928.868 -90.656 801.227C-79.7657 673.586 -43.8412 549.341 15.0662 435.583L15.0662 435.584Z"
          stroke-width="3"
        />
      </svg>
      <svg
        class="arc arc-one"
        width="465"
        height="460"
        viewBox="0 0 465 460"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.0662 435.584C73.9736 321.826 154.71 220.786 252.667 138.231C350.623 55.676 463.88 -6.77669 585.973 -45.5613C708.065 -84.3459 836.601 -98.7029 964.242 -87.8125C1091.88 -76.9221 1216.13 -40.9976 1329.89 17.9098C1443.64 76.8172 1544.68 157.554 1627.24 255.51C1709.79 353.466 1772.25 466.724 1811.03 588.816C1849.81 710.908 1864.17 839.444 1853.28 967.085C1842.39 1094.73 1806.47 1218.97 1747.56 1332.73C1688.65 1446.49 1607.91 1547.53 1509.96 1630.08C1412 1712.64 1298.74 1775.09 1176.65 1813.87C1054.56 1852.66 926.024 1867.02 798.384 1856.12C670.743 1845.23 546.497 1809.31 432.74 1750.4C318.983 1691.5 217.942 1610.76 135.387 1512.8C52.8323 1414.85 -9.62033 1301.59 -48.4049 1179.5C-87.1895 1057.4 -101.546 928.868 -90.656 801.227C-79.7657 673.586 -43.8412 549.341 15.0662 435.583L15.0662 435.584Z"
          stroke-width="4"
        />
      </svg>
    </div>
  `,
  styles: `
    :host {
      display: block;
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      pointer-events: none;
    }

    .arc-box {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .arc {
      fill: none;
      stroke-width: 1px;
      stroke-dasharray: 700;
      stroke-dashoffset: -700;
      will-change: stroke-dashoffset;
    }

    .arc-one {
      position: absolute;
      right: -50px;
      bottom: -50px;
      width: 465px;
      height: 460px;
      stroke: var(--calian-primary);
      animation: arc-animation 3s forwards;
    }

    .arc-two {
      position: absolute;
      right: -100px;
      bottom: -100px;
      width: 465px;
      stroke: #fff;
      animation: arc-animation 3.5s forwards;
      animation-delay: 0.5s;
    }

    @keyframes arc-animation {
      to {
        stroke-dashoffset: 0;
      }
    }
  `
})
export class ArcComponent {}
