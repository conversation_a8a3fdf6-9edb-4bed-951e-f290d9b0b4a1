import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnDestroy } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NavigationEnd, Router, RouterModule } from '@angular/router';

import { filter, Subscription, takeUntil } from 'rxjs';

import { environment } from '../../../environments/environment';

import { DataService } from '../../data/data.service';
import { MaterialModule } from '../../material/material.module';
import { NODE_ICONS } from 'src/app/data/node/node.model';
import { Store } from '@ngrx/store';
import { selectHideDataman } from '../reducers/layout.reducer';

@Component({
  selector: 'vcci-navigation',
  template: `
    <div class="button-container" *ngIf="environment.cursit">
      <button mat-button routerLink="/cursit" [disabled]="route === '/cursit'">
        <mat-icon inline class="cursit-icon" svgIcon="world-map" />
        CURSIT
      </button>
    </div>
    <div class="button-container">
      <button mat-button routerLink="/router" [disabled]="route === '/router'">
        <mat-icon inline svgIcon="router" />
        ROUTER
      </button>
    </div>
    <div class="button-container">
      <button
        mat-button
        routerLink="/services"
        [disabled]="route === '/services'"
      >
        <mat-icon inline svgIcon="node-protocol" />
        SERVICES
      </button>
    </div>

    @if (!(hideDataman$ | async)) {
      <div
        class="dataman-icon"
        [matTooltip]="'Dataman ' + (datamanStatus$ | async)"
        [class]="(datamanStatus$ | async).toLocaleLowerCase()"
      >
        <mat-icon
          *ngIf="NODE_ICONS['DATAMAN']; else defaultMatIcon"
          [svgIcon]="NODE_ICONS['DATAMAN']"
        />
        <ng-template #defaultMatIcon><mat-icon>hub</mat-icon></ng-template>
      </div>
    }
  `,
  styles: [
    `
      .dataman-icon-container {
        margin-bottom: 5px;
      }

      .dataman-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 60px;

        &.starting,
        &.terminating {
          color: var(--pending) !important;
        }

        &.error,
        &.timed_out {
          color: var(--error) !important;
        }

        &.unlicensed {
          color: var(--error-dark) !important;
        }

        &.on {
          color: var(--success) !important;
        }

        &.off {
          color: grey !important;
        }
      }

      button {
        font-family:
          Segoe UI,
          Helvetica Neue,
          Arial,
          sans-serif;
        line-height: normal;
        font-style: normal;
        letter-spacing: normal;
        font-size: 21px;
        font-weight: 600;
        line-height: 28px;
        font-variant: small-caps;
      }

      mat-icon {
        height: 28px !important;
        margin-right: 14px !important;
      }

      .cursit-icon {
        position: relative;
        top: 3px;
      }

      .cursit-icon ::ng-deep .foregroundColor {
        fill: currentColor;
      }

      .button-container:not(
          .button-container:nth-last-child(1 of .button-container)
        ) {
        border-right: 2px solid gray;
      }

      .button-container {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        padding: 0 10px;
        height: 28px;
        min-width: 110px;
      }
    `
  ],
  imports: [
    MatButtonModule,
    MatIconModule,
    MaterialModule,
    RouterModule,
    NgIf,
    AsyncPipe
  ],
  host: {
    style: `
      display: flex;
      flex-direction: row;
      align-items: center;
    `
  }
})
export class NavigationComponent implements OnDestroy {
  protected readonly NODE_ICONS = NODE_ICONS;

  private navSub!: Subscription;
  route: string = '/cursit';
  datamanStatus$ = this.data.datamanStatus$;
  hideDataman$ = this.store.select(selectHideDataman);

  constructor(
    private router: Router,
    private data: DataService,
    private store: Store
  ) {
    this.navSub = this.router.events
      .pipe(
        takeUntil(this.data.onRefresh$),
        filter((event) => event instanceof NavigationEnd)
      )
      .subscribe((event: NavigationEnd) => (this.route = event.url));
  }

  ngOnDestroy() {
    this.navSub.unsubscribe();
  }

  protected readonly environment = environment;
}
