import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { MilSymDTOItemList } from '../LeafletTacticalGraphics';

@Injectable({ providedIn: 'root' })
export class LeafletTacticalGraphicsService {
  constructor(private http: HttpClient) {}

  requestTacticalGraphics(sentTacticalGraphics: MilSymDTOItemList): Observable<any> {
    return this.http.post<any[]>('http://localhost:8080/tactical', sentTacticalGraphics, {
      headers: new HttpHeaders().set('Content-Type', 'application/json'),
      responseType: 'json'
    }).pipe(map((response: any[]) => response.map(item => item)));
  }
}
