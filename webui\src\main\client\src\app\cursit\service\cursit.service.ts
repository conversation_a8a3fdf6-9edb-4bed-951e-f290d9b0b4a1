import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { MapDisplay } from '../../gis/interface/MapDisplay';
import * as fromCursit from '../reducers/cursit.reducer';

@Injectable({ providedIn: 'root' })
export class CursitService {
  _mapDisplay: MapDisplay<object, object>;

  constructor(private _store: Store<fromCursit.State>) {
    console.log('cursit service created');
  }

  get store(): Store<fromCursit.State> {
    return  this._store;
  }

  get mapDisplay(): MapDisplay<object, object> {
    return this._mapDisplay;
  }

  initMap(id: number): Observable<boolean> {
    console.log('service?', id);
    return of(true);
  }
}
