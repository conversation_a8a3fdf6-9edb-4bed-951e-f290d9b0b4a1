<ng-container class="toolbar-container">
  <!--  <vcci-top-right-toolbar [hideLabel]="!showLabels">-->
  <!--  </vcci-top-right-toolbar>-->
  <vcci-entity-toolbar
    (entityToolbarExpanded)="toggleToolbars($event)"
    [all]="expandedToolbars.all"
    [expanded]="expandedToolbars.entities"
    style="pointer-events: all;">
  </vcci-entity-toolbar>
  @if (!env.production) {
    <vcci-entity-movement-toolbar
      (entityMovementToolbarExpanded)="toggleToolbars($event)"
      [expanded]="expandedToolbars.movement"
      [all]="expandedToolbars.all"
      style="pointer-events: all;">
    </vcci-entity-movement-toolbar>
    <vcci-shape-toolbar
      (shapeToolbarExpanded)="toggleToolbars($event)"
      [all]="expandedToolbars.all"
      [expanded]="expandedToolbars.drawing"
      style="pointer-events: all;">
    </vcci-shape-toolbar>
  }
  <vcci-shape-options-toolbar
    (shapeOptionsToolbarExpanded)="toggleToolbars($event)"
    [all]="expandedToolbars.all"
    [expanded]="expandedToolbars.styling"
    style="pointer-events: all;">
  </vcci-shape-options-toolbar>
  <vcci-right-toolbar-options
    (rightToolbarsExpandedEmitter)="toggleToolbars($event)"
    [expanded]="expandedToolbars.all"
    style="pointer-events: all;">
  </vcci-right-toolbar-options>
  <!--  <vcci-additional-actions-toolbar/>-->
</ng-container>
