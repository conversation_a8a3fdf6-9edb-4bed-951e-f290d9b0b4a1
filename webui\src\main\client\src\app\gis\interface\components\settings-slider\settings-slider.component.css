.slider-wrapper {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
}

.slider-background-wrapper {
  width: 100%;
}

.slider-background-world {
  width: 40px;
  height: auto;
  margin-bottom: -10px;
}

.slider-background-scale {
  margin-top: -30px;
  width: 20px;
  height: auto;
}

.element-resize-scale {
  margin-top: 0;
  width: 60px;
  height: auto;
}

::ng-deep .map-opacity-slider.mat-mdc-slider.mat-primary {
  --mdc-slider-handle-color: var(--calian-secondary);
  --mdc-slider-focus-handle-color: var(--calian-secondary);
  --mdc-slider-hover-handle-color: var(--calian-secondary);
  --mdc-slider-active-track-color: var(--text);
  --mdc-slider-inactive-track-color: var(--text);
  --mdc-slider-with-tick-marks-active-container-color: var(--text);
  --mdc-slider-with-tick-marks-inactive-container-color: var(--text);
  --mat-mdc-slider-ripple-color: var(--text);
  --mat-mdc-slider-hover-ripple-color: var(--text);
  --mat-mdc-slider-focus-ripple-color: var(--text);
}

::ng-deep .mat-mdc-menu-panel {
  overflow: unset;
}

.active {
  background-color: var(--active);
}

.inActive {
  background-color: var(--calian-secondary);
}
