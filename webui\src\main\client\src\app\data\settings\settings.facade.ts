import { Injectable } from '@angular/core';
import {
  DEFAULT_SETTINGS,
  isValidSettings,
  Settings,
  SettingsFacadeBase
} from './settings.model';
import { PayloadTypedAction } from '@simfront/common-libs';
import { SettingsResetDialog } from './settings-reset.dialog';
import { LayoutActions } from 'src/app/core/actions/layout.actions';
import { concatMap, take, tap } from 'rxjs';
import { resetAllSettings, showSnackBar } from './actions/settings.action';
import { createEffect, ofType } from '@ngrx/effects';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({ providedIn: 'root' })
export class SettingsFacade extends SettingsFacadeBase {
  resetAllSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(resetAllSettings),
      concatMap(() => [
        this.actions.updateOne({
          id: 'user',
          changes: DEFAULT_SETTINGS
        }),
        LayoutActions.setInitialLayoutState({...DEFAULT_SETTINGS.connPanel, ...DEFAULT_SETTINGS.layout}),
        showSnackBar({ message: 'Setting have been set to default.' })
      ])
    )
  );
  //TODO Move snackbar handling to its own reducer and make it global for shared snackbar access across facades
  showSnackBar$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(showSnackBar),
        tap(({ message }) => {
          this.snackbar.open(message, 'Close', {
            duration: 2000
          });
        })
      ),
    { dispatch: false }
  );

  constructor(private snackbar: MatSnackBar) {
    super();
    // TODO: Remove this setTimeout, currently needed to avoid entities not accessible in console
    setTimeout(() => {
      this.get$('user')
        .pipe(take(1))
        .subscribe((settings) => {
          this.store.dispatch(
            LayoutActions.setInitialLayoutState({
              showLeftSideNav: settings.connPanel.expanded,
              showRightSideNav: settings.connPanel.expanded,
              sidePanel: settings.connPanel.mode,
              connectionPanelDisplay: settings.connPanel.connectionDisplay,
              connectionsExpanded: settings.connPanel.connectionsExpanded,
              panelPosition: settings.connPanel.position,
              hideDataman: settings.layout.hideDataman,
              hideArcs: settings.layout.hideArcs
            })
          );
        });
    }, 0);
  }

  override inboundMapOne<P, Type extends string>(
    t: Settings,
    pta: PayloadTypedAction<P, Type>
  ): Settings {
    const valid = isValidSettings(t);
    if (!valid) {
      this.dialog.open(SettingsResetDialog);
      this.createOne(DEFAULT_SETTINGS, 'user');
      return DEFAULT_SETTINGS;
    }
    return t;
  }
}
