import { createEverything } from '@simfront/common-libs';
import { environment } from '../../../environments/environment';
import { RootState } from '../index';

export interface LogEntry {
  stackTrace: string;
  message: string;
  originator: string;
  time: number;
  severity: LogSeverity;
}

export type LogSeverity = 'DEBUG' | 'TRACE' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
export const LOG_SEVERITIES: LogSeverity[] = [
  'DEBUG',
  'TRACE',
  'INFO',
  'WARN',
  'ERROR',
  'FATAL'
];

export const {
  reducer: logReducer,
  facade: LogFacadeBase,
  initialState
} = createEverything<RootState, LogEntry, 'log', any, string>(
  'log',
  l => `${l.originator}-${l.time}`,
  `${environment.origin}/logs`,
  state => state.log ?? initialState
);
