import { Pipe, PipeTransform } from '@angular/core';

export type StringTransformType = 'uppercase' | 'titleCase' | 'first' | 'custom' | 'fromCamelCase';

@Pipe({
  name: 'string',
  pure: true,
  standalone: true
})
export class StringPipe implements PipeTransform {
  transform(value: string, type: StringTransformType, func?: (v: string, a: any) => string, args?: any): string {
    switch (type) {
      case 'first':
        return value.charAt(0).toUpperCase();
      case 'titleCase':
        return value.replace(
          /\w\S*/g,
          txt => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
        );
      case 'uppercase':
        return value.toUpperCase();
      case 'custom':
        return func ? func(value, args) : value;
      case 'fromCamelCase':
        const converted = value.replace(/([a-z])([A-Z])/g, '$1 $2');
        return `${converted.charAt(0).toUpperCase()}${converted.substring(1)}`;
    }
  }
}
