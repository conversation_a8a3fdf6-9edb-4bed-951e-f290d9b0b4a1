import { Directive, EventEmitter, Input, Output } from '@angular/core';
import { DataParameter } from '../../../../data/bs-params/bs-params.model';

@Directive()
export abstract class BsDataParamBase {
  @Input() usingDefault = false;
  @Output() getNext: EventEmitter<DataParameter> = new EventEmitter();
  @Output() networkInterfaceChanges = new EventEmitter<string>();
  onNext(event: DataParameter): void {
    this.getNext.emit(event);
  }
  onNetworkInterfaceChange(event: string): void {
    this.networkInterfaceChanges.emit(event);
  }
}
