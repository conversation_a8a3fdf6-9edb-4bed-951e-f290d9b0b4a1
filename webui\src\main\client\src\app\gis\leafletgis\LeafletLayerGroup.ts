/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 1/3/2024, 12:29 PM
 */
import { Layer, LayerGroup } from 'leaflet';
import { getLayerGroupVisibility } from '../interface/gis-util';
import { MapLayer } from '../interface/MapLayer';
import { AbstractMapLayerGroup, MapLayerGroup } from '../interface/MapLayerGroup';
import { LayerGroupExtended } from './LayerGroupExtended';
import { LeafletMap } from './LeafletMap';
import { LeafletMapDisplay } from './LeafletMapDisplay';
import {LeafletMapLayer} from "./LeafletMapLayer";

export class LeafletLayerGroup extends AbstractMapLayerGroup<Layer, LayerGroup> {
  leafletMap: LeafletMap;

  constructor(mapDisplay: LeafletMapDisplay, layerGroup?: LayerGroup) {
    super(mapDisplay);
    this.leafletMap = mapDisplay.leafletMap;
    if (layerGroup) {
      this.layerGroup = layerGroup;
    }
  }

  setGISGroupLayer(layer: LayerGroup): void {
    this.layerGroup = layer;
  }

  getGISGroupLayer(): LayerGroup {
    return this.layerGroup;
  }

  addLayer(
    mapLayer: MapLayer<Layer> | MapLayerGroup<Layer, LayerGroupExtended>,
    parentLayer?: MapLayerGroup<Layer, LayerGroupExtended>
  ): void {
    this.childLayers[mapLayer.layerId] = mapLayer;
    if (mapLayer['getGISLayer'] !== undefined) {
      this.layerGroup.addLayer(mapLayer.getGISLayer());
    } else {
      this.layerGroup.addLayer((mapLayer as MapLayerGroup<Layer, LayerGroupExtended>).getGISGroupLayer())
    }
  }

  removeLayer(
    mapLayer?: MapLayer<Layer>,
    mapLayerId?: string,
    mapLayerName?: string
  ): void {
    if (mapLayer) {
      delete this.childLayers[mapLayer.layerId];
      this.layerGroup.removeLayer(mapLayer.getGISLayer());
    } else if (mapLayerId) {
      delete this.childLayers[mapLayerId];
      this.layerGroup.removeLayer(parseInt(mapLayerId, 10));
    } else if (mapLayerName) {

    }
  }

  setVisible(visible: boolean): void {
    this._visible = true;
    if (visible) {
      this.layerGroup.addTo(this.leafletMap);
    } else {
      this.layerGroup.removeFrom(this.leafletMap);
    }
  }

  getVisibility(): boolean {
    const visibility = getLayerGroupVisibility(this);
    return true;
    // this.layerGroup.eachLayer();
    // Object.values(this.childLayers).forEach(child => {
    //   if (child instanceof LeafletMapLayerGroup) {
    //     return child.getVisibility();
    //   }
    //   child.visible;
    // });
  }

  findLayerAndRemove(name: string): void {
    const removeLayerRecursively = (group: LayerGroup): void => {
      group.eachLayer((layer: Layer) => {
        if (layer instanceof LayerGroup) {
          // Recursively check nested LayerGroups
          removeLayerRecursively(layer);
        } else if (layer['layerName'] === name) {
          // Remove the layer from the layer group
          group.removeLayer(layer);

          // Remove the layer from the map (if added to the map)
          if (layer.onRemove && layer['map']) {
            layer.onRemove(layer['map']);
          }
        }
      });
    };

    removeLayerRecursively(this.layerGroup);
  }

  getLayer(layerName?: string, layerId?: string): MapLayer<Layer> {
    return null;

    // if (layerName) {
    //   if (this.layerName === layerName) {
    //     return this.;
    //   }
    //
    //   Object.values(this.childLayers).forEach(child => {
    //     if (child instanceof LeafletMapLayerGroup) {
    //       const foundLayer = child.getLayer(layerName);
    //       if (foundLayer) {
    //         if (foundLayer instanceof LeafletMapLayerGroup) {
    //           return foundLayer;
    //         }
    //         if (foundLayer instanceof LeafletMapLayer) {
    //           return foundLayer.getGISLayer();
    //         }
    //       }
    //     } else if (child.layerName === layerName) {
    //       if (child instanceof LeafletMapLayer) {
    //         return child.getGISLayer();
    //       }
    //     }
    //   });
    // } else {
    //   Object.values(this.childLayers).forEach(child => {
    //     if (child instanceof LeafletMapLayerGroup) {
    //       const foundLayer = child.getLayer(undefined, layerId);
    //       if (foundLayer) {
    //         if (foundLayer instanceof LeafletMapLayerGroup) {
    //           return foundLayer;
    //         }
    //         if (foundLayer instanceof LeafletMapLayer) {
    //           return foundLayer.getGISLayer();
    //         }
    //       }
    //     } else if (child.layerName === layerName) {
    //       if (child instanceof LeafletMapLayer) {
    //         return child.getGISLayer();
    //       }
    //     }
    //   });
    // }
    //
    // return null;
  }
}
