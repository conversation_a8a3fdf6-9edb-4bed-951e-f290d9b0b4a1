import { Injectable } from '@angular/core';
import { GisService } from '../gis.service';
import { Entity } from '../../../data/entity/entity.model';
import * as L from 'leaflet';
import 'leaflet.markercluster';
import './leaflet-tilelayer-wmts.js';
import { CLUSTER_GROUP_OPTIONS, LeafletEntity, MAP_OPTIONS, TILE_LAYERS_OPTIONS } from './leaflet.model';
import { LayerOptions } from '../gis.model';
import { SFMap } from '@simfront/common-libs';

@Injectable()
export class LeafletService extends GisService {

  private map: L.Map;
  private entities: Map<string, LeafletEntity> = new Map();
  private layers: Map<string, L.TileLayer> = new Map();
  private markerClusterGroup: L.MarkerClusterGroup;
  private ms2525IconTextureHash: SFMap<L.DivIcon> = {};

  override initMap() {
    // Create the map.
    this.map = L.map('map', MAP_OPTIONS);
    // Listen to resize.
    this.container = this.map.getContainer();
    // Add default maps here.
    this.addLayer(
      'NatGeo_World_Map',
      'https://server.arcgisonline.com/arcgis/rest/services/NatGeo_World_Map/MapServer/WMTS',
    );
    this.addLayer(
      'Leaflet Open Street',
      'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      { opacity: 0.5, zIndex: 2 }
    );
    // Create the cluster group, this helps a lot with performance.
    this.markerClusterGroup = L.markerClusterGroup(CLUSTER_GROUP_OPTIONS);
    this.map.addLayer(this.markerClusterGroup);
  }

  override removeLayer(layerName: string): void {
    const layer = this.layers.get(layerName);
    if (layer) {
      this.map.removeLayer(layer);
      this.layers.delete(layerName);
    }
  }

  override addEntity(entity: Entity) {
    if (!this.map || !this.markerClusterGroup) {
      return;
    }
    if (this.entities.has(entity.uniqueId)) {
      // const { symbolCode, coordinates, bearing } = extractEntityInfo(entity);
      // const symbol = new ms.Symbol(symbolCode, { size: 25, direction: `${bearing}`, commonIdentifier: entity.name }).asSVG();
      // const icon = L.divIcon({
      //   className: 'my-custom-icon',
      //   html: symbol,
      //   iconSize: [25, 25],
      //   iconAnchor: [17.5, 17.5]
      // });
      // this.entities.get(entity.uniqueId).setLatLng([
      //   entity.defaultLayerIcon.location.latitude,
      //   entity.defaultLayerIcon.location.longitude
      // ]).setIcon(icon);
    } else {
      this.createMilitarySymbol(entity);
    }
  }

  private createMilitarySymbol(entity: Entity): void {
    // const { symbolCode, coordinates, bearing } = extractEntityInfo(entity);
    // const type = Array.isArray(coordinates[0]) ? ShapeType.POLYLINE : ShapeType.POINT;
    // switch (type) {
    //   case ShapeType.POINT:
    //     // TODO: Merge the SVG with a direction arrow so I can update it in one call.
    //     if (!this.ms2525IconTextureHash[symbolCode]) {
    //       const symbol = new ms.Symbol(symbolCode, { size: 25, direction: `${bearing}`, commonIdentifier: entity.name }).asSVG();
    //       this.ms2525IconTextureHash[symbolCode] = L.divIcon({
    //         className: 'my-custom-icon',
    //         html: symbol,
    //         iconSize: [25, 25],
    //         iconAnchor: [17.5, 17.5]
    //       });
    //     }
    //     const marker = L.marker(coordinates as Coordinate, { icon: this.ms2525IconTextureHash[symbolCode] })
    //       .addTo(this.markerClusterGroup);
    //     this.entities.set(entity.uniqueId, marker);
    //     break;
    //   case ShapeType.POLYLINE:
    //     break;
    //   default:
    //     break;
    // }
  }

  override addTileLayer(layerName: string, url: string, options?: LayerOptions) {
    this.layers.set(layerName, L.tileLayer(url, { ...TILE_LAYERS_OPTIONS, ...options }).addTo(this.map));
  }

  override addWMSLayer(layerName: string, url: string, options?: LayerOptions) {
    this.layers.set(layerName, L.tileLayer.wms(url, { ...TILE_LAYERS_OPTIONS, ...options }).addTo(this.map));
  }

  override async addWMTSLayer(layerName: string, url: string, options?: LayerOptions) {
    const capabilities = await L.tileLayer.getWMTSCapabilities(url);
    this.layers.set(layerName, L.tileLayer.wmts(url, { maxZoom: 21, ...capabilities, ...options }).addTo(this.map));
  }

  override resize(): void {
    if (this.map && this.map.getContainer()) {
      this.map.invalidateSize();
    }
  }

}
