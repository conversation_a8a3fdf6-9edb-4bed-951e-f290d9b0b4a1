import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ChangeDetectionStrategy, Component } from '@angular/core';

import { Store } from '@ngrx/store';

import { environment } from '../../../environments/environment';
import { DataService } from '../../data/data.service';
import { customWidthExpandCollapse } from '../../shared/util/animation';

import { LayoutActions } from '../actions/layout.actions';
import {
  ConnectionPanelDisplay,
  selectConnectionPanelDisplay,
  selectConnectionsExpanded,
  selectHideArcs,
  selectPanelPosition,
  selectShowLeftSideNav,
  selectShowRightSideNav,
  selectSidePanel
} from '../reducers/layout.reducer';
import { IconsService } from '../services/icons.service';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';

@Component({
  selector: 'vcci-app',
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: 'app2.component.html',
  styleUrls: ['app2.component.scss'],
  animations: [customWidthExpandCollapse('translate', '60px', '60px')],
  standalone: false
})
export class App2Component {

  sidePanelMode$ = this.store.select(selectSidePanel);
  sidePanelPos$ = this.store.select(selectPanelPosition);
  showRightSideNav$ = this.store.select(selectShowRightSideNav);
  showLeftSideNav$ = this.store.select(selectShowLeftSideNav);
  displayType$ = this.store.select(selectConnectionPanelDisplay);
  connectionsExpanded$ = this.store.select(selectConnectionsExpanded);
  hideArc$ = this.store.select(selectHideArcs);

  payload = {
    path: '',
    host: '',
    user: 'vcci',
    password: 'vcci'
  };

  constructor(
    private store: Store,
    private http: HttpClient,
    public data: DataService,
    private iconRegistry: IconsService,
    private router: Router
  ) {
    this.data.node.readAll();
    this.data.connection.readAll();
    this.data.endpoint.readAll();
    this.data.dataPath.readAll();
    this.data.entity.readAll();
    this.data.layer.readAll();
    this.iconRegistry.init();
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.restartAnimation();
      });
  }

  restartAnimation(): void {
    const arcBox = document.querySelector('.arc-box');
    if (arcBox) {
      const presentArc = arcBox.innerHTML;
      arcBox.innerHTML = '';
      arcBox.innerHTML = presentArc;
    }
  }

  handleSideNavToggle(open: boolean, isLeft = true): void {
    this.store.dispatch(
      isLeft
        ? LayoutActions.toggleLeftSidenav(open)
        : LayoutActions.toggleRightSidenav(open)
    );
  }

  databaseBackup(): void {
    this.http
      .get(`${environment.origin}/database/backup`, { responseType: 'text' })
      .subscribe((val) => {
        console.log(val);
        //
      });
  }

  databaseRestore(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.abk';
    fileInput.title = 'Select a backup file to restore';
    fileInput.capture = 'environment';
    fileInput.addEventListener('change', (event: Event) => {
      const target: HTMLInputElement = event.target as HTMLInputElement;

      if (target.files) {
        const file: File = target.files[0];

        this.payload.path = file.name;
        this.http
          .post('http://localhost:8086/database/restore', this.payload, {
            headers: new HttpHeaders().set('Content-Type', 'application/json'),
            responseType: 'text'
          })
          .subscribe({
            next: (response) => {
              console.log('POST request successful', response);
            },
            error: (error) => {
              console.error('POST request error', error);
            }
          });
      }
    });

    fileInput.click();

    // this.dialogRef = this.dialog.open(FileChooserComponent, {
    //   width: '500px'
    // });
    // this.dialogRef.componentInstance.fileSelected.subscribe((file: File) => {
    //   this.payload.path = file.name;
    //   this.http.post('http://localhost:8086/database/restore', this.payload, {
    //     headers: new HttpHeaders().set('Content-Type', 'application/json'),
    //     responseType: 'text'
    //   }).subscribe({
    //     next: (response) => {
    //       console.log('POST request successful', response);
    //     },
    //     error: (error) => {
    //       console.error('POST request error', error);
    //     }
    //   });
    //
    //
    // });
  }

  changeConnectionDisplayType(type: ConnectionPanelDisplay): void {
    this.store.dispatch(LayoutActions.changeConnectionPanelDisplay(type));
  }
}
