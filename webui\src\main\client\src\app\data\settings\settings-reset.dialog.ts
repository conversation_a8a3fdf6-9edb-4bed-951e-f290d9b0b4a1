import { Component } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'settings-reset-dialog',
  standalone: true,
  imports: [MatDialogModule, MatIconModule, MatButtonModule],
  template: ` <div mat-dialog-content>
      Default settings were reset. Please reconfigure your preferences.
    </div>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Dismiss</button>
    </mat-dialog-actions>`
})
export class SettingsResetDialog {}
