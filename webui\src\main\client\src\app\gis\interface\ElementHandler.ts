import { ShapeStyleInfo } from './models/gis.model';

export interface ElementHandler {
  shapeStyleInfo: ShapeStyleInfo;

  updateSelectedElementStyle(): void;
}

export  abstract class MapElementHandler implements ElementHandler {
  private _shapeStyleInfo: ShapeStyleInfo = {
    normal: {
      strokeInfo: {
        strokeColor: 'rgb(0, 0, 255)',
        strokeWidth: 1,
        strokeType: 'solid'
      },
      fillInfo: {
        fillType: 'plain',
        fillColor: 'rgb(0, 0, 0, 0.2)'
      }
    },
    selected: {
      strokeInfo: {
        strokeColor: 'rgb(255,213,0)',
        strokeWidth: 1,
        strokeType: 'solid'
      },
      fillInfo: {
        fillType: 'plain',
        fillColor: 'rgb(0, 0, 0, 0.2)'
      }
    }
  };

  abstract updateSelectedElementStyle(): void;

  set shapeStyleInfo(shapeStyleInfo: ShapeStyleInfo) {
    this._shapeStyleInfo = shapeStyleInfo;
  }

  get shapeStyleInfo(): ShapeStyleInfo {
    return this._shapeStyleInfo;
  }
}

