import { enableProdMode, EnvironmentInjector } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

let globalInjector: EnvironmentInjector;

if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .then((appRef) => {
    globalInjector = appRef.injector;
  })
  .catch((err) => console.error(err));

// Function to get the global injector outside Angular
export function getGlobalInjector(): EnvironmentInjector {
  if (!globalInjector) {
    throw new Error('Injector not yet available');
  }
  return globalInjector;
}
