// @ts-check
const prettierPlugin = require("eslint-plugin-prettier");
const typescriptParser = require("@typescript-eslint/parser");
const tsPlugin = require("@typescript-eslint/eslint-plugin");
const angularPlugin = require("@angular-eslint/eslint-plugin");
const angularTemplateParser = require("@angular-eslint/template-parser");
const eslintPluginPrettierRecommended = require("eslint-plugin-prettier/recommended");

module.exports = [
  {
    ignores: ["node_modules/", "projects/**/*"],
  },
  {
    files: ["**/*.ts"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        project: [
          "./tsconfig.json",
          "./tsconfig.app.json",
          "./tsconfig.spec.json",
        ],
      },
    },
    plugins: {
      "@typescript-eslint": tsPlugin,
      "@angular-eslint": angularPlugin,
      prettier: prettierPlugin,
    },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      ...angularPlugin.configs.recommended.rules,
      ...prettierPlugin.configs?.rules,

      "@angular-eslint/directive-selector": [
        "error",
        {
          type: "attribute",
          prefix: ["vcci", "nexus"],
          style: "camelCase",
        },
      ],
      "@angular-eslint/component-selector": [
        "error",
        {
          type: "element",
          prefix: ["vcci", "nexus"],
          style: "kebab-case",
        },
      ],
      "@typescript-eslint/no-empty-interface": [
        "error",
        { allowSingleExtends: true },
      ],
      "@typescript-eslint/comma-dangle": ["error", "never"],
      "linebreak-style": ["error", "windows"],
      "@typescript-eslint/lines-between-class-members": [
        "error",
        "always",
        { exceptAfterSingleLine: true },
      ],
      "import/extensions": [
        "error",
        "ignorePackages",
        {
          js: "never",
          jsx: "never",
          ts: "never",
          tsx: "never",
        },
      ],
      "import/prefer-default-export": "off",
      "object-curly-newline": ["error", { multiline: true }],
      "quote-props": ["error", "consistent-as-needed"],
      "spaced-comment": ["error", "always"],
      "arrow-parens": ["error", "as-needed"],
      "eol-last": ["error", "always"],
      "no-multiple-empty-lines": ["error", { max: 1, maxEOF: 1 }],
      "no-underscore-dangle": "off",
      "no-param-reassign": ["error", { props: false }],
      "import/no-cycle": "off",
      "class-methods-use-this": "warn",
      "no-multi-spaces": "off",
      "@typescript-eslint/type-annotation-spacing": [
        "error",
        {
          before: false,
          overrides: { arrow: { before: true } },
        },
      ],
      "key-spacing": [
        "error",
        {
          beforeColon: false,
          afterColon: true,
          mode: "minimum",
        },
      ],
      "@typescript-eslint/member-delimiter-style": [
        "error",
        {
          multiline: {
            delimiter: "comma",
            requireLast: false,
          },
          singleline: {
            delimiter: "comma",
            requireLast: false,
          },
          overrides: {
            interface: {
              multiline: {
                delimiter: "semi",
                requireLast: true,
              },
            },
          },
        },
      ],
      "@typescript-eslint/no-unused-expressions": [
        "error",
        { allowTernary: true },
      ],
      "no-console": "off",
      "prettier/prettier": [
        "error",
        {
          endOfLine: "auto",
        },
      ],
    },
  },
  {
    files: ["**/*.html"],
    languageOptions: {
      parser: angularTemplateParser,
    },
    plugins: {
      "@angular-eslint": angularPlugin,
      "@angular-eslint/template": angularPlugin,
      prettier: prettierPlugin,
    },
    rules: {
      "prettier/prettier": ["error", { parser: "angular" }],
    },
  },
  eslintPluginPrettierRecommended,
];
