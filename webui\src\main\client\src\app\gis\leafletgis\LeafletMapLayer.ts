import { Layer } from 'leaflet';
import { GISElement } from '../interface/GISElement';
import { AbstractMapLayer } from '../interface/MapLayer';
import { LeafletPixiContainer } from './leaflet-symbology-layer/LeafletPixiContainer';
import { MilitarySymbologyContainer } from './leaflet-symbology-layer/MilitarySymbologyContainer';
import { LeafletEntity } from './LeafletEntity';
import { LeafletMap } from './LeafletMap';
import { LeafletGraphics } from './LeafletGraphics';
import { LeafletTacticalGraphics } from './LeafletTacticalGraphics';
export class LeafletMapLayer extends AbstractMapLayer<Layer> {
  map: LeafletMap;

  constructor(
    layer?: Layer,
    id?: string,
    layername?: string,
    map?: LeafletMap
  ) {
    super(layer, id, layername);
    this.map = map;
  }

  getElement(id: string): GISElement<Layer> | null | undefined {
    if (id in this.elementCache) {
      return this.elementCache[id];
    }

    return null;
  }

  setLeafLetMap(leafletMap: LeafletMap): void {
    this.map = leafletMap;
  }

  gisAddElement(element: GISElement<Layer>): void {
    if (element.gisObject) {
      const gisLayer = this.getGISLayer();
      if (gisLayer && gisLayer instanceof MilitarySymbologyContainer) {
        const { gisObject } = element;
        if (gisObject instanceof LeafletEntity) {
          gisLayer.addGraphics(gisObject);
        } else if (gisObject instanceof LeafletGraphics) {
          gisLayer.addLeafletGraphics(gisObject);
        } else if (gisObject instanceof LeafletTacticalGraphics) {
          gisLayer.addTacticalGraphics(gisObject);
        }
      }
    }
  }

  gisRemoveElement(element?: GISElement<Layer>): boolean {
    if (element && element.gisObject) {
      const gisLayer: any = this.getGISLayer();
      if (gisLayer && gisLayer instanceof LeafletPixiContainer) {
        (gisLayer as LeafletPixiContainer).removeGraphics(element.gisObject);
      }
    }
    return false;
  }

  isVisible(): boolean {
    const layer = this.getGISLayer();
    if (layer && layer instanceof MilitarySymbologyContainer) {
      const { visible } = layer.getContainer();
      return !!visible;
    }
    return this._visible;
  }

  setEditable(editable: boolean): void {
    const layer = this.getGISLayer();
    if (layer instanceof MilitarySymbologyContainer) {
      layer.getContainer().interactiveChildren = editable;
    }
  }

  setGISLayerVisibility(visible: boolean): void {
    const layer = this.getGISLayer();
    if (layer instanceof Layer) {
      if (this.map) {
        if (visible) {
          layer.addTo(this.map);
        } else {
          layer.removeFrom(this.map);
        }
      }
    }
  }

  setGISLayerGroupVisibility(visible: boolean): void {
    const layer = this.getGISLayer();
    if (layer instanceof Layer) {
      if (this.map) {
        if (visible) {
          layer.addTo(this.map);
        } else {
          layer.removeFrom(this.map);
        }
      }
    }
  }

  setSelectable(selectable: boolean): void {
    const layer = this.getGISLayer();
    if (layer instanceof MilitarySymbologyContainer) {
      layer.getContainer().interactiveChildren = selectable;
    }
  }

  override async redraw(): Promise<void> {
    if (this.isRedrawLayer()) {
      const lyr = this.getGISLayer();
      if (lyr instanceof LeafletPixiContainer && this.map.hasLayer(lyr)) {
        lyr.redrawAll();
      }
    }
    return null;
  }

  isFeatureLayer(): boolean {
    const layer = this.getGISLayer();
    return layer instanceof LeafletPixiContainer;
  }
}
