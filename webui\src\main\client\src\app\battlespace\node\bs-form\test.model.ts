import {
  DataPage,
  DataParameter,
  DataSection
} from '../../../data/bs-params/bs-params.model';
import { SFMap } from '@simfront/common-libs';

const PARAMS_BANK: SFMap<DataParameter> = {
  STRING: {
    name: 'STRING',
    visible: true,
    type: 'STRING',
    editable: true,
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx: ''
  },
  INT: {
    name: 'INT',
    visible: true,
    type: 'INT',
    editable: true,
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx: ''
  },
  BOOLEAN: {
    name: 'BOOLEAN',
    visible: true,
    type: 'BOOLEAN',
    editable: true,
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx: ''
  },
  IPADDRESS: {
    name: 'IPADDRESS',
    visible: true,
    type: 'IPADDRESS',
    editable: true,
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx:
      '^(([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}([01]?\\d\\d?|2[0-4]\\d|25[0-5])$'
  },
  NETWORK_INTERFACE: {
    name: 'NETWORK_INTERFACE',
    visible: true,
    editable: true,
    type: 'NETWORK_INTERFACE',
    mandatory: true,
    list: true,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    listOptions: ['************', '**************'],
    defaultValue: '',
    validatorRegEx: ''
  },
  FILE: {
    name: 'FILE',
    visible: true,
    editable: true,
    type: 'FILE',
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx: ''
  },
  IDENTIFIER: {
    name: 'IDENTIFIER',
    visible: true,
    editable: true,
    type: 'IDENTIFIER',
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx: '^[a-zA-Z0-9@#\\-_! ]*$'
  },
  IPPORT: {
    name: 'IPPORT',
    visible: true,
    editable: true,
    type: 'IPPORT',
    mandatory: true,
    list: false,
    valueList: false,
    hasNext: false,
    hasPrev: false,
    value: '',
    defaultValue: '',
    validatorRegEx:
      '^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$'
  }
};

export const CREATE_PARAM = (param?: Partial<DataParameter>): DataParameter =>
  ({
    ...PARAMS_BANK[param?.type || 'STRING'],
    ...(param || {})
  }) as DataParameter;
export const CREATE_PARAMS = (
  range: number,
  id?: 'ENDPOINT' | 'CONNECTION',
  random = true
): SFMap<DataParameter> =>
  Array.from({ length: random ? RANDOM_NUMBER(1, range) : range }, (_, i) =>
    CREATE_PARAM(
      i === 0 && id
        ? { name: `${id}_NAME`, type: 'IDENTIFIER' }
        : { name: `PARAM_${i + 1}` }
    )
  ).reduce<SFMap<DataParameter>>((acc, param) => {
    acc[param.name] = param;
    return acc;
  }, {});

export const CREATE_SECTION = (
  section?: Partial<DataSection>
): DataSection => ({
  sectionName: '',
  mandatory: true,
  lastSection: false,
  parameters: {},
  ...(section || {})
});
export const CREATE_SECTIONS = (
  range: number,
  id?: 'ENDPOINT' | 'CONNECTION',
  paramRange: number = 6,
  random = true
): SFMap<DataSection> =>
  Array.from({ length: random ? RANDOM_NUMBER(1, range) : range }, (_, i) =>
    CREATE_SECTION({
      sectionName: `Section ${i + 1}`,
      parameters: CREATE_PARAMS(paramRange, i === 0 ? id : undefined, random),
      lastSection: i === range - 1
    })
  ).reduce<SFMap<DataSection>>((acc, section) => {
    acc[section.sectionName] = section;
    return acc;
  }, {});

export const CREATE_PAGE = (page?: Partial<DataPage>): DataPage => ({
  pageName: '',
  mandatory: true,
  lastPage: false,
  parameters: {},
  sections: {},
  ...(page || {})
});
export const CREATE_PAGES = (
  pageRange: number,
  id: 'ENDPOINT' | 'CONNECTION',
  sectionRange: number = 3,
  paramRange: number = 6,
  random = true
): DataPage[] =>
  Array.from({ length: pageRange }, (_, i) =>
    CREATE_PAGE({
      pageName: `Page ${i + 1}`,
      lastPage: i === pageRange - 1,
      sections: CREATE_SECTIONS(
        sectionRange,
        i === 0 ? id : undefined,
        paramRange,
        random
      )
    })
  );

export const CONNECTION_PARAM: DataParameter = CREATE_PARAM({
  name: 'CONNECTION_NAME',
  type: 'IDENTIFIER'
});
export const IP_PARAM: DataParameter = CREATE_PARAM({
  name: 'PARAMETER_IPADDRESS',
  type: 'IPADDRESS'
});
export const PORT_PARAM: DataParameter = CREATE_PARAM({
  name: 'PARAMETER_PORT',
  type: 'IPPORT'
});
export const BIND_IP_PARAM: DataParameter = CREATE_PARAM({
  name: 'PARAMETER_BIND_IPADDRESS',
  type: 'NETWORK_INTERFACE'
});
export const ENDPOINT_PARAM: DataParameter = CREATE_PARAM({
  name: 'ENDPOINT_NAME',
  type: 'IDENTIFIER'
});
export const FILE_PARAM: DataParameter = CREATE_PARAM({
  name: 'FILE_PARAM',
  type: 'FILE'
});

export const EXAMPLE_PARAMS_ONE: SFMap<DataParameter> = {
  CONNECTION_NAME: CONNECTION_PARAM,
  PARAMETER_IPADDRESS: IP_PARAM,
  PARAMETER_PORT: PORT_PARAM,
  PARAMETER_BIND_IPADDRESS: BIND_IP_PARAM,
  ENDPOINT_NAME: ENDPOINT_PARAM,
  FILE_PARAM: FILE_PARAM,
  USE_AUTH: CREATE_PARAM({ name: 'USE_AUTH', type: 'BOOLEAN' }),
  USERNAME: CREATE_PARAM({
    name: 'USERNAME',
    hasPrev: true,
    prevParamName: 'USE_AUTH'
  }),
  PASSWORD: CREATE_PARAM({
    name: 'PASSWORD',
    hasPrev: true,
    prevParamName: 'USE_AUTH'
  }),
  GRP_A: CREATE_PARAM({ name: 'GRP_A', type: 'BOOLEAN' }),
  GRP_A_1: CREATE_PARAM({
    name: 'GRP_A_1',
    group: {
      name: 'GRP_A',
      groupType: 'SINGLE_SELECT',
      mandatory: true,
      defaultValues: []
    },
    hasPrev: true,
    prevParamName: 'GRP_A'
  }),
  GRP_A_2: CREATE_PARAM({
    name: 'GRP_A_2',
    group: {
      name: 'GRP_A',
      groupType: 'SINGLE_SELECT',
      mandatory: true,
      defaultValues: []
    },
    hasPrev: true,
    prevParamName: 'GRP_A'
  }),
  GRP_A_3: CREATE_PARAM({
    name: 'GRP_A_3',
    group: {
      name: 'GRP_A',
      groupType: 'SINGLE_SELECT',
      mandatory: true,
      defaultValues: []
    },
    hasPrev: true,
    prevParamName: 'GRP_A'
  }),
  GRP_B: CREATE_PARAM({ name: 'GRP_B' }),
  GRP_B_1: CREATE_PARAM({
    name: 'GRP_B_1',
    group: {
      name: 'GRP_B',
      groupType: 'SINGLE_SELECT',
      mandatory: true,
      defaultValues: []
    },
    type: 'BOOLEAN',
    hasPrev: true,
    prevParamName: 'GRP_B'
  }),
  GRP_B_2: CREATE_PARAM({
    name: 'GRP_B_2',
    group: {
      name: 'GRP_B',
      groupType: 'SINGLE_SELECT',
      mandatory: true,
      defaultValues: []
    },
    type: 'BOOLEAN',
    hasPrev: true,
    prevParamName: 'GRP_B'
  }),
  GRP_B_3: CREATE_PARAM({
    name: 'GRP_B_3',
    group: {
      name: 'GRP_B',
      groupType: 'SINGLE_SELECT',
      mandatory: true,
      defaultValues: []
    },
    type: 'BOOLEAN',
    hasPrev: true,
    prevParamName: 'GRP_B'
  })
};

export const EXAMPLE_SECTION_ONE: DataSection = CREATE_SECTION({
  sectionName: 'Configuration',
  parameters: EXAMPLE_PARAMS_ONE
});
export const EXAMPLE_PAGE_ONE: DataPage = CREATE_PAGE({
  pageName: 'Service A',
  sections: { Configuration: EXAMPLE_SECTION_ONE }
});

export const RANDOM_NUMBER = (min = 1, max = 1) =>
  Math.floor(Math.random() * (max - min + 1) + min);
