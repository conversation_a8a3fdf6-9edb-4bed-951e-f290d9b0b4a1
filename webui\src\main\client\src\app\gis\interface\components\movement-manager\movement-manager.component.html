<h1 mat-dialog-title>Movement Manager</h1>
@if (creatingRoute()) {
  <mat-dialog-content> Select Route </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button (click)="restoreCreateMovement()">Done</button>
  </mat-dialog-actions>
} @else {
  <mat-dialog-content [formGroup]="createMovementForm">
    <div class="movement-tabs-header">
      <mat-form-field>
        <mat-label>Mission Name</mat-label>
        <input matInput type="text" formControlName="name" />
      </mat-form-field>
      <mat-form-field>
        <mat-label>Select Mission</mat-label>
        <mat-select formControlName="selectMission">
          <mat-option value="1">1</mat-option>
          <mat-option value="2">2</mat-option>
          <mat-option value="3">3</mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field>
        <mat-label>Movement Engine</mat-label>
        <mat-select
          formControlName="movementEngine"
          [value]="selectedNode()?.name"
          (selectionChange)="onNodeSelected($event)"
        >
          @for (node of movementEngine(); track node.name) {
            <mat-option [value]="node.name">{{ node.name }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>
    <nexus-movement-tabs
      [createMovementForm]="createMovementForm"
      [movementEngine]="selectedNode()"
      [creatingRoute]="creatingRoute"
      [savedMovement]="savedMovement()"
      (createRouteRequested)="openCreateRoute()"
      (createMovementRestoreRequested)="restoreCreateMovement()"
    ></nexus-movement-tabs>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button
      mat-button
      (click)="createMovement()"
      [disabled]="disableCreateMovement()"
    >
      Create
    </button>
    <button
      mat-button
      (click)="stashMovement()"
      [disabled]="disableStashMovement()"
    >
      <mat-icon>save</mat-icon>
      Stash
    </button>
    <button
      mat-button
      (click)="restoreStashedMovement()"
      [disabled]="disableRestoreMovement()"
    >
      <mat-icon>restore</mat-icon>
      Restore
    </button>
    <button mat-button mat-dialog-close (click)="cancelMovementCreation()">
      Close
    </button>
  </mat-dialog-actions>
}
