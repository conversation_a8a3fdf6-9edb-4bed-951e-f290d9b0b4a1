/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/21/2023, 1:40 PM
 */
import {QuadTreeNode} from './QuadtreeNode';

export class QuadTree {
  constructor(x, y, width, height, maxObjects, maxLevels) {
    this.root = new QuadTreeNode(x, y, width, height, maxObjects, maxLevels, 0);
  }

  insert(entity) {
    this.root.insert(entity);
  }

  retrieve(entity) {
    return this.root.retrieve(entity);
  }
}
