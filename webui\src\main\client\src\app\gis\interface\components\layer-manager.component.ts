/**
 * <AUTHOR> <<EMAIL>>
 * @company SimFront a Calian Company
 * Created on 12/22/2023, 2:04 PM
 */
import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  TemplateRef,
  ViewChild
} from '@angular/core';
import {
  buildTreeData,
  NestedTreeItem,
  TrackByFns,
  TreeUtilFunctions
} from '@simfront/common-libs';
import { mapActions } from '../actions/map.actions';
import {
  GridType,
  LayerInfo,
  LayerLockStatus,
  LayerManagerActions, LayerNode,
  LayerVisibility
} from '../../../data/layer-manager/layer-manager.model';
import { GisService } from '../service/gis.service';
import { getLayerInfo } from '../gis-util';
import { Dictionary } from '@ngrx/entity';
import { DataService } from '../../../data/data.service';

@Component({
  selector: 'vcci-layer-manager',
  templateUrl: './layer-manager.component.html',
  styleUrls: ['./layer-manager.component.css'],
  standalone: false
})

export class LayerManagerComponent implements AfterViewInit, OnChanges {
  @Input() allLayers: LayerNode[];
  @Input() show: boolean;
  trackByFunc = (_: number, layer: NestedTreeItem<LayerInfo>): string => `${layer.id}${layer.label}${layer.children.length > 0}`;
  trackByFuncCtnrl = (_: number, layer: NestedTreeItem<LayerInfo>): string => layer.id;
  trackByFns: TrackByFns<NestedTreeItem<LayerInfo>> = {
    template: this.trackByFunc,
    control: this.trackByFuncCtnrl
  };

  @ViewChild('custom') custom!: TemplateRef<any>;
  gridLayerTree: LayerInfo[] = [];
  isCollapsed = false;
  isSmallSize = false;
  layerManagerElement: HTMLElement;
  layerManagerLastHeight: string;
  toggleDown = false;
  isSlideToggleChecked = true;
  lastVisibleGridLayer: NestedTreeItem<LayerInfo>;
  layerListData: NestedTreeItem<LayerInfo>[] = [];
  gridLayerListData: NestedTreeItem<LayerInfo>[] = [];
  shapeLayerListData: NestedTreeItem<LayerInfo>[] = [];
  gridType: GridType = 'MGRS';
  openTreeAtNodeId: string;

  opacitySlider: { [key: string]: boolean } = {};


  addListToOpacitySlider(list: LayerInfo[]) {
    list.forEach(item => {
      if (item.opacity && !this.opacitySlider[item.layerId]) {
        this.opacitySlider[item.layerId] = false;
      }
    });
  }

  resizeObserver = new ResizeObserver(entries => {
    for (const entry of entries) {
      const {height} = entry.contentRect;
      if (height > 40) {
        this.layerManagerLastHeight = `${entry.contentRect.height}px`;
      }
    }
  });

  private LayerTreeNodeUtilFunctions: TreeUtilFunctions<LayerNode> = {
    id: (node: LayerNode): string => node.layerId,
    children: (node: LayerNode): LayerNode[] => [...node.layers],
    hasMenu: () => false,
    customTemplate: (n: LayerNode, templates: TemplateRef<unknown>[]): TemplateRef<unknown> | undefined => (templates.length > 0 ? templates[0] : undefined)
  }

  constructor(private gisService: GisService, private data: DataService) {

  }

  toggleCollapse(): void {
    // if (this.lyrTree) {
    //   this.lyrTree.openTreeAt('root', true);
    // }
    this.isCollapsed = !this.isCollapsed;
    this.isSmallSize = !this.isSmallSize;
    if (this.isSmallSize) {
      this.layerManagerElement.style.height = '40px';
    } else {
      this.layerManagerElement.style.height = this.layerManagerLastHeight;
    }
  }

  close(): void {
    this.gisService.mapDisplay.toggleLayerManagerVisibility();
  }

  getParentId(LayerList: LayerInfo[]) {
    if (LayerList[0]?.layerType === 'Parent') {
      return LayerList[0].layerId;
    }

    return undefined;
  }

  setLayerOpacity(layer, opacity: number): void {
    this.gisService.setLayerOpacity(layer, opacity);
    this.data.layer.setLayerOpacity(layer.layerId, opacity);
  }

  toggleOpacity(layerId: string) {
    this.opacitySlider[layerId] = !this.opacitySlider[layerId];
  }

  toggleAllGridLayers(checked: boolean): void {
    if (checked) {
      this.gisService.setLayerVisibility(this.lastVisibleGridLayer, checked);
    } else {
      this.lastVisibleGridLayer = this.gridLayerListData.find((layer) => layer.value.visibility === 'VISIBLE');
      if (this.lastVisibleGridLayer.value.layerName.includes('MGRS')) {
        this.gridType = 'MGRS';
      } else if (this.lastVisibleGridLayer.value.layerName.includes('Lat')) {
        this.gridType = 'LatLon';
      } else {
        this.gridType = 'GARS';
      }
      this.gridLayerListData.forEach(layerInfo => {
        this.gisService.setLayerVisibility(layerInfo, checked);
      });
    }
  }

  toggleGridLayers(layer: NestedTreeItem<LayerInfo>): void {
    this.gridLayerListData.forEach(layerData => {
      layer === layerData ? this.gisService.setGridLayerVisibility(layer, true) : this.gisService.setGridLayerVisibility(layerData, false);
    });

    if (layer.value.visibility === 'HIDDEN') {
      this.lastVisibleGridLayer = layer;
      if (this.lastVisibleGridLayer.value.layerName.includes('MGRS')) {
        this.gridType = 'MGRS';
      } else if (this.lastVisibleGridLayer.value.layerName.includes('Lat')) {
        this.gridType = 'LatLon';
      } else {
        this.gridType = 'GARS';
      }
    }
  }

  toggleLayerVisibility(layerNode: NestedTreeItem<LayerInfo>, visibility: boolean): void {
    this.gisService.setLayerVisibility(layerNode, visibility);
  }

  lockLayer(layerInfo: LayerInfo): void {
    let lockStatus: LayerLockStatus = 'unlocked';
    if (layerInfo.locked === 'unlocked') {
      lockStatus = 'locked';
    }
    this.gisService.store.dispatch(LayerManagerActions.setLayerLockStatus(
      {
        layerId: layerInfo.layerId,
        layerType: layerInfo.layerType,
        layerLockStatus: lockStatus
      }
    ));
  }


  ngOnChanges(changes: SimpleChanges): void {
    const dynamicLayerRoot = this.data.layer.getLayerById('-999', this.allLayers);
    this.layerListData = buildTreeData<LayerNode>(
      dynamicLayerRoot ? [dynamicLayerRoot] : [],
      this.LayerTreeNodeUtilFunctions,
      [this.custom]
    );
   const gridLayerRoot =  this.data.layer.getLayerById('-777', this.allLayers);
    this.gridLayerListData = buildTreeData<LayerInfo>(
      gridLayerRoot ? this.LayerTreeNodeUtilFunctions.children(gridLayerRoot) : [],
      this.LayerTreeNodeUtilFunctions,
      [this.custom]
    );
   const shapeLayerRoot =  this.data.layer.getLayerById('-888', this.allLayers);
    this.shapeLayerListData = buildTreeData<LayerInfo>(
      shapeLayerRoot ? [shapeLayerRoot] : [],
      this.LayerTreeNodeUtilFunctions,
      [this.custom]
    );

    const numberOfFalseGridLayers  = this.gridLayerTree.filter(
      layerInfo => layerInfo.visibility === 'HIDDEN'
    ).length;

    if (numberOfFalseGridLayers > 1) {
      this.isSlideToggleChecked = false;
    } else {
      this.isSlideToggleChecked = true;
    }

    //only Base layers can have their opacity set
    if (shapeLayerRoot) {
      this.addListToOpacitySlider(shapeLayerRoot.layers);
    }
  }

  ngAfterViewInit(): void {
    // this.lyrTree.openTreeAt('root', true);
    this.layerManagerElement = document.getElementById('layerManager');
    this.layerManagerLastHeight = this.layerManagerElement.style.height;
    this.addResizeListener();
    // this.updateResizeHandlePosition();
    window.addEventListener('resize', this.onResize.bind(this));
    this.openTreeAtNodeId = '-999';
  }

  toggleButtonReleased(): void {
    this.toggleDown = false;
  }

  removeResizeListener(): void {
    this.toggleDown = true;
    this.resizeObserver.disconnect();
    setTimeout(() => {
      if (!this.toggleDown) {
        this.resizeObserver.observe(this.layerManagerElement);
      }
    }, 2000);
  }

  addResizeListener(): void {
    this.resizeObserver.observe(this.layerManagerElement);
  }

  onResize(): void {
    const divHeight = this.layerManagerElement.clientHeight;
    // console.log('Div height after resizing:', divHeight);
  }

  // updateResizeHandlePosition(): void {
  //   const firstDivRect = this.layerManagerElement.getBoundingClientRect();
  // }

  // startResizing(event: MouseEvent): void {
  //   const element = document.getElementById('layerManager');
  //   const initialHeight = element.clientHeight;
  //   const initialY = event.clientY;
  //
  //   const moveListener = this.renderer.listen('document', 'mousemove', (moveEvent: MouseEvent) => {
  //     const deltaY = moveEvent.clientY - initialY;
  //     const newHeight = initialHeight + deltaY;
  //
  //
  //     if (newHeight <= 5) {
  //       this.isSmallSize = true;
  //     } else {
  //       element.style.height = `${newHeight}px`;
  //     }
  //   });
  //
  //   const upListener = this.renderer.listen('document', 'mouseup', () => {
  //     moveListener();
  //     upListener();
  //   });
  // }

}
