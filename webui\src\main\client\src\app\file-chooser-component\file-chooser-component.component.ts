import { Component, EventEmitter, Output } from '@angular/core';

@Component({
    selector: 'vcci-file-chooser-component',
    templateUrl: './file-chooser-component.component.html',
    styleUrls: ['./file-chooser-component.component.css'],
    standalone: false
})

export class FileChooserComponent {
  @Output() fileSelected = new EventEmitter<File>();

  openFileChooser(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.abk';

    fileInput.addEventListener('change', (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (target.files) {
        const file: File = target.files[0];
        this.fileSelected.emit(file);

        fileInput.value = '';
      }
    });

    fileInput.click();
  }
}
